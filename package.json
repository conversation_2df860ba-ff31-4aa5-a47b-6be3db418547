{"name": "cis-frontend-pc", "uniqueName": "cis-frontend-pc", "version": "1.0.9", "description": "Clinical Information System PC", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": true, "scripts": {"order-cloud:tag": "git abc tag config mf-order-cloud && git abc tag create && git abc tag config pc", "deepseek:tag": "git abc tag config mf-deepseek && git abc tag create && git abc tag config pc", "dev": "export NODE_OPTIONS=\"--max-old-space-size=6144\" && webpack serve --color --progress --config build/webpack.backend.online.conf.js ", "build": "export NODE_OPTIONS=\"--max-old-space-size=8192\" && node build/build.js", "analyz": "NODE_ENV=production  npm_config_report=true npm run build", "setup:b2b": "yarn && git checkout feature/b2b-mall && git submodule init && git submodule update && sh cd src/modules/b2b-mall && git checkout master", "setup:cypress": "git submodule init cypress && git submodule update cypress && cd cypress && git checkout master", "postinstall": "npx husky install && patch-package", "swagger": "node tool/swagger2js/index.js && eslint --fix ./api/*.js", "dev:social": "export SOCIAL_ENV=true && export SOCIAL_PORT=8081 && yarn dev", "create:page": "plop --plopfile tool/generators/page.js", "create:table": "plop --plopfile tool/generators/pro-table.js", "create:statistics-page": "plop --plopfile tool/generators/statistics-page.js", "create:composite-component": "plop --plopfile tool/generators/components-composite.js", "build:composite-component": "cross-env NODE_ENV=production rollup -c build-v2/rollup.config.js", "create:hooks": "plop --plopfile tool/generators/hooks.js", "dev:engine": "export ABC_FE_ENGINE_URL=http://localhost:10001/loader.js && yarn dev", "dev:engine-pharmacy": "export ABC_FE_ENGINE_URL=http://localhost:10001/loader.js && yarn dev:pharmacy", "dev:print": "export PRINT_DEV=true && yarn dev", "dev:lis": "export LIS_ENV=true && yarn dev", "dev:public-health": "export PUBLIC_HEALTH_ENV=true && yarn dev", "dev:login": "export LOGIN_DEV=true && yarn dev", "dev:hospital": "export NODE_OPTIONS=\"--max-old-space-size=6120\" && webpack serve --color --progress --config build/webpack.hospital.only.dev.js", "dev:hospital:print": "export PRINT_DEV=true && NODE_OPTIONS=\"--max-old-space-size=6120\" && webpack serve --color --progress --config build/webpack.hospital.only.dev.js", "dev:pharmacy": "export NODE_OPTIONS=\"--max-old-space-size=4096\" && webpack serve --color --progress --config build/webpack.pharmacy.only.dev.js", "dev:pharmacy:print": "export PRINT_DEV=true && NODE_OPTIONS=\"--max-old-space-size=4096\" && webpack serve --color --progress --config build/webpack.pharmacy.only.dev.js", "dev:emr": "export PRINT_DEV=true && export ABC_MEDICAL_IMAGING_VIEWER_SDK_URL=\"http://localhost:12222/loader.js\" && yarn dev", "q": "node ./tool/quick-start.mjs", "dev:lite": "export ABC_LITE_ENTRY=all && export NODE_OPTIONS=\"--max-old-space-size=6144\" && rspack serve --config build-v2/webpack.lite.config.js", "dev:lite:pharmacy": "export ABC_LITE_ENTRY=pharmacy && export NODE_OPTIONS=\"--max-old-space-size=6144\" && rspack serve --config build-v2/webpack.lite.config.js", "dev:lite:analyz": "export RSDOCTOR=true && export ABC_LITE_ENTRY=pharmacy && export NODE_OPTIONS=\"--max-old-space-size=6144\" && rspack serve --config build-v2/webpack.lite.config.js", "dev:lite:print": "export PRINT_DEV=true && export ABC_LITE_ENTRY=all  && export NODE_OPTIONS=\"--max-old-space-size=6144\" && rspack serve --config build-v2/webpack.lite.config.js", "dev:lite:all:print": "export PRINT_DEV=true && export ABC_LITE_ENTRY=all && export NODE_OPTIONS=\"--max-old-space-size=6144\" && rspack serve --config build-v2/webpack.lite.config.js", "storybook": "storybook dev -p 6008", "build-storybook": "storybook build", "vetur": "node vetur"}, "dependencies": {"@abc-emr-editor/constants": "0.0.43", "@abc-emr-editor/tools": "0.0.21", "@abc/anti-crawler": "1.2.1116", "@abc/constants": "1.28.92", "@abc/cornerstone-tools": "^6.0.9", "@abc/error-monitor": "1.1.1137", "@abc/sortable": "1.3.288", "@abc/ui-pc": "1.469.0", "@abc/utils": "1.2.1115", "@abc/utils-date": "1.2.1116", "@abc/utils-dom": "1.10.10", "@panzoom/panzoom": "^4.6.0", "@popperjs/core": "^2.11.5", "abc-fe-engine": "^0.0.6", "abc-form-engine": "1.3.12", "abc-growth-chart": "^0.0.14", "abc-js-cookie": "0.0.16", "abc-micro-frontend": "0.0.31", "abc-safe-pkg": "^0.1.6", "axios": "0.15.3", "babel-helper-vue-jsx-merge-props": "^2.0.3", "big.js": "^6.2.1", "broadcastchannel-polyfill": "^1.0.1", "compressorjs": "^1.0.6", "core-js": "3", "cornerstone-math": "^0.1.10", "cornerstone-wado-image-loader": "^4.2.0", "cornerstone-web-image-loader": "^2.1.1", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "d3": "^5.16.0", "d3-6": "npm:d3@^6.7.0", "dcmjs": "^0.24.7", "dicom-parser": "^1.8.13", "dicomweb-client": "^0.8.4", "echarts": "^5.0.2", "file-saver": "^2.0.5", "hammerjs": "^2.0.8", "html2canvas": "^1.0.0-rc.7", "html2pdf.js": "^0.10.2", "iconv-lite": "0.6.3", "isomorphic-base64": "^1.0.2", "jquery": "^3.2.1", "jsbarcode": "3.8.0", "jsonrepair": "^3.12.0", "jspdf": "^2.3.1", "libphonenumber-js": "^1.11.14", "lodash.chunk": "^4.2.0", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lodash.merge": "^4.6.2", "lodash.pick": "^4.4.0", "lru-cache": "^7.14.0", "marked": "^15.0.7", "md5": "^2.2.1", "mousetrap": "^1.6.5", "moveable": "0.51.1", "normalize.css": "3.0.2", "object-hash": "^3.0.0", "path-to-regexp": "^6.2.1", "perfect-scrollbar": "^1.4.0", "pinia": "2.0.15", "qrcode": "^1.2.2", "qs": "^6.5.2", "query-string": "^7.1.1", "requestidlecallback-polyfill": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "sat": "^0.9.0", "socket.io-client": "^2.0.3", "stream": "^0.0.2", "timers": "^0.1.1", "validate.js": "^0.13.1", "viewerjs": "^1.11.6", "vue": "2.7.14", "vue-cropper": "^0.4.9", "vue-drag-resize": "1.5.4", "vue-i18n": "8.28.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.4", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "xml2js": "^0.6.2", "xss": "^0.3.3"}, "devDependencies": {"@abc/cli": "0.11.130", "@abc/image-compress": "1.0.4", "@abc/plugin-babel-catch": "0.3.1-alpha.1941", "@babel/core": "^7.22.17", "@babel/eslint-parser": "^7.19.1", "@babel/parser": "^7.13.13", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/preset-env": "^7.22.15", "@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@module-federation/concat-runtime": "^0.0.1", "@rollup/plugin-alias": "3.1.2", "@rollup/plugin-babel": "5.3.0", "@rollup/plugin-commonjs": "18.0.0", "@rollup/plugin-image": "2.0.6", "@rollup/plugin-json": "4.1.0", "@rollup/plugin-node-resolve": "11.2.1", "@rsdoctor/rspack-plugin": "0.3.11", "@rspack/cli": "1.2.8", "@rspack/core": "1.2.8", "@storybook/addon-actions": "7.2.0", "@storybook/addon-console": "2.0.0", "@storybook/addon-docs": "7.2.0", "@storybook/addon-essentials": "7.2.0", "@storybook/addon-interactions": "^7.2.1", "@storybook/addon-knobs": "7.0.2", "@storybook/addon-links": "7.2.0", "@storybook/addon-mdx-gfm": "7.2.0", "@storybook/addon-notes": "5.3.21", "@storybook/addon-styling": "^1.3.5", "@storybook/addon-viewport": "7.2.0", "@storybook/blocks": "7.2.0", "@storybook/mdx1-csf": "^1.0.0", "@storybook/react": "^7.2.1", "@storybook/testing-library": "^0.2.0", "@storybook/theming": "7.2.0", "@storybook/vue": "7.2.0", "@storybook/vue-webpack5": "7.2.0", "@swc/core": "1.7.6", "@vue/babel-helper-vue-jsx-merge-props": "1.2.1", "@vue/babel-preset-jsx": "1.2.4", "@vue/compiler-sfc": "3.2.30", "@webpack-cli/serve": "^2.0.5", "abc-fed-build-tool": "0.8.5", "ali-oss": "^4.11.2", "autoprefixer": "^8.6.2", "babel-eslint": "^10.1.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^9.1.3", "babel-plugin-component": "^1.1.1", "chalk": "^2.0.1", "circular-dependency-plugin": "^5.2.2", "commitlint-config-cz": "^0.13.2", "copy-webpack-plugin": "^5.1.0", "cross-env": "^7.0.3", "css-loader": "^6.6.0", "css-minimizer-webpack-plugin": "^3.4.1", "cz-conventional-changelog": "3.3.0", "cz-customizable": "^6.3.0", "deep-diff": "^0.3.8", "ejs": "^3.1.6", "eslint": "^8.29.0", "eslint-plugin-abc": "0.2.0", "eslint-plugin-vue": "^9.8.0", "execa": "8.0.0", "expose-loader": "^3.1.0", "external-remotes-plugin": "^1.0.0", "friendly-errors-webpack-plugin": "^1.7.0", "fs-extra": "^9.1.0", "got": "^11.8.2", "happypack": "^5.0.1", "html-loader": "^3.1.0", "html-webpack-plugin": "^5.5.0", "husky": "^6.0.0", "inquirer": "9.0.0", "javascript-obfuscator": "^4.1.1", "js-beautify": "^1.15.1", "launch-editor-middleware": "^2.3.0", "lint-staged": "^10.2.2", "mini-css-extract-plugin": "2.7.6", "node-notifier": "^5.1.2", "normalize-path": "3.0.0", "ora": "^1.2.0", "patch-package": "^8.0.0", "plop": "^3.1.1", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^6.2.1", "postcss-url": "^8.x", "rimraf": "^2.6.0", "rollup": "2.45.2", "rollup-plugin-asset-url": "^0.1.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-postcss": "4.0.2", "rollup-plugin-progress": "1.1.2", "rollup-plugin-sass": "^1.12.20", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-terser": "7.0.2", "rollup-plugin-vue": "5.1.9", "sass": "^1.69.7", "sass-loader": "7.x", "semver": "^5.3.0", "shelljs": "^0.7.6", "storybook": "^7.2.1", "storybook-addon-jsx": "^7.3.14", "style-loader": "^3.3.1", "stylelint": "^13.12.0", "stylelint-config-recess-order": "^2.3.0", "stylelint-config-standard": "^21.0.0", "stylelint-order": "^4.1.0", "stylelint-scss": "^3.19.0", "stylus-loader": "^6.2.0", "terser-webpack-plugin": "^5.3.1", "thread-loader": "^3.0.4", "to-string-loader": "^1.2.0", "vue-docgen-api": "^4.79.2", "vue-eslint-parser": "^9.1.0", "vue-loader": "^15.10.1", "vue-style-loader": "^4.1.3", "vue-template-compiler": "2.7.14", "webpack": "^5.91.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-merge": "^5.8.0", "webpack-obfuscator": "^3.5.1"}, "engines": {"node": ">= 8.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 0.1%", "last 5 versions", "iOS >= 8", "Android > 4", "not ie <= 8"], "lint-staged": {"*.{js,jsx,vue}": ["eslint --fix"], "*.{scss,css,vue}": ["stylelint --config ./.stylelintrc --fix"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}