const STORAGE_KEYS = {
    COOKIES: 'meituan_cookies',
    PRODUCTS: 'meituan_products',
    PRESCRIPTIONS: 'meituan_prescriptions',
    ORDERS: 'meituan_orders',
    TAGS: 'meituan_tags',
};

const database = {
    saveMeituanPrescription(param) {
        try {
            const prescriptions = this.getMeituanPrescriptionBatch() || [];
            prescriptions.push(param);
            localStorage.setItem(STORAGE_KEYS.PRESCRIPTIONS, JSON.stringify(prescriptions));
            return {
                created: [param], updated: [], 
            };
        } catch (error) {
            console.error('Error saving prescription:', error);
            return {
                created: [], updated: [], 
            };
        }
    },

    saveMeituanCookies(cookies) {
        try {
            localStorage.setItem(STORAGE_KEYS.COOKIES, JSON.stringify(cookies));
            return true;
        } catch (error) {
            console.error('Error saving cookies:', error);
            return false;
        }
    },

    saveMeituanProductBatch(products) {
        try {
            const existingProducts = this.getMeituanProductBatch() || [];
            const updated = [];
            const created = [];

            products.forEach((product) => {
                const existingIndex = existingProducts.findIndex((p) => p.id === product.id);
                if (existingIndex !== -1) {
                    existingProducts[existingIndex] = {
                        ...product, updatedAt: new Date().toISOString(), 
                    };
                    updated.push(product);
                } else {
                    existingProducts.push({
                        ...product, createdAt: new Date().toISOString(), 
                    });
                    created.push(product);
                }
            });

            localStorage.setItem(STORAGE_KEYS.PRODUCTS, JSON.stringify(existingProducts));
            return {
                created, updated, 
            };
        } catch (error) {
            console.error('Error saving products:', error);
            return {
                created: [], updated: [], 
            };
        }
    },

    saveMeituanPrescriptionBatch(allPrescriptions) {
        try {
            const existingPrescriptions = this.getMeituanPrescriptionBatch() || [];
            const updated = [];
            const created = [];

            allPrescriptions.forEach((prescription) => {
                const existingIndex = existingPrescriptions.findIndex((p) => p.id === prescription.id);
                if (existingIndex !== -1) {
                    existingPrescriptions[existingIndex] = {
                        ...prescription, updatedAt: new Date().toISOString(), 
                    };
                    updated.push(prescription);
                } else {
                    existingPrescriptions.push({
                        ...prescription, createdAt: new Date().toISOString(), 
                    });
                    created.push(prescription);
                }
            });

            localStorage.setItem(STORAGE_KEYS.PRESCRIPTIONS, JSON.stringify(existingPrescriptions));
            return {
                created, updated, 
            };
        } catch (error) {
            console.error('Error saving prescriptions:', error);
            return {
                created: [], updated: [], 
            };
        }
    },

    getMeituanCookies() {
        try {
            const cookies = localStorage.getItem(STORAGE_KEYS.COOKIES);
            return cookies ? JSON.parse(cookies) : null;
        } catch (error) {
            console.error('Error getting cookies:', error);
            return null;
        }
    },

    getMeituanProductBatch() {
        try {
            const products = localStorage.getItem(STORAGE_KEYS.PRODUCTS);
            return products ? JSON.parse(products) : [];
        } catch (error) {
            console.error('Error getting products:', error);
            return [];
        }
    },

    getMeituanPrescriptionBatch() {
        try {
            const prescriptions = localStorage.getItem(STORAGE_KEYS.PRESCRIPTIONS);
            return prescriptions ? JSON.parse(prescriptions) : [];
        } catch (error) {
            console.error('Error getting prescriptions:', error);
            return [];
        }
    },

    saveMeituanOrderBatch(currentPageOrders) {
        try {
            const existingOrders = JSON.parse(localStorage.getItem(STORAGE_KEYS.ORDERS) || '[]');
            const updated = [];
            const created = [];

            currentPageOrders.forEach((order) => {
                const existingIndex = existingOrders.findIndex((o) => o.id === order.id);
                if (existingIndex !== -1) {
                    existingOrders[existingIndex] = {
                        ...order, updatedAt: new Date().toISOString(), 
                    };
                    updated.push(order);
                } else {
                    existingOrders.push({
                        ...order, createdAt: new Date().toISOString(), 
                    });
                    created.push(order);
                }
            });

            localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(existingOrders));
            return {
                created, updated, 
            };
        } catch (error) {
            console.error('Error saving orders:', error);
            return {
                created: [], updated: [], 
            };
        }
    },

    saveMeituanTagBatch(tags) {
        try {
            const existingTags = this.getMeituanTagBatch() || [];
            const updated = [];
            const created = [];

            tags.forEach((tag) => {
                const existingIndex = existingTags.findIndex((t) => t.id === tag.id);
                if (existingIndex !== -1) {
                    existingTags[existingIndex] = {
                        ...tag, updatedAt: new Date().toISOString(), 
                    };
                    updated.push(tag);
                } else {
                    existingTags.push({
                        ...tag, createdAt: new Date().toISOString(), 
                    });
                    created.push(tag);
                }
            });

            localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(existingTags));
            return {
                created, updated, 
            };
        } catch (error) {
            console.error('Error saving tags:', error);
            return {
                created: [], updated: [], 
            };
        }
    },

    getMeituanTagBatch() {
        try {
            const tags = localStorage.getItem(STORAGE_KEYS.TAGS);
            return tags ? JSON.parse(tags) : [];
        } catch (error) {
            console.error('Error getting tags:', error);
            return [];
        }
    },

    clearAllData() {
        try {
            Object.values(STORAGE_KEYS).forEach((key) => localStorage.removeItem(key));
            return true;
        } catch (error) {
            console.error('Error clearing data:', error);
            return false;
        }
    },
};

export {
    database,
};
