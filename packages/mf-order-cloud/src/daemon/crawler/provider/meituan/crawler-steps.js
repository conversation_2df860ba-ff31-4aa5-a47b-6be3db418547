import RPAConfig from './rpa-config';

/**
 * 美团爬虫的公共操作步骤
 */
class MeituanCrawlerSteps {

    static async navigateToPage(page, url) {
        // 导航到商品列表页面
        await page.goto(url);
    }

    static async getHashFrame(page) {
        // 获取iframe元素并切换上下文
        const iframeElement = await page.waitForSelector(RPAConfig.DomSelector.CrawlerSteps.IFrame);
        const iframe = await iframeElement.contentFrame();
        return iframe;
    }

    static getDelayTime(minSeconds = 0, mathSeconds = 1) {
        return Math.floor((Math.random() * (mathSeconds - minSeconds) + minSeconds) * 1000);
    }

    static async clickExpandButton(page, iframe) {
        const buttonElement = await iframe.$(RPAConfig.DomSelector.ProductList.ExpandButton);
        if (buttonElement) {
            await iframe.click(RPAConfig.DomSelector.ProductList.ExpandButton);
            await page.waitForTimeout(this.getDelayTime(0.5, 0.6));

            // 等待输入框出现
            await iframe.waitForSelector(RPAConfig.DomSelector.ProductList.GoodsTypeDropDown);
            const dropElements = await iframe.$$(RPAConfig.DomSelector.ProductList.GoodsTypeDropDown);
            if (dropElements && dropElements.length) {
                const box = await dropElements[0].boundingBox();
                if (box) {
                    await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
                    await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2);
                }
            }
            await page.waitForTimeout(this.getDelayTime(0.5, 0.6));

            // 等待选项出现
            await iframe.waitForSelector(RPAConfig.DomSelector.ProductList.GoodsTypeOption);
            const optionElement = await iframe.$(RPAConfig.DomSelector.ProductList.GoodsTypeOption);
            if (optionElement) {
                const box = await optionElement.boundingBox();
                if (box) {
                    await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
                    await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2);
                }
            }
            await page.waitForTimeout(this.getDelayTime(0.5, 0.6));
        }
    }

    static async clickProductOnSaleTab(page, iframe) {
        const navTabElements = await iframe.$$(RPAConfig.DomSelector.ProductList.SelectNavTab);
        if (navTabElements && navTabElements.length > 0) {
            await navTabElements[4].click();
        }
        await page.waitForTimeout(this.getDelayTime(5, 6));
    }

    static async iframeClickSearchSkuId(page, iframe) {
        // 点击下拉框打开选项
        const dropdown = await iframe.$(RPAConfig.DomSelector.CrawlerSteps.SkuIdDropdown);
        if (dropdown) {
            const box = await dropdown.boundingBox();
            if (box) {
                await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
                await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2);
            }
        }
        await iframe.waitForSelector(RPAConfig.DomSelector.CrawlerSteps.SkuIdOption);
        await page.waitForTimeout(this.getDelayTime(0.2, 0.4));
        const option = await iframe.$(RPAConfig.DomSelector.CrawlerSteps.SkuIdOption);
        if (option) {
            const box = await option.boundingBox();
            if (box) {
                await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
                await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2);
            }
        }
        await page.waitForTimeout(this.getDelayTime(0.2, 0.4));
    }

    static async iframeTypeSkuId(page, iframe, skuId) {
        // 等待输入框出现
        await iframe.waitForSelector(RPAConfig.DomSelector.CrawlerSteps.SkuIdInput);
        const input = await iframe.$(RPAConfig.DomSelector.CrawlerSteps.SkuIdInput);
        if (input) {
            const box = await input.boundingBox();
            if (box) {
                await page.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
                await page.mouse.click(box.x + box.width / 2, box.y + box.height / 2);
            }
            // 清空原有内容，确保输入无误
            await input.click({ clickCount: 3 });
            await input.press('Backspace');
            await input.type(skuId.toString());

            await page.waitForTimeout(this.getDelayTime(1, 2));
            await input.press('Enter');
        }
    }

    static async iframeClickSearch(page,iframe) {
        // 点击查询按钮
        const searchBtn = await iframe.$(RPAConfig.DomSelector.CrawlerSteps.SearchButton);
        if (searchBtn) {
            await iframe.click(RPAConfig.DomSelector.CrawlerSteps.SearchButton);
        }
    }

    /**
     * 点击商品编辑按钮
     * @param {Object} iframe Puppeteer Frame对象
     */
    static async clickProductEditButton(iframe) {
        const editButton = await iframe.$(RPAConfig.DomSelector.CrawlerSteps.EditButton);
        await editButton.click();
    }

    static async clickProductEditSvgButton(page, iframe) {
        await page.waitForTimeout(this.getDelayTime(1, 2));
        const editButtonElements = await iframe.$$(RPAConfig.DomSelector.CrawlerSteps.SvgButton);
        if (editButtonElements && editButtonElements.length > 0) {
            await editButtonElements[1].click();
        }
        await page.waitForTimeout(this.getDelayTime(1, 2));
    }

    static waitForProductSearchResponse(page) {
        return new Promise((resolve) => {
            const responseHandler = async (response) => {
                if (response.url().includes(RPAConfig.WatchAPI.ProductSearchList)) {
                    page.off('response', responseHandler); // 移除监听器
                    resolve(response);
                }
            };
            page.on('response', responseHandler);
        });
    }

    /**
     * 更新商品库存
     * @param {Object} page Puppeteer Page对象
     * @param {Object} iframe Puppeteer Frame对象
     * @param {number} stock 新的库存值
     */
    static async updateProductStock(page, iframe, stock) {
        await iframe.waitForSelector(RPAConfig.DomSelector.CrawlerSteps.StockInput);
        const input = await iframe.$(RPAConfig.DomSelector.CrawlerSteps.StockInput);
        if (input) {
            const propertyHandle = await input.getProperty('value');
            const value = await propertyHandle.jsonValue();
            if (value === stock.toString()) return false;

            await input.focus();

            await page.keyboard.down('Control');
            await page.waitForTimeout(this.getDelayTime(0.05, 0.1));

            await page.keyboard.press('A');
            await page.waitForTimeout(this.getDelayTime(0.05, 0.1));

            await page.keyboard.up('Control');
            await page.waitForTimeout(this.getDelayTime(0.05, 0.1));

            await input.press('Backspace');
            await page.waitForTimeout(this.getDelayTime(0.05, 0.1));

            await input.type(stock.toString());
            await page.waitForTimeout(this.getDelayTime(0.05, 0.1));

            await input.press('Enter');

            return true;
        }
        return false;
    }
}

export default MeituanCrawlerSteps;
