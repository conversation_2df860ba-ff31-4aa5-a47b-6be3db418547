import Logger from '../../../../utils/logger';
import MeituanCrawler from './meituan-crawler';

const { remote } = window;
const path = window.require?.('path') || {};
const fs = window.require?.('fs') || {};

const logger = Logger.create('MeituanAuthManager');

export default class MeituanAuthManager {
    /**
     * @type {MeituanAuthManager}
     */
    static instance;

    storageFilePath;

    accountMap = new Map();

    static getInstance() {
        if (!this.instance) {
            this.instance = new MeituanAuthManager();
        }
        return this.instance;
    }

    constructor() {
        this.storageFilePath = path.join(remote.app.getPath('userData'), 'rpa/meituan-account-info.json');

        try {
            // 确保文件存在
            fs.mkdirSync(path.dirname(this.storageFilePath), { recursive: true });

            if (!fs.existsSync(this.storageFilePath)) {
                fs.writeFileSync(this.storageFilePath, '');
            }

            const data = fs.readFileSync(this.storageFilePath, 'utf8');
            if (data) {
                this.accountMap = new Map(JSON.parse(data));
            }

            logger.info('meituan account map read', this.accountMap);
        } catch (e) {
            logger.error('meituan account map read error', e);
        }
    }

    /**
     * 请求授权
     * @param {string} accountId - 账号ID
     * @param {Function} onBrowsersClosed - 当所有浏览器实例关闭时的回调函数
     * @return {Promise<void>}
     */
    async requestBind(accountId, onBrowsersClosed) {
        const crawler = new MeituanCrawler(accountId);

        const authResponse = await crawler.requestBind(onBrowsersClosed);
        if (authResponse.status === false) {
            logger.error('requestAuth error', authResponse);
            return authResponse;
        }
        return authResponse;
    }

    async requestAuth(accountId, onBrowsersClosed) {
        const crawler = new MeituanCrawler(accountId);

        const authResponse = await crawler.requestAuth(onBrowsersClosed);
        if (authResponse.status === false) {
            logger.error('requestAuth error', authResponse);
            return authResponse;
        }
        return authResponse;
    }

    /**
     * 添加账号信息，持久化存储 cookies 及 accountInfo，保存的 cookies 需要和登录的美团账号(account)绑定。后续用户会将 clinic 与 account 绑定，后续请求时，会根据 clinic -> account -> cookie 的映射关系，找到对应的 cookie，进行请求
     * @param {*} accountId
     * @param {*} authData
     */
    async addAccount(accountId, authData) {
        const {
            accountInfo,
            cookies,
            clinicId,
        } = authData;

        this.accountMap.set(accountId, {
            cookies,
            accountInfo,
            clinicId,
        });
        await this.saveAccountMap();
    }

    async clearToken(accountId) {
        const authData = this.getAccount(accountId);
        if (!authData) {
            return;
        }
        authData.cookies = [];
        await this.saveAccountMap();
    }

    getAccount(accountId) {
        return this.accountMap.get(accountId);
    }

    /**
     * 保存 accountMap
     */
    async saveAccountMap() {
        try {
            await fs.promises.writeFile(this.storageFilePath, JSON.stringify(Array.from(this.accountMap)));
        } catch (e) {
            logger.error('meituan account map save error', e);
        }
    }
}
