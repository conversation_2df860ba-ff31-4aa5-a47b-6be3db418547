// const orderListRsp = {
//     'code': 0,
//     'msg': 'success',
//     'data': {
//         'wmOrderList': [
//             {
//                 'businessType': 0,
//                 'commonInfo': {
//                     'wm_poi_order_dayseq': 34,
//                     'wm_order_id_view': 2001468303116312241,
//                     'wmOrderViewIdStr': '2001468303116312241',
//                     'order_time': 1739432404,
//                     'delivery_btime': 0,
//                     'estimateArrivalTime': 1739434879,
//                     'poiPushDay': 0,
//                     'orderStatus': 4,
//                     'flashOrder': false,
//                     'payStatus': 0,
//                     'logisticsStatus': 0,
//                     'isLogisticsAbnormal': false,
//                     'foodDoneStatus': 0,
//                     'pickType': 0,
//                     'wmOrderPayType': 0,
//                     'estimateArrivalTimeLeft': 0,
//                     'estimateArrivalTimeRight': 0,
//                     'jointOrder': false,
//                     'hasRemark': false,
//                     'shippingNoThreshold': false,
//                     'transferOrder': false,
//                     'isPreOrder': false,
//                     'isPreSale': false,
//                     'logisticsScene': 0,
//                     'shippingService': 0,
//                     'estimatedDeliveryTime': 0,
//                     'estimateArrivalTimeV2': 0,
//                     'preOrder': false,
//                     'preSale': false,
//                 },
//                 'orderInfo': {
//                     'unifiedBasicInfo': {
//                         'daySeq': 34,
//                         'labels': [],
//                         'needTableWare': true,
//                         'orderTag': 0,
//                         'expectTimeVO': {
//                             'expectPrefix': '立即送达 ',
//                             'expectTimeFmt': '建议 16:21',
//                             'expectSuffix': ' 前送达',
//                             'expectType': null,
//                             'expectTimeBeforeModify': '',
//                             'expectTimeBeforeModifyTips': '',
//                             'expectTimeFmtColor': '',
//                             'deliveryBtimeFmt': '01-01 08:00',
//                         },
//                         'orderStatusDesc': '骑手已取货',
//                         'orderCompleteText': '骑手未确认送达，订单将于 02-14 16:24 时自动完成并在当日生成账单；如用户主动确认收货，订单将立即完成',
//                         'cancelReason': '',
//                         'orderStatusText': null,
//                         'orderModifyInfoVO': null,
//                         'poiName': '恒杏园（高新天久南巷店）',
//                         'cityName': '成都市',
//                         'orderTimeFmt': '02-13 15:40',
//                         'wmOrderViewId': '2001468303116312241',
//                         'postSellerId': 0,
//                         'showConfirmButton': false,
//                         'showGPRSAutoConfirmBtn': false,
//                         'showCopyOrderButton': true,
//                         'cancelButton': {
//                             'isShow': true,
//                             'content': '取消订单并退款',
//                             'canClick': true,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'refundButton': {
//                             'isShow': true,
//                             'content': '部分退款',
//                             'canClick': true,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'showPrintBtn': true,
//                         'printCnt': '1',
//                         'fontString': null,
//                         'orderCopyContent': '2月13日  #34号单#  恒杏园（高新天久南巷店）  张(女士)  为保护顾客隐私，电话及地址已隐藏  [一汀宁]聚维酮碘溶液5%*100ml/瓶/盒 (5%*100ml/瓶/盒) 2份  [一汀宁]聚维酮碘溶液5%*100ml/瓶/盒 (5%*100ml/瓶/盒) 1份  合计：31.95元 (已付款)  在线支付订单',
//                         'wmPoiId': 24925045,
//                         'medicareTag': 0,
//                         'b2cOrder': false,
//                         'orderStatus': 4,
//                         'confirmTime': 1739432420,
//                         'preDays': 0,
//                     },
//                     'orderChargeBlockBO': {
//                         'giftDetails': [],
//                         'settlementItems': [
//                             {
//                                 'type': 3,
//                                 'name': '小计',
//                                 'priceText': '￥31.20',
//                                 'nameTag': null,
//                                 'tipsInfo': {
//                                     'isShow': false,
//                                     'type': 0,
//                                     'title': null,
//                                     'formulaDesc': null,
//                                     'desc': null,
//                                     'details': null,
//                                     'priceText': null,
//                                     'detailUrl': null,
//                                     'priceTextGreyDisplayKeyword': null,
//                                 },
//                                 'subSettlementItems': null,
//                                 'price': 0.0,
//                             },
//                             {
//                                 'type': 4,
//                                 'name': '商家活动支出',
//                                 'priceText': '-￥5.25',
//                                 'nameTag': null,
//                                 'tipsInfo': {
//                                     'isShow': true,
//                                     'type': 1,
//                                     'title': '活动支出详情',
//                                     'formulaDesc': null,
//                                     'desc': null,
//                                     'details': [
//                                         {
//                                             'name': '购买聚维酮碘溶液5%*100ml/瓶/盒原价10.4元现价9.15元',
//                                             'priceText': '-￥1.25',
//                                             'subDetails': null,
//                                         },
//                                         {
//                                             'name': '减配送费4.0元',
//                                             'priceText': '-￥4.00',
//                                             'subDetails': null,
//                                         },
//                                     ],
//                                     'priceText': '-￥5.25',
//                                     'detailUrl': null,
//                                     'priceTextGreyDisplayKeyword': null,
//                                 },
//                                 'subSettlementItems': null,
//                                 'price': 0.0,
//                             },
//                             {
//                                 'type': 5,
//                                 'name': '佣金',
//                                 'priceText': '-￥1.30',
//                                 'nameTag': null,
//                                 'tipsInfo': {
//                                     'isShow': true,
//                                     'type': 2,
//                                     'title': '佣金',
//                                     'formulaDesc': null,
//                                     'desc': null,
//                                     'details': [
//                                         {
//                                             'name': '计算规则详见协议和对账单',
//                                             'priceText': null,
//                                             'subDetails': null,
//                                         },
//                                     ],
//                                     'priceText': null,
//                                     'detailUrl': null,
//                                     'priceTextGreyDisplayKeyword': null,
//                                 },
//                                 'subSettlementItems': null,
//                                 'price': 0.0,
//                             },
//                             {
//                                 'type': 6,
//                                 'name': '配送服务费',
//                                 'priceText': '-￥3.00',
//                                 'nameTag': null,
//                                 'tipsInfo': {
//                                     'isShow': true,
//                                     'type': 3,
//                                     'title': '配送服务费',
//                                     'formulaDesc': null,
//                                     'desc': '',
//                                     'details': [
//                                         {
//                                             'name': '距离收费（阶梯计费）',
//                                             'priceText': '',
//                                             'subDetails': [],
//                                         },
//                                         {
//                                             'name': '导航距离3.2公里',
//                                             'priceText': '￥3.00',
//                                             'subDetails': [
//                                                 {
//                                                     'name': '0-50公里(3.00元)', 'priceText': null,
//                                                 },
//                                             ],
//                                         },
//                                     ],
//                                     'priceText': null,
//                                     'detailUrl': null,
//                                     'priceTextGreyDisplayKeyword': null,
//                                 },
//                                 'subSettlementItems': null,
//                                 'price': 0.0,
//                             },
//                             {
//                                 'type': 8,
//                                 'name': '店铺环保捐赠',
//                                 'priceText': '-￥0.02',
//                                 'nameTag': null,
//                                 'tipsInfo': {
//                                     'isShow': true,
//                                     'type': 2,
//                                     'title': null,
//                                     'formulaDesc': null,
//                                     'desc': '商家参与青山捐助计划，每单将捐献一定金额助力环保',
//                                     'details': null,
//                                     'priceText': null,
//                                     'detailUrl': null,
//                                     'priceTextGreyDisplayKeyword': null,
//                                 },
//                                 'subSettlementItems': null,
//                                 'price': 0.0,
//                             },
//                             {
//                                 'type': 17,
//                                 'name': '本单顾客实际支付（已支付）',
//                                 'priceText': '￥31.95',
//                                 'nameTag': null,
//                                 'tipsInfo': {
//                                     'isShow': false,
//                                     'type': 0,
//                                     'title': null,
//                                     'formulaDesc': null,
//                                     'desc': null,
//                                     'details': null,
//                                     'priceText': null,
//                                     'detailUrl': null,
//                                     'priceTextGreyDisplayKeyword': null,
//                                 },
//                                 'subSettlementItems': null,
//                                 'price': 0.0,
//                             },
//                         ],
//                         'settlementAmounts': [
//                             {
//                                 'type': 5,
//                                 'name': '本单预计收入',
//                                 'priceText': '￥21.63',
//                                 'heighlight': false,
//                                 'tipsInfo': null,
//                             },
//                         ],
//                         'finalSettlementAmount': 21.63,
//                     },
//                     'orderFoodBlockBO': {
//                         'foodKindsAndNum': '共3件商品',
//                         'cartDetailVOs': [
//                             {
//                                 'cartName': '1号口袋',
//                                 'details': [
//                                     {
//                                         'foodName': '[一汀宁]聚维酮碘溶液5%*100ml/瓶/盒 (5%*100ml/瓶/盒)',
//                                         'count': 2,
//                                         'originFoodPrice': '￥10.40',
//                                         'foodId': 26924211512,
//                                         'detailId': '7250022368081',
//                                         'totalFoodPrice': '￥20.80',
//                                         'cartId': 0,
//                                         'foodRealPayTotalPrice': '￥20.80',
//                                         'unit': '份',
//                                     },
//                                     {
//                                         'foodName': '[一汀宁]聚维酮碘溶液5%*100ml/瓶/盒 (5%*100ml/瓶/盒)',
//                                         'count': 1,
//                                         'originFoodPrice': '￥10.40',
//                                         'foodId': 26924211512,
//                                         'detailId': '7250022368145',
//                                         'totalFoodPrice': '￥10.40',
//                                         'cartId': 0,
//                                         'foodRealPayTotalPrice': '￥9.15',
//                                         'unit': '份',
//                                     },
//                                 ],
//                             },
//                         ],
//                         'remarkInfo': { 'remark': '' },
//                         'invoiceInfo': {
//                             'isShow': false,
//                             'invoiceTitle': null,
//                             'invoiceTaxpayerId': null,
//                         },
//                         'foodRealPayExtInfo': {
//                             'foodRealPayShowType': 1,
//                             'foodRealPayNoticeMsg': '拟计算为用户实付价格 仅供参考',
//                         },
//                     },
//                     'unifiedReminderInfo': {
//                         'orderRemindRecordVOs': null,
//                         'invalidTips': null,
//                         'isShowReplyBtn': null,
//                     },
//                     'unifiedUserInfo': {
//                         'recipientName': '张(女士)',
//                         'wmUserId': *********,
//                         'recipientPhoneVO': {
//                             'pcRecipientPhoneVO': {
//                                 'phoneTips': '',
//                                 'pcPhoneVOS': [
//                                     {
//                                         'type': 2,
//                                         'name': '隐私号码',
//                                         'phoneShow': ['18428941632 转 7169'],
//                                         'queryPrivacyPhoneButtonVO': null,
//                                         'encrypted': 0,
//                                     },
//                                     {
//                                         'type': 2,
//                                         'name': '备用号码',
//                                         'phoneShow': ['17882905057 转 8700'],
//                                         'queryPrivacyPhoneButtonVO': null,
//                                         'encrypted': 0,
//                                     },
//                                     {
//                                         'type': 1,
//                                         'name': '顾客电话',
//                                         'phoneShow': ['手机尾号2768'],
//                                         'queryPrivacyPhoneButtonVO': {
//                                             'isShow': false,
//                                             'content': null,
//                                             'canClick': false,
//                                             'tips': null,
//                                             'showType': 2,
//                                         },
//                                         'encrypted': 0,
//                                     },
//                                 ],
//                             },
//                         },
//                         'customerAddressVO': {
//                             'isShow': true,
//                             'isHide': true,
//                             'recipientAddress': '成都SKP (大董烤鸭店（北馆，靠大魔方出入口）)',
//                             'isShowMap': true,
//                             'distanceAndAddressVO': {
//                                 'inArea': 0,
//                                 'addressLatitude': 30569868,
//                                 'addressLongitude': 104070120,
//                                 'poiLatitude': 30592654,
//                                 'poiLongitude': 104073244,
//                                 'orderDistance': 3195.0,
//                             },
//                         },
//                         'showIMBtn': true,
//                         'rxOrder': false,
//                         'customerLabels': [{
//                             'type': 1, 'text': '门店新客',
//                         }],
//                     },
//                     'unifiedLogisticsInfo': {
//                         'isShow': true,
//                         'title': '',
//                         'selfLogisticsVO': {
//                             'isShow': false,
//                             'statusDesc': null,
//                             'expressInfoVO': null,
//                         },
//                         'dispatcherInfoVO': {
//                             'dispatcherId': '48092174',
//                             'isShow': true,
//                             'dispatcherName': '杨建华',
//                             'dispatcherTeam': '',
//                             'phoneQueryBtn': {
//                                 'isShow': true,
//                                 'content': '查看骑手电话',
//                                 'canClick': null,
//                                 'tips': null,
//                                 'showType': 0,
//                             },
//                             'dispatcherPhoneInfo': {
//                                 'isShow': null,
//                                 'showType': 0,
//                                 'showRealPhoneBtn': false,
//                                 'dispatcherPhoneShow': '',
//                                 'orgLeaderPhoneShow': '',
//                                 'orgEmergencyPhoneShow': '',
//                             },
//                         },
//                         'logisticsHistoryVO': {
//                             'lastTimeFormat': '15:48',
//                             'lastStatusDesc': '骑手已取货',
//                             'isShow': true,
//                         },
//                         'showShippingFee': true,
//                         'deliveryFeeVO': {
//                             'isShow': false,
//                             'showShippingFee': false,
//                             'shippingFee': 0.0,
//                             'couponAmount': 0.0,
//                             'activityName': null,
//                             'activityAmount': 0.0,
//                             'extraFee': 0.0,
//                             'tipAmount': 0.0,
//                             'payAmount': 0.0,
//                             'payType': 0,
//                             'shippingFeeBase': 0.0,
//                             'bmlAmount': 0.0,
//                         },
//                         'poiShippingFeeVO': {
//                             'isShow': false,
//                             'poiShippingFee': 0.0,
//                             'tipAmount': 0.0,
//                         },
//                         'cancelDeliveryButtonVO': {
//                             'isShow': false,
//                             'content': '取消配送',
//                             'canClick': null,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'isShowLocationIcon': true,
//                         'evaluateDispatcherButtonVO': {
//                             'isShow': true,
//                             'content': '评价骑手',
//                             'canClick': null,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'logisticsRightVO': {
//                             'isShow': null,
//                             'rightsIcon': null,
//                             'rightsContent': null,
//                             'rightsColor': null,
//                             'rightsBgColor': null,
//                             'rightsValid': null,
//                         },
//                         'sendDeliveryButtonVO': {
//                             'isShow': false,
//                             'content': '发起配送',
//                             'zbResourceInfoVOListAndRights': null,
//                         },
//                         'logisticsTimeVO': {
//                             'isShow': false,
//                             'showType': 0,
//                             'showTime': 0,
//                             'timerPrefix': null,
//                             'timerSuffix': null,
//                             'fixedTimeDesc': null,
//                         },
//                         'reportExceptionButtonVO': {
//                             'isShow': false,
//                             'content': '骑手未到店投诉骑手',
//                             'canClick': null,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'addFeeButtonVO': {
//                             'isShow': false,
//                             'content': '',
//                             'canClick': null,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'delayDispatchInfo': {
//                             'isShow': false,
//                             'delayDispatchTimeVO': null,
//                             'delayDispatchButton': null,
//                         },
//                         'juheDeliveryButtonVO': {
//                             'isShow': false,
//                             'content': null,
//                             'canClick': null,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'sendSelfDeliveryButtonVO': {
//                             'isShow': false,
//                             'content': null,
//                             'canClick': null,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'uploadSelfDeliveryButtonVO': {
//                             'isShow': false,
//                             'content': '',
//                             'canClick': null,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'changeToSelfDeliveryButtonVO': {
//                             'isShow': false,
//                             'content': null,
//                             'canClick': null,
//                             'tips': null,
//                             'showType': 0,
//                         },
//                         'logisticsCode': '1003',
//                         'logisticsStatus': 20,
//                         'logisticsId': 101,
//                         'ptGray': true,
//                         'isKS': true,
//                         'hasJuheAndZB': false,
//                         'shippingService': 1020,
//                         'tipAmount': 0.0,
//                         'shippingFeeErrorMsg': null,
//                         'ptTimeShowVO': {
//                             'show': false, 'estimatedAllocationDesc': null,
//                         },
//                         'ownRights': false,
//                         'deliveryTipAmount': 0.0,
//                         'deliveryShippingFee': 0.0,
//                     },
//                     'unifiedMealInfo': {
//                         'isShow': true,
//                         'dispatchExceptionTips': '',
//                         'showFoodDoneInfo': true,
//                         'curSysTime': 1739433132,
//                         'pcFoodDoneInfoVO': {
//                             'title': '备货时长',
//                             'foodDoneStatusDesc': '已完成',
//                             'pickTimeInfo': {
//                                 'isShow': true,
//                                 'showType': 3,
//                                 'showTime': 0,
//                                 'timerPrefix': null,
//                                 'timerSuffix': null,
//                                 'fixedTimeDesc': '00:04:54',
//                             },
//                             'suggestPickTimeText': '',
//                             'speedRefundPickTimer': {
//                                 'isShow': false,
//                                 'showType': 0,
//                                 'showTime': 0,
//                                 'timerPrefix': null,
//                                 'timerSuffix': null,
//                                 'fixedTimeDesc': null,
//                             },
//                         },
//                         'foodDoneButtonVO': {
//                             'isShow': false,
//                             'content': null,
//                             'canClickButtonTime': 0,
//                         },
//                         'prepareStatus': 2,
//                     },
//                     'orderRefundBlockBO': {
//                         'refundTitle': null,
//                         'refundGoodsInfoList': [],
//                     },
//                     'orderCansunBlockBO': {
//                         'isShow': false, 'canSunRecords': null,
//                     },
//                 },
//             },
//         ],
//         'otherData': {
//             'lastLabel': '',
//             'nextLabel': {
//                 'day': 20250213,
//                 'day_seq': 25,
//                 'page': 0,
//                 'setCursor': false,
//                 'setDay': true,
//                 'setDay_seq': true,
//                 'setPage': false,
//             },
//         },
//         'wmRetailRemindOrderVoV2List': null,
//     },
//     'serverTime': 1739433132,
//     'newOrderCookieLabel': null,
// };
//
// const newOrderRsp = {
//     'code': 0,
//     'msg': 'Success',
//     'data': {
//         'businessType': 0,
//         'commonInfo': {
//             'wm_poi_order_dayseq': 33,
//             'wm_order_id_view': 3801468293601814226,
//             'wmOrderViewIdStr': '3801468293601814226',
//             'order_time': 1739432140,
//             'delivery_btime': 0,
//             'estimateArrivalTime': 1739434793,
//             'poiPushDay': 0,
//             'orderStatus': 4,
//             'flashOrder': false,
//             'payStatus': 0,
//             'logisticsStatus': 0,
//             'isLogisticsAbnormal': false,
//             'foodDoneStatus': 0,
//             'pickType': 0,
//             'wmOrderPayType': 0,
//             'estimateArrivalTimeLeft': 0,
//             'estimateArrivalTimeRight': 0,
//             'jointOrder': false,
//             'hasRemark': false,
//             'shippingNoThreshold': false,
//             'transferOrder': false,
//             'isPreOrder': false,
//             'isPreSale': false,
//             'logisticsScene': 0,
//             'shippingService': 0,
//             'estimatedDeliveryTime': 0,
//             'estimateArrivalTimeV2': 0,
//             'preOrder': false,
//             'preSale': false,
//         },
//         'orderInfo': {
//             'unifiedBasicInfo': {
//                 'daySeq': 33,
//                 'labels': [],
//                 'needTableWare': true,
//                 'orderTag': 0,
//                 'expectTimeVO': {
//                     'expectPrefix': '立即送达 ',
//                     'expectTimeFmt': '建议 16:19',
//                     'expectSuffix': ' 前送达',
//                     'expectType': null,
//                     'expectTimeBeforeModify': '',
//                     'expectTimeBeforeModifyTips': '',
//                     'expectTimeFmtColor': '',
//                     'deliveryBtimeFmt': '01-01 08:00',
//                 },
//                 'orderStatusDesc': '骑手已取货',
//                 'orderCompleteText': '骑手未确认送达，订单将于 02-14 16:21 时自动完成并在当日生成账单；如用户主动确认收货，订单将立即完成',
//                 'cancelReason': '',
//                 'orderStatusText': null,
//                 'orderModifyInfoVO': null,
//                 'poiName': '恒杏园（高新天久南巷店）',
//                 'cityName': '成都市',
//                 'orderTimeFmt': '02-13 15:35',
//                 'wmOrderViewId': '3801468293601814226',
//                 'postSellerId': 0,
//                 'showConfirmButton': false,
//                 'showGPRSAutoConfirmBtn': false,
//                 'showCopyOrderButton': true,
//                 'cancelButton': {
//                     'isShow': true,
//                     'content': '取消订单并退款',
//                     'canClick': true,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'refundButton': {
//                     'isShow': true,
//                     'content': '部分退款',
//                     'canClick': true,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'showPrintBtn': true,
//                 'printCnt': '1',
//                 'fontString': null,
//                 'orderCopyContent': '2月13日  #33号单#  恒杏园（高新天久南巷店）  罗(先生)  为保护顾客隐私，电话及地址已隐藏  [伊可新]维生素AD滴剂(胶囊型 1岁以上 2000U:700U)*10粒*3板/盒 ((2000U:700U)*10粒*3板/盒) 1份  [维福佳]维生素C片0.1g*100片/瓶 (0.1g*100片/瓶) 1份  [999]感冒灵颗粒10g*9袋/盒 (10g*9袋/盒) 1份  合计：55.77元 (已付款)  在线支付订单',
//                 'wmPoiId': 24925045,
//                 'medicareTag': 0,
//                 'b2cOrder': false,
//                 'orderStatus': 4,
//                 'confirmTime': 1739432154,
//                 'preDays': 0,
//             },
//             'orderChargeBlockBO': {
//                 'giftDetails': [],
//                 'settlementItems': [
//                     {
//                         'type': 3,
//                         'name': '小计',
//                         'priceText': '￥102.48',
//                         'nameTag': null,
//                         'tipsInfo': {
//                             'isShow': false,
//                             'type': 0,
//                             'title': null,
//                             'formulaDesc': null,
//                             'desc': null,
//                             'details': null,
//                             'priceText': null,
//                             'detailUrl': null,
//                             'priceTextGreyDisplayKeyword': null,
//                         },
//                         'subSettlementItems': null,
//                         'price': 0.0,
//                     },
//                     {
//                         'type': 4,
//                         'name': '商家活动支出',
//                         'priceText': '-￥54.21',
//                         'nameTag': null,
//                         'tipsInfo': {
//                             'isShow': true,
//                             'type': 1,
//                             'title': '活动支出详情',
//                             'formulaDesc': null,
//                             'desc': null,
//                             'details': [
//                                 {
//                                     'name': '购买维生素AD滴剂(胶囊型)(1岁以上)(2000U:700U)*10粒*3板/盒原价67.38元现价32.69元',
//                                     'priceText': '-￥34.69',
//                                     'subDetails': null,
//                                 },
//                                 {
//                                     'name': '购买感冒灵颗粒10g*9袋/盒原价31.6元现价14.5元',
//                                     'priceText': '-￥17.10',
//                                     'subDetails': null,
//                                 },
//                                 {
//                                     'name': '购买维生素C片0.1g*100片/瓶原价3.5元现价3.08元',
//                                     'priceText': '-￥0.42',
//                                     'subDetails': null,
//                                 },
//                                 {
//                                     'name': '减配送费2.0元',
//                                     'priceText': '-￥2.00',
//                                     'subDetails': null,
//                                 },
//                             ],
//                             'priceText': '-￥54.21',
//                             'detailUrl': null,
//                             'priceTextGreyDisplayKeyword': null,
//                         },
//                         'subSettlementItems': null,
//                         'price': 0.0,
//                     },
//                     {
//                         'type': 5,
//                         'name': '佣金',
//                         'priceText': '-￥2.41',
//                         'nameTag': null,
//                         'tipsInfo': {
//                             'isShow': true,
//                             'type': 2,
//                             'title': '佣金',
//                             'formulaDesc': null,
//                             'desc': null,
//                             'details': [
//                                 {
//                                     'name': '计算规则详见协议和对账单',
//                                     'priceText': null,
//                                     'subDetails': null,
//                                 },
//                             ],
//                             'priceText': null,
//                             'detailUrl': null,
//                             'priceTextGreyDisplayKeyword': null,
//                         },
//                         'subSettlementItems': null,
//                         'price': 0.0,
//                     },
//                     {
//                         'type': 6,
//                         'name': '配送服务费',
//                         'priceText': '-￥3.00',
//                         'nameTag': null,
//                         'tipsInfo': {
//                             'isShow': true,
//                             'type': 3,
//                             'title': '配送服务费',
//                             'formulaDesc': null,
//                             'desc': '',
//                             'details': [
//                                 {
//                                     'name': '距离收费（阶梯计费）',
//                                     'priceText': '',
//                                     'subDetails': [],
//                                 },
//                                 {
//                                     'name': '导航距离5.06公里',
//                                     'priceText': '￥3.00',
//                                     'subDetails': [
//                                         {
//                                             'name': '0-50公里(3.00元)', 'priceText': null,
//                                         },
//                                     ],
//                                 },
//                             ],
//                             'priceText': null,
//                             'detailUrl': null,
//                             'priceTextGreyDisplayKeyword': null,
//                         },
//                         'subSettlementItems': null,
//                         'price': 0.0,
//                     },
//                     {
//                         'type': 8,
//                         'name': '店铺环保捐赠',
//                         'priceText': '-￥0.02',
//                         'nameTag': null,
//                         'tipsInfo': {
//                             'isShow': true,
//                             'type': 2,
//                             'title': null,
//                             'formulaDesc': null,
//                             'desc': '商家参与青山捐助计划，每单将捐献一定金额助力环保',
//                             'details': null,
//                             'priceText': null,
//                             'detailUrl': null,
//                             'priceTextGreyDisplayKeyword': null,
//                         },
//                         'subSettlementItems': null,
//                         'price': 0.0,
//                     },
//                     {
//                         'type': 17,
//                         'name': '本单顾客实际支付（已支付）',
//                         'priceText': '￥55.77',
//                         'nameTag': null,
//                         'tipsInfo': {
//                             'isShow': false,
//                             'type': 0,
//                             'title': null,
//                             'formulaDesc': null,
//                             'desc': null,
//                             'details': null,
//                             'priceText': null,
//                             'detailUrl': null,
//                             'priceTextGreyDisplayKeyword': null,
//                         },
//                         'subSettlementItems': null,
//                         'price': 0.0,
//                     },
//                 ],
//                 'settlementAmounts': [
//                     {
//                         'type': 5,
//                         'name': '本单预计收入',
//                         'priceText': '￥42.84',
//                         'heighlight': false,
//                         'tipsInfo': null,
//                     },
//                 ],
//                 'finalSettlementAmount': 42.84,
//             },
//             'orderFoodBlockBO': {
//                 'foodKindsAndNum': '共3件商品',
//                 'cartDetailVOs': [
//                     {
//                         'cartName': '1号口袋',
//                         'details': [
//                             {
//                                 'foodName': '[伊可新]维生素AD滴剂(胶囊型 1岁以上 2000U:700U)*10粒*3板/盒 ((2000U:700U)*10粒*3板/盒)',
//                                 'count': 1,
//                                 'originFoodPrice': '￥67.38',
//                                 'foodId': 26924844964,
//                                 'detailId': '8039218742987',
//                                 'totalFoodPrice': '￥67.38',
//                                 'cartId': 0,
//                                 'foodRealPayTotalPrice': '￥32.69',
//                                 'unit': '份',
//                             },
//                             {
//                                 'foodName': '[维福佳]维生素C片0.1g*100片/瓶 (0.1g*100片/瓶)',
//                                 'count': 1,
//                                 'originFoodPrice': '￥3.50',
//                                 'foodId': 26924844967,
//                                 'detailId': '8039218743051',
//                                 'totalFoodPrice': '￥3.50',
//                                 'cartId': 0,
//                                 'foodRealPayTotalPrice': '￥3.08',
//                                 'unit': '份',
//                             },
//                             {
//                                 'foodName': '[999]感冒灵颗粒10g*9袋/盒 (10g*9袋/盒)',
//                                 'count': 1,
//                                 'originFoodPrice': '￥31.60',
//                                 'foodId': 26923222523,
//                                 'detailId': '8039218743115',
//                                 'totalFoodPrice': '￥31.60',
//                                 'cartId': 0,
//                                 'foodRealPayTotalPrice': '￥14.50',
//                                 'unit': '份',
//                             },
//                         ],
//                     },
//                 ],
//                 'remarkInfo': { 'remark': '' },
//                 'invoiceInfo': {
//                     'isShow': false,
//                     'invoiceTitle': null,
//                     'invoiceTaxpayerId': null,
//                 },
//                 'foodRealPayExtInfo': {
//                     'foodRealPayShowType': 1,
//                     'foodRealPayNoticeMsg': '拟计算为用户实付价格 仅供参考',
//                 },
//             },
//             'unifiedReminderInfo': {
//                 'orderRemindRecordVOs': null,
//                 'invalidTips': null,
//                 'isShowReplyBtn': null,
//             },
//             'unifiedUserInfo': {
//                 'recipientName': '罗(先生)',
//                 'wmUserId': 2354315233,
//                 'recipientPhoneVO': {
//                     'pcRecipientPhoneVO': {
//                         'phoneTips': '',
//                         'pcPhoneVOS': [
//                             {
//                                 'type': 2,
//                                 'name': '隐私号码',
//                                 'phoneShow': ['18428941563 转 8633'],
//                                 'queryPrivacyPhoneButtonVO': null,
//                                 'encrypted': 0,
//                             },
//                             {
//                                 'type': 2,
//                                 'name': '备用号码',
//                                 'phoneShow': ['17882903371 转 8937'],
//                                 'queryPrivacyPhoneButtonVO': null,
//                                 'encrypted': 0,
//                             },
//                             {
//                                 'type': 1,
//                                 'name': '顾客电话',
//                                 'phoneShow': ['手机尾号3091'],
//                                 'queryPrivacyPhoneButtonVO': {
//                                     'isShow': false,
//                                     'content': null,
//                                     'canClick': false,
//                                     'tips': null,
//                                     'showType': 2,
//                                 },
//                                 'encrypted': 0,
//                             },
//                         ],
//                     },
//                 },
//                 'customerAddressVO': {
//                     'isShow': true,
//                     'isHide': true,
//                     'recipientAddress': 'YE-SKIN壹心医学美容(高新院) (2楼)',
//                     'isShowMap': true,
//                     'distanceAndAddressVO': {
//                         'inArea': 0,
//                         'addressLatitude': 30558935,
//                         'addressLongitude': 104057725,
//                         'poiLatitude': 30592654,
//                         'poiLongitude': 104073244,
//                         'orderDistance': 5058.0,
//                     },
//                 },
//                 'showIMBtn': true,
//                 'rxOrder': false,
//                 'customerLabels': [{
//                     'type': 1, 'text': '门店新客',
//                 }],
//             },
//             'unifiedLogisticsInfo': {
//                 'isShow': true,
//                 'title': '',
//                 'selfLogisticsVO': {
//                     'isShow': false,
//                     'statusDesc': null,
//                     'expressInfoVO': null,
//                 },
//                 'dispatcherInfoVO': {
//                     'dispatcherId': '48092174',
//                     'isShow': true,
//                     'dispatcherName': '杨建华',
//                     'dispatcherTeam': '',
//                     'phoneQueryBtn': {
//                         'isShow': true,
//                         'content': '查看骑手电话',
//                         'canClick': null,
//                         'tips': null,
//                         'showType': 0,
//                     },
//                     'dispatcherPhoneInfo': {
//                         'isShow': null,
//                         'showType': 0,
//                         'showRealPhoneBtn': false,
//                         'dispatcherPhoneShow': '',
//                         'orgLeaderPhoneShow': '',
//                         'orgEmergencyPhoneShow': '',
//                     },
//                 },
//                 'logisticsHistoryVO': {
//                     'lastTimeFormat': '15:48',
//                     'lastStatusDesc': '骑手已取货',
//                     'isShow': true,
//                 },
//                 'showShippingFee': true,
//                 'deliveryFeeVO': {
//                     'isShow': false,
//                     'showShippingFee': false,
//                     'shippingFee': 0.0,
//                     'couponAmount': 0.0,
//                     'activityName': null,
//                     'activityAmount': 0.0,
//                     'extraFee': 0.0,
//                     'tipAmount': 0.0,
//                     'payAmount': 0.0,
//                     'payType': 0,
//                     'shippingFeeBase': 0.0,
//                     'bmlAmount': 0.0,
//                 },
//                 'poiShippingFeeVO': {
//                     'isShow': false,
//                     'poiShippingFee': 0.0,
//                     'tipAmount': 0.0,
//                 },
//                 'cancelDeliveryButtonVO': {
//                     'isShow': false,
//                     'content': '取消配送',
//                     'canClick': null,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'isShowLocationIcon': true,
//                 'evaluateDispatcherButtonVO': {
//                     'isShow': true,
//                     'content': '评价骑手',
//                     'canClick': null,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'logisticsRightVO': {
//                     'isShow': null,
//                     'rightsIcon': null,
//                     'rightsContent': null,
//                     'rightsColor': null,
//                     'rightsBgColor': null,
//                     'rightsValid': null,
//                 },
//                 'sendDeliveryButtonVO': {
//                     'isShow': false,
//                     'content': '发起配送',
//                     'zbResourceInfoVOListAndRights': null,
//                 },
//                 'logisticsTimeVO': {
//                     'isShow': false,
//                     'showType': 0,
//                     'showTime': 0,
//                     'timerPrefix': null,
//                     'timerSuffix': null,
//                     'fixedTimeDesc': null,
//                 },
//                 'reportExceptionButtonVO': {
//                     'isShow': false,
//                     'content': '骑手未到店投诉骑手',
//                     'canClick': null,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'addFeeButtonVO': {
//                     'isShow': false,
//                     'content': '',
//                     'canClick': null,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'delayDispatchInfo': {
//                     'isShow': false,
//                     'delayDispatchTimeVO': null,
//                     'delayDispatchButton': null,
//                 },
//                 'juheDeliveryButtonVO': {
//                     'isShow': false,
//                     'content': null,
//                     'canClick': null,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'sendSelfDeliveryButtonVO': {
//                     'isShow': false,
//                     'content': null,
//                     'canClick': null,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'uploadSelfDeliveryButtonVO': {
//                     'isShow': false,
//                     'content': '',
//                     'canClick': null,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'changeToSelfDeliveryButtonVO': {
//                     'isShow': false,
//                     'content': null,
//                     'canClick': null,
//                     'tips': null,
//                     'showType': 0,
//                 },
//                 'logisticsCode': '1003',
//                 'logisticsStatus': 20,
//                 'logisticsId': 101,
//                 'ptGray': true,
//                 'isKS': true,
//                 'hasJuheAndZB': false,
//                 'shippingService': 1020,
//                 'tipAmount': 0.0,
//                 'shippingFeeErrorMsg': null,
//                 'ptTimeShowVO': {
//                     'show': false, 'estimatedAllocationDesc': null,
//                 },
//                 'ownRights': false,
//                 'deliveryTipAmount': 0.0,
//                 'deliveryShippingFee': 0.0,
//             },
//             'unifiedMealInfo': {
//                 'isShow': true,
//                 'dispatchExceptionTips': '',
//                 'showFoodDoneInfo': true,
//                 'curSysTime': 1739433154,
//                 'pcFoodDoneInfoVO': {
//                     'title': '备货时长',
//                     'foodDoneStatusDesc': '已完成',
//                     'pickTimeInfo': {
//                         'isShow': true,
//                         'showType': 3,
//                         'showTime': 0,
//                         'timerPrefix': null,
//                         'timerSuffix': null,
//                         'fixedTimeDesc': '00:05:54',
//                     },
//                     'suggestPickTimeText': '',
//                     'speedRefundPickTimer': {
//                         'isShow': false,
//                         'showType': 0,
//                         'showTime': 0,
//                         'timerPrefix': null,
//                         'timerSuffix': null,
//                         'fixedTimeDesc': null,
//                     },
//                 },
//                 'foodDoneButtonVO': {
//                     'isShow': false,
//                     'content': null,
//                     'canClickButtonTime': 0,
//                 },
//                 'prepareStatus': 2,
//             },
//             'orderRefundBlockBO': {
//                 'refundTitle': null, 'refundGoodsInfoList': [],
//             },
//             'orderCansunBlockBO': {
//                 'isShow': false, 'canSunRecords': null,
//             },
//         },
//     },
//     'serverTime': 1739433154,
// };
