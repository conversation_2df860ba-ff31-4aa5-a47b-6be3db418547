export default class RPAConfig {
    static Page = {
        Login: {
            name: '登录',
            url: 'https://waimaie.meituan.com/new_fe/login_gw#/login',
        },
        HomePage: {
            name: '首页',
            url: 'https://yiyao.meituan.com/main/frame#/page/home',
        },
        OrderHistory: {
            name: '全部订单',
            url: 'https://yiyao.meituan.com/main/frame#/page/orderbusiness#/order/history',
        },
        PreOrderList: {
            name: '预订单',
            url: 'https://yiyao.meituan.com/main/frame#/page/orderbusiness#/order/reserve',
        },
        TodayTodo: {
            name: '今日待办',
            url: 'https://yiyao.meituan.com/main/frame#/page/orderbusiness#/order/unprocessed',
        },
        ProductList: {
            name: '商品列表',
            url: 'https://yiyao.meituan.com/main/frame#/page/product/list/single',
        },
        PrescriptionList: {
            name: '处方列表',
            url: 'https://yiyao.meituan.com/main/frame#/page/ysorder#/',
        },
        NewOrder: {
            name: '接单',
            url: 'https://yiyao.meituan.com/main/frame#/page/orderManage#/new',
        },
        OrderReminder: {
            name: '催单',
            url: 'https://yiyao.meituan.com/main/frame#/page/orderManage#/reminder',
        },
        DeliveryException: {
            name: '配送异常',
            url: 'https://yiyao.meituan.com/main/frame#/page/orderManage#/deliveryException',
        },
        OrderCompensate: {
            name: '货损赔付',
            url: 'https://yiyao.meituan.com/main/frame#/page/orderManage#/compensate',
        },
        ShopInfo: {
            name: '门店信息',
            url: 'https://yiyao.meituan.com/main/frame#/v2/shop/manage/shopInfo',
        },
    };

    static WatchAPI = {
        Home: 'health/center/pc/home',
        PoiInfo: 'sc/index/r/poiInfo/get',
        // OrderList: 'https://yiyao.meituan.com/gw/api/bm/merchant/order/mix/history/list/common',

        NewOrder: 'https://yiyao.meituan.com/gw/api/bm/merchant/order/mix/detail/common', // 看起来是增量订单/修改订单
        OrderDataChannel: 'https://yiyao.meituan.com/gw/api/bm/merchant/order/mix/new/interval/list/common', // 定时获取订单数量
        OrderCanceledCount: 'https://yiyao.meituan.com/v2/order/receive/distribute/r/canceledCount',
        OrderUnprocessedCount: 'https://yiyao.meituan.com/v2/order/receive/distribute/r/unprocessedCount',
        PrescriptionList: 'https://yiyao.meituan.com/v1/prescription/unaudited/query',
        OrderUnauditCount: 'https://yiyao.meituan.com/v1/prescription/order/auditcount/query', // 处方审核数量
        OrderUnprocessedList: 'https://yiyao.meituan.com/gw/api/bm/merchant/order/mix/unprocessed/list/common',
        PrescriptionDetail: 'https://yiyao.meituan.com/v1/prescription/history/detail/query', // 处方详情
        ProductSearchList: 'reuse/health/product/retail/r/searchListPageV2', // 商品列表搜索接口
        ProductDetail: '/reuse/health/product/shangou/r/detailProductAndMedicine', // 商品详情接口
        ProductPackageDetail: 'reuse/health/product/shangou/package/r/detailProduct', // 组包商品详情接口
        PrescriptionAuditQuery: '/v1/prescription/history/prescriptionAudit/query', // 处方审核查询接口
        UpdateStock: 'reuse/health/product/retail/w/batchUpdateSkuStock', // 更新库存

        PreOrderList: 'https://yiyao.meituan.com/gw/api/unified/r/order/list/page/preorder', // 预订单列表
        RefundOrderCountNotify: 'https://yiyao.meituan.com/v2/order/history/r/refundOrderCount/notify', // 售后退单通知（已验证）
        OrderNoticeInfo: 'https://yiyao.meituan.com/v2/sc/index/r/order', // 首页订单相关提醒接口（已验证）
        NewOrderV2: 'https://yiyao.meituan.com/gw/api/unified/r/order/detail/info', // 增量订单/修改订单（已验证）
        OrderList: 'https://yiyao.meituan.com/gw/api/unified/r/order/list/page/history', // 新版历史订单
        OrderListInterval: 'https://yiyao.meituan.com/gw/api/unified/r/order/list/interval', // 增量订单/修改订单
        OrderCountAndSum: 'https://yiyao.meituan.com/v2/order/receive/r/countAndSumOfToday',
        PrescriptionPharmacistStatus: 'https://yiyao.meituan.com/v1/pharmacist/status/query', // 在处方列表详情中获取药师信息
        ClinicLogo: 'https://yiyao.meituan.com/v2/shop/shopInfo/r/poiPicUrl', // 获取门店logo
    };

    static DomSelector = {
        HistoryOrder: {
            IFrame: '#hashframe',
            FilterForm: '.history-order-filter-form',
            AllRadio: '.form-group:first-child .roo-radio:first-child',
            PaginationNextBtn: '.roo-pagination .arrow:last-child a',
            YesterdayTab: '.date-btn-wrapper_64d77 .date-btn_1de74:last-child',
        },
        PrescriptionList: {
            Tabs: '.boo-tabs-nav',
            AllTab: '.boo-tabs-tab:last-child',
            Return: '.detail-wrap .left-detail .return',
            PRTable: '.boo-table',
            PRTableRow: '.boo-table-fixed-right .boo-table-tbody .boo-table-row',
            PRTableRowCell: '.boo-table-cell',
            PaginationNextBtn: '.pag-table .boo-page-next:not(.boo-page-disabled)',
        },
        ProductList: {
            IFrame: '#hashframe',
            PageSizeDropdown: '.dropdown.page-select-wrapper .roo-input-group.has-icon input.roo-input.roo-input.roo-input-md',
            PageSizePopup: '.roo-popup .roo-dropdown-menu',
            PageSize100Option: '.roo-dropdown-menu .roo-selector-option-item[data-value="100"]',
            NextPageButton: '.roo-pagination li.arrow > a[aria-label="Next"]',
            SelectNavTab: '.roo-tabs-nav .tab-item',
            ProductTotal: '.page-total',
            ExpandButton: '.roo-btn.roo-btn-link.roo-btn-link-brand.roo-btn-normal',
            GoodsTypeDropDown: 'input.roo-input.roo-input-md[placeholder="全部"]',
            GoodsTypeOption: '.roo-selector-option-item[title="非组包商品"]',
        },
        PrescriptionDetail: {
            DetailContainer: '.prescription-detail',
            PatientName: '.patient-name',
            PrescriptionDate: '.prescription-date',
            DoctorName: '.doctor-name',
            MedicationDetail: '.medication-detail',
            MedicationName: '.medication-name',
            MedicationDosage: '.medication-dosage',
            MedicationFrequency: '.medication-frequency',
        },
        CrawlerSteps: {
            IFrame: '#hashframe',
            SkuIdDropdown: 'input.roo-input.roo-input-md[placeholder="请选择"][value="综合查询"]', // 2025年03月14日从  value="请选择" 变为 value="综合查询"
            SkuIdOption: '.roo-selector-option-item[title="多个SKUID"]',
            SkuIdInput: 'input[placeholder="最多可录入50个SKUID，以英文逗号隔开"]',
            SearchButton: 'button.roo-btn-primary',
            EditButton: '._opreation_182r0_5 ._item_182r0_8',
            SvgButton: '.roo-icon.roo-iconfont._item_1vyss_51',
            StockInput: '.roo-input._edit-wrapper_1vyss_5',
        },
        GuideModal: {
            Modal: '.guide-modal',
            CloseButton: '.guide-modal-close-icon',
        },
        GlobalModal: {
            Modal: '.roo-modal',
            CloseButton: '.roo-modal .close',
        },
        MultipleModal: {
            Modal: '.multiple-modal-item.multiple-modal-active.active',
            CloseButton: '.multiple-modal-item.multiple-modal-active.active .roo-icon.roo-icon-close',
        },
        UpgradeModal: {
            Modal: '.roo-modal-dialog.roo-modal-default._feature-guide-modal_nn21s_2',
            CloseButton: '.roo-btn.roo-btn-default.roo-btn-normal',
        },
        ConfirmationDialog: {
            Modal: '.roo-modal-dialog.roo-modal-default',
            CloseButton: '.roo-modal-dialog.roo-modal-default .close',
        },
        FeatureGuideModal: {
            Modal: '._feature-guide-modal_nn21s_2',
            CloseButton: '._feature-guide-modal_nn21s_2 .roo-modal-footer .roo-btn-default',
        },
        NewVersionModal: {
            Modal: '._modal-hidden_nd7o9_50._scale-page-box_nd7o9_90',
            CloseButton: '.roo-icon.roo-icon-close._close_nd7o9_203',
        },
        NewMenuModal: {
            Modal: '.app_menu_guide_new.guide-modal',
            CloseButton: '.app_menu_guide_new.guide-modal .guide-modal-close-icon',
        },
        ServicePlatformModal: {
            Modal: '.guide-modal.account-guide-modal',
            CloseButton: '.guide-modal.account-guide-modal .guide-modal-close-icon',
        },
        AiAlertModal: {
            Modal: '.guide-modal.ai-assistant-guide-modal.account-guide-modal',
            CloseButton: '.guide-modal.ai-assistant-guide-modal.account-guide-modal .guide-modal-close-icon',
        },
    };

    static CSSContent = `
        ._survey-container_nd7o9_5,
        [class*="survey-container"],
        [class*="react-draggable"],
        .react-draggable-dragged {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }`;

    static localStorageScript = `
        localStorage.setItem('productListGuideExamination', 'productListGuideExamination');
        localStorage.setItem('account_guide_app_menu_guide_new_209456750', '1');
        localStorage.setItem('account_guide_app_menu_guide_new_216960073', 'true');
        localStorage.setItem('account_guide_app_menu_guide_new_217160509', 'true');
        localStorage.setItem('account_guide_yiyao-im-workbench_217160509', 'true');
        localStorage.setItem('account_guide_yiyao-im-workbench_176037284', 'true');
        localStorage.setItem('account_guide_yiyao-im-workbench_155116125', 'true');
        localStorage.setItem('account_guide_ai-search-guide-modal_217160509', 'true');
        localStorage.setItem('account_guide_ai_assistant_guide_new_217160509', 'true');
        localStorage.setItem('newFunctionOfSortPrompt', 'newFunctionOfSortPrompt');
        localStorage.setItem('CREATE_ONE_PRODUCT_TIP', 'CREATE_ONE_PRODUCT_TIP');
        localStorage.setItem('info_reported', 'true');
        localStorage.setItem('ORDER_DEGRADE_CACHE', 'true');
        localStorage.setItem('pageNoticeDate', '********');
        localStorage.setItem('suggestAnnouncement_216960073', '3');
        localStorage.setItem('YY_ALL_NEW_ORDER_NEWBIE_GUIDE', '2');
        console.log('Context prepared via CDP', localStorage.getItem('account_guide_app_menu_guide_new_209456750'));
    `;
}
