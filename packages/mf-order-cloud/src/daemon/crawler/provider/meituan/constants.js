export const MtClientUA = 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36';

export const MtCookies = [
    {
        'name': '_lxsdk_cuid',
        'value': '194eec16cdcc8-006a8e0252c147-1d525636-1aeaa0-194eec16cdcc8',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': '_lxsdk',
        'value': '194eec16cdcc8-006a8e0252c147-1d525636-1aeaa0-194eec16cdcc8',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'WEBDFPID',
        'value': 'x0x161z4v6yz543v17uu24347wy2746y805vvx82z5w5795888153w35-1742004367535-1741917713169QSACIQQ75613c134b6a252faa6802015be905511987',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'device_uuid',
        'value': '!0fd04b6e-6c7e-451a-8f81-7f1e4e91d4ed',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'bsid',
        'value': 'C7zc19K_tc5pk0n6yOkafpedkWjDC715cpyt3_FzUeN5E1G0eWaVM-28dsZqFn05pxv5h2z0tjFAIj_qrvDMwQ',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'region_version',
        'value': '0',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'region_id',
        'value': '0',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'ignore_set_router_proxy',
        'value': 'false',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'existBrandPoi',
        'value': 'true',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'isChain',
        'value': '0',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'city_id',
        'value': '0',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'isOfflineSelfOpen',
        'value': 'false',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'brandId',
        'value': '-1',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'uuid_update',
        'value': 'true',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'newCategory',
        'value': 'true',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'wmPoiId',
        'value': '********',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'acctId',
        'value': '*********',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'token',
        'value': '07YlcNr8IW40On1rKVOvDRjcM5UBBFcZsZSciRcwEqRQ*',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'accountAllPoiBusinessType',
        'value': '1',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'single_poi_businesstype',
        'value': '1',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'account_businesstype',
        'value': '1',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'location_id',
        'value': '510107',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'city_location_id',
        'value': '510100',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'provinceId',
        'value': '510000',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'cityId',
        'value': '510100',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'logistics_support',
        'value': '1',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'account_second_type',
        'value': '200',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'acct_name',
        'value': 'mt663199i',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'acct_id',
        'value': '*********',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'poi_first_category_id',
        'value': '22',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'poi_second_category_id',
        'value': '179',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'poi_id',
        'value': '********',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'com.sankuai.yiyao.shangjia.main_random',
        'value': '',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'pushToken',
        'value': '0zgJKRZCOzttVf8PdFEWjxl5eN4aKr0PtW5fnbsGLCKc*',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'pharmacistAccount',
        'value': '1',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'bmm-uuid',
        'value': '21b945ce-acb5-f474-e9b1-ea0a7bafd352',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'poiId',
        'value': '********',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'reviewData',
        'value': '{"orderViewId":"2001347302803959919","auditViewId":"2001347302803959919","remainAuditTime":"00:09:33","poiId":********,"pendingRpQcResult":null,"isCheckZYPoi":false}',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'orderViewId',
        'value': '2001347302803959919',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'igateApp',
        'value': 'shangouepc',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'labelInfo',
        'value': '20241202:0:0',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'cacheTimeMark',
        'value': '2024-12-02',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'shopCategory',
        'value': 'medicine',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'wpush_server_url',
        'value': 'wss://wpush.meituan.com',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'set_info',
        'value': '{"wmPoiId":********,"region_id":"1000510100","region_version":1727339401}',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'grayPoiIds',
        'value': '%5B%22********%22%5D',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'yy-epassport-accessToken',
        'value': 'C7zc19K_tc5pk0n6yOkafpedkWjDC715cpyt3_FzUeN5E1G0eWaVM-28dsZqFn05pxv5h2z0tjFAIj_qrvDMwQ',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': 'terminal',
        'value': 'bizCenter',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
    {
        'name': '_lxsdk_s',
        'value': '19592522310-9ff-61-722%7C%7C109',
        domain: 'yiyao.meituan.com',
        path: '/',
        secure: false,
        httpOnly: false,
        session: true,
        sameSite: 'unspecified',
    },
];

export const MEITUAN_STOCK_LOCAL_CACHE_KEY = 'meituan-stock-local-cache';

export const MEITUAN_EVENT_AUTH_STATUS_CHANGED = 'meituan-event-auth-status-changed';
export const MEITUAN_EVENT_SYNC_PRODUCT_COMPOSE_LIST = 'meituan-event-sync-product-compose-list';
export const MEITUAN_EVENT_ORDER_LIST_CHANGED = 'meituan-event-order-list-changed';
export const MEITUAN_EVENT_ORDER_LIST_NEW = 'meituan-event-order-list-new';
export const MEITUAN_EVENT_ORDER_SUMMARY_CHANGED = 'meituan-event-order-summary-changed';
export const MEITUAN_EVENT_PRESCRIPTION_LIST_CHANGED = 'meituan-event-prescription-list-changed';
export const MEITUAN_EVENT_SYNC_IS_START = 'meituan-event-sync-is-start';
export const MEITUAN_EVENT_SYNC_IS_DONE = 'meituan-event-sync-is-done';
export const MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE = 'meituan-event-sync-product-is-done';
export const MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED = 'meituan-event-sync-product-is-failed';
export const MEITUAN_EVENT_SYNC_PRODUCT_LIST = 'meituan-event-sync-product-list';
export const MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_DONE = 'meituan-event-sync-product-summary-is-done';
export const MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_FAILED = 'meituan-event-sync-product-summary-is-failed';
export const MEITUAN_EVENT_SYNC_PRODUCT_INFO = 'meituan-event-sync-product-info';

export const OrderStatusEnum = {
    newOrder: 1, // 用户已提交订单
    waitingDelivery: 4, //'待配送' // 骑手已取货
    delivering: 6, //'配送中',
    delivered: 7, //'已送达',
    received: 8, //'顾客已确认收货',
    canceled: 9, //'订单取消',
};

export const OrderStatusTextEnum = {
    [OrderStatusEnum.newOrder]: '新订单',
    [OrderStatusEnum.waitingDelivery]: '待配送',
    [OrderStatusEnum.delivering]: '配送中',
    [OrderStatusEnum.delivered]: '已送达',
    [OrderStatusEnum.received]: '用户已确认收货',
    [OrderStatusEnum.canceled]: '无效订单',
};


// 0-配送单发往配送，5-配送侧压单，10-配送单已确认，15-骑手已到店，20-骑手已取货，40-骑手已送达，100-配送单已取消。
export const OrderLogisticsStatusEnum = {
    waitingDelivery: 0, //'待发起配送',
    waitingRider: 1, //'待骑手接单',
    waitingSide: 5, //'待配送侧压单',
    waitingGet: 10, //'待骑手取货',
    riderArrived: 15, //'骑手已到店',
    riderGet: 20, //'骑手已取货',
    riderDelivered: 40, //'骑手已送达',
    canceled: 100, //'配送已取消',
};

export const OrderLogisticsStatusTextEnum = {
    [OrderLogisticsStatusEnum.waitingDelivery]: '待发起配送',
    [OrderLogisticsStatusEnum.waitingRider]: '待骑手接单',
    [OrderLogisticsStatusEnum.waitingSide]: '待配送侧压单',
    [OrderLogisticsStatusEnum.waitingGet]: '待骑手取货',
    [OrderLogisticsStatusEnum.riderArrived]: '骑手已到店',
    [OrderLogisticsStatusEnum.riderGet]: '骑手已取货',
    [OrderLogisticsStatusEnum.riderDelivered]: '骑手已送达',
    [OrderLogisticsStatusEnum.canceled]: '配送已取消',
};

export const OrderPayTypeEnum = {
    cash: '货到付款',
    online: '在线支付',
};


