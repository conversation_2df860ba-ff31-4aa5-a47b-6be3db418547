import Logger from '../../../../utils/logger';
import PddCrawler from './pdd-crawler';

const { remote } = window;
const path = window.require?.('path') || {};
const fs = window.require?.('fs') || {};

const logger = Logger.create('PddAuthManager');


const PDD_HOST = 'https://mms.pinduoduo.com';
const PDD_AUTH_KEY = 'PASS_ID';

function getSessionByAccount(account) {
    return window.remote.require('electron').session.fromPartition(`persist:${account}`);
}

export async function getCookiesByAccount(account) {
    const session = getSessionByAccount(account);
    return session?.cookies.get({
        url: PDD_HOST,
    });
}

export function getCookieByName(cookies, name) {
    return cookies?.find((item) => item.name === name);
}

export default class PddAuthManager {
    /**
     * @type {PddAuthManager}
     */
    static instance;

    storageFilePath;

    accountMap = new Map();

    static getInstance() {
        if (!this.instance) {
            this.instance = new PddAuthManager();
        }
        return this.instance;
    }

    constructor() {
        this.storageFilePath = path.join(remote.app.getPath('userData'), 'rpa/pdd-account-info.json');

        try {
            // 确保文件存在
            fs.mkdirSync(path.dirname(this.storageFilePath), { recursive: true });

            if (!fs.existsSync(this.storageFilePath)) {
                fs.writeFileSync(this.storageFilePath, '');
            }

            const data = fs.readFileSync(this.storageFilePath, 'utf8');
            if (data) {
                this.accountMap = new Map(JSON.parse(data));
            }
            logger.info('pdd account map read', this.accountMap);
        } catch (e) {
            logger.error('pdd account map read error', e);
        }
    }

    /**
     * 请求授权
     * @return {Promise<void>}
     */
    async requestAuth() {
        const crawler = new PddCrawler();

        const authResponse = await crawler.requestAuth();
        if (authResponse.status === false) {
            logger.error('requestAuth error', authResponse);
            return authResponse;
        }
        return authResponse;
    }

    /**
     * 添加账号信息，持久化存储 cookies 及 accountInfo，保存的 cookies 需要和登录的账号(account)绑定。后续用户会将 clinic 与 account 绑定，后续请求时，会根据 clinic -> account -> cookie 的映射关系，找到对应的 cookie，进行请求
     * @param {*} accountId
     * @param {accountInfo, passId} authData
     */
    async addAccount(accountId, authData) {
        const {
            accountInfo,
            passId,
            clinicId,
        } = authData;

        this.accountMap.set(accountId, {
            passId,
            accountInfo,
            clinicId,
        });
        await this.saveAccountMap();
    }


    /**
     * 清除账号信息
     * @param accountId
     * @return {Promise<void>}
     */
    async clearPassId(accountId) {
        const account = this.getAccount(accountId);
        if (!account) {
            return;
        }

        account.passId = '';
        this.accountMap.set(accountId, account);
        await this.saveAccountMap();
    }

    getPassId(accountId) {
        return this.getAccount(accountId)?.passId || '';
    }


    /**
     * 获取账号信息
     * @param accountId
     * @return {{accountInfo, passId}}
     */
    getAccount(accountId) {
        return this.accountMap.get(accountId);
    }

    async _getPassId(accountId) {
        const cookies = await getCookiesByAccount(accountId);
        return getCookieByName(cookies, PDD_AUTH_KEY)?.value;
    }

    /**
     * 保存 accountMap
     */
    async saveAccountMap() {
        try {
            await fs.promises.writeFile(this.storageFilePath, JSON.stringify(Array.from(this.accountMap)));
        } catch (e) {
            logger.error('pdd account map save error', e);
        }
    }
}
