import PddBean from '../pdd-bean';
import {
    modifyWithTimeString, prevDate,
} from '@abc/utils-date';

export default class GetAfterSalesOrderList extends PddBean {
    constructor(params) {
        super(params);
        this.path = '/honolulu/after_sales_order/paging';
        this.method = 'POST';
    }

    createRequestBody() {
        const now = new Date();
        now.setMilliseconds(0);
        const _endTime = modifyWithTimeString(now, '23:59:59');
        const _startTime = prevDate(_endTime, 15);
        const {
            keyList = [],
            startTime = _startTime.getTime(),
            endTime = _endTime.getTime(),
            statistics = true,
            shippingTmsStatusList = [],
            tmsReturnStatusList = [],
            tempNewFrontVersion1008 = true,
            mallIdList = [],
            timeType = 'refundUpdateTime',
            processStatus = 0,
            shippingId = '',
            page = 1,
            pageSize = 100,
        } = this.params || {};
        return {
            keyList,
            startTime,
            endTime,
            statistics,
            shippingTmsStatusList,
            tmsReturnStatusList,
            tempNewFrontVersion1008,
            mallIdList,
            timeType,
            processStatus,
            shippingId,
            page,
            pageSize,
        };
    }
}
