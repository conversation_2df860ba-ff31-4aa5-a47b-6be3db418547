import PddBean from '../pdd-bean';

export default class GetUserInfo extends PddBean {
    constructor() {
        super();
        this.path = '/janus/api/new/userinfo';
        this.method = 'POST';
    }

    parseResponse(response) {
        const parsedResponse = super.parseResponse(response);
        if (parsedResponse.status === false) {
            return parsedResponse;
        }
        parsedResponse.data.mall_id = String(parsedResponse.data.mall_id);
        return parsedResponse;
    }
}
