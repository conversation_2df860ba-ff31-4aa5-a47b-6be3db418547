/**
 * 订单跟踪警告规则。
 */
export interface OrderTraceWarnRule {
    waitAccept: number;
    accept: number;
    station: number;
    jzhwRegion: number;
    jjjRegion: number;
    sameProvince: number;
    diffProvince: number;
    specialRegion: number;
    isShowReturnPackage: boolean;
    traceWarnPopSetting: number;
}

/**
 * 报告设置。
 */
export interface ReportSetting {
    deliveryReportSwitch: boolean;
    refundReportSwitch: boolean;
    goodsCostPercent: number | null;
    shippingCost: number | null;
}

/**
 * 发货记录设置。
 */
export interface DeliveryRecordSetting {
    deliveryRecordSwitch: boolean;
    deliveryRecordRemarkSwitch: boolean;
    recordMustPrint: boolean;
}

/**
 * 自动发货设置。
 */
export interface AutoDeliverySetting {
    timedHour: number | null;
    remainingHours: number | null;
    timeDeliveryMustPrint: boolean;
}

/**
 * 打印排序设置。
 */
export interface PrintSortSetting {
    printSortType: number;
    mergeSort: number;
}

/**
 * 表示系统配置数据的接口。
 */
export interface PddUserSetting {
    orderTraceWarnRule: OrderTraceWarnRule;
    reportSetting: ReportSetting;
    deliveryRecordSetting: DeliveryRecordSetting;
    logisticsDeliverySwitch: number;
    autoDeliverySetting: AutoDeliverySetting;
    receiverIdRule: number;
    isOpenAssignShipping: boolean;
    deliveredPrintRemarkSwitch: number;
    printSortSetting: PrintSortSetting;
    slaveMallVisibility: number;
    autoUnlockSwitch: number;
    remainHour: number;
    mergeConsoReceiverIdRule: number;
    presaleNotAutoMerge: number;
    aimSkuNotAutoMerge: number;
    mergeOrderSort: number;
    crossMallMergeSwitch: number;
}
