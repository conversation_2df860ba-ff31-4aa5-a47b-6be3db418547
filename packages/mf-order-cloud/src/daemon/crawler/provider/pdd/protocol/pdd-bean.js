import BaseBean from '../../../protocol/base-bean';
import Response from '../../../common/Response';
import { ResponseErrorCode } from '../../../common/constants';

export default class PddBean extends BaseBean {
    constructor(params) {
        super(params);
        this.baseUrl = 'https://mms.pinduoduo.com/';
    }

    parseResponse(response) {
        const {
            success,
            result,
        } = response;

        if (!success) {
            // PDD 的 errorCode 字段可能有多个，这里统一处理
            // 43001: cookie 过期或者没有携带 cookie
            // 1003: 服务器忙，请稍后再试
            const {
                error_msg: msg,
                error_code: code,
                errorMessage: msg2,
                errorMsg: msg3,
                errorCode: code2,
            } = response;
            const errorMsg = msg || msg2 || msg3;
            const errorCode = code || code2;
            return Response.error(errorMsg, {
                errorMsg,
                code: errorCode === 43001 ? ResponseErrorCode.AUTH_NOT_AUTHED : errorCode,
            });
        }

        return Response.success(result);
    }
}
