import Response from '../../../common/Response';
import BaseProtocol from '../../../protocol/base-protocol';

export default class PddProtocol extends BaseProtocol {
    static protocolId = 'pdd';

    // 授权数据
    authData = {
        passId: '',
    };

    constructor() {
        super({
            protocol: 'https',
            hostname: 'mms.pinduoduo.com',
            port: '443',
        });
    }

    setAuthData(authData) {
        this.authData = authData;
    }


    /**
     * 请求方法
     * @param bean
     * @returns {Promise<*>}
     */
    async requestApi(bean) {
        const options = this.createRequestOptions(bean);
        let result = null;
        let response = null;
        try {
            result = await window.electron.network.request(options);
            const parsedResponse = bean.parseResponse(JSON.parse(result));
            if (parsedResponse.status === false) {
                // 如果是token失效，则清空存储的数据，需要重新登录
                return parsedResponse;
            }
            response = parsedResponse;
        } catch (error) {
            console.log('requestApi error', error);
            response = Response.error(error.message || '接口调用失败');
        }
        return response;
    }

    createRequestOptions(bean) {
        const {
            passId,
        } = this.authData;
        const options = {
            hostname: this.hostname,
            protocol: this.protocol,
            port: this.port,
            path: bean.createUrl(),
            body: JSON.stringify(bean.createRequestBody()),
            method: bean.method,
            headers: {
                'Content-Type': 'application/json',
            },
            responseType: 'json',
        };
        if (passId) {
            options.headers.Cookie = `PASS_ID=${passId}`;
        }
        return options;
    }

}
