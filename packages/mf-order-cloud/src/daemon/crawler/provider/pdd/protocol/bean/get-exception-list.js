import {
    modifyWithTimeString, prevDate,
} from '@abc/utils-date';
import PddBean from '../pdd-bean';

export default class GetExceptionList extends PddBean {
    constructor(params) {
        super(params);
        this.path = '/honolulu/order/exception/list';
        this.method = 'POST';
    }

    createRequestBody() {
        const now = new Date();
        now.setMilliseconds(0);
        const endTime = modifyWithTimeString(now, '23:59:59');
        const startTime = prevDate(endTime, 2);
        const {
            page = 1,
            pageSize = 100,
            mallIdList = [],
            // 异常开始时间
            exceptionStartTime = startTime.getTime(),
            // 异常结束时间
            exceptionEndTime = endTime.getTime(),
            // 处理状态：0 待处理，1 已处理
            ignoreStatusList = [0],
        } = this.params || {};
        return {
            page,
            pageSize,
            mallIdList,
            exceptionStartTime,
            exceptionEndTime,
            ignoreStatusList,
        };
    }
}
