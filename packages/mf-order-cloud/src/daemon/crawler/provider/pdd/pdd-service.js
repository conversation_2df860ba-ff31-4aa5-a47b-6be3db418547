import BaseService from '../../base/base-service';
import Response from '../../common/Response';
import {
    AuthStatus, ResponseErrorCode,
} from '../../common/constants';
import {
    openWindow,
} from '../../common/utils';
import PddCrawler from './pdd-crawler';
import CrawlerTask from '../../base/crawler-task';
import Logger from '@/utils/logger';
import PddAuthManager from '@/daemon/crawler/provider/pdd/auth-manager';
import {
    PDD_EVENT_AUTH_STATUS_CHANGED,
    PDD_EVENT_EXCEPTION_LIST_CHANGED,
    PDD_EVENT_EXPRESS_WARN_CHANGED,
} from '@/daemon/crawler/provider/pdd/constants';

function getSessionByAccount(account) {
    return window.remote.require('electron').session.fromPartition(`persist:${account}`);
}

async function clearCookiesByAccount(account) {
    const session = getSessionByAccount(account);
    await session?.clearStorageData({
        storages: ['cookies'],
    });
}

/**
 * 拼多多服务，封装业务逻辑，管理授权状态
 */
export default class PddService extends BaseService {
    protocol;
    authStatus = AuthStatus.INIT;

    /**
     * @type {PddAuthManager}
     */
    authManager;

    /**
     * @type {PddCrawler}
     */
    crawler;

    /**
     * @type {Logger}
     */
    logger;

    exceptionListTaskId;

    expressWarnTaskId;

    unregisterTaskListener;

    constructor(account, scheduler) {
        super(account, scheduler);
        this.platform = 'pdd';
        this.handleTaskEvent = this.handleTaskEvent.bind(this);
        this.authManager = PddAuthManager.getInstance();
        this.crawler = new PddCrawler(account);
        this.scheduler.addCrawler(this.platform, this.account, this.crawler);
        this.logger = Logger.create(`PddService[${this.account}]`);
        this.logger.info(`PddService constructor, account: ${this.account}`, this.authStatus);
    }

    /**
     * 初始化，需要在所有接口调用前调用
     * @return {Promise<void>}
     */
    init() {
        super.init();
        this.logger.info(`PddService init, account: ${this.account}`);
        // 初始化时从本地缓存中获取授权状态
        const authData = this.authManager.getAccount(this.account);
        this.authStatus = authData?.passId ? AuthStatus.AUTHED : AuthStatus.INIT;
        this._emitEvent(PDD_EVENT_AUTH_STATUS_CHANGED, {
            authStatus: this.authStatus,
        });
    }

    /**
     * 启动服务
     * @return {Promise<void>}
     */
    async start() {
        // 监听任务事件
        this.unregisterTaskListener = this.scheduler.registerTaskListener(this.platform, this.account, this.handleTaskEvent);
        // 检查授权状态，授权状态正常，才会启动其他任务
        const checkAuthResponse = await this.checkAuth();
        if (checkAuthResponse.status === false) {
            this.logger.info(`start checkAuth failed, ${checkAuthResponse}`);
            return checkAuthResponse;
        }
        this.logger.info('start checkAuth success, start tasks');

        this.scheduleTasks();
    }

    scheduleTasks() {
        // 开启定时任务
        // 1. 获取异常列表，30s 一次
        const exceptionListTask = new CrawlerTask(this.platform, this.account, 'getExceptionList', {
            type: 'interval',
            interval: 1000 * 30,
            immediate: true,
        });

        this.exceptionListTaskId = this.scheduler.scheduleTask(exceptionListTask);

        // 2. 获取物流预警，30s 一次
        const expressWarnTask = new CrawlerTask(this.platform, this.account, 'getExpressWarn', {
            type: 'interval',
            interval: 1000 * 30,
            immediate: true,
        });

        this.expressWarnTaskId = this.scheduler.scheduleTask(expressWarnTask);
    }

    cancelTasks() {
        this.scheduler.cancelTask(this.exceptionListTaskId);
        this.scheduler.cancelTask(this.expressWarnTaskId);
        this.exceptionListTaskId = null;
        this.expressWarnTaskId = null;
    }

    async handleTaskEvent(event) {
        const {
            taskId,
        } = event;
        if (taskId !== this.exceptionListTaskId && taskId !== this.expressWarnTaskId) {
            return;
        }
        switch (event.eventType) {
            case 'success': {
                const {
                    response, task,
                } = event.data;

                if (response.status === false) {
                    // 任务执行失败，处理失败逻辑
                    this.logger.error(`task faile, ${task.type}`, response);
                    if (response.data?.code === ResponseErrorCode.AUTH_NOT_AUTHED) {
                        this.logger.error(`task failed, auth not authed, clearAuth, stop task, this.exceptionListTaskId: ${this.exceptionListTaskId}, this.expressWarnTaskId: ${this.expressWarnTaskId}`);
                        // 清除授权信息
                        await this._clearAuth();
                    }
                } else {
                    this.logger.info(`task success, ${task.type}`, response);
                    if (task.type === 'getExceptionList') {
                        this._emitEvent(PDD_EVENT_EXCEPTION_LIST_CHANGED, response.data);
                    } else if (task.type === 'getExpressWarn') {
                        this._emitEvent(PDD_EVENT_EXPRESS_WARN_CHANGED, response.data);
                    }
                }

                break;
            }
            case 'cancelled': {
                const {
                    task,
                } = event.data;
                this.logger.info(`task cancelled, ${task.type}`);
                break;
            }
            default:
                break;
        }

    }

    /**
     * 销毁
     * @return {Promise<void>}
     */
    async destroy() {
        this.logger.info(`PddService destroy, account: ${this.account}`);
        super.destroy();
        this.cancelTasks();
        this.unregisterTaskListener?.();
        this.scheduler.removeCrawler(this.platform, this.account);
        this.protocol = null;
    }

    /**
     * 获取当前授权状态
     * @return {AuthStatus}
     */
    getAuthStatus() {
        return this.authStatus;
    }

    /**
     * 检查授权状态
     * @return {Promise<Response>}
     */
    async checkAuth() {
        const task = new CrawlerTask(this.platform, this.account, 'checkAuth');
        const response = await this.scheduler.execTaskSync(task);
        if (response.status === false) {
            await this._clearAuth();
        }
        return response;
    }

    /**
     * 请求授权，如果已经授权，直接返回，否则将打开授权弹窗
     */
    async requestAuth() {
        const task = new CrawlerTask(this.platform, this.account, 'requestAuth');
        const response = await this.scheduler.execTaskSync(task);
        if (response.status === true) {
            const result = response.data;
            await this.authManager.addAccount(this.account, result);
            this.authStatus = AuthStatus.AUTHED;
            this._emitEvent(PDD_EVENT_AUTH_STATUS_CHANGED, {
                authStatus: this.authStatus,
            });
            // 授权成功，重新调度任务
            this.scheduleTasks();
            return Response.success(result);
        }

        await this._clearAuth();
        return response;
    }

    /**
     * 取消授权，清除授权信息，调试用
     */
    async cancelAuth() {
        await this._clearAuth();
        return clearCookiesByAccount(this.account);
    }

    /**
     * @typedef {Object} EcPageValue
     * @property {string} name - The name of the page.
     * @property {string} url - The URL of the page.
     * @property {string} [locator] - An optional locator for the page subsection.
     */

    /**
     * 打开指定的页面
     * @param options {EcPageValue}
     * @return {BrowserWindow}
     */
    openPage(options) {
        const {
            url,
            locator,
        } = options;
        const page = openWindow(url, {
            width: 1280,
            height: 768,
            webPreferences: {
                partition: `persist:${this.account}`,
            },
        }, {
            'did-finish-load': () => {
                this._optimizePageElements(page);
                if (locator) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this._clickByText(page, locator);
                    }, 1000);
                }
            },
        });
        return page;
    }

    async _clearAuth() {
        const accountInfo = this.authManager.getAccount(this.account);
        this.authStatus = accountInfo ? AuthStatus.EXPIRED : AuthStatus.INIT;
        await this.authManager.clearPassId(this.account);
        this._emitEvent(PDD_EVENT_AUTH_STATUS_CHANGED, {
            authStatus: this.authStatus,
        });
        this.cancelTasks();
    }

    /**
     * 优化页面元素，主要隐藏了顶栏和客服
     * @param page {BrowserWindow}
     * @private
     */
    _optimizePageElements(page) {
        const commonCss = `
                        #root .header-link {
                            display: none;
                        }
                        #root .billing-tool-header {
                            display: none;
                        }

                        #root .customer_service_pendant {
                            display: none;
                        }
                    `;
        // 修改页面 css
        page.webContents.insertCSS(commonCss);

        const js = `
                        const tabs = document.querySelectorAll('.page-top-tab .top-tab .tab-item');
                        if (tabs) {
                            tabs.forEach(function(tab) {
                              // 获取每个 tab 的文本内容
                              var text = tab.textContent || tab.innerText;

                              // 检查文本内容，如果不是指定的内容，则隐藏元素
                              if (text !== '异常订单' && text !== '物流预警' && text !== '售后管理') {
                                tab.style.display = 'none'; // 将元素隐藏
                              }
                            });
                        }
                    `;

        // 执行 js
        page.webContents.executeJavaScript(js);
    }

    _clickByText(page, text) {
        const js = `
            var _abc_target_elements = document.querySelectorAll('div[data-testid="beast-core-card"]');
            if (_abc_target_elements) {
                for (const element of _abc_target_elements) {
                    if (element.textContent.includes("${text}")) {
                        element.click();
                        break;
                    }
                }
            }
        `;
        console.log('clickByText', js);
        page.webContents.executeJavaScript(js);
    }

    _emitEvent(type, data) {
        this.emit('event', {
            type,
            data,
            service: this,
        });
    }
}
