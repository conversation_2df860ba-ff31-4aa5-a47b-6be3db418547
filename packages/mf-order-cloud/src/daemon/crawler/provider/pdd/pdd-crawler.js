import Response from '../../common/Response';
import PddProtocol from './protocol/pdd-protocol';
import GetUserInfo from './protocol/bean/get-user-info';
import GetUserSetting from './protocol/bean/get-user-setting';
import GetExceptionList from './protocol/bean/get-exception-list';
import GetOrderTraceCount from './protocol/bean/get-order-trace-count';
import GetAfterSalesOrderList from './protocol/bean/get-after-sales-order-list';
import {
    PddPage, ResponseErrorCode,
} from '../../common/constants';
import {
    openWindow, sleep,
} from '../../common/utils';
import BaseCrawler from '../../base/base-crawler';
import PddAuthManager from './auth-manager';
import Logger from '@/utils/logger';

const logger = Logger.create('PddCrawler');

const PDD_HOST = 'https://mms.pinduoduo.com';
const PDD_AUTH_KEY = 'PASS_ID';

function getSessionByAccount(account) {
    return window.remote.require('electron').session.fromPartition(`persist:${account}`);
}

export async function getCookiesByAccount(account) {
    const session = getSessionByAccount(account);
    return session?.cookies.get({
        url: PDD_HOST,
    });
}

async function clearCookiesByAccount(account) {
    const session = getSessionByAccount(account);
    await session?.clearStorageData({
        storages: ['cookies'],
    });
}

export function getCookieByName(cookies, name) {
    return cookies?.find((item) => item.name === name);
}

function BindAuth() {
    return function (target, name, descriptor) {
        const originFn = descriptor.value;

        descriptor.value = async function (...args) {
            const bindAuthResponse = this._bindAuth(...args);
            if (bindAuthResponse.status === false) {
                return bindAuthResponse;
            }

            const response = await originFn.call(this, ...args);

            return response;
        };
        return descriptor;
    };
}

const AUTH_URL = PddPage.LOGIN.url;

export default class PddCrawler extends BaseCrawler {
    accountId = '';
    protocol;

    constructor(accountId) {
        super();

        this.accountId = accountId;
        this.authManager = PddAuthManager.getInstance();
        this.protocol = new PddProtocol();
        this.logger = logger;
    }

    setAccountId(accountId) {
        this.accountId = accountId;
    }

    async executeTask(task) {
        const { type } = task;
        switch (type) {
            case 'checkAuth':
                return this.checkAuth();
            case 'requestAuth':
                return this.requestAuth();
            case 'openPage':
                return this.openPage();
            case 'getUserInfo':
                return this.getUserInfo();
            case 'getUserSetting':
                return this.getUserSetting();
            case 'getExceptionList':
                return this.getExceptionList();
            case 'getExpressWarn':
                return this.getExpressWarn();
            case 'getAfterSalesOrderList':
                return this.getAfterSalesOrderList();

            default:
                return Response.error('Invalid task type', { code: ResponseErrorCode.INVALID_TASK_TYPE });
        }
    }

    /**
     * 销毁
     * @return {Promise<void>}
     */
    async destroy() {
        this.protocol = null;
    }

    async checkAuth() {
        this.logger.debug('checkAuth start');

        // 先从持久化中获取 passId
        const authData = this.authManager.getAccount(this.accountId);
        this.logger.debug('checkAuth authData from local', authData);


        if (!authData?.passId) {
            return Response.error('未授权', {
                code: ResponseErrorCode.AUTH_NOT_AUTHED,
            });
        }

        // 发起请求，检查是否过期
        this._setPassId(authData.passId);

        const response = await this._checkPermissionValid();

        if (response.status === true) {
            return Response.success({
                accountInfo: response.data,
                passId: authData.passId,
            });
        }

        return response;
    }

    /**
     * 请求授权，如果已经授权，直接返回，否则将打开授权弹窗
     */
    async requestAuth() {
        // 如果当前已经授权，则调用接口，验证是否过期
        const bindAuthResponse = await this.checkAuth();
        // 如果已经授权，直接返回
        if (bindAuthResponse.status === true) {
            this.logger.info('requestAuth 已经授权');
            return bindAuthResponse;
        }

        this.logger.info('requestAuth start');


        // 没有授权或授权已经过期，则先清理Cookie，重新授权
        await clearCookiesByAccount(this.accountId);

        let loopCheck = true;

        window.outerHeight;
        const win = openWindow(AUTH_URL, {
            width: Math.ceil(window.outerWidth * 0.8),
            height: Math.ceil(window.outerHeight * 0.9),
            webPreferences: {
                partition: `persist:${this.accountId}`,
            },
        }, {
            close: () => {
                loopCheck = false;
            },
        });

        // 循环检查，等待授权
        let passId = '';
        do {
            const cookies = await getCookiesByAccount(this.accountId);
            passId = getCookieByName(cookies, PDD_AUTH_KEY)?.value;
            if (passId) {
                this._setPassId(passId);
                break;
            }
            await sleep(500);
        } while (!passId && loopCheck);

        const isUserCancel = win.isDestroyed();
        if (isUserCancel) {
            return Response.error('取消授权', { code: ResponseErrorCode.REQUEST_AUTH_CANCEL });
        }
        // 非用户主动关闭，需要自动关闭授权窗口
        win.close();

        // 授权完毕后检查权限
        const response = await this._checkPermissionValid();
        // 授权成功
        if (response.status === true) {
            return Response.success({
                accountInfo: response.data,
                passId,
            });
        }
        // 授权失败，清除授权
        await clearCookiesByAccount(this.accountId);

        return response;
    }

    /**
     * 取消授权，清除授权信息，调试用
     */
    async cancelAuth() {
        return clearCookiesByAccount(this.accountId);
    }

    /**
     * @typedef {Object} EcPageValue
     * @property {string} name - The name of the page.
     * @property {string} url - The URL of the page.
     * @property {string} [locator] - An optional locator for the page subsection.
     */

    /**
     * 打开指定的页面
     * @param options {EcPageValue}
     * @return {BrowserWindow}
     */
    openPage(options) {
        const {
            url,
            locator,
        } = options;
        const page = openWindow(url, {
            width: 1280,
            height: 768,
            webPreferences: {
                partition: `persist:${this.accountId}`,
            },
        }, {
            'did-finish-load': () => {
                this._optimizePageElements(page);
                if (locator) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this._clickByText(page, locator);
                    }, 1000);
                }
            },
        });
        return page;
    }

    /**
     * 获取用户信息
     * @return {ApiResponse<PddUserInfo>}
     */
    @BindAuth()
    async getUserInfo() {
        const getUserInfoBean = new GetUserInfo();
        return this.protocol.requestApi(getUserInfoBean);
    }

    /**
     * 获取用户设置
     * @return {UserSetting<PddUserSetting>}
     */
    @BindAuth()
    async getUserSetting() {
        const getUserSettingBean = new GetUserSetting();
        return this.protocol.requestApi(getUserSettingBean);
    }

    /**
     * 获取异常列表
     * @param params
     * @return {ApiResponse<PddExceptionList>}
     */
    @BindAuth()
    async getExceptionList(params) {
        const getExceptionListBean = new GetExceptionList(params);
        return this.protocol.requestApi(getExceptionListBean);
    }

    /**
     * 获取物流预警
     * @return {ApiResponse<PddOrderTraceCount>}
     */
    @BindAuth()
    async getExpressWarn() {
        const getOrderTraceCountBean = new GetOrderTraceCount();
        return this.protocol.requestApi(getOrderTraceCountBean);
    }

    /**
     * 获取售后订单
     * @return {ApiResponse<PddAfterSalesOrderList>}
     */
    @BindAuth()
    async getAfterSalesOrderList() {
        const getAfterSalesOrderListBean = new GetAfterSalesOrderList({
            mallIdList: [this.accountId],
        });
        return this.protocol.requestApi(getAfterSalesOrderListBean);
    }


    _bindAuth() {
        const passId = this.authManager.getPassId(this.accountId);
        this._setPassId(passId);
        if (!passId) {
            return Response.error('未授权', {
                code: ResponseErrorCode.AUTH_NOT_AUTHED,
            });
        }
        return Response.success();
    }

    _setPassId(passId) {
        this.protocol.setAuthData({
            passId,
        });
    }

    async _checkPermissionValid() {
        const getUserInfoBean = new GetUserInfo();
        const getUserSettingBean = new GetUserSetting();
        const [
            getUserInfoResponse,
            getUserSettingResponse,
        ] = await Promise.all(
            [
                this.protocol.requestApi(getUserInfoBean),
                this.protocol.requestApi(getUserSettingBean),
            ],
        );


        if (getUserInfoResponse.status === false || getUserSettingResponse.status === false) {
            return Response.error('获取授权信息失败', {
                code: ResponseErrorCode.REQUEST_AUTH_GET_USER_INFO_FAILED,
            });
        }

        if (getUserInfoResponse.data?.mall_id !== this.accountId) {
            return Response.error('授权账号与绑定门店不匹配，请重新授权', {
                code: ResponseErrorCode.REQUEST_AUTH_ACCOUNT_NOT_MATCH,
            });
        }

        //TODO 检查权限
        return getUserInfoResponse;
    }

    /**
     * 优化页面元素，主要隐藏了顶栏和客服
     * @param page {BrowserWindow}
     * @private
     */
    _optimizePageElements(page) {
        const commonCss = `
                        #root .header-link {
                            display: none;
                        }
                        #root .billing-tool-header {
                            display: none;
                        }

                        #root .customer_service_pendant {
                            display: none;
                        }
                    `;
        // 修改页面 css
        page.webContents.insertCSS(commonCss);

        const js = `
                        const tabs = document.querySelectorAll('.page-top-tab .top-tab .tab-item');
                        if (tabs) {
                            tabs.forEach(function(tab) {
                              // 获取每个 tab 的文本内容
                              var text = tab.textContent || tab.innerText;

                              // 检查文本内容，如果不是指定的内容，则隐藏元素
                              if (text !== '异常订单' && text !== '物流预警' && text !== '售后管理') {
                                tab.style.display = 'none'; // 将元素隐藏
                              }
                            });
                        }
                    `;

        // 执行 js
        page.webContents.executeJavaScript(js);
    }

    _clickByText(page, text) {
        const js = `
            var _abc_target_elements = document.querySelectorAll('div[data-testid="beast-core-card"]');
            if (_abc_target_elements) {
                for (const element of _abc_target_elements) {
                    if (element.textContent.includes("${text}")) {
                        element.click();
                        break;
                    }
                }
            }
        `;
        console.log('clickByText', js);
        page.webContents.executeJavaScript(js);
    }
}
