import <PERSON>EventHand<PERSON> from './base-event-handler';
import Logger from '@/utils/logger';
import {
    MEITUAN_EVENT_SYNC_PRODUCT_COMPOSE_LIST,
    MEITUAN_EVENT_ORDER_LIST_CHANGED,
    MEITUAN_EVENT_ORDER_SUMMARY_CHANGED,
    MEITUAN_EVENT_ORDER_LIST_NEW,
    MEITUAN_EVENT_PRESCRIPTION_LIST_CHANGED,
    MEITUAN_EVENT_SYNC_PRODUCT_LIST,
} from '../provider/meituan/constants';
import TAGoodsAPI from '@/api/ta-goods';
import ECOrder<PERSON>I from '@/api/order';

const logger = Logger.create('MeituanEventHandler');

/**
 * @class MeituanEventHandler
 * @classdesc 美团事件处理器
 */
export default class MeituanEventHandler extends BaseEventHandler {

    async handleEvent(event) {
        const {
            type,
            data,
            service,
        } = event;
        logger.info('handleMeituanEvent', type, data, service);
        const authAccount = service.getAccount();

        switch (type) {
            case MEITUAN_EVENT_SYNC_PRODUCT_COMPOSE_LIST:
                await this._handleSyncComposeList(authAccount, data);
                break;
            case MEITUAN_EVENT_SYNC_PRODUCT_LIST:
                await this._handleSyncProductList(service, authAccount ,data);
                break;
            case MEITUAN_EVENT_ORDER_LIST_CHANGED:
                await this._handleOrderListChanged(data, authAccount);
                break;
            case MEITUAN_EVENT_ORDER_LIST_NEW:
                await this._handleOrderListNew(data, authAccount);
                break;
            case MEITUAN_EVENT_ORDER_SUMMARY_CHANGED:
                await this._handleOrderSummaryChanged(data, authAccount);
                break;
            case MEITUAN_EVENT_PRESCRIPTION_LIST_CHANGED:
                await this._handlePrescriptionListChanged(data, authAccount);
                break;
            default:
                break;
        }
        this.notifyListeners(event);
        if (event.eventResHandler && typeof event.eventResHandler === 'function') {
            event.eventResHandler(event);
        }
    }

    async _handleSyncProductList(service, authAccount ,data) {
        logger.info('handleMeituanEvent MEITUAN_EVENT_SYNC_PRODUCT_LIST', data);
        const {
            singleList, tagList,
        } = data;

        try {
            const res = await TAGoodsAPI.syncTaGoodsList({
                isAllSync: 0,
                goodsList: singleList,
                tagList,
                mallId: authAccount.ecMallId,
            });
            if (res) {
                service.needSyncExtGoodsSkuStocks.push(...(res?.needSyncExtGoodsSkuStocks || []));
                service.syncInfo.binded = res?.boundHisGoodsCount || 0;
                logger.info('handleMeituanEvent:', service.needSyncExtGoodsSkuStocks);
            }
        } catch (error) {
            service.failedProductList.push({
                singleList,
                tagList,
            });
            logger.error('handleMeituanEvent syncTaGoodsList error', error);
            const lastExecutionTimeKey = 'meituan_scrape_products_last_execution';
            localStorage.removeItem(lastExecutionTimeKey);
        }
    }

    async _handleSyncComposeList(authAccount, productDetailRes) {
        logger.info('handleMeituanEvent MEITUAN_EVENT_SYNC_PRODUCT_COMPOSE_LIST', productDetailRes);
        try {
            await TAGoodsAPI.syncTaGoodsComposeDetail({
                mallId: authAccount.ecMallId,
                goodsList: productDetailRes.data?.detail ? [productDetailRes.data?.detail] : [],
            });
        } catch (error) {
            const lastExecutionTimeKey = 'meituan_scrape_products_last_execution';
            localStorage.removeItem(lastExecutionTimeKey);
            logger.error('handleMeituanEvent syncTaGoodsComposeDetail error', error);
        }
    }

    async _handleOrderListChanged(data, authAccount) {
        logger.info('meituan 同步订单列表', data);
        if (data?.length === 0) return;
        ECOrderAPI.syncMeituanOrder({
            ecMallId: authAccount.ecMallId,
            mtOrders: data,
        });
    }

    async _handleOrderListNew(data) {
        logger.info('meituan 同步新订单', data);
    }

    async _handleOrderSummaryChanged(data, authAccount) {
        logger.info('meituan 同步美团summary信息', data);
        ECOrderAPI.syncMTOrderInfo({
            ecMallId: authAccount.ecMallId,
            ...data,
        });
    }

    async _handlePrescriptionListChanged(data) {
        logger.info('meituan 同步prescription list', data);
        if (data?.length === 0) return;
        ECOrderAPI.syncMeituanPrescription({
            mtPrescriptions: data,
        });
    }
}
