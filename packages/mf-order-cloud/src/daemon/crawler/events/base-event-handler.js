/**
 * @class BaseEventHandler
 * @classdesc 事件处理器基类
 */
export default class BaseEventHandler {
    listeners = [];
    constructor(store) {
        this.store = store;
    }

    /**
     * 处理事件
     * @param {Object} event 事件对象
     * @returns {Promise<void>}
     */
    async handleEvent() {
        throw new Error('Method not implemented');
    }

    /**
     * 添加监听器
     * @param {Function} listener 监听器函数
     */
    addListener(listener) {
        this.listeners.push(listener);
    }

    /**
     * 移除监听器
     * @param {Function} listener 监听器函数
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index !== -1) {
            this.listeners.splice(index, 1);
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(data) {
        for (let i = 0; i < this.listeners.length; i++) {
            const listener = this.listeners[i];
            if (typeof listener === 'function') {
                listener(data);
            }
        }
    }
}
