import BaseEventHandler from './base-event-handler';
import Logger from '@/utils/logger';
import {
    PDD_EVENT_AUTH_STATUS_CHANGED,
    PDD_EVENT_EXCEPTION_LIST_CHANGED,
    PDD_EVENT_EXPRESS_WARN_CHANGED,
} from '../provider/pdd/constants';

const logger = Logger.create('PddEventHandler');

/**
 * @class PddEventHandler
 * @classdesc 拼多多事件处理器
 */
export default class PddEventHandler extends BaseEventHandler {
    async handleEvent(event) {
        const {
            type,
            data,
            service,
        } = event;
        const accountId = service.getAccount();
        logger.info('handlePddEvent', type, data, service.getAccount(), service.getAuthStatus());

        switch (type) {
            case PDD_EVENT_AUTH_STATUS_CHANGED:
                this.store.setEcMallFEInfo(accountId, {
                    authStatus: service.getAuthStatus(),
                });
                break;
            case PDD_EVENT_EXCEPTION_LIST_CHANGED:
                this.store.setEcMallFEInfo(accountId, {
                    exceptionList: data,
                });
                break;
            case PDD_EVENT_EXPRESS_WARN_CHANGED:
                this.store.setEcMallFEInfo(accountId, {
                    expressWarn: data,
                });
                break;
            default:
                break;
        }
        this.notifyListeners(event);

    }
}
