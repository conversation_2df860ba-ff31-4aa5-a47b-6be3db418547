import PddEventHandler from './pdd-event-handler';
import Meituan<PERSON><PERSON><PERSON><PERSON><PERSON> from './meituan-event-handler';
import { ECTypeEnum } from '@/utils/constants';

/**
 * @class EventHandlerFactory
 * @classdesc 事件处理器工厂类
 */
export default class EventHandlerFactory {
    static createHandler(platform, store) {
        switch (platform) {
            case ECTypeEnum.PDD:
                return new PddEventHandler(store);
            case ECTypeEnum.MT:
                return new MeituanEventHandler(store);
            default:
                throw new Error(`Unsupported platform: ${platform}`);
        }
    }
}
