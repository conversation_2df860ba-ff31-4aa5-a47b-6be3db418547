import { AUTH_STORE_KEY } from './common/constants';

export const AppPreferences = window.electron?.appSharedPreferences || {
    get: (key) => {
        return window.localStorage.getItem(key);
    },
    set: (key, value) => {
        window.localStorage.setItem(key, JSON.stringify(value));
    },
};

/**
 * 获取授权账号信息
 * @return {null}
 */
export function getAuthAccountInfo() {
    let authAccountInfo = null;
    try {
        authAccountInfo = AppPreferences.get(AUTH_STORE_KEY);
    } catch (e) {
        console.log('getAuthAccountInfo error', e);
    }
    return authAccountInfo;
}

/**
 * 账号是否授权过
 * @param account
 * @return {boolean}
 */
export function isAuthedByAccount(account) {
    const authAccountInfo = getAuthAccountInfo();
    if (!authAccountInfo) {
        return false;
    }
    return !!authAccountInfo[account];
}

/**
 * 设置账号授权状态
 * @param account
 */
export function setAccountAuthed(account) {
    let authAccountInfo = getAuthAccountInfo();
    if (!authAccountInfo) {
        authAccountInfo = {};
    }
    authAccountInfo[account] = true;
    AppPreferences.set(AUTH_STORE_KEY, authAccountInfo);
}
