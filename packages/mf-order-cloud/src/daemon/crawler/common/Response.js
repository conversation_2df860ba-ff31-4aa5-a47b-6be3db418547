export default class Response {
    constructor() {
        this.status = true;
        this.message = '';
        this.data = null;
    }
    base(status, message, data) {
        this.status = status;
        this.message = message;
        this.data = data;
        return this;
    }
    success(data) {
        return this.base(true, 'success', data);
    }
    error(message, data) {
        return this.base(false, message, data);
    }
    static success(data) {
        return new Response().success(data);
    }
    static error(message, data) {
        return new Response().error(message, data);
    }
}
