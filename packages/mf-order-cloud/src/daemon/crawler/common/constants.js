export const AUTH_STORE_KEY = '_order_cloud_auth_';
export const AuthStatus = Object.freeze({
    INIT: 0,
    AUTHED: 1,
    EXPIRED: 2,
});

export const ResponseErrorCode = Object.freeze({
    AUTH_NOT_AUTHED: 401, // 未授权
    AUTH_EXPIRED: 402, // 授权过期

    REQUEST_AUTH_CANCEL: 1000, // 用户取消授权
    REQUEST_AUTH_GET_USER_INFO_FAILED: 1001, // 获取用户信息失败
    REQUEST_AUTH_ACCOUNT_NOT_MATCH: 1002, // 授权账号不匹配
    REQUEST_AUTH_ACCOUNT_NO_PERMISSION: 1003, // 授权账号无权限

    REQUEST_AUTH_OPEN_WINDOW_FAILED: 1004, // 打开授权窗口失败
});


// 定义美团组合标签枚举
export const MeituanCombinationLabel = Object.freeze({
    PACKAGE: 1,
    SINGLE: 2,
});

export { PddPage } from '../provider/pdd/constants.js';
