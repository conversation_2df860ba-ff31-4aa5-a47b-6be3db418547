import Logger from '@/utils/logger';
const logger = Logger.create('EventEmitter');
export default class EventEmitter {
    constructor() {
        this._events = {};
    }

    on(eventName, listener) {
        if (!this._events[eventName]) {
            this._events[eventName] = [];
        }
        this._events[eventName].push(listener);
        return this;
    }

    emit(eventName, ...args) {
        const listeners = this._events[eventName] || [];
        listeners.forEach((listener) => {
            try {
                listener(...args);
            } catch (error) {
                logger.error(`Error in event listener for ${eventName}:`, error);
            }
        });
        return this;
    }

    off(eventName, listener) {
        if (!this._events[eventName]) return this;

        this._events[eventName] = this._events[eventName].filter(
            (existingListener) => existingListener !== listener,
        );
        return this;
    }
}
