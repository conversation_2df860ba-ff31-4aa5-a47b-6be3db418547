import { AuthStatus } from '../common/constants';
import Logger from '@/utils/logger';
import EventEmitter from '@/daemon/crawler/base/event-emitter';

const logger = Logger.create('BaseService');

export default class BaseService extends EventEmitter {
    account;
    authStatus = AuthStatus.INIT;
    /**
     * @type {CrawlerScheduler}
     */
    scheduler = null;

    platform = '';

    constructor(account, scheduler) {
        super();
        logger.debug('BaseService constructor');
        this.account = account;
        this.scheduler = scheduler;
    }

    init() {
        logger.debug(`BaseService ${this.platform}-${this.account} init`);
    }

    start() {
        logger.debug(`BaseService ${this.platform}-${this.account} start`);
    }

    destroy() {
        logger.debug(`BaseService ${this.platform}-${this.account} destroy`);
    }

    getAccount() {
        return this.account;
    }

    getAuthStatus() {
        return this.authStatus;
    }
}
