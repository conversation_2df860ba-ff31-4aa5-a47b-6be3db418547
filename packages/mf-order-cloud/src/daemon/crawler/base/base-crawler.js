import Logger from '@/utils/logger';

const logger = Logger.create('BaseCrawler');

export default class BaseCrawler {
    responseHandlers = new Map();
    initializePage(page) {
        if (page) {
            this.setupGlobalResponseHandler(page);
        }
    }

    setupGlobalResponseHandler(page) {
        page.on('response', async (response) => {
            const request = response.request();
            const url = response.url();

            // 跳过预检请求、非 GET/POST 等
            if (request.method() === 'OPTIONS' || request.method() === 'HEAD') return;

            if (response.status() < 200 || response.status() >= 300) return;

            // 执行所有注册的响应处理器
            for (const [handlerId, handler] of this.responseHandlers) {
                try {
                    await handler(url, response, request);
                } catch (error) {
                    console.log(handlerId);
                    logger.error('Response handler failed:', error);
                }
            }
        });
    }

    /**
     * 注册响应处理器
     * @param {string} handlerId - 处理器ID
     * @param {Function} handler - 处理器函数 (response, request, url) => void
     */
    registerResponseHandler(handlerId, handler) {
        if (this.responseHandlers.get(handlerId)) {
            console.warn('重复handlerId，handler将被覆盖');
        }
        this.responseHandlers.set(handlerId, handler);
    }

    /**
     * 移除响应处理器
     * @param {string} handlerId - 处理器ID
     */
    removeResponseHandler(handlerId) {
        this.responseHandlers.delete(handlerId);
    }

    /**
     *
     * @param {CrawlerTask} task
     * @return {Promise<void>}
     */
    async executeTask(task) {
        // Simulate task execution
        logger.info(`Crawler executing task: ${task.type} for platform ${task.platform}`);
        await new Promise((resolve) => setTimeout(resolve, Math.random() * 3000));
    }

    /**
     *
     * @return {CrawlerTask}
     */
    createTask() {
    }

    /**
     * 等待响应
     * @param {import('puppeteer').Page} page
     * @param {string | Function} urlOrPredicateFn
     * @param {{once?: boolean}} options
     * @returns {Promise<import('puppeteer').HTTPResponse>}
     */
    async waitForResponse(page, urlOrPredicateFn, { once = true } = {}) {
        if (typeof urlOrPredicateFn !== 'function' && typeof urlOrPredicateFn !== 'string') {
            throw new Error('urlOrPredicateFn must be a function or a string');
        }

        return new Promise((resolve) => {
            const handler = function (response) {
                const request = response.request();
                if (
                    ['OPTION', 'OPTIONS'].indexOf(request.method().toUpperCase()) > -1
                ) {
                    return;
                }

                if (typeof urlOrPredicateFn === 'function' && urlOrPredicateFn(response)) {
                    if (once) {
                        page.off('response', handler);
                    }
                    resolve(response);
                } else if (typeof urlOrPredicateFn === 'string' && response.url().includes(urlOrPredicateFn)) {
                    if (once) {
                        page.off('response', handler);
                    }
                    resolve(response);
                }
            };
            page.on('response', handler);
        });

    }
}
