export default class CrawlerTask {
    /**
     * @param {'pdd' | 'meituan'} platform
     * @param {number} userId
     * @param {string} type 任务类型
     * @param {number} priority
     * @param {TaskStrategy} strategy 任务策略 {type: 'once' | 'interval' | 'schedule', interval?: number, cron?: string, immediate?: boolean}
     * @param {object} extraParams 扩展参数
     */
    constructor(
        platform,
        userId,
        type,
        strategy = { type: 'once' },
        priority = -1,
        extraParams = {},
    ) {
        this.platform = platform;
        this.userId = userId;
        this.type = type;
        this.strategy = strategy;
        this.priority = priority;
        this.extraParams = extraParams;
    }

    getTaskKey() {
        return `${this.platform}-${this.userId}-${this.type}`;
    }
}
