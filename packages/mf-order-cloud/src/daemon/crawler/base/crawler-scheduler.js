/* eslint-disable max-classes-per-file */
// import EventEmitter from 'events';
// import schedule from 'node-schedule';
import EventEmitter from '@/daemon/crawler/base/event-emitter';

const schedule = {}; //;window.remote.require('node-schedule') || {};
// const EventEmitter = window.remote.require('events');

import Logger from '@/utils/logger';
import Response from '@/daemon/crawler/common/Response';

const logger = Logger.create('CrawlerScheduler');

// Task status enum
export const TaskStatus = {
    PENDING: 'pending',
    RUNNING: 'running',
    COMPLETED: 'completed',
    FAILED: 'failed',
    PAUSED: 'paused',
    CANCELLED: 'cancelled',
};

/**
 * Enhanced Crawler Scheduler with comprehensive task management
 */
export default class CrawlerScheduler extends EventEmitter {
    constructor(options = {}) {
        super();
        // Configuration
        this.maxConcurrentTasks = options.maxConcurrentTasks || 5;
        this.maxRetries = options.maxRetries || 3;

        // Task management
        this.tasks = new Map(); // All tasks
        this.runningTasks = new Set(); // Currently running tasks
        this.crawlerMap = new Map(); // Crawler instances

        // Concurrency tracking
        this.currentTaskCount = 0;
    }

    /**
     * Register a crawler for a specific platform and user
     * @param {string} platform
     * @param {string} userId
     * @param {Object} crawler
     */
    addCrawler(platform, userId, crawler) {
        const key = `${platform}-${userId}`;
        this.crawlerMap.set(key, crawler);
    }

    /**
     * Remove a crawler
     * @param {string} platform
     * @param {string} userId
     */
    removeCrawler(platform, userId) {
        const key = `${platform}-${userId}`;
        this.crawlerMap.delete(key);
    }

    /**
     * Get a crawler for a specific platform and user
     * @param {string} platform
     * @param {string} userId
     * @returns {Object|undefined}
     */
    getCrawler(platform, userId) {
        const key = `${platform}-${userId}`;
        return this.crawlerMap.get(key);
    }

    /**
     * 同步执行任务，仅支持 once 任务
     * @param {*} task
     * @returns {Promise<Response>} Task response
     */
    async execTaskSync(task) {
        const taskId = this._generateTaskId(task);

        try {
            task.status = TaskStatus.RUNNING;
            this._emitTaskEvent(taskId, 'start', { task });

            const crawler = this.getCrawler(task.platform, task.userId);
            const response = await crawler.executeTask(task);

            this._emitTaskEvent(taskId, 'success', {
                task,
                response,
            });

            if (response.status === false) {
                return response;
            }

            return Response.success(response.data);
        } catch (error) {
            task.status = TaskStatus.FAILED;
            this._emitTaskEvent(taskId, 'error', {
                task,
                error,
            });
            return Response.error(error);
        }
    }

    /**
     * Schedule a task with advanced configuration
     * @param {Object} task
     * @returns {string} Task ID
     */
    scheduleTask(task) {
        const taskId = this._generateTaskId(task);
        // Validate task
        if (!this._validateTask(task)) {
            this._emitTaskEvent(taskId, 'error', {
                task,
                error: new Error('Invalid task configuration'),
            });
            return taskId;
        }

        // Store task
        this.tasks.set(taskId, {
            ...task,
            id: taskId,
            status: TaskStatus.PENDING,
            createdAt: Date.now(),
            attempts: 0,
        });

        // Schedule based on task type
        switch (task.strategy.type) {
            case 'interval':
                this._scheduleIntervalTask(taskId);
                break;
            case 'schedule':
                this._scheduleScheduledTask(taskId);
                break;
            case 'once':
                this._scheduleOneTimeTask(taskId);
                break;
            default:
                this._emitTaskEvent(taskId, 'error', {
                    task,
                    error: new Error('Unsupported task type'),
                });
        }

        return taskId;
    }

    /**
     * Cancel a specific task
     * @param {string} taskId
     */
    cancelTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        // Clear scheduling timeout if exists
        if (task.scheduleTimeout) {
            logger.info('clearTimeout', task.scheduleTimeout);
            clearTimeout(task.scheduleTimeout);
        }

        // Cancel scheduled job if exists
        if (task.job) {
            task.job.cancel();
        }

        // Update task status
        task.status = TaskStatus.CANCELLED;

        this._emitTaskEvent(taskId, 'cancelled', {
            task,
        });
        this.tasks.delete(taskId);
    }

    /**
     * Pause a task
     * @param {string} taskId
     */
    pauseTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        task.status = TaskStatus.PAUSED;
        this._emitTaskEvent(taskId, 'paused', {
            task,
        });
    }

    /**
     * Resume a paused task
     * @param {string} taskId
     */
    resumeTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task || task.status !== TaskStatus.PAUSED) return;

        task.status = TaskStatus.PENDING;
        this._scheduleIntervalTask(taskId);
        this._emitTaskEvent(taskId, 'resumed', {
            task,
        });
    }

    /**
     * Cancel all running tasks in the scheduler
     */
    cancelAllTasks() {
        // Get all task IDs
        const taskIds = Array.from(this.tasks.keys());
        
        // Cancel each task
        taskIds.forEach((taskId) => {
            this.cancelTask(taskId);
        });
        
        logger.info('All tasks have been cancelled');
    }

    /**
     * Internal method to schedule interval tasks
     * @param {string} taskId
     * @private
     */
    _scheduleIntervalTask(taskId) {
        let task = this.tasks.get(taskId);
        if (!task) return;

        // Ensure interval is specified
        if (typeof task.strategy.interval !== 'number') {
            logger.error(`Interval not specified for task: ${taskId}`);
            return;
        }

        // Flag to prevent multiple concurrent scheduling
        let isScheduling = false;

        const scheduleNextExecution = () => {
            task = this.tasks.get(taskId);
            if (!task) {
                return;
            }
            // Prevent multiple scheduling attempts
            if (isScheduling) return;

            // Stop scheduling if task is no longer active
            if (task.status === TaskStatus.CANCELLED ||
                task.status === TaskStatus.PAUSED) {
                return;
            }

            if (task.strategy.immediate) {
                const crawler = this.getCrawler(task.platform, task.userId);
                crawler.executeTask(task).then((response) => {
                    this._emitTaskEvent(taskId, 'success', {
                        task, response,
                    });
                });
            }

            // Use setTimeout to schedule next execution
            const scheduleTimeout = setTimeout(async () => {
                // Prevent concurrent executions
                if (isScheduling) {
                    clearTimeout(scheduleTimeout);
                    return;
                }

                isScheduling = true;

                // Skip if concurrency limit reached
                if (this.currentTaskCount >= this.maxConcurrentTasks) {
                    isScheduling = false;
                    scheduleNextExecution();
                    return;
                }

                try {
                    task.status = TaskStatus.RUNNING;
                    task.attempts++;
                    this.currentTaskCount++;
                    this._emitTaskEvent(taskId, 'start', task);

                    const crawler = this.getCrawler(task.platform, task.userId);
                    if (!crawler) {
                        throw new Error(`Crawler not found for platform ${task.platform}`);
                    }

                    const response = await crawler.executeTask(task);

                    task.status = TaskStatus.COMPLETED;
                    this._emitTaskEvent(taskId, 'success', {
                        task, response,
                    });

                    if (task.status === TaskStatus.CANCELLED) {
                        logger.info('Task cancelled, ignore nextSchedule', taskId);
                        return;
                    }

                    // Reset status and schedule next execution
                    task.status = TaskStatus.PENDING;
                    isScheduling = false;
                    scheduleNextExecution();
                } catch (error) {
                    if (task.status === TaskStatus.CANCELLED) {
                        return;
                    }
                    task.status = TaskStatus.FAILED;
                    this._emitTaskEvent(taskId, 'error', {
                        task, error,
                    });

                    // Retry logic
                    if (task.attempts < this.maxRetries) {
                        // Wait before next retry
                        // eslint-disable-next-line abc/no-timer-id
                        setTimeout(() => {
                            task.status = TaskStatus.PENDING;
                            scheduleNextExecution();
                        }, task.strategy.retryInterval || 2000);
                    } else {
                        // Stop scheduling if max retries reached
                        this._emitTaskEvent(taskId, 'maxRetriesReached', {
                            task, error,
                        });
                    }
                } finally {
                    this.currentTaskCount--;
                    isScheduling = false;
                }
            }, task.strategy.interval);

            // Store timeout for potential cancellation
            task.scheduleTimeout = scheduleTimeout;
        };

        // Start initial scheduling
        scheduleNextExecution();

        // Add cancel method to task
        task.cancel = () => {
            if (task.scheduleTimeout) {
                clearTimeout(task.scheduleTimeout);
            }
            this.cancelTask(taskId);
        };
    }

    /**
     * Internal method to schedule cron-based tasks
     * @param {string} taskId
     * @private
     */
    _scheduleScheduledTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        task.job = schedule.scheduleJob(task.strategy.cron, async () => {
            if (task.status === TaskStatus.PAUSED) return;

            try {
                task.status = TaskStatus.RUNNING;
                this._emitTaskEvent(taskId, 'start', {
                    task,
                });

                const crawler = this.getCrawler(task.platform, task.userId);
                const response = await crawler.executeTask(task);

                task.status = TaskStatus.COMPLETED;
                this._emitTaskEvent(taskId, 'success', {
                    task, response,
                });
            } catch (error) {
                task.status = TaskStatus.FAILED;
                this._emitTaskEvent(taskId, 'error', {
                    task, error,
                });
            }
        });
    }

    /**
     * Internal method to schedule one-time tasks
     * @param {string} taskId
     * @private
     */
    _scheduleOneTimeTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        const executeTask = async () => {
            try {
                task.status = TaskStatus.RUNNING;
                this._emitTaskEvent(taskId, 'start', {
                    task,
                });

                const crawler = this.getCrawler(task.platform, task.userId);
                const response = await crawler.executeTask(task);

                task.status = TaskStatus.COMPLETED;
                this._emitTaskEvent(taskId, 'success', {
                    task, response,
                });
                this.tasks.delete(taskId);
            } catch (error) {
                task.status = TaskStatus.FAILED;
                this._emitTaskEvent(taskId, 'error', {
                    task, error,
                });
            }
        };

        // Immediate or delayed execution
        if (task.strategy.delay) {
            // eslint-disable-next-line abc/no-timer-id
            setTimeout(executeTask, task.strategy.delay);
        } else {
            executeTask();
        }
    }

    /**
     * Validate task configuration
     * @param {Object} task
     * @returns {boolean}
     * @private
     */
    _validateTask(task) {
        // Basic task validation
        return task &&
               task.platform &&
               task.userId &&
               task.strategy &&
               ['interval', 'schedule', 'once'].includes(task.strategy.type);
    }

    /**
     * Generate a unique task ID
     * @param {Object} task
     * @returns {string}
     * @private
     */
    _generateTaskId(task) {
        return `${task.platform}-${task.userId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 注册事件监听
     * @param {*} platform
     * @param {*} accountId
     * @param {*} listener
     * @returns {Function} 取消事件监听
     */
    registerTaskListener(platform, accountId, listener) {
        const eventKey = this._generateEventKey(platform, accountId);
        this.on(eventKey, listener);
        return () => this.off(eventKey, listener);
    }

    _generateEventKey(platform, userId) {
        return `task:${platform}:${userId}`;
    }

    /**
     * Emit task-related events
     * @param {string} taskId
     * @param {string} eventType
     * @param {Object} data
     * @private
     */
    _emitTaskEvent(taskId, eventType, data) {
        const task = this.tasks.get(taskId);
        if (!task) return;

        const eventKey = this._generateEventKey(task.platform, task.userId);
        const eventData = {
            taskId,
            eventType,
            timestamp: Date.now(),
            data,
        };
        this.emit(eventKey, eventData);
    }
}
