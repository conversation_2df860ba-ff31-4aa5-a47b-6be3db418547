import Response from '../common/Response';

export default class BaseProtocol {
    static protocolId = 'base';

    protocol = 'https';
    hostname = '';
    port = '443';

    /**
     *
     * @param options
     */
    constructor(options = {}) {
        const {
            protocol,
            hostname,
            port,
        } = options;

        this.protocol = protocol;
        this.hostname = hostname;
        this.port = port;
    }

    /**
     * 前置检查
     */
    async checkBefore() {
        return Response.success();
    }
}
