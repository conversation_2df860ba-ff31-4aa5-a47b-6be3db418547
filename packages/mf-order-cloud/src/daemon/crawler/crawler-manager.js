import PromiseTask from '../common/promise-task';
import Response from './common/Response';
import OrderCloudStore from './store.js';
import { ECTypeEnum } from '../../utils/constants.js';
import AbcSocket from 'MfBase/single-socket';
import CrawlerScheduler from './base/crawler-scheduler';
import Logger from '@/utils/logger';
import EventHandlerFactory from './events/event-handler-factory';

const logger = Logger.create('CrawlerManager');

/**
 * @class CrawlerManager
 * @classdesc 用于管理电商爬虫，维护多平台，多账号的爬虫调度，并维护爬取的数据，为桌面助手和订单云提供数据
 */
export default class CrawlerManager extends PromiseTask {
    scheduler;
    // 是否正在请求授权，正在授权时，暂停轮询
    isRequestAuth = false;

    listeners = [];
    serviceCtorMap = new Map();
    serviceMap = new Map(); // 多账号维护service
    eventHandlers = new Map();

    ecGoodsWatcherList = [];
    isSyncingMeituanStock = false;

    constructor(ecGoodsWatcherManager) {
        super();
        this.ecGoodsWatcherManager = ecGoodsWatcherManager;
        this.handleBindMallChangeSocketMessage = this.handleBindMallChangeSocketMessage.bind(this);
        this._handleEvent = this._handleEvent.bind(this);
    }

    async _run() {
        // 初始化 store
        this.store = new OrderCloudStore();
        await this.store.init();

        if (!this.store.onAuthMallList.length) {
            logger.info('没有绑定的订单云门店');
            return;
        }

        // 初始化 scheduler
        this.scheduler = new CrawlerScheduler();

        // 初始化 service
        const {
            PddService,
            MeituanService,
        } = await import('./index');
        this.serviceCtorMap.set(ECTypeEnum.PDD, PddService);
        this.serviceCtorMap.set(ECTypeEnum.MT, MeituanService);
        await this._initServiceFromMallList(this.store.onAuthMallList);
        this.resolve(this);
    }

    async start() {
        try {
            await super.start();
            await this.init();
            this._syncMeituanStock = this.syncMeituanStock.bind(this);
            this.ecGoodsWatcherManager.addListener(this._syncMeituanStock);
            logger.info('CrawlerManager started');
        } catch (error) {
            logger.error(error);
        }
    }

    async stop() {
        this.serviceCtorMap.clear();
        this._socket && this._socket.off('ec.bind_mall', this.handleBindMallChangeSocketMessage);
        this._syncMeituanStock = null;
        await this.destroyServices();
    }

    /**
     * @desc 初始化
     * 1. 刷新服务调用获取绑定门店接口
     * 2. 监听绑定门店更新
     * 3. 检查各个爬虫服务的授权状态是否过期
     * <AUTHOR> Yang
     * @date 2024-12-10 17:17:51
     */
    async init() {
        logger.info('CrawlerManager init');
        const { socket } = AbcSocket.getSocket();
        this._socket = socket;
        this._socket.on('ec.bind_mall', this.handleBindMallChangeSocketMessage);
    }

    /**
     * @desc 根据 mallList 初始化 service，如果 service 已存在，则直接使用，否则创建新的 service，并初始化
     * @param {*} mallList
     * @returns
     */
    async _initServiceFromMallList(mallList) {
        const extMallIdSet = new Set(mallList.map((it) => it.extMallId));
        // 从 serviceMap 中销毁不存在的 service
        for (const [extMallId, service] of this.serviceMap) {
            if (!extMallIdSet.has(extMallId)) {
                // 不存在，清除掉
                logger.info(`initServiceFromMallList remove service for [${service.platform}], extMallId: ${extMallId}`);
                service.off('event', this._handleEvent);
                await service.destroy();
                this.serviceMap.delete(extMallId);
                this.eventHandlers.delete(extMallId);
            }
        }

        if (!mallList.length) {
            logger.info('initServiceFromMallList mallList is empty');
            return;
        }

        // 根据 mallList 初始化 service，如果 service 已存在，则直接使用，否则创建新的 service，并初始化
        for (let i = 0; i < mallList.length; i++) {
            const item = mallList[i];
            let service = this.serviceMap.get(item.extMallId);
            if (service) {
                continue;
            }
            const ServiceCtor = this.serviceCtorMap.get(item.ecType);
            if (!ServiceCtor) {
                logger.error('initServiceFromMallList ServiceCtor not found, ecType: ', item.ecType);
                continue;
            }

            service = new ServiceCtor(item.extMallId, this.scheduler, item);

            this.serviceMap.set(item.extMallId, service);
            // 创建对应的事件处理器
            const eventHandler = EventHandlerFactory.createHandler(item.ecType, this.store);
            this.eventHandlers.set(item.extMallId, eventHandler);

            logger.info(`initServiceFromMallList add service for [${service.platform}], extMallId: ${item.extMallId}`);
            service.on('event', this._handleEvent);
            await service.init();
            await service.start();
        }
    }

    async _handleEvent(event) {
        const { service } = event;
        const { extMallId } = service.getAccount();
        const eventHandler = this.eventHandlers.get(extMallId);
        if (eventHandler) {
            await eventHandler.handleEvent(event);
        }
    }

    async refreshService() {
        await this.store.fetchBindAuthList();
        await this._initServiceFromMallList(this.store.onAuthMallList);
    }

    async destroyServices() {
        for (const [extMallId, service] of this.serviceMap) {
            logger.info(`destroyServices destroy service for [${service.platform}], extMallId: ${extMallId}`);
            service.off('event', this._handleEvent);
            await service.destroy();
        }
        this.serviceMap.clear();
    }

    async handleBindMallChangeSocketMessage() {
        logger.info('CrawlerManager handleBindMallChangeSocketMessage');
        await this.bindMallChange();
    }

    async bindMallChange() {
        logger.info('CrawlerManager bindMallChange');
        await this.refreshService();
    }

    async getService(extMallId) {
        return this.serviceMap.get(extMallId);
    }

    async syncMeituanStock({ data }) {
        this.ecGoodsWatcherList.push(data);
        await this.triggerSyncMeituanStock();
    }

    async triggerSyncMeituanStock() {
        if (this.isSyncingMeituanStock) {
            return;
        }

        try {
            this.isSyncingMeituanStock = true;
            while (this.ecGoodsWatcherList.length > 0) {
                const data = this.ecGoodsWatcherList.shift();
                const { extMallId } = data;
                const skuId = data.goodsList[0].skuList[0].extGoodsSkuId;
                const stock = data.goodsList[0].skuList[0].quantity;
                await this.serviceMap.get(extMallId).syncProductStockBySkuId(skuId, stock);
            }
        } finally {
            this.isSyncingMeituanStock = false;
        }
    }

    async getAuthMallList() {
        await this.promise;
        return this.store.authMallList;
    }

    async requestAuth(extMallId) {
        this.isRequestAuth = true;
        await this.promise;
        if (!extMallId) {
            return Response.error('请求授权需指定 extMallId');
        }
        const service = this.serviceMap.get(extMallId);
        const response = await service.requestAuth();
        this.isRequestAuth = false;
        return response;
    }

    /**
     *
     * @param {string} extMallId - 电商id
     * @param options {EcPageValue}
     */
    async openPage(extMallId, options) {
        await this.promise;

        if (!extMallId) {
            console.error('需要指定ecMallId');
            return;
        }
        const service = this.serviceMap.get(extMallId);
        service.openPage(options);
    }

    addListener(listener, extMallId) {
        const eventHandler = this.eventHandlers.get(extMallId);
        if (eventHandler) {
            eventHandler.addListener(listener);
        } else {
            this.listeners.push(listener);
        }
    }

    removeListener(listener, extMallId) {
        const eventHandler = this.eventHandlers.get(extMallId);
        if (eventHandler) {
            eventHandler.removeListener(listener);
        } else {
            this.listeners = this.listeners.filter((l) => l !== listener);
        }
    }

    cancelScheduler() {
        this.scheduler.cancelAllTasks();
    }

    getTargetService(extMallId) {
        return this.serviceMap.get(extMallId);
    }
}
