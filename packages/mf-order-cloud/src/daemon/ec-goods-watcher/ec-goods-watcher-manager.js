import * as business from 'MfFeEngine/business';
import AbcSocket from 'MfBase/single-socket';

/**
 * 用于管理电商商品监听，并维护商品数据
 */
export default class EcGoodsWatcherManager {
    listeners = [];
    data = {};
    _socketECGoodsService = null;


    async start() {
        this.triggerListeners();
        this.watchECGoodsServiceSocket();
    }

    stop() {
        this._socketECGoodsService?.destroy();
        this._socketECGoodsService = null;
        this.timer = null;
    }

    addListener(listener) {
        if (this.listeners.includes(listener)) {
            return;
        }
        this.listeners.push(listener);
    }

    removeListener(listener) {
        const index = this.listeners.findIndex(listener);
        if (index !== -1) {
            this.listeners.splice(index, 1);
        }
    }

    triggerListeners() {
        for (let i = 0; i < this.listeners.length; i++) {
            const listener = this.listeners[i];
            if (typeof listener === 'function') {
                listener(this);
            }
        }
    }


    watchECGoodsServiceSocket() {
        const { socket } = AbcSocket.getSocket();
        if (business.ECGoodsService) {
            this._socketECGoodsService = new business.ECGoodsService(socket);
            this._socketECGoodsService.onECGoodsStockUpdate((data) => {
                console.log('onECGoodsStockUpdate', data);
                this.data = data;
                this.triggerListeners();
            });
        }
    }
}
