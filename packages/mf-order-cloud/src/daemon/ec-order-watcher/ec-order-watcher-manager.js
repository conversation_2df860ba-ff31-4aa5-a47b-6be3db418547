import ECDesktopAssistantStatAPI from '../../api/desktop-assistant';
import * as business from 'MfFeEngine/business';
import AbcSocket from 'MfBase/single-socket';
import AbcPlayer from 'MfBase/abc-player';
import { ECTypeEnum } from '../../utils/constants';

/**
 * @class EcOrderWatcherManager
 * @classdesc 用于管理电商订单监听，并维护订单数据，为桌面助手和订单云提供数据
 */
export default class EcOrderWatcherManager {
    listeners = [];
    desktopAssistantDto = {};
    orderNewInfo = {};
    afterSaleInfo = {};

    _socketDesktopAssistantService = null;

    // 是否播报设置
    voiceSetting = {
        enableOrderNewOrderPlay: true, // 新订单播报开启状态
        enableOrderAfterSalePlay: true, // 新售后单播报开启状态
    };
    isPlaying = false;
    timer = null;

    _orderNewVoicePlay = new AbcPlayer(require('@/assets/audio/order-new.mp3'));
    _orderAfterSaleVoicePlay = new AbcPlayer(require('@/assets/audio/after-sale.mp3'));

    async start() {
        await this.getDesktopAssistantStat();
        this.triggerListeners();
        this.watchECDesktopAssistantSocket();
    }

    stop() {
        this._socketDesktopAssistantService?.destroy();
        this._socketDesktopAssistantService = null;
        this.timer = null;
    }

    addListener(listener) {
        if (this.listeners.includes(listener)) {
            return;
        }
        this.listeners.push(listener);
    }

    removeListener(listener) {
        const index = this.listeners.findIndex(listener);
        if (index !== -1) {
            this.listeners.splice(index, 1);
        }
    }

    triggerListeners() {
        for (let i = 0; i < this.listeners.length; i++) {
            const listener = this.listeners[i];
            if (typeof listener === 'function') {
                listener(this);
            }
        }
    }

    /**
     * 监听socket-desktop-assistant新订单和新售后
     */
    async watchECDesktopAssistantSocket() {
        const { socket } = AbcSocket.getSocket();
        if (business.DesktopAssistantService) {
            this._socketDesktopAssistantService = new business.DesktopAssistantService(socket);
            await this._socketDesktopAssistantService.socketInstance?.ready();
            this._socketDesktopAssistantService.onECDesktopAssistantOrderNew(async (data) => {
                console.log('onECDesktopAssistantOrderNew', data);
                await this.getDesktopAssistantStat();
                this.orderNewInfo = data;
                this.triggerListeners();
                if (data.ecType === ECTypeEnum.PDD) {
                    this.orderNewVoicePlay();
                }
            });
            this._socketDesktopAssistantService.onECDesktopAssistantAfterSale(async (data) => {
                console.log('onECDesktopAssistantAfterSale', data);
                await this.getDesktopAssistantStat();
                this.afterSaleInfo = data;
                this.triggerListeners();
                if (data.ecType === ECTypeEnum.PDD) {
                    this.orderAfterSaleVoicePlay();
                }
            });
            this._socketDesktopAssistantService.onECDesktopAssistantTodoUpdate(async (data) => {
                console.log('onECDesktopAssistantTodoUpdate', data);
                await this.getDesktopAssistantStat();
                this.triggerListeners();
            });
        }
    }

    /**
     * 获取待打单，待发货数据
     * @returns {Promise<void>}
     */
    async getDesktopAssistantStat() {
        const res = await ECDesktopAssistantStatAPI.getEcDesktopAssistantStat();
        for (const key in res) {
            if (res.hasOwnProperty(key)) {
                this.desktopAssistantDto[key] = res[key];
            }
        }
    }

    /**
     * 获取新订单数据
     */
    getOrderNewInfo() {
        return this.orderNewInfo;
    }

    /**
     * 获取新售后订单
     */
    getAfterSaleInfo() {
        return this.afterSaleInfo;
    }

    /**
     * 新订单语音播报
     */
    orderNewVoicePlay() {
        if (!this.isPlaying && this._orderNewVoicePlay && this.voiceSetting.enableOrderNewOrderPlay) {
            this._orderNewVoicePlay.play();
            this.isPlaying = true;
            this.timer = setTimeout(() => {
                this.isPlaying = false;
            }, 7000);
        }
    }

    /**
     * 新售后单语音播报
     */
    orderAfterSaleVoicePlay() {
        if (!this.isPlaying && this._orderAfterSaleVoicePlay && this.voiceSetting.enableOrderAfterSalePlay) {
            this._orderAfterSaleVoicePlay.play();
        }
        this.isPlaying = true;
        this.timer = setTimeout(() => {
            this.isPlaying = false;
        }, 7000);
    }

    /**
     * 设置最新的设置状态
     * @param voiceNewOrder
     * @param voiceAfterSale
     */
    setVoiceSetting(voiceNewOrder = false,voiceAfterSale = false) {
        this.voiceSetting = {
            enableOrderNewOrderPlay: voiceNewOrder,
            enableOrderAfterSalePlay: voiceAfterSale,
        };
    }
}
