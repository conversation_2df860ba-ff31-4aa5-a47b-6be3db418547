export function generatePromise() {
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject) => {
        resolve = _resolve;
        reject = _reject;
    });
    return {
        resolve,
        reject,
        promise,
    };
}
class PromiseTask {
    constructor() {
        if (new.target === PromiseTask) {
            throw new Error('Cannot instantiate PromiseTask directly');
        }
    }

    start() {
        if (this.promise) {
            return this.promise;
        }
        const {
            resolve, reject, promise,
        } = generatePromise();
        this.promise = promise;
        this.reject = reject;
        this.resolve = resolve;

        this._run();

        return this.promise;
    }

    _run() {
        throw new Error('_run method must be implemented by subclass');
    }
}

export default PromiseTask;
