import Vue from 'vue';

const KEY_DESKTOP_ASSISTANT_PREFERENCE = '_desktop_assistant_preference_';

const DefaultPreference = {
    enable: 0,
};

export function usePreference() {
    let _pref = null;
    try {
        _pref = JSON.parse(localStorage.getItem(KEY_DESKTOP_ASSISTANT_PREFERENCE));
    } catch (e) {
        console.log('usePreference error', e);
    }

    if (!_pref) {
        _pref = DefaultPreference;
    }

    const store = Vue.observable(_pref);

    function setEnable(enable) {
        store.enable = +enable;
        localStorage.setItem(KEY_DESKTOP_ASSISTANT_PREFERENCE, JSON.stringify(store));
    }

    return {
        setEnable,
        isEnable() {
            return store.enable;
        },
    };
}
