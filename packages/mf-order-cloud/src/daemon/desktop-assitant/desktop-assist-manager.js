import { usePreference } from './store';
import { openWindow } from '../crawler/common/utils';
import {
    ASSIST_MESSAGE,
    MAIN_MESSAGE,
    assistMessageOn,
    mainWindowSend, removeAssistMessageOn,
} from '../../utils/message';
import {
    isLocal, FEATURE_ENV, OWN_ENV,
} from '../../utils/build-env';

/**
 * @class DesktopAssistManager
 * @classdesc 用于根据条件决定是否启动桌面助手，管理桌面助手的窗口，以及和桌面助手窗口的双向通信
 */
export default class DesktopAssistManager {
    // 需要和页面的 title 一致，以此作为唯一标识，防止窗口重复打开
    static WINDOW_TITLE = '订单云桌面助手';
    /**
     * @type {EcOrderWatcherManager}
     */
    ecOrderWatcherManager;
    /**
     * @type {CrawlerManager}
     */
    crawlerManager;
    preference;
    assistantWindow;

    desktopAssistantDto = {};
    desktopAssistantConfig = {};
    orderNewInfo;
    afterSaleInfo;

    constructor(ecOrderWatcherManager, crawlerManager) {
        this.ecOrderWatcherManager = ecOrderWatcherManager;
        this.crawlerManager = crawlerManager;
        this.preference = usePreference();
    }

    start() {
        if (this.preference.isEnable()) {
            this._openDesktopAssistant();
        }
        this.handleCrawler();
        // 监听待打单和待发货数据
        this._handelAssistantStat = this.handelAssistantStat.bind(this);
        this.ecOrderWatcherManager.addListener(this._handelAssistantStat);
        // 监听新订单
        this._handleOrderNew = this.handleOrderNew.bind(this);
        this.ecOrderWatcherManager.addListener(this._handleOrderNew);
        // 监听新售后
        this._handleAfterSale = this.handleAfterSale.bind(this);
        this.ecOrderWatcherManager.addListener(this._handleAfterSale);
        // 监听爬虫数据
        this._handleCrawler = this.handleCrawler.bind(this);
        this.crawlerManager.addListener(this._handleCrawler);
        // 监听桌面助手状态
        this.watchDesktopAssistant();
    }

    stop() {
        this.unWatchDesktopAssistant();
        this.ecOrderWatcherManager.removeListener(this._handelAssistantStat);
        this.ecOrderWatcherManager.removeListener(this._handleOrderNew);
        this.ecOrderWatcherManager.removeListener(this._handleAfterSale);
        this.crawlerManager.removeListener(this._handleCrawler);
        this._handelAssistantStat = null;
        this._handleOrderNew = null;
        this._handleAfterSale = null;
        this._handleCrawler = null;
    }

    /**
     * 获取设置桌面助手状态
     */
    getPreferenceEnable() {
        return this.preference.isEnable();
    }

    /**
     * 启用桌面助手
     */
    enableDesktopAssistant() {
        this.preference.setEnable(1);
        this._openDesktopAssistant();
    }

    /**
     * 禁用桌面助手
     */
    disableDesktopAssistant() {
        this.preference.setEnable(0);
        this._closeDesktopAssistant();
    }

    isDesktopAssistantEnable() {
        return this.preference.isEnable();
    }

    /**
     * 打开桌面助手
     */
    _openDesktopAssistant() {
        const win = window.remote.BrowserWindow.getAllWindows()
            .find((item) => item.getTitle() === DesktopAssistManager.WINDOW_TITLE);
        if (win) {
            this.assistantWindow = win;
        }

        if (this.assistantWindow) {
            return;
        }


        const assistantSessionKey = 'desktop:assistant';
        const webPreferences = {
            nodeIntegration: true,
            enableRemoteModule: true,
            contextIsolation: false,
            preload: window.electron.remote.app.jsApiFile,
        };
        if (window.remote.app?.registerAbcyunSchemeForSession) {
            // 注册协议，解决session隔离参数导致无法打开桌面助手的问题
            window.remote.app.registerAbcyunSchemeForSession(assistantSessionKey);
            webPreferences.session = window.remote.session.fromPartition(assistantSessionKey);
        }

        const { width } = window.remote.screen.getPrimaryDisplay().workAreaSize || 1920;
        const winWidth = 400;
        this.assistantWindow = openWindow(this._getUrl(), {
            width: winWidth,
            height: 60,
            y: -11,
            x: (width - winWidth) / 2,
            frame: false,
            alwaysOnTop: true,
            resizable: false,
            transparent: true,
            webPreferences,
        });
        this.assistantWindow.setTitle(DesktopAssistManager.WINDOW_TITLE);
        this.assistantWindow.setSkipTaskbar(true);
    }

    /**
     * 关闭桌面助手
     */
    _closeDesktopAssistant() {
        if (this.assistantWindow) {
            this.assistantWindow.close();
            this.assistantWindow = null;
        }
    }

    /**
     *
     * @return {string}
     * @private
     */
    _getUrl() {
        if (isLocal) {
            return `${location.protocol}//${location.hostname}:9001/`;
        }
        if (FEATURE_ENV) {
            return `${location.protocol}//test.abczs.cn/order-cloud-desktop-assistant`;
        }
        if (OWN_ENV) {
            return `${location.protocol}//dev.abczs.cn/order-cloud-desktop-assistant`;
        }
        return `${location.protocol}//${location.hostname}/order-cloud-desktop-assistant`;
    }

    /**
     * 获取stat数据
     */
    handelAssistantStat({ desktopAssistantDto }) {
        if (!desktopAssistantDto) {
            return;
        }
        Object.assign(this.desktopAssistantDto, desktopAssistantDto);
        this.sendAssistantStat();
    }

    /**
     * 获取新订单数据
     */
    handleOrderNew() {
        const orderNewInfo = this.ecOrderWatcherManager.getOrderNewInfo();
        if (!orderNewInfo) {
            return;
        }
        if (this.orderNewInfo === orderNewInfo) {
            return;
        }
        this.orderNewInfo = orderNewInfo;
        this.sendOrderNew();
    }

    /**
     * 获取新售后数据
     */
    handleAfterSale() {
        const afterSaleInfo = this.ecOrderWatcherManager.getAfterSaleInfo();
        if (!afterSaleInfo) {
            return;
        }
        if (this.afterSaleInfo === afterSaleInfo) {
            return;
        }
        this.afterSaleInfo = afterSaleInfo;
        this.sendAfterSale();
    }

    /**
     * 获取爬虫数据
     */
    async handleCrawler() {
        const authMallList = await this.crawlerManager.getAuthMallList();
        const exceptionOrderCount = authMallList.reduce((acc, cur) => {
            const {
                exceptionList,
            } = cur.FEInfo || {};
            acc += exceptionList?.orderCount || 0;
            return acc;
        }, 0);

        const orderTraceCountTotal = authMallList.reduce((acc, cur) => {
            const {
                orderTraceCount = {},
            } = cur.FEInfo || {};
            acc += orderTraceCount?.acceptUnTraceErrorCount || 0;
            acc += orderTraceCount?.acceptUnTraceWarnCount || 0;
            acc += orderTraceCount?.packageReturnCount || 0;
            acc += orderTraceCount?.shippedUnAcceptErrorCount || 0;
            acc += orderTraceCount?.shippedUnAcceptWarnCount || 0;
            acc += orderTraceCount?.stationErrorCount || 0;
            acc += orderTraceCount?.stationWarnCount || 0;
            acc += orderTraceCount?.traceErrorCount || 0;
            acc += orderTraceCount?.traceWarnCount || 0;
            return acc;
        }, 0);
        if (exceptionOrderCount) {
            Object.assign(this.desktopAssistantDto, { exceptionOrderCount });
        }
        if (orderTraceCountTotal) {
            Object.assign(this.desktopAssistantDto, { orderTraceCount: orderTraceCountTotal });
        }
        Object.assign(this.desktopAssistantConfig, {
            hasOnlineStore: !!authMallList.length,
            onlineStoreList: authMallList,
        });
        this.sendAssistantConfig();
        this.sendAssistantStat();
    }

    /**
     * 发送配置数据数据到桌面助手
     */
    sendAssistantConfig() {
        mainWindowSend(this.assistantWindow, MAIN_MESSAGE, {
            type: 'config',
            data: this.desktopAssistantConfig,
        });
    }

    /**
     * 发送订单数据到桌面助手
     */
    sendAssistantStat() {
        if (!this.desktopAssistantDto) {
            return;
        }
        mainWindowSend(this.assistantWindow, MAIN_MESSAGE, {
            type: 'statusBar',
            data: this.desktopAssistantDto,
        });
    }

    /**
     * 发送新订单到桌面助手
     */
    sendOrderNew() {
        if (!this.orderNewInfo) {
            return;
        }
        mainWindowSend(this.assistantWindow, MAIN_MESSAGE, {
            type: 'orderNewInfo',
            data: this.orderNewInfo,
        });
    }

    /**
     * 发送新订单到桌面助手
     */
    sendAfterSale() {
        if (!this.afterSaleInfo) {
            return;
        }
        mainWindowSend(this.assistantWindow, MAIN_MESSAGE, {
            type: 'afterSaleInfo',
            data: this.afterSaleInfo,
        });
    }

    /**
     * 监听桌面助手状态
     */
    watchDesktopAssistant() {
        assistMessageOn(ASSIST_MESSAGE, (e, data) => {
            // 桌面助手启动后操作
            if (data.type === 'running') {
                this.sendAssistantConfig();
                this.sendAssistantStat();
            }
        });
    }

    /**
     * 取消监听桌面助手状态
     */
    unWatchDesktopAssistant() {
        removeAssistMessageOn(ASSIST_MESSAGE);
    }
}
