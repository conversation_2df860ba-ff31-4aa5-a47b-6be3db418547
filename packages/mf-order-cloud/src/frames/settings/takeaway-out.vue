<template>
    <abc-layout>
        <abc-layout-content>
            <abc-manage-page :label-width="132" style="padding: 0;">
                <abc-form ref="outSetting">
                    <abc-manage-group>
                        <abc-manage-item label="自动出库">
                            <abc-radio-group v-model="params.autoStockOut" :item-block="true">
                                <abc-flex vertical :gap="10">
                                    <abc-manage-tip wrap tip="手动选择准确出库批次、采集追溯码后出库">
                                        <abc-radio :label="0">
                                            平台配送订单手动出库
                                        </abc-radio>
                                    </abc-manage-tip>
                                    <abc-manage-tip wrap tip="骑手取货后自动出库，出库批次按效期顺序出库，效期近的先出，不采集追溯码">
                                        <abc-radio :label="1">
                                            平台配送订单自动出库
                                        </abc-radio>
                                    </abc-manage-tip>
                                    <abc-manage-tip v-if="params.autoStockOut === 1">
                                        <abc-space :size="8" style="padding-left: 24px;">
                                            自动出库订单默认销售人为
                                            <employee-selector
                                                v-model="params.autoStockOutSeller"
                                                clearable
                                                placeholder="销售人"
                                                :employees="employeeList"
                                                data-cy="pharmacy-goods-selector-salesman"
                                            >
                                            </employee-selector>
                                            ，药师为
                                            <employee-selector
                                                v-model="params.autoStockOutPharmacist"
                                                clearable
                                                :employees="pharmacistEmployees"
                                                placeholder="药师"
                                                data-cy="pharmacy-goods-selector-pharmacist"
                                            >
                                            </employee-selector>
                                        </abc-space>
                                    </abc-manage-tip>
                                    <abc-text tag="div">
                                        非平台配送订单仅支持手动出库
                                    </abc-text>
                                </abc-flex>
                            </abc-radio-group>
                        </abc-manage-item>

                        <abc-manage-item
                            label="商品库存预警"
                            :label-style="{
                                'display': 'inline',
                            }"
                            :align="'center'"
                        >
                            <abc-manage-layout>
                                <abc-manage-tip>
                                    <span>周转天数小于</span>
                                    <abc-form-item style="margin: 0;">
                                        <abc-input
                                            v-model="params.inventoryWarnDay"
                                            :width="60"
                                            :max-length="5"
                                            type="number"
                                            :input-custom-style="{
                                                textAlign: 'center'
                                            }"
                                        ></abc-input>
                                    </abc-form-item>
                                    天，系统预警提示
                                </abc-manage-tip>
                            </abc-manage-layout>
                        </abc-manage-item>
                    </abc-manage-group>
                </abc-form>
                <template slot="footer">
                    <abc-button :disabled="!isUpdated" @click="onClickSave">
                        保存
                    </abc-button>
                </template>
            </abc-manage-page>
        </abc-layout-content>
    </abc-layout>
</template>
<script>
    import {
        AbcManageGroup,
        AbcManageItem,
        AbcManageLayout,
        AbcManagePage,
        AbcManageTip,
    } from 'MfBase/abc-manage';
    import { isEqual } from 'MfBase/lodash';
    import { clone } from '@abc/utils';
    import EmployeeSelector from 'MfBase/employee-selector';
    import { mapGetters } from 'vuex';
    import { ROLE_PHARMACY_DOCTOR } from 'MfBase/constants';

    export default {
        name: 'OutSetting',
        components: {
            EmployeeSelector,
            AbcManageGroup,
            AbcManageItem,
            AbcManageLayout,
            AbcManagePage,
            AbcManageTip,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                params: {
                    autoStockOut: 0,
                    inventoryWarnDay: 0,
                    autoStockOutPharmacist: '',
                    autoStockOutSeller: '',
                },
                cacheParams: {
                    autoStockOut: 0,
                    inventoryWarnDay: 0,
                    autoStockOutPharmacist: '',
                    autoStockOutSeller: '',
                },
                initConfig: false,
            };
        },
        computed: {
            ...mapGetters([
                'employeeList',
            ]),
            pharmacistEmployees() {
                return this.employeeList?.filter((it) => it.roleIds?.includes(ROLE_PHARMACY_DOCTOR)) || [];
            },
            // 页面发生变化
            isUpdated() {
                return !isEqual(this.params, this.cacheParams);
            },
            orderCloudConfigInit() {
                return this.$abcPage.$store.state.orderCloudConfigInit;
            },
        },
        watch: {
            orderCloudConfigInit: {
                handler(val) {
                    if (val) {
                        this.cacheParams = clone(this.$abcPage.$store.o2oSettings);
                        this.params = clone(this.$abcPage.$store.o2oSettings);
                    }
                },
                immediate: true,
            },
        },
        methods: {
            async onClickSave() {
                this.$refs.outSetting.validate(async (valid) => {
                    if (valid) {
                        const {
                            autoStockOut,
                            inventoryWarnDay,
                            autoStockOutPharmacist,
                            autoStockOutSeller,
                        } = this.params;
                        await this.$abcPage.$store.updateTakeawayOutSetting({
                            autoStockOut,
                            inventoryWarnDay: Number(inventoryWarnDay),
                            autoStockOutPharmacist,
                            autoStockOutSeller,
                        });
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                        this.cacheParams = clone(this.params);
                    }
                });
            },
        },
    };
</script>

