<template>
    <abc-layout class="setting-print-templates-wrapper">
        <abc-layout-content>
            <abc-manage-page :label-width="132" style="padding: 0;">
                <abc-form ref="deliverSetting">
                    <abc-manage-group>
                        <abc-manage-item v-if="isDeliverSetting" label="自动合并打单">
                            <abc-manage-tip wrap tip="收件人姓名、电话和地址完全相同的订单，将自动合并打单">
                                <abc-checkbox v-model="params.autoMergeOrder" type="number">
                                    开启
                                </abc-checkbox>
                            </abc-manage-tip>
                        </abc-manage-item>

                        <abc-manage-item
                            v-if="isEcommerceOutSetting"
                            label="商品库存预警"
                            :label-style="{
                                'display': 'inline',
                            }"
                            :align="'center'"
                        >
                            <abc-manage-layout>
                                <abc-manage-tip>
                                    <span>周转天数小于</span>
                                    <abc-form-item style="margin: 0;">
                                        <abc-input
                                            v-model="params.inventoryWarnDay"
                                            :width="60"
                                            :max-length="5"
                                            type="number"
                                            :input-custom-style="{
                                                textAlign: 'center'
                                            }"
                                        ></abc-input>
                                    </abc-form-item>
                                    天，系统预警提示
                                </abc-manage-tip>
                            </abc-manage-layout>
                        </abc-manage-item>
                    </abc-manage-group>
                </abc-form>
                <template slot="footer">
                    <abc-button :disabled="!isUpdated" @click="onClickSave">
                        保存
                    </abc-button>
                </template>
            </abc-manage-page>
        </abc-layout-content>
    </abc-layout>
</template>
<script>
    import {
        AbcManageGroup,
        AbcManageItem,
        AbcManageLayout,
        AbcManagePage,
        AbcManageTip,
    } from 'MfBase/abc-manage';
    import { isEqual } from 'MfBase/lodash';
    import { clone } from '@abc/utils';
    export default {
        name: 'DeliverSetting',
        components: {
            AbcManageGroup,
            AbcManageItem,
            AbcManageLayout,
            AbcManagePage,
            AbcManageTip,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                params: {
                    autoMergeOrder: 0,
                    inventoryWarnDay: 0,
                },
                cacheParams: {
                    autoMergeOrder: 0,
                    inventoryWarnDay: 0,
                },
                initConfig: false,
                currentRouteName: '',
            };
        },
        computed: {
            // 页面发生变化
            isUpdated() {
                return !isEqual(this.params, this.cacheParams);
            },
            orderCloudConfigInit() {
                return this.$abcPage.$store.state.orderCloudConfigInit;
            },
            // 判断是否为打单发货设置页面
            isDeliverSetting() {
                return this.$route.name === '@PharmacyOrderCloudSettingsDeliver';
            },
            // 判断是否为电商出库设置页面
            isEcommerceOutSetting() {
                return this.$route.name === '@PharmacyOrderCloudSettingsEcommerceOut';
            },
        },
        watch: {
            orderCloudConfigInit: {
                handler(val) {
                    if (val) {
                        this.initSettingData();
                    }
                },
                immediate: true,
            },
            // 监听路由变化，当路由发生变化时重新初始化数据
            '$route.name': {
                handler(newRouteName, oldRouteName) {
                    if (newRouteName !== oldRouteName && this.orderCloudConfigInit) {
                        this.initSettingData();
                    }
                },
                immediate: true,
            },
        },
        methods: {
            // 初始化设置数据
            initSettingData() {
                this.cacheParams = clone(this.$abcPage.$store.orderCloudConfig.printOrderSetting);
                this.params = clone(this.$abcPage.$store.orderCloudConfig.printOrderSetting);
            },
            async onClickSave() {
                this.$refs.deliverSetting.validate(async (valid) => {
                    if (valid) {
                        await this.$abcPage.$store.updatePrintOrderSetting({
                            printOrderSetting: {
                                autoMergeOrder: this.params.autoMergeOrder,
                                inventoryWarnDay: Number(this.params.inventoryWarnDay),
                            },
                        });
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                        this.cacheParams = clone(this.params);
                    }
                });
            },
        },
    };
</script>
