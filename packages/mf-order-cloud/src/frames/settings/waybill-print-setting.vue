<template>
    <abc-layout class="setting-print-templates-wrapper">
        <abc-layout-content style="padding-top: 0;">
            <abc-section class="setting-print-templates-header">
                <abc-flex justify="space-between">
                    <abc-tabs-v2
                        v-model="curEcType"
                        :option="tabOptions"
                        type="outline"
                        size="middle"
                    >
                    </abc-tabs-v2>

                    <abc-button
                        shape="square"
                        icon="s-b-add-line-medium"
                        variant="fill"
                        theme="success"
                        size="normal"
                        @click="handleOpenDialog(null)"
                    >
                        新建模板
                    </abc-button>
                </abc-flex>
            </abc-section>
            <abc-section>
                <abc-content-empty v-if="templateViews && templateViews.length === 0" style="height: 300px;"></abc-content-empty>
                <abc-row wrap="wrap" :gutter="16">
                    <abc-col v-for="template in templateViews" :key="template.id" :span="8">
                        <div
                            class="template-card-wrapper"
                            :class="{
                                'is-expired': templateAuthExpired(template)
                            }"
                        >
                            <div v-if="templateAuthExpired(template)" class="expired-tag">
                                网店授权已失效
                            </div>
                            <abc-flex class="card-title">
                                <abc-space class="wp-logo">
                                    <abc-image :width="24" :height="24" :src="WpCodeLogo[template.wpCode]"></abc-image>
                                    <span>{{ template.wpName }} - {{ template.ecMallName }}</span>
                                    <abc-tag-v2
                                        v-if="template.shared"
                                        variant="outline"
                                        shape="square"
                                        size="mini"
                                        theme="success"
                                    >
                                        共享
                                    </abc-tag-v2>
                                </abc-space>
                            </abc-flex>
                            <abc-descriptions
                                class="card-body"
                                :column="1"
                                :label-width="92"
                                :bordered="false"
                                content-padding="16"
                                ellipsis
                            >
                                <abc-descriptions-item label="纸张尺寸" class="template-info-item">
                                    {{ template.standardTemplateName }}
                                </abc-descriptions-item>
                                <abc-descriptions-item label="发货地址-取件" class="template-info-item">
                                    {{ getAddressStr(template.wpShippAddress) }}
                                </abc-descriptions-item>
                                <abc-descriptions-item label="发货地址-展示" :label-width="74" class="template-info-item">
                                    {{ contactAddress(template.shipper) }}
                                </abc-descriptions-item>
                                <abc-descriptions-item label="自定义区域" class="template-info-item">
                                    {{ template.customAreaSwitch ? '商品明细' : '无' }}
                                </abc-descriptions-item>
                            </abc-descriptions>
                            <abc-flex class="card-footer" justify="flex-end">
                                <abc-space v-if="template.canModify">
                                    <abc-button
                                        shape="square"
                                        variant="ghost"
                                        theme="danger"
                                        @click="handleClickDelete(template)"
                                    >
                                        删除
                                    </abc-button>
                                    <abc-tooltip v-if="!templateAuthExpired(template)" :disabled="!disabledTemplateTips(template)" :content="disabledTemplateTips(template)">
                                        <div>
                                            <abc-button
                                                shape="square"
                                                variant="ghost"
                                                :disabled="!!disabledTemplateTips(template)"
                                                @click="handleOpenDialog(template)"
                                            >
                                                编辑
                                            </abc-button>
                                        </div>
                                    </abc-tooltip>
                                </abc-space>
                                <abc-button
                                    v-else
                                    shape="square"
                                    variant="ghost"
                                    @click="handleOpenDialog(template)"
                                >
                                    查看
                                </abc-button>
                            </abc-flex>
                        </div>
                    </abc-col>
                </abc-row>
            </abc-section>
        </abc-layout-content>
    </abc-layout>
</template>

<script type="text/ecmascript-6">
// 自定义组件
    import DialogWaybillForm from '../../components/dialog-waybill-form';
    import {
        ECTypeText,
    } from '../../utils/constants';
    import ECWayBillAPI from '../../api/way-bill';
    import { WpCodeLogo } from '../../utils/constants';
    import { getAddressStr } from '../../utils/index.js';

    export default {
        name: 'HospitalSetting',
        components: {
        },
        data() {
            return {
                WpCodeLogo,
                templates: [],
                curEcType: '',
            };
        },
        computed: {
            tabOptions() {
                return this.templates.map((item) => {
                    return {
                        label: ECTypeText[item.ecType],
                        value: item.ecType,
                    };
                });
            },
            templateViews() {
                const res = this.templates.find((it) => it.ecType === this.curEcType);
                return res?.templateViews || [];
            },
        },
        created() {
            this.fetchEcWaybillTemplate();
        },
        methods: {
            getAddressStr,
            templateAuthExpired(template) {
                return template.ecMallBindStatus === 2;
            },
            disabledTemplateTips(template) {
                // ecMallBindStatus：0：已解绑，1：正常，2：授权过期
                if (template.ecMallBindStatus === 0) {
                    return '网店已解绑';
                }
                if (template.ecMallBindStatus === 2) {
                    return '网店授权失效，请授权网店续期';
                }
                return '';
            },
            contactAddress(shipper) {
                const {
                    name = '',
                    mobile = '',
                    address,
                } = shipper || {};

                return `${name}|${mobile}|${getAddressStr(address)}`;
            },
            async fetchEcWaybillTemplate() {
                const res = await ECWayBillAPI.fetchEcWaybillTemplate({
                    includeExpired: 1,
                });
                this.templates = res?.rows || [];
                if (!this.curEcType && this.templates[0]) {
                    this.curEcType = this.templates[0].ecType;
                }
            },
            handleClickDelete(template) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不可恢复，是否确定删除？',
                    onConfirm: () => {
                        this.deleteEcWaybillTemplate(template);
                    },
                });
            },
            async deleteEcWaybillTemplate(template) {
                try {
                    this.btnLoading = true;
                    await ECWayBillAPI.deleteEcWaybillTemplate({
                        ecType: template.ecType,
                        id: template.id,
                    });
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    this.fetchEcWaybillTemplate();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }

            },
            handleOpenDialog(template) {
                new DialogWaybillForm({
                    ecType: this.curEcType,
                    defaultData: template,
                    disabled: template && !template.canModify,
                    onRefresh: this.fetchEcWaybillTemplate,
                }).generateDialogAsync({ parent: this });
            },

        },
    };
</script>


<style lang="scss">
    .setting-print-templates-wrapper {
        overflow: auto;

        .setting-print-templates-header {
            position: sticky;
            top: 0;
            z-index: 10;
            padding-top: var(--abc-layout-content-padding);
            background-color: #ffffff;
        }

        .template-card-wrapper {
            position: relative;
            display: inline-flex;
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
            height: 262px;
            border: 1px solid var(--abc-color-P7);
            border-radius: var(--abc-border-radius-small);

            &.is-expired {
                background: var(--abc-color-AbcDivGrey);

                .template-info-item {
                    color: var(--abc-color-T2);
                }

                .expired-tag {
                    position: absolute;
                    top: -23px;
                    right: -38px;
                    z-index: 1;
                    padding: 5px 20px 5px 39px;
                    clip-path: polygon(29% 0%, 88% 0%, 100% 100%, 0% 100%);
                    font-size: 12px;
                    color: var(--abc-color-T2);
                    background: var(--abc-color-P7);
                    transform: rotate(33deg);
                    transform-origin: top left;
                }
            }

            .card-title {
                padding: 16px 16px 0;

                .wp-logo {
                    display: flex;
                    align-items: center;
                }
            }

            .card-body {
                flex: 1;
                width: 100%;
                height: 0;
            }

            .card-footer {
                width: 100%;
                padding: 12px 16px;
                background: var(--abc-color-AbcDivGrey);
                border-top: 1px solid var(--abc-color-P7);
                border-bottom-right-radius: var(--abc-border-radius-small);
                border-bottom-left-radius: var(--abc-border-radius-small);
            }
        }
    }
</style>


