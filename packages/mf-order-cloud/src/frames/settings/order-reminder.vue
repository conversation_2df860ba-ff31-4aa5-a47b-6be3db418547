<template>
    <abc-layout class="setting-desktop-assistant-wrapper">
        <abc-layout-content style="overflow-y: auto;">
            <abc-section>
                <abc-manage-page :label-width="132" style="padding: 0;">
                    <abc-form ref="deliverSetting">
                        <abc-manage-group>
                            <abc-manage-item label="新订单语音播报">
                                <abc-space align="center">
                                    <abc-checkbox
                                        v-model="params.voiceNewOrder"
                                        type="number"
                                        control
                                        @click="onClick('voiceNewOrder')"
                                    >
                                        开启
                                    </abc-checkbox>
                                    <div class="trumpet-img" :class="{ 'playing-trumpet': isOrderNewPlaying }" @click="playOrderNewVoice"></div>
                                </abc-space>
                            </abc-manage-item>

                            <abc-manage-item label="新售后语音播报">
                                <abc-space align="center">
                                    <abc-checkbox
                                        v-model="params.voiceAfterSale"
                                        type="number"
                                        control
                                        @click="onClick('voiceAfterSale')"
                                    >
                                        开启
                                    </abc-checkbox>
                                    <div class="trumpet-img" :class="{ 'playing-trumpet': isOrderAfterSalePlaying }" @click="playOrderAfterSaleVoice"></div>
                                </abc-space>
                            </abc-manage-item>
                        </abc-manage-group>
                    </abc-form>
                    <template slot="footer">
                        <abc-button :disabled="!isUpdated" @click="onClickSave">
                            保存
                        </abc-button>
                    </template>
                </abc-manage-page>
            </abc-section>
        </abc-layout-content>
    </abc-layout>
</template>

<script type="text/ecmascript-6">
    import { OrderCloudDaemonService } from '../../daemon';
    import {
        AbcManageGroup,
        AbcManageItem,
        AbcManagePage,
    } from 'MfBase/abc-manage';
    import { isEqual } from 'MfBase/lodash';
    import AbcPlayer from 'MfBase/abc-player';
    import { clone } from '@abc/utils';
    import DialogDownloadClient from '../../components/dialog-download-client/index.js';
    import { isClientSupportPharmacy } from '../../utils/electron';
    export default {
        name: 'DesktopAssistantSetting',
        components: {
            AbcManageGroup,
            AbcManageItem,
            AbcManagePage,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                params: {
                    voiceNewOrder: 0,
                    voiceAfterSale: 0,
                },
                cacheParams: {
                    voiceNewOrder: 0,
                    voiceAfterSale: 0,
                },
                isOrderNewPlaying: false,
                isOrderAfterSalePlaying: false,

                pddData: {
                    authStatus: -1,
                    exceptionList: {},
                    orderTrace: {},
                    userInfo: {},
                    userSetting: {},
                    afterSalesOrderList: {},
                },
                curPage: '',
            };
        },
        computed: {
            // 页面发生变化
            isUpdated() {
                return !isEqual(this.params, this.cacheParams);
            },
            orderCloudConfigInit() {
                return this.$abcPage.$store.state.orderCloudConfigInit;
            },
        },
        watch: {
            orderCloudConfigInit: {
                handler(val) {
                    if (val) {
                        if (isClientSupportPharmacy()) {
                            this.cacheParams = clone(this.$abcPage.$store.orderCloudConfig.orderAssistantSetting);
                            this.params = clone(this.$abcPage.$store.orderCloudConfig.orderAssistantSetting);
                        }
                    }
                },
                immediate: true,
            },
        },
        async created() {
            this._waitOrderNewVoicePlay = new AbcPlayer(require('@/assets/audio/order-new.mp3'));
            this._waitOrderAfterSaleVoicePlay = new AbcPlayer(require('@/assets/audio/after-sale.mp3'));

            this.crawlerManager = OrderCloudDaemonService.getInstance().getCrawlerManager();
        },
        destroyed() {
            this.destroyElement();
        },
        methods: {
            async requestAuth() {
                await this.crawlerManager.requestAuth();
            },
            async cancelAuth() {
                await this.crawlerManager.cancelAuth();
            },

            openAuth() {
                this.crawlerManager.requestAuth();
            },

            async onClickSave() {
                this.$refs.deliverSetting.validate(async (valid) => {
                    if (valid) {
                        await this.$abcPage.$store.updateOrderAssistantSetting({
                            orderAssistantSetting: this.params,
                        });
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                        this.cacheParams = clone(this.params);
                    }
                });
            },

            playOrderNewVoice() {
                if (this.isOrderNewPlaying) {
                    return;
                }
                this.isOrderNewPlaying = true;
                this._timer = setTimeout(() => {
                    this.isOrderNewPlaying = false;
                }, 7000);
                this._waitOrderNewVoicePlay.play();
            },

            playOrderAfterSaleVoice() {
                if (this.isOrderAfterSalePlaying) {
                    return;
                }
                this.isOrderAfterSalePlaying = true;
                this._timer = setTimeout(() => {
                    this.isOrderAfterSalePlaying = false;
                }, 7000);
                this._waitOrderAfterSaleVoicePlay.play();
            },

            destroyElement() {
                this._waitOrderNewVoicePlay && this._waitOrderNewVoicePlay.destroy();
                this._waitOrderNewVoicePlay = null;
                this._waitOrderAfterSaleVoicePlay && this._waitOrderAfterSaleVoicePlay.destroy();
                this._waitOrderAfterSaleVoicePlay = null;
            },

            onClick(type) {
                // 浏览器关闭
                if (!isClientSupportPharmacy()) {
                    new DialogDownloadClient().generateDialogAsync();
                    return;
                }
                if (this.params.hasOwnProperty(type)) {
                    this.params[type] = !this.params[type] ? 1 : 0;
                }
            },
        },
    };
</script>


<style lang="scss">
    .setting-desktop-assistant-wrapper {
        .template-card-wrapper {
            display: inline-flex;
            flex-direction: column;
            align-items: flex-start;
            width: 470px;
            height: 262px;
            margin-right: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--abc-color-P7);
            border-radius: 6px;

            .card-title {
                padding: 16px 16px 0;

                .wp-logo {
                    display: flex;
                    align-items: center;
                    line-height: 24px;
                }
            }

            .card-body {
                flex: 1;
                width: 100%;
                height: 0;
            }

            .card-footer {
                width: 100%;
                padding: 12px 16px;
                background: var(--abc-color-AbcDivGrey);
            }
        }

        .trumpet-img {
            width: 18px;
            height: 16px;
            margin-left: 8px;
            cursor: pointer;
            background-image: url("~assets/images/voice/trumpet-grey.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;

            &:hover {
                background-image: url("~assets/images/voice/trumpet-blue-0.png");
            }

            &.playing-trumpet {
                animation: auto-play 0.8s infinite;
            }
        }

        @keyframes auto-play {
            0% {
                background-image: url("~assets/images/voice/trumpet-blue-1.png");
            }

            50% {
                background-image: url("~assets/images/voice/trumpet-blue-2.png");
            }

            100% {
                background-image: url("~assets/images/voice/trumpet-blue-3.png");
            }
        }
    }
</style>


