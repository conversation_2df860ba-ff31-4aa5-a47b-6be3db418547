<template>
    <abc-card class="content-card-wrapper" :border="false">
        <abc-flex
            v-if="isO2OStore && !installChromeStatus"
            align="center"
            justify="center"
            vertical
            style="margin-top: 200px;"
        >
            <img
                class="install-chrome-img"
                src="~assets/images/setting/install-icon.png"
                width="450"
                style="width: 450px;"
                alt="install"
            />
            <abc-text size="xxlarge" bold style="margin-top: 40px;">
                未安装订单云外卖
            </abc-text>
            <abc-text size="large" style="margin-top: 16px;">
                本机尚未安装订单云外卖应用，请安装后使用
            </abc-text>
            <abc-button style="margin-top: 40px;" size="large" @click="handleInstallChrome">
                立即安装
            </abc-button>
        </abc-flex>
        <abc-flex v-else vertical style="height: 100%;">
            <!--            <abc-tabs-v2-->
            <!--                v-if="tabOptions.length > 1"-->
            <!--                v-model="currentTab"-->
            <!--                :option="tabOptions"-->
            <!--                size="large"-->
            <!--                @change="onChangeTab"-->
            <!--            ></abc-tabs-v2>-->
            <abc-layout preset="page-table">
                <abc-layout-header>
                    <abc-flex justify="space-between">
                        <abc-space>
                            <abc-select
                                v-model="params.ecType"
                                :width="160"
                                clearable
                                placeholder="平台"
                                @change="getAuthorizedEcList"
                            >
                                <abc-option
                                    v-for="item in ecTypes"
                                    :key="`${item.ecName}|${item.clientId}`"
                                    :label="item.ecName"
                                    :value="item.ecType"
                                ></abc-option>
                            </abc-select>
                            <abc-select
                                v-model="params.ecMallId"
                                :width="160"
                                clearable
                                placeholder="绑定网店"
                                @change="getAuthorizedEcList"
                            >
                                <abc-option
                                    v-for="item in authorizedEcList"
                                    :key="`${item.mallName}|${item.ecMallId}`"
                                    :label="item.mallName"
                                    :value="item.ecMallId"
                                ></abc-option>
                            </abc-select>
                            <abc-select
                                v-model="params.bindClinicId"
                                placeholder="接单门店"
                                :width="160"
                                clearable
                                @change="getAuthorizedEcList"
                            >
                                <abc-option
                                    v-for="clinic in bindClinicList"
                                    :key="clinic.bindClinicId"
                                    :label="clinic.bindClinicName"
                                    :value="clinic.bindClinicId"
                                ></abc-option>
                            </abc-select>
                        </abc-space>
                        <abc-space>
                            <template v-if="canOperate">
                                <abc-button
                                    v-abc-check-electron="{
                                        disableTips: '当前为网页浏览器，不能进行网店绑定，请在ABC客户端中使用。', installTips: true
                                    }"
                                    icon="n-add-line-medium"
                                    theme="success"
                                    @click="openAddOnlineStoreDialog"
                                >
                                    新增网店
                                </abc-button>
                            </template>
                        </abc-space>
                    </abc-flex>
                </abc-layout-header>
                <abc-layout-content v-abc-loading="contentLoading">
                    <abc-form ref="onlineStoreFormRef">
                        <abc-table
                            type="excel"
                            :need-selected="false"
                            :show-hover-tr-bg="false"
                            :render-config="renderConfig"
                            :data-list="authorizedEcList"
                        >
                            <template #mallName="{ trData }">
                                <abc-table-cell>
                                    <abc-text :theme="getTrActiveStatus(trData) ? 'black' : 'gray-light'">
                                        {{ trData.mallName }}
                                    </abc-text>
                                </abc-table-cell>
                            </template>
                            <template #ecType="{ trData }">
                                <abc-table-cell>
                                    <abc-text :theme="getTrActiveStatus(trData) ? 'black' : 'gray-light'">
                                        {{ getEcTypeText(trData.ecType) }}
                                    </abc-text>
                                </abc-table-cell>
                            </template>
                            <template #status="{ trData }">
                                <abc-table-cell>
                                    <abc-text v-if="trData.ecType === ECTypeEnum.MT && trData.goodsAccessStatus === 0 && isSyncing">
                                        同步网店商品中({{ syncInfo.progress }}%)
                                    </abc-text>
                                    <abc-text v-else-if="trData.ecType === ECTypeEnum.MT && trData.goodsAccessStatus === 0">
                                        待同步网店商品
                                    </abc-text>
                                    <abc-flex v-else :gap="10" class="ellipsis">
                                        <abc-text :theme="getTrActiveStatus(trData) ? 'success-light' : 'warning-light'">
                                            {{ getStatusText(trData.status) }}
                                        </abc-text>
                                        <abc-text :theme="getTrActiveStatus(trData) ? 'black' : 'gray-light'">
                                            {{ trData.expireTime | formatDate('YYYY-MM-DD HH:mm') }}前有效
                                        </abc-text>
                                        <abc-text
                                            :theme="getAuthStatusObj(trData).authTheme"
                                            class="ellipsis"
                                            :title="getAuthStatusObj(trData).authText"
                                        >
                                            {{ getAuthStatusObj(trData).authText }}
                                        </abc-text>
                                    </abc-flex>
                                </abc-table-cell>
                            </template>
                            <template #bindClinicName="{ trData }">
                                <abc-table-cell v-if="trData.ecType === ECTypeEnum.MT">
                                    {{ trData.bindClinicName }}
                                </abc-table-cell>
                                <abc-form-item
                                    v-else
                                    required
                                    trigger="change"
                                    :validate-event="validateBindClinic"
                                    style="width: 100%;"
                                >
                                    <abc-select
                                        v-model="trData.bindClinicId"
                                        placeholder="请选择接单门店"
                                        clearable
                                        width="100%"
                                        :disabled="!getTrActiveStatus(trData) || !canOperate"
                                        :before-change="(props) => handleBindClinicBeforeChange(props, trData)"
                                    >
                                        <abc-option
                                            v-for="clinic in clinicList"
                                            :key="clinic.id"
                                            :disabled="clinic.disabled"
                                            :value="clinic.id"
                                            :label="clinic.name"
                                        >
                                            <abc-flex justify="space-between" flex="1" align="center">
                                                <abc-text v-abc-title.ellipsis="clinic.name" :theme="clinic.disabled ? 'gray' : 'black'">
                                                </abc-text>
                                                <abc-text v-if="clinic.disabled" theme="gray" size="mini">
                                                    需要升级到专业版
                                                </abc-text>
                                            </abc-flex>
                                        </abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </template>
                            <template #operation="{ trData }">
                                <abc-table-cell>
                                    <abc-button
                                        v-if="trData.ecType === ECTypeEnum.MT && trData.goodsAccessStatus === 0"
                                        v-abc-check-electron="{
                                            disableTips: '当前为网页浏览器，不能进行网店绑定，请在ABC客户端中使用。', installTips: true
                                        }"
                                        size="small"
                                        variant="text"
                                        @click="handleSyncGoods(trData)"
                                    >
                                        查看
                                    </abc-button>
                                    <template v-else>
                                        <abc-button
                                            v-if="trData.ecType === ECTypeEnum.MT"
                                            v-abc-check-electron="{
                                                disableTips: '当前为网页浏览器，不能进行续期，请在ABC客户端中使用。',
                                                installTips: true
                                            }"
                                            variant="text"
                                            theme="primary"
                                            size="small"
                                            :disabled="!canOperate"
                                            @click="handleMTRenewAuth(trData)"
                                        >
                                            续期
                                        </abc-button>
                                        <abc-button
                                            v-if="trData.ecType === ECTypeEnum.PDD"
                                            variant="text"
                                            theme="primary"
                                            size="small"
                                            :disabled="!canOperate"
                                            @click="handleRenewAuth(trData)"
                                        >
                                            {{ getAuthStatusObj(trData).buttonText }}
                                        </abc-button>
                                        <abc-button
                                            variant="text"
                                            theme="danger"
                                            size="small"
                                            style="margin-left: 0;"
                                            :disabled="!canOperate"
                                            @click="handleDeleteAuth(trData)"
                                        >
                                            删除
                                        </abc-button>
                                    </template>
                                </abc-table-cell>
                            </template>
                        </abc-table>
                    </abc-form>
                </abc-layout-content>
                <abc-layout-footer>
                    <abc-pagination
                        :pagination-params="paginationParams"
                        :count="clinicTotalCount"
                        @current-change="pageChange"
                    ></abc-pagination>
                </abc-layout-footer>
            </abc-layout>
        </abc-flex>
        <dialog-install-status
            v-if="showInstallChrome"
            v-model="showInstallChrome"
        ></dialog-install-status>
    </abc-card>
</template>

<script>
    import DialogEcTypes from '../../components/dialog-ec-types';
    import ECAuthAPI from '@/api/auth';
    import { mapGetters } from 'vuex';
    import { EditionKeyEnum } from 'MfBase/access-constant';
    import {
        formatDate,
    } from '@abc/utils-date';
    import { getTargetHost } from 'MfBase/build-env';
    import {
        ECTypeEnum,
        BindStatusEnum,
        EcShopTypeEnum,
    } from '../../utils/constants';
    import OrderCloudDaemonService from '@/daemon/order-cloud-daemon-service';
    import { PharmacyOrderCloudRouterNameKeys } from '../../core/routes';
    import DialogInstallStatus from '@/components/dialog-install-status/index.vue';
    import { isClientSupportPharmacy } from '@/utils/electron';
    import DialogDownloadClient from '@/components/dialog-download-client';
    import DialogEcBind from '@/components/dialog-ec-bind/index';
    import OrderCloudStore from '@/daemon/crawler/store';
    import {
        MEITUAN_EVENT_SYNC_PRODUCT_INFO,
        MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE,
        MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED,
    } from '@/daemon/crawler/provider/meituan/constants';
    import { AuthStatus } from '@/daemon/crawler/common/constants';
    import DialogEcAuth from '@/components/dialog-ec-auth';

    export default {
        name: 'OnlineStore',
        components: { DialogInstallStatus },
        filters: {
            formatDate,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                EcShopTypeEnum,
                ECTypeEnum,
                params: {
                    limit: 10,
                    offset: 0,
                    shopType: EcShopTypeEnum.O2O,
                    ecType: '',
                    bindClinicId: '',
                    ecMallId: '',
                    purchaseInfo: 1,
                },
                clinicTotalCount: 0,
                syncInfo: {},
                isSyncing: false,
                renderConfig: {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'mallName',
                            label: '绑定网店',
                            testValue: '',
                            slot: true,
                            style: {
                                'flex': 1,
                                // 'width': '320px',
                                // 'maxWidth': '320px',
                                'minWidth': '320px',
                                // 'paddingLeft': '',
                                // 'paddingRight': '',
                                'textAlign': 'left',
                            },
                        }, {
                            key: 'ecType',
                            label: '平台',
                            testValue: '',
                            slot: true,
                            style: {
                                'flex': 1,
                                // 'width': '120px',
                                // 'maxWidth': '120px',
                                'minWidth': '100px',
                                // 'paddingLeft': '',
                                // 'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'bindClinicName',
                            label: '接单门店',
                            style: {
                                'flex': 1,
                                // 'width': '320px',
                                // 'maxWidth': '320px',
                                'minWidth': '220px',
                                // 'paddingLeft': '',
                                // 'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'status',
                            label: '状态',
                            style: {
                                'flex': 1,
                                // 'width': '',
                                // 'maxWidth': '',
                                'minWidth': '300px',
                                // 'paddingLeft': '',
                                // 'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'operation',
                            label: '操作',
                            slot: true,
                            style: {
                                'flex': 1,
                                // 'width': '120px',
                                'maxWidth': '120px',
                                'minWidth': '120px',
                                // 'paddingLeft': '',
                                // 'paddingRight': '',
                                'textAlign': 'center',
                            },
                        },
                    ],
                },
                dataList: [
                    {
                        keyId: 'a1',
                        name: '诊疗项目1',
                        unitPrice: '10',
                        count: '1',
                        totalPrice: '10',
                    }, {
                        keyId: 'a2',
                        name: '诊疗项目2',
                        unitPrice: '20',
                        count: '2',
                        totalPrice: '40',
                    }, {
                        keyId: 'a3',
                        name: '诊疗项目3',
                        unitPrice: '30',
                        unitCount: '3',
                        totalPrice: '90',
                    }],
                tableData: [],
                tableTrHeight: 72,
                contentLoading: false,
                authorizedEcList: [],
                ecTypes: [],
                thirdPartyAuthorizationUrl: '',
                clinicList: [],
                currentTab: EcShopTypeEnum.O2O, // 默认为外卖网店，在created中会根据路由更新
                showInstallChrome: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isAdmin',
                'isSingleStore',
                'isChainSubStore',
            ]),
            //  电商网店仅总部可见
            visibleB2C() {
                return this.isAdmin;
            },
            installChromeStatus() {
                return this.$abcPage.$store?.state?.installChromeStatus;
            },
            tabOptions() {
                return [
                    {
                        label: '外卖网店',
                        value: EcShopTypeEnum.O2O,
                        visible: true,
                    },
                    {
                        label: '电商网店',
                        value: EcShopTypeEnum.B2C,
                        visible: this.visibleB2C,
                    },
                ].filter((item) => item.visible);
            },
            canOperateO2OStore() {
                return this.isSingleStore || this.isChainSubStore;
            },
            canOperate() {
                return this.isO2OStore && this.canOperateO2OStore || this.isB2CStore;
            },
            isO2OStore() {
                return this.currentTab === EcShopTypeEnum.O2O;
            },
            isB2CStore() {
                return this.currentTab === EcShopTypeEnum.B2C;
            },
            paginationParams() {
                const {
                    limit, offset,
                } = this.params;
                const pageIndex = Math.ceil(offset / limit);
                return {
                    pageIndex,
                    pageSize: limit,
                    count: this.clinicTotalCount,
                };
            },
            bindClinicList() {
                return this.authorizedEcList.reduce((acc, curr) => {
                    const found = acc.find((item) => item.bindClinicId === curr.bindClinicId);
                    if (!found) {
                        return [...acc, curr];
                    }
                    return acc;
                }, []).filter((item) => item.bindClinicId);
            },
            disabledBind() {
                if (this.isO2OStore) {
                    const ecTypes = this.ecTypes.filter((ecType) => !this.authorizedEcList.some((item) => item.ecType === ecType.ecType));
                    return ecTypes.length === 0 || this.contentLoading;
                }
                return false;
            },
        },
        created() {
            // 初始化判断当前路由是外卖还是电商
            this.checkCurrentRoute();

            // 监听路由变化
            this.$watch(
                () => this.$route.name,
                () => {
                    this.checkCurrentRoute();
                },
            );

            this.getAuthorizedEcList();
            if (this.$route.query?.code) {
                this.handleAuthEc();
            }
            this.fetchClinics();
            this.getEcTypes();
            this.$abcEventBus.$on('ec-auth-success', () => this.getAuthorizedEcList(), this);
            this.$nextTick(() => {
                this.$refs.onlineStoreFormRef?.validate();
            });
        },
        beforeDestroy() {
            clearInterval(this._winLoop);
            localStorage.removeItem('ec_code');
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            handleInstallChrome() {
                if (window.localStorage.getItem('mockInstallChrome')) {
                    this.$abcPage.$store.state.installChromeStatus = true;
                    return;
                }
                if (!isClientSupportPharmacy()) {
                    new DialogDownloadClient().generateDialogAsync();
                    return;
                }
                this.showInstallChrome = true;
            },
            getTrActiveStatus(trData) {
                return trData.status === BindStatusEnum.UNKNOWN || trData.status === BindStatusEnum.BIND;
            },
            getAuthStatusObj(item) {
                const {
                    status,
                    purchaseInfo,
                } = item;

                if (purchaseInfo?.status === 20) {
                    return {
                        buttonText: '授权',
                        authText: '已购买服务，点击右侧【授权】按钮，更新服务有效期',
                        authTheme: 'success-light',
                    };
                }

                if (status === BindStatusEnum.EXPIRED) {
                    return {
                        buttonText: '续期',
                        authText: '',
                        authTheme: '',
                    };
                }

                if (status === BindStatusEnum.BIND) {
                    return {
                        buttonText: '续期',
                        authText: '',
                        authTheme: '',
                    };
                }

                return {
                    buttonText: '购买',
                    authText: '点击右侧【购买】按钮，购买服务',
                    authTheme: 'warning-light',
                };
            },
            async handleAuthEc() {
                try {
                    const res = await ECAuthAPI.authEc({
                        authCode: this.$route.query?.code,
                        ecType: 1,
                    });

                    if (res) {
                        this.getAuthorizedEcList();
                    }
                } catch (err) {
                    console.log(err);
                }
            },
            pageChange(pageIndex) {
                this.params.offset = (pageIndex - 1) * this.params.limit;
            },
            getEcTypeText(type) {
                if (type === 1) return '拼多多';
                if (type === 2) return '饿了么';
                if (type === 3) return '京东';
                if (type === 4) return '美团';
            },
            getStatusText(status) {
                if (status === 1) return '生效中';
                return '已失效';
            },

            async fetchClinics() {
                try {
                    const data = await ECAuthAPI.chainClinicV3();
                    this.clinicList = (data?.rows || [])
                        // 过滤掉总店
                        .filter((item) => !item.chainAdmin)
                        .map((item) => {
                            return {
                                ...item,
                                name: item?.shortName || item.name,
                                disabled: item.edition.key === EditionKeyEnum.BASIC,
                            };
                        }).sort((a, b) => +b.disabled - +a.disabled);
                } catch (error) {
                    console.log('fetchClinics error', error);
                }
            },
            async getAuthorizedEcList() {
                try {
                    this.contentLoading = true;
                    const { rows } = await ECAuthAPI.fetchAuthorizedEcList({
                        ...this.params,
                    });
                    const { rows: bindList } = await ECAuthAPI.fetchBindAuthList({
                        offset: 0,
                        limit: 1000,
                        onlyUnexpired: 0, // 在授权有效期内
                    });
                    this.authorizedEcList = [];
                    if (this.isB2CStore) {
                        this.authorizedEcList = rows || [];
                    } else {
                        (rows || []).forEach((item) => {
                            const target = bindList?.find((one) => item.ecMallId === one.ecMallId && item.ecType === one.ecType);
                            if (target) {
                                this.authorizedEcList.push({
                                    ...item,
                                    goodsAccessStatus: target?.goodsAccessStatus,
                                });
                            }
                        });
                    }
                    this.clinicTotalCount = this.authorizedEcList.length;
                    OrderCloudDaemonService.getInstance().start();

                    if (this.isO2OStore && !this.meituanService && this.clinicTotalCount) {
                        await new OrderCloudStore().fetchBindAuthList();
                        await this.initCrawler();
                    }
                } catch (err) {
                    console.error(err);
                } finally {
                    this.contentLoading = false;
                }
            },

            async deleteAuthSubmit(item) {
                const {
                    ecMallId,
                    ecType,
                } = item;
                try {
                    await ECAuthAPI.deleteAuth({
                        ecMallId,
                        ecType,
                    });
                    this.getAuthorizedEcList();
                } catch (err) {
                    console.error(err);
                }
                // 终止爬虫
                OrderCloudDaemonService.getCrawlerManager().cancelScheduler();
            },
            async cancelClinicBindSubmit(item) {
                const {
                    ecMallId,
                    ecType,
                } = item;
                try {
                    const res = await ECAuthAPI.cancelBind({
                        ecMallId,
                        ecType,
                    });
                    if (res) {
                        this.getAuthorizedEcList();
                    }
                } catch (err) {
                    console.error(err);
                }
            },
            async handleDeleteAuth(item) {
                const hasAuth = item.ecMallId;
                if (hasAuth) {
                    let content = '删除后，该网店订单将无法再通过ABC系统接单发货，库存也无法同步，确定删除？';
                    if (item.ecType === EcShopTypeEnum.O2O) {
                        content = '删除网店后接单门店将不再接收网店订单，确认删除吗？';
                    }
                    this.$confirm({
                        type: 'warn',
                        title: '删除确认',
                        confirmText: '',
                        content,
                        onConfirm: () => {
                            this.deleteAuthSubmit(item);
                        },
                    });
                }
            },

            async handleBindClinicConfirm(item, value) {
                const {
                    bindClinicId, ecMallId, ecType,
                } = item;
                try {
                    const res = await ECAuthAPI.authBind({
                        bindClinicId: bindClinicId || value,
                        ecMallId,
                        ecType,
                    });
                    if (res) {
                        this.getAuthorizedEcList();
                        return true;
                    }
                } catch (err) {
                    console.error(err);
                }
            },
            validateBindClinic(value, callback) {
                if (!value) {
                    callback({
                        validate: false,
                        message: '请选择接单门店',
                    });
                }
            },
            async handleBindClinicBeforeChange({
                value, done,
            }, item) {
                const currentValue = item.bindClinicId;
                if (value === currentValue) return;

                const payload = {
                    ...item,
                    bindClinicId: value,
                };

                if (!currentValue && value) {
                    const res = await this.handleBindClinicConfirm(payload);
                    if (res) {
                        done();
                        return;
                    }
                }

                if (value && currentValue && (value !== currentValue)) {
                    this.$confirm({
                        type: 'warn',
                        title: '切换接单门店确认',
                        confirmText: '确定切换',
                        content: `
                            <div>
                                <p>切换门店会影响接单和库存同步，建议选择订单量较少时间段进行：</p>
                                <p>* 新订单将发往新接单门店，已产生的订单仍在原接单门店处理 </p>
                                <p>* 网店商品将同步新设置的接单门店库存，请在 [商品] 模块中确认库存是否充足，以免缺货影响销售。 </p>
                            </div>
                        `,
                        onConfirm: async () => {
                            const res = await this.handleBindClinicConfirm(payload);
                            if (res) {
                                done();
                            }
                        },
                    });
                }

                if (!value && currentValue) {
                    const content = '删除接单门店后，该网店订单将没有任何门店可通过 ABC 系统接单发货， 网店商品也无法同步 ABC 系统库存，是否确定？';
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        confirmText: '',
                        content,
                        onConfirm: async () => {
                            const res = await this.cancelClinicBindSubmit(payload);
                            if (res) {
                                done();
                            }
                        },
                        onCancel: () => {

                        },
                    });
                }
            },
            async getEcTypes() {
                try {
                    const res = await ECAuthAPI.fetchEcTypes({
                        ...this.params,
                    });
                    const ecTypes = res?.rows || [];
                    this.ecTypes = ecTypes;
                } catch (err) {
                    console.error(err);
                }
            },
            openAddOnlineStoreDialog() {
                if (this.disabledBind) {
                    if (this.authorizedEcList.find((item) => item.status === BindStatusEnum.BIND)?.goodsAccessStatus === 0) {
                        this.$alert({
                            type: 'warn',
                            title: '存在接入中的美团网店',
                            content: '门店存在接入中的美团网店，请删除后再新增网店',
                        });
                    } else {
                        this.$alert({
                            type: 'warn',
                            title: '美团网店已存在',
                            content: '门店已接入美团网店，请删除原网店后重新添加网店',
                        });
                    }
                    return;
                }
                const ecTypes = this.ecTypes.filter((ecType) => {
                    if (this.isO2OStore) {
                        return !this.authorizedEcList.some((item) => item.ecType === ecType.ecType);
                    }
                    return ecType.ecType === ECTypeEnum.PDD;
                });
                if (this.isO2OStore && ecTypes.length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '当前门店已经绑定所有可支持的O2O平台',
                    });
                    return;
                }

                new DialogEcTypes({
                    ecTypes,
                    shortId: this.currentClinic?.chain?.shortId,
                    bindClinic: this.currentClinic,
                    authCode: this.$route.query?.code,
                }).generateDialogAsync({ parent: this });
            },

            /**
             * @desc 美团续期
             * <AUTHOR> Yang
             * @date 2025-02-12 15:30:35
            */
            async handleMTRenewAuth(item) {
                await new DialogEcAuth({
                    authCode: this.authCode,
                    extMallId: item.extMallId,
                    mallId: item.ecMallId,
                    finishFunc: () => {
                        this.getAuthorizedEcList();
                    },
                }).generateDialogAsync({ parent: this });
            },

            async handleRenewAuth(item) {
                const {
                    ecMallId,
                    serviceMarketPageUrl,
                    purchaseInfo,
                    status,
                } = item;
                let authWindow;

                const onMessage = (message) => {
                    if (message.origin.indexOf('global') !== -1) {
                        if (message.data === 'ec_auth_success') {
                            this.$abcEventBus.$emit('ec-auth-success');
                            this.visible = false;
                            window.removeEventListener('message', onMessage);
                            authWindow.close();
                        }
                    }
                };

                window.addEventListener('message', onMessage);
                const redirectUri = encodeURIComponent(`https://${getTargetHost()}/ec-oauth-callback`);
                const state = encodeURIComponent(`${this.currentClinic?.chain?.shortId}@${location.hostname}@${ecMallId}`);
                let thirdUrl = 'https://fuwu.pinduoduo.com/service-market/auth';
                if (status !== 1 && purchaseInfo === null) {
                    thirdUrl = serviceMarketPageUrl || thirdUrl;
                }
                const mmsUrl = 'https://mms.pinduoduo.com';

                const url = `${thirdUrl}?response_type=code&client_id=${item.clientId}&redirect_uri=${redirectUri}&state=${state}`;
                if (window.remote?.session) {
                    try {
                        const mmsCookies = await window.remote.session.defaultSession.cookies.get({ url: mmsUrl });
                        for (const cookie of mmsCookies) {
                            await window.remote.session.defaultSession.cookies.remove(mmsUrl, cookie.name);
                        }
                    } catch (error) {
                        console.error(error);
                    }

                    try {
                        const cookies = await window.remote.session.defaultSession.cookies.get({ url: thirdUrl });
                        for (const cookie of cookies) {
                            await window.remote.session.defaultSession.cookies.remove(thirdUrl, cookie.name);
                        }
                    } catch (error) {
                        console.error(error);
                    }
                }
                const currentWidth = window.innerWidth;
                const currentHeight = window.innerHeight;

                const newWidth = Math.round(currentWidth * 0.8);
                const newHeight = Math.round(currentHeight * 0.8);

                authWindow = window.open(url, '_blank', `width=${newWidth},height=${newHeight}`);
                this._winLoop = setInterval(() => {
                    if (authWindow.closed) {
                        clearInterval(this._winLoop);
                        this.getAuthorizedEcList();
                    }
                }, 1000);
            },
            onChangeTab() {
                this.params.shopType = this.currentTab;
                // 重置其他查询参数
                this.params.ecType = '';
                this.params.bindClinicId = '';
                this.params.ecMallId = '';
                this.params.offset = 0;
                this.getAuthorizedEcList();
                this.getEcTypes();
            },
            // 检查当前路由是外卖还是电商
            checkCurrentRoute() {
                // 根据路由名称判断
                const routeName = this.$route.name;
                if (routeName === PharmacyOrderCloudRouterNameKeys.ecSettingsEcommerceOnlineStore) {
                    // 电商网店
                    this.currentTab = EcShopTypeEnum.B2C;
                } else if (routeName === PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOnlineStore) {
                    // 外卖网店
                    this.currentTab = EcShopTypeEnum.O2O;
                }
                this.onChangeTab();
            },
            handleSyncGoods(item) {
                new DialogEcBind({
                    active: 1,
                    bindClinic: this.currentClinic,
                    bindInfo: {
                        accountInfo: {
                            wmPoiName: item.mallName,
                            headImgUrl: item.mallLogo,
                        },
                        ecMallId: item.ecMallId,
                    },
                    isBind: true,
                }).generateDialogAsync({ parent: this });
            },
            async initCrawler() {
                const crawlerManager = OrderCloudDaemonService.getInstance().getCrawlerManager();
                if (crawlerManager) {
                    const authMallList = await crawlerManager.getAuthMallList();
                    const MTMall = authMallList.find((mall) => mall.shopType === EcShopTypeEnum.O2O && mall.ecType === ECTypeEnum.MT);
                    this.meituanService = crawlerManager.getTargetService(MTMall.extMallId);
                    this.meituanService.on('event', (event) => {
                        if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE) {
                            this.loading = false;
                            this.syncInfo = this.meituanService.syncInfo;
                            this._needTips = false;
                        }
                        if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED) {
                            this.loading = false;
                            if (!this._needTips) return;
                            // 显示失败提示
                            this.$alert({
                                type: 'error',
                                title: '同步商品失败',
                                content: '同步商品失败，可以重新尝试',
                            });
                            this._needTips = false;
                        }
                        // 同步进度
                        if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_INFO) {
                            this.isSyncing = true;
                            this.syncInfo = this.meituanService.syncInfo;
                        }
                    });

                    const { authStatus } = this.meituanService;
                    if (authStatus !== AuthStatus.AUTHED) return;
                    // 检查是否正在同步商品
                    this.syncInfo = this.meituanService.syncInfo;
                    this.isSyncing = this.meituanService.isSyncingProduct;
                }
            },
        },
    };
</script>
