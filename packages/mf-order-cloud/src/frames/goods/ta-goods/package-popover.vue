<template>
    <abc-popover
        placement="bottom-start"
        trigger="hover"
        theme="yellow"
        :visible-arrow="false"
        :disabled="!isPackage"
        :open-delay="500"
        style="width: 100%;"
        popper-class="ec-goods-detail__popover"
        :offset="-1"
        :popper-style="{
            padding: 0,
            margin: 0,
        }"
    >
        <div slot="reference">
            <slot></slot>
        </div>
        <abc-table
            :data-list="composeItems"
            :render-config="goodsTableConfig"
            style="border: none;"
            cell-size="xxxlarge"
            class="order-cloud-goods-list-table"
        >
            <template #goodsInfo="{ trData: row }">
                <abc-table-cell align="start" style="padding-top: 8px;">
                    <abc-flex style="width: 100%;" justify="space-between" gap="large">
                        <abc-flex gap="middle" style="flex: 1;">
                            <abc-image
                                :src="item?.imageUrl"
                                :width="56"
                                :height="56"
                            ></abc-image>
                            <abc-flex vertical :gap="2" style="flex: 1;">
                                <abc-text>
                                    {{ row.name }}
                                </abc-text>
                                <abc-space size="small">
                                    <abc-text size="mini" theme="gray">
                                        SKUID: {{ row.externalSkuId }}
                                    </abc-text>
                                    <abc-text size="mini" theme="gray">
                                        店内码/货号: {{ row.extSourceFoodCode || '-' }}
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </abc-flex>
                        <abc-text>
                            x {{ row?.count }}
                        </abc-text>
                    </abc-flex>
                </abc-table-cell>
            </template>
            <template #bindInfo="{ trData: row }">
                <abc-table-cell align="start" style="padding-top: 8px;">
                    <abc-flex justify="space-between" style="width: 100%;">
                        <abc-flex vertical :gap="2" style="flex: 1;">
                            <abc-flex>
                                <abc-text
                                    class="ellipsis"
                                    style="width: 100%;"
                                    :title="row?.relHisGoodsList[0]?.hisGoodsInfo?.displayName || '-'"
                                    :theme="isMeituanUnbindWarn(row?.warnFlag) ? 'warning' : ''"
                                >
                                    {{ row?.relHisGoodsList[0]?.hisGoodsInfo?.displayName || '-' }}
                                </abc-text>
                                <abc-tag-v2 v-if="isMeituanUnbindWarn(row?.warnFlag)" variant="ghost" size="mini">
                                    停用
                                </abc-tag-v2>
                            </abc-flex>
                            <abc-space v-if="row && row.relHisGoodsList[0] && row.relHisGoodsList[0].hisGoodsInfo">
                                <abc-text size="mini" theme="gray">
                                    {{ row.relHisGoodsList[0].hisGoodsInfo.displaySpec || '' }}
                                </abc-text>
                                <abc-text size="mini" theme="gray">
                                    {{ row.relHisGoodsList[0].hisGoodsInfo.manufacturer || '' }}
                                </abc-text>
                                <abc-text size="mini" theme="gray">
                                    {{ row.relHisGoodsList[0].hisGoodsInfo.shortId || '' }}
                                </abc-text>
                            </abc-space>
                        </abc-flex>
                        <abc-text v-if="row?.relHisGoodsList[0]?.useDismounting" tag="div" style="height: 56px; text-align: center;">
                            {{ row?.relHisGoodsList[0]?.useDismounting ?
                                `x ${ row?.relHisGoodsList[0]?.bindPieceCount }` :
                                `x ${ row?.relHisGoodsList[0]?.bindPackageCount }` }}{{ row?.relHisGoodsList[0]?.unit }}
                        </abc-text>
                        <abc-text v-else tag="div" style="height: 56px; text-align: center;">
                            -
                        </abc-text>
                    </abc-flex>
                </abc-table-cell>
            </template>
            <template #stockCount="{ trData: row }">
                <abc-table-cell align="start" style="padding-top: 8px;">
                    {{ row?.relHisGoodsList[0]?.useDismounting ?
                        row?.relHisGoodsList[0]?.stockPieceCount || '-' :
                        row?.relHisGoodsList[0]?.stockPackageCount || '' }}{{ row?.relHisGoodsList[0]?.unit || '-' }}
                </abc-table-cell>
            </template>
        </abc-table>
    </abc-popover>
</template>
<script>
    import {
        MeituanComposeType,isMeituanUnbindWarn,
    } from './constants.js';
    export default {
        name: 'GoodsPackagePopover',
        props: {
            item: {
                type: Object,
                default: () => ({}),
            },
            skuList: {
                type: Array,
                default: () => [],
            },
        },
        computed: {
            isPackage() {
                return this.skuList.filter((s) => s.composeType === MeituanComposeType.COMPOSE_PACKAGE).length > 0;
            },
            composeItems() {
                return this.skuList[0]?.composeItems || [];
            },
            goodsTableConfig() {
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'goodsInfo',
                            label: '组包包含的单品',
                            style: {
                                flex: 'none',
                                width: '400px',
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                            headerStyle: {
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                        },
                        {
                            key: 'bindInfo',
                            label: '绑定的ABC系统商品',
                            style: {
                                flex: 'none',
                                width: '300px',
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                            headerStyle: {
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                        },
                        {
                            key: 'stockCount',
                            label: '可售库存',
                            style: {
                                flex: 'none',
                                width: '100px',
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                                textAlign: 'right',
                            },
                            headerStyle: {
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                        },
                    ],
                };
            },
        },
        methods: {
            isMeituanUnbindWarn,
        },
    };
</script>
<style lang="scss">
.ec-goods-detail__popover {
    .bind-goods-name-popover {
        padding: 16px;

        &__header {
            display: grid;
            grid-template-columns: 341px 124px 156px 44px 80px;
        }

        &__content {
            display: grid;
            grid-template-columns: 341px 124px 156px 44px 80px;
            align-items: center;
            justify-content: flex-start;
            margin-top: 4px;

            &__name-img {
                display: flex;

                img {
                    flex-shrink: 0;
                }
            }

            &__available-stock {
                height: 56px;
                text-align: right;
            }
        }
    }

    .order-cloud-goods-list-table .abc-table-header {
        border-color: var(--abc-color-LY1) !important;
    }
}
</style>
