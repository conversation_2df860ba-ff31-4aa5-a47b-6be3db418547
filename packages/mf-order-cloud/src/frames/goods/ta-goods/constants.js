export const MeituanComposeType = Object.freeze({
    SINGLE: 0,
    COMPOSE_PACKAGE: 1,
    COMPOSE_SUB_PACKAGE: 2,
});

export const MeituanWarnFlag = Object.freeze({
    UNBOUND: 1,
    STOCK_WARN: 2,
    BOUND_WARN: 4,

    UNBOUND_STOCK_WARN: 3,
    UNBOUND_BOUND_WARN: 5,
    STOCK_BOUND_WARN: 6,
    UNBOUND_STOCK_WARN_BOUND_WARN: 7,
});

export const isMeituanUnbindWarn = (warnFlag) => {
    return warnFlag === MeituanWarnFlag.BOUND_WARN ||
        warnFlag === MeituanWarnFlag.UNBOUND_BOUND_WARN ||
        warnFlag === MeituanWarnFlag.STOCK_BOUND_WARN ||
        warnFlag === MeituanWarnFlag.UNBOUND_STOCK_WARN_BOUND_WARN;
};