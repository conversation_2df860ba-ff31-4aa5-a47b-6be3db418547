import BaseProTable from 'MfBase/base-pro-table';

export default class OutRecordTable extends BaseProTable {
    name = 'OutRecordTable';
    static staticConfig = {
        hasInnerBorder: true,
        list: [{
            'key': 'time',
            'label': '时间',
            'isCheckbox': false,
            slot: true,
            'style': {
                'flex': '1','width': '160px','minWidth': '160px', 'maxWidth': '160px', 'paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },{
            'key': 'type',
            'label': '类型',
            'testValue': '',
            'isCheckbox': false,
            slot: true,
            'style': {
                'flex': '1','width': '88px','minWidth': '88px', 'maxWidth': '88px', 'paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        },{
            'key': 'goods',
            'label': 'ABC出库商品',
            'isCheckbox': false,
            slot: true,
            'style': {
                'flex': '1','width': '280px','maxWidth': '280px', 'paddingLeft': '', 'paddingRight': '','textAlign': 'left',
            },
        },{
            'key': 'count',
            'label': '数量',
            'isCheckbox': false,
            'colType': 'text',
            slot: true,
            'style': {
                'flex': '1','width': '64px','minWidth': '64px', 'maxWidth': '64px', 'paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
        },{
            'key': 'batchNo',
            'label': '批号',
            'testValue': '',
            'isCheckbox': false,
            slot: true,
            'style': {
                'flex': '1','width': '124px','maxWidth': '124px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },{
            'key': 'order',
            'label': '关联订单',
            'testValue': '',
            'isCheckbox': false,
            'colType': 'text',
            slot: true,
            'style': {
                'flex': '1','width': '426px','maxWidth': '456px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },{
            'key': 'sku',
            'label': '关联订单SKU',
            'testValue': '222',
            'isCheckbox': false,
            slot: true,
            'style': {
                'flex': '1','width': '360px','maxWidth': '360px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },
        {
            'key': 'operation',
            'label': '操作',
            'testValue': '',
            'isCheckbox': false,
            'colType': 'text',
            slot: true,
            // pinned: 'right',
            'style': {
                'flex': '1','width': '160px','maxWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
        }],
    };
}
