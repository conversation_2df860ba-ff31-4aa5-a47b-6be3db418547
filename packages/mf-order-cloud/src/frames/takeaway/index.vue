<template>
    <abc-card class="content-card-wrapper pharmacy__ec-goods-table-section" :border="false" radius-size="none">
        <abc-flex
            v-if="!installChromeStatus"
            align="center"
            justify="center"
            vertical
            style="margin-top: 200px;"
        >
            <img
                class="install-chrome-img"
                src="~assets/images/setting/install-icon.png"
                width="450"
                style="width: 450px;"
                alt="install"
            />
            <abc-text size="xxlarge" bold style="margin-top: 40px;">
                未安装订单云外卖
            </abc-text>
            <abc-text size="large" style="margin-top: 16px;">
                本机尚未安装订单云外卖应用，请安装后使用
            </abc-text>
            <abc-button style="margin-top: 40px;" size="large" @click="handleInstallChrome">
                立即安装
            </abc-button>
        </abc-flex>
        <template v-else>
            <abc-tabs-v2
                v-model="currentTab"
                :option="tabOptions"
                size="large"
                :item-min-width="64"
                :border-style="{ borderBottom: 'none' }"
                class="content-card-tabs"
                @change="handleTabChange"
            ></abc-tabs-v2>
            <abc-divider margin="none" style="width: auto; min-width: auto; margin: 0 20px;"></abc-divider>
            <router-view></router-view>
        </template>

        <dialog-install-status
            v-if="showInstallChrome"
            v-model="showInstallChrome"
            @install-success="handleInstallSuccess"
        ></dialog-install-status>
    </abc-card>
</template>

<script>
    import {
        OrderCloudModuleId, PharmacyOrderCloudRouterNameKeys,
    } from '../../core/routes';
    import { mapGetters } from 'vuex';
    import { isClientSupportPharmacy } from '@/utils/electron';
    import DialogDownloadClient from '@/components/dialog-download-client';
    const DialogInstallStatus = () => import('@/components/dialog-install-status/index.vue');

    export default {
        name: 'OrderCloudTakeaway',
        components: { DialogInstallStatus },
        inject: {
            eCOrderStat: {
                default: () => ({
                    totalMtTodoCount: 0,
                    waitingOutStockCount: 0,
                    afterSaleWaitingDealCount: 0,
                }),
            },
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                currentTab: '',
                showInstallChrome: false,
                installChromeStatus: false,
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
            ]),
            tabOptions() {
                const options = [];
                // 工作台选项
                if (this.hasTakeawayAllModule || this.moduleArr.includes(OrderCloudModuleId.takeawayOrder)) {
                    options.push({
                        label: '工作台',
                        value: PharmacyOrderCloudRouterNameKeys.takeawayOrder,
                        moduleId: OrderCloudModuleId.takeawayOrder,
                        maxNoticeNumber: 99,
                        noticeNumber: this.eCOrderStat.totalMtTodoCount || 0,
                        separation: false,
                    });
                }

                // 订单出库
                if (this.hasTakeawayAllModule || this.moduleArr.includes(OrderCloudModuleId.takeawayOutStorage)) {
                    options.push({
                        label: '订单出库',
                        value: PharmacyOrderCloudRouterNameKeys.takeawayOutStorage,
                        moduleId: OrderCloudModuleId.takeawayOutStorage,
                        maxNoticeNumber: 99,
                        noticeNumber: this.eCOrderStat.waitingOutStockCount || 0,
                    });
                }

                // 订单退库
                if (this.hasTakeawayAllModule || this.moduleArr.includes(OrderCloudModuleId.takeawayRefundStorage)) {
                    options.push({
                        label: '订单退库',
                        value: PharmacyOrderCloudRouterNameKeys.takeawayRefundStorage,
                        moduleId: OrderCloudModuleId.takeawayRefundStorage,
                        maxNoticeNumber: 99,
                        noticeNumber: this.eCOrderStat.afterSaleWaitingDealCount || 0,
                    });
                }

                // 商品选项
                if (this.hasTakeawayAllModule || this.moduleArr.includes(OrderCloudModuleId.takeawayMtGoods)) {
                    options.push({
                        label: '商品管理',
                        value: PharmacyOrderCloudRouterNameKeys.takeawayMtGoods,
                        moduleId: OrderCloudModuleId.takeawayMtGoods,
                        separation: true,
                    });
                }

                // 订单统计
                if (this.hasTakeawayAllModule || this.moduleArr.includes(OrderCloudModuleId.takeawayOrderStatistics)) {
                    options.push({
                        label: '订单统计',
                        value: PharmacyOrderCloudRouterNameKeys.takeawayOrderStatistics,
                        moduleId: OrderCloudModuleId.takeawayOrderStatistics,
                    });
                }

                return options;
            },
            moduleIds() {
                return (this.userInfo && this.userInfo.moduleIds) || '';
            },
            moduleArr() {
                if (!this.moduleIds) {
                    return [];
                }
                return this.moduleIds.split(',');
            },
            hasTakeawayAllModule() {
                if (!this.moduleIds) {
                    return false;
                }
                return this.moduleIds === '0' || this.moduleArr.includes(OrderCloudModuleId.main) || this.moduleArr.includes(OrderCloudModuleId.takeaway);
            },
        },
        created() {
            this.initRouteAndTab();
            this.installChromeStatus = this.$abcPage.$store.installChromeStatus;
        },
        methods: {
            handleInstallChrome() {
                if (window.localStorage.getItem('mockInstallChrome')) {
                    this.$abcPage.$store.updateInstallChromeStatus(true);
                    this.installChromeStatus = true;
                    return;
                }
                if (!isClientSupportPharmacy()) {
                    new DialogDownloadClient().generateDialogAsync();
                    return;
                }
                this.showInstallChrome = true;
            },
            handleInstallSuccess() {
                this.$abcPage.$store.updateInstallChromeStatus(true);
                this.installChromeStatus = true;
            },
            initRouteAndTab() {
                const currentRouteInTabs = this.tabOptions.some((tab) => tab.value === this.$route.name);

                // 如果当前路由不在 tabOptions 中，则跳转到第一个可用的选项卡
                if (!currentRouteInTabs) {
                    const availableTab = this.tabOptions[0];

                    if (availableTab) {
                        // 跳转到第一个可用的选项卡
                        this.$router.push({
                            name: availableTab.value,
                        });
                        this.currentTab = availableTab.value;
                    }
                } else {
                    // 如果当前路由在 tabOptions 中，则更新当前选中的选项卡
                    this.currentTab = this.$route.name;
                }
            },
            handleTabChange(name) {
                this.$router.push({
                    name,
                });
            },
        },
    };
</script>
