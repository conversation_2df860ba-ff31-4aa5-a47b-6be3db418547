<template>
    <abc-popover
        placement="bottom-start"
        trigger="click"
        :visible-arrow="false"
        theme="yellow"
        padding-size="large"
        style="height: 100%;"
        size="large"
    >
        <abc-table-cell
            slot="reference"
            class="ecommerce-table-cell"
            vertical
            align="flex-start"
            justify="center"
            clickable
        >
            <abc-flex vertical gap="8">
                <div
                    v-for="(item, index) in goodsList"
                    :key="`${item.goodsId }_${ index}`"
                    :title="item.goodsName"
                >
                    ×{{ item.goodsCount }} <span v-if="item.isGift" class="danger" style="margin-right: 2px;">[赠品]</span>{{ item.goodsName }} <abc-text>
                        {{ item.goodsSpec || '' }}
                    </abc-text>
                </div>
            </abc-flex>
        </abc-table-cell>

        <div id="table-cell-goods-popover">
            <h5>订单SKU</h5>
            <abc-flex
                v-for="(item, index) in goodsList"
                :key="`${item.goodsId }_${ index}`"
                align="center"
                class="goods-item"
            >
                <abc-image
                    style="border-radius: var(--abc-border-radius-small);"
                    :src="item.goodsImg"
                    :width="44"
                    :height="44"
                ></abc-image>
                <abc-flex class="count" align="center" justify="center">
                    ×{{ item.goodsCount }}
                </abc-flex>
                <div class="goods-name">
                    <span v-if="item.isGift" class="danger">[赠品]</span>{{ item.goodsName }} {{ item.goodsSpec || '' }}
                </div>
            </abc-flex>
        </div>
    </abc-popover>
</template>

<script>
    export default {
        props: {
            goodsList: {
                type: Array,
                required: true,
            },
        },
        data() {
            return {
            };
        },
        computed: {
            otherTotalCount() {
                const res = this.goodsList.reduce((total, it) => {
                    return total + +it.goodsCount;
                }, 0);
                return res - this.goodsList[0].goodsCount;
            },
            hasGift() {
                return this.goodsList.some((it) => it.isGift);
            },
        },
    };
</script>

<style lang="scss">
@import "../styles/theme.scss";
@import "../styles/mixin.scss";

#table-cell-goods-popover {
    width: 332px;
    max-height: 280px;

    h5 {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: bold;
        line-height: 22px; /* 157.143% */
        color: var(--abc-color-T1, #000000);
    }

    .goods-item {
        min-height: 44px;

        .count {
            width: 44px;
        }

        & + .goods-item {
            margin-top: 12px;
        }

        .goods-name {
            flex: 1;
            width: 0;
        }
    }

    .danger {
        color: var(--abc-color-R6);
    }
}
</style>
