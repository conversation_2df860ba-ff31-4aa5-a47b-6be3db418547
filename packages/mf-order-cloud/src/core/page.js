import { BasePage } from 'MfBase/core';
import PageStore from './store.js';
import ECAuthAPI from '../api/auth';

export default class PharmacyOrderCloudPage extends BasePage {
    name = '@PharmacyOrderCloud';

    constructor() {
        super({
            store: new PageStore(),
        });
        this.instanceKey = this.name;
    }

    fetchPddIsvPageCode(params) {
        return ECAuthAPI.fetchPddIsvPageCode(params);
    }

    async pddFingerprint(data) {
        if (!data.ecMallIds?.length) {
            console.log('初始化拼多多SDK失败: ecMallIds 不能为空');
            return;
        }
        const res = await this.getPatiPageCode(data);
        return ECAuthAPI.pddFingerprint({
            ...data,
            ...res,
        });
    }

    /**
     * 初始化拼多多SDK并使用
     * @param {Object} options
     * @returns {Promise}
     */
    async getPatiPageCode(data = {}) {
        if (!data.ecMallIds) {
            console.log('初始化拼多多SDK失败: ecMallIds 不能为空');
            return;
        }
        if (!window.PDD_OPEN_init) {
            console.log('初始化拼多多SDK失败: SDK 未加载');
            return;
        }
        const pageCode = await this.fetchPddIsvPageCode(data); //获取 pagecode，可选
        return new Promise((resolve) => {
            window.PDD_OPEN_init({
                code: pageCode,
                // 对于获取 code 接口或未登录态，可不传 code：PDD_OPEN_init({}, function () { ... })
            }, () => {
                // 初始化已完成
                window.PDD_OPEN_getPati().then((pati) => {
                    resolve({
                        pati,
                        pageCode,
                    });
                }).catch((error) => {
                    console.error('获取拼多多SDK失败:', error);
                    resolve();
                });
            });
        });
    }
}
