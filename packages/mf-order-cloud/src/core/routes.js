import Index from '../index.vue';
import { RouterScope } from 'MfBase/constants';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const Takeaway = () => import('../frames/takeaway/index.vue');
const TakeawayOrder = () => import('../frames/takeaway/order.vue');
const OutStorage = () => import('../frames/takeaway/out-storage.vue');
const RefundStorage = () => import('../frames/takeaway/refund-storage.vue');
const OrderStatistics = () => import('../frames/takeaway/order-statistics.vue');
const TakeawayMtGoods = () => import('../frames/goods/ta-goods/mt-goods.vue');
const Ecommerce = () => import('../frames/ecommerce/index.vue');
const EcommerceOrder = () => import('../frames/ecommerce/order.vue');
const EcommerceEcGoods = () => import('../frames/goods/ec-goods/ec-goods.vue');
const OnlineStore = () => import('../frames/settings/online-store.vue');
const ServiceIntro = () => import('../frames/settings/service-intro.vue');
const OutRecord = () => import('@/frames/ecommerce/out-record/index.vue');
const SettingsIndex = () => import('../frames/settings/index.vue');
const SettingsPrint = () => import('../frames/settings/waybill-print-setting.vue');
const SettingsDeliver = () => import('../frames/settings/deliver.vue');
const SettingsOrderAssistant = () => import('../frames/settings/order-assistant.vue');
const ECStatistics = () => import('../frames/ec-statistics/ec-statistics.vue');
const SettingsOrderReminder = () => import('../frames/settings/order-reminder.vue');

// 外部跳转都应该用该字段
export const PharmacyOrderCloudRouterNameKeys = {
    index: '@PharmacyOrderCloud',
    takeaway: '@PharmacyOrderCloudTakeaway',
    takeawayOrder: '@PharmacyOrderCloudTakeawayOrder',
    takeawayOutStorage: '@PharmacyOrderCloudTakeawayOutStorage',
    takeawayRefundStorage: '@PharmacyOrderCloudTakeawayRefundStorage',
    takeawayMtGoods: '@PharmacyOrderCloudTakeawayMtGoods',
    takeawayOnlineStore: '@PharmacyOrderCloudTakeawayOnlineStore',
    takeawayOrderStatistics: '@PharmacyOrderCloudTakeawayOrderStatistics',
    ecommerce: '@PharmacyOrderCloudEcommerce',
    ecommerceOrder: '@PharmacyOrderCloudEcommerceOrder',
    ecommerceEcGoods: '@PharmacyOrderCloudEcommerceEcGoods',
    ecommerceUnPrint: '@PharmacyOrderCloudEcommerceUnPrint',
    ecommerceOnlineStore: '@PharmacyOrderCloudEcommerceOnlineStore',
    ecommerceOutRecord: '@PharmacyOrderCloudEcommerceOutRecord',
    ecommerceEcStatistics: '@PharmacyOrderCloudEcommerceECStatistics',
    serviceIntro: '@PharmacyOrderCloudServiceIntro',
    goods: '@PharmacyOrderCloudGoods',
    ecSettings: '@PharmacyOrderCloudSettings',
    ecSettingsPrint: '@PharmacyOrderCloudSettingsPrint',
    ecSettingsShipPrint: '@PharmacyOrderCloudSettingsShipPrint',
    ecSettingsDeliver: '@PharmacyOrderCloudSettingsDeliver',
    ecSettingDesktopAssistant: '@PharmacyOrderCloudSettingsOrderAssistant',
    ecSettingsTakeawayOut: '@PharmacyOrderCloudSettingsTakeawayOut',
    ecSettingsServiceIntro: '@PharmacyOrderCloudSettingsServiceIntro',
    ecSettingsTakeawayOnlineStore: '@PharmacyOrderCloudSettingsTakeawayOnlineStore',
    ecSettingsEcommerceOnlineStore: '@PharmacyOrderCloudSettingsEcommerceOnlineStore',
    ecSettingsEcommerceOut: '@PharmacyOrderCloudSettingsEcommerceOut',
    ecSettingOrderReminder: '@PharmacyOrderCloudSettingsOrderReminder',
};

export const OrderCloudModuleId = {
    main: '710',

    takeaway: '71008', // 外卖
    takeawayOrder: '7100801', // 外卖-工作台
    takeawayOutStorage: '71007', // 外卖-订单出库
    takeawayRefundStorage: '7100802', // 外卖-订单退库
    takeawayMtGoods: '7100806', // 外卖-商品
    takeawayOrderStatistics: '7100807', // 外卖-订单统计
    // takeawayOnlineStore: '7100803', // 外卖-网店

    ecommerce: '71009', // 电商
    ecommerceOrder: '71001', // 电商-订单
    ecommerceOutRecord: '71002', // 电商-出库明细
    ecommerceEcStatistics: '71003', // 电商-营收统计
    ecommerceEcGoods: '71004', // 电商-商品
    // ecommerceOnlineStore: '71005', // 电商-网店

    setting: '71006', // 订单云设置
};
export default {
    path: 'order-cloud',
    name: PharmacyOrderCloudRouterNameKeys.index,
    component: Index,
    meta: {
        name: '订单云',
        needAuth: true,
        moduleId: OrderCloudModuleId.main,
        pageAsyncClass: PageAsync,
        icon: 's-cloud-color',
        selectedIcon: 's-cloud-color',
        scope: RouterScope.CHAIN_ADMIN | RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
    },
    redirect: { name: PharmacyOrderCloudRouterNameKeys.takeaway },
    children: [
        {
            path: 'takeaway',
            component: Takeaway,
            name: PharmacyOrderCloudRouterNameKeys.takeaway,
            meta: {
                name: '外卖',
                moduleId: OrderCloudModuleId.takeaway,
                needAuth: true,
            },
            children: [
                {
                    path: 'order',
                    name: PharmacyOrderCloudRouterNameKeys.takeawayOrder,
                    component: TakeawayOrder,
                    meta: {
                        name: '工作台',
                        needAuth: true,
                        moduleId: OrderCloudModuleId.takeawayOrder,
                    },
                },
                {
                    path: 'out-storage',
                    component: OutStorage,
                    name: PharmacyOrderCloudRouterNameKeys.takeawayOutStorage,
                    meta: {
                        name: '订单出库',
                        moduleId: OrderCloudModuleId.takeawayOutStorage,
                        needAuth: true,
                    },
                },
                {
                    path: 'refund-storage',
                    component: RefundStorage,
                    name: PharmacyOrderCloudRouterNameKeys.takeawayRefundStorage,
                    meta: {
                        name: '订单退库',
                        moduleId: OrderCloudModuleId.takeawayRefundStorage,
                        needAuth: true,
                    },
                },
                {
                    path: 'mt-goods',
                    name: PharmacyOrderCloudRouterNameKeys.takeawayMtGoods,
                    component: TakeawayMtGoods,
                    meta: {
                        name: '商品管理',
                        needAuth: true,
                        moduleId: OrderCloudModuleId.takeawayMtGoods,
                    },
                },
                {
                    path: 'order-statistics',
                    name: PharmacyOrderCloudRouterNameKeys.takeawayOrderStatistics,
                    component: OrderStatistics,
                    meta: {
                        name: '订单统计',
                        needAuth: true,
                        moduleId: OrderCloudModuleId.takeawayOrderStatistics,
                    },
                },
            ],
        },
        {
            path: 'ecommerce',
            component: Ecommerce,
            name: PharmacyOrderCloudRouterNameKeys.ecommerce,
            meta: {
                name: '电商',
                moduleId: OrderCloudModuleId.ecommerce,
                needAuth: true,
            },
            children: [
                {
                    path: 'order',
                    name: PharmacyOrderCloudRouterNameKeys.ecommerceOrder,
                    component: EcommerceOrder,
                    meta: {
                        name: '订单管理',
                        needAuth: true,
                        moduleId: OrderCloudModuleId.ecommerceOrder,
                    },
                },
                {
                    path: 'out-record',
                    component: OutRecord,
                    name: PharmacyOrderCloudRouterNameKeys.ecommerceOutRecord,
                    meta: {
                        name: '出库记录',
                        moduleId: OrderCloudModuleId.ecommerceOutRecord,
                        needAuth: true,
                    },
                },
                {
                    path: 'ec-goods',
                    name: PharmacyOrderCloudRouterNameKeys.ecommerceEcGoods,
                    component: EcommerceEcGoods,
                    meta: {
                        name: '商品管理',
                        needAuth: true,
                        moduleId: OrderCloudModuleId.ecommerceEcGoods,
                    },
                },
                {
                    path: 'ec-statistics',
                    name: PharmacyOrderCloudRouterNameKeys.ecommerceEcStatistics,
                    component: ECStatistics,
                    meta: {
                        name: '营收统计',
                        moduleId: OrderCloudModuleId.ecommerceEcStatistics,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'setting',
            component: SettingsIndex,
            name: PharmacyOrderCloudRouterNameKeys.ecSettings,
            meta: {
                name: '订单云设置',
                needAuth: false,
                moduleId: OrderCloudModuleId.setting,
                // scope: RouterScope.SINGLE_STORE | RouterScope.CHAIN_SUB,
            },
            children: [
                {
                    path: 'service-intro',
                    component: ServiceIntro,
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsServiceIntro,
                    meta: {
                        name: '开通与安装',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'takeaway-online-store',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOnlineStore,
                    component: OnlineStore,
                    meta: {
                        name: '绑定外卖网店',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'ecommerce-online-store',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsEcommerceOnlineStore,
                    component: OnlineStore,
                    meta: {
                        name: '绑定电商网店',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'takeaway-out',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOut,
                    component: () => import('../frames/settings/takeaway-out.vue'),
                    meta: {
                        name: '外卖出库',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'deliver',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsDeliver,
                    component: SettingsDeliver,
                    meta: {
                        name: '打单发货',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'waybill-print-setting',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsPrint,
                    component: SettingsPrint,
                    meta: {
                        name: '快递面单',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'ship-print-setting',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsShipPrint,
                    component: () => import('../frames/settings/ship-print-setting.vue'),
                    meta: {
                        name: '打印发货单',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'order-assistant',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingDesktopAssistant,
                    component: SettingsOrderAssistant,
                    meta: {
                        name: '订单助手',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'ecommerce-out',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsEcommerceOut,
                    component: SettingsDeliver,
                    meta: {
                        name: '电商出库',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
                {
                    path: 'order-reminder',
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingOrderReminder,
                    component: SettingsOrderReminder,
                    meta: {
                        name: '订单提醒',
                        needAuth: false,
                        moduleId: OrderCloudModuleId.setting,
                    },
                },
            ],
        },
    ],
};
