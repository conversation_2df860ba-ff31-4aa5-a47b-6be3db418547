import { BasePageStore } from 'MfBase/core';
import PropertyAPI from 'MfBase/property-api';
import OrderCloudDaemonService from '../daemon/order-cloud-daemon-service';
import ECOrderAPI from '../api/order';

/**
* @desc 页面局部store，伴随page生命周期
*/
export default class PharmacyOrderCloudPageStore extends BasePageStore {
    constructor() {
        const namespace = '@PharmacyOrderCloud';
        const state = {
            orderCloudConfig: {
                enable: 0,
                settings: {
                    orderAssistantSetting: {
                        voiceNewOrder: 0,
                        voiceAfterSale: 0,
                        isEnableDesktopAssistant: 0,
                    },
                    printOrderSetting: {
                        autoMergeOrder: 0,
                        inventoryWarnDay: 30,
                    },
                },
                o2oSettings: {
                    autoStockOut: 0,
                    inventoryWarnDay: 30,
                },
            },
            orderCloudConfigInit: false,

            shipPrintConfig: {
                mallName: '',
                receiverAddressEnable: 1,
                receiverNameEnable: 1,
                receiverPhoneEnable: 1,
                sellerAddress: {},
                sellerPhone: '',
            },
            shipPrintConfigInit: false,
            installChromeStatus: false,
        };
        super(namespace, state);
    }

    get orderCloudConfig() {
        return this.state.orderCloudConfig.settings;
    }

    get o2oSettings() {
        return this.state.orderCloudConfig.o2oSettings;
    }

    get orderCloudConfigInit() {
        return this.state.orderCloudConfigInit;
    }

    get shipPrintConfig() {
        return this.state.shipPrintConfig;
    }

    get installChromeStatus() {
        return this.state.installChromeStatus;
    }

    updateInstallChromeStatus(val) {
        this.state.installChromeStatus = val;
    }

    // 获取订单云设置参数
    async getOrderCloudSetting() {
        const { data } = await PropertyAPI.get('orderCloud', 'clinic', 'v3');
        this.state.orderCloudConfig = data;
        // 设置桌面助手状态
        const desktopAssistManager = OrderCloudDaemonService
            .getInstance()
            .getDesktopAssistManager();
        if (desktopAssistManager) {
            this.state
                .orderCloudConfig
                .settings
                .orderAssistantSetting
                .isEnableDesktopAssistant = desktopAssistManager.getPreferenceEnable();
        }

    }

    // 设置订单云打印参数
    async updatePrintOrderSetting(data) {
        try {
            await PropertyAPI.update('orderCloud.settings', 'clinic', data, 'v3');
            this.state.orderCloudConfig.settings.printOrderSetting = data.printOrderSetting;
        } catch (e) {
            console.error('更新订单云打单发货设置异常',e);
        }
    }

    // 设置订单云订单助手参数
    async updateOrderAssistantSetting(data) {
        try {
            await PropertyAPI.update('orderCloud.settings', 'clinic', data, 'v3');
            this.state.orderCloudConfig.settings.orderAssistantSetting = data.orderAssistantSetting;
            OrderCloudDaemonService
                .getInstance()
                .getEcOrderWatcherManager()
                .setVoiceSetting(this.state.orderCloudConfig.settings.orderAssistantSetting.voiceNewOrder,
                    this.state.orderCloudConfig.settings.orderAssistantSetting.voiceAfterSale);
        } catch (e) {
            console.error('更新订单云打单发货设置异常',e);
        }
    }

    // 设置订单云订单助手开启状态
    async updateOrderAssistantEnabledStatus(isEnableDesktopAssistant) {
        try {
            await PropertyAPI.update('orderCloud.settings.orderAssistantSetting', 'clinic', {
                isEnableDesktopAssistant,
            }, 'v3');
            this.state.orderCloudConfig.settings.orderAssistantSetting.isEnableDesktopAssistant = isEnableDesktopAssistant;
        } catch (e) {
            console.error('更新订单云桌面助手设置状态异常',e);
        }
    }

    // 设置订单云外卖出库
    async updateTakeawayOutSetting(data) {
        try {
            await PropertyAPI.update('orderCloud.o2oSettings', 'clinic', data, 'v3');
            this.state.orderCloudConfig.o2oSettings = data;
        } catch (e) {
            console.error('更新订单云外卖出库设置异常',e);
        }
    }

    async init() {
        await this.initChromePuppeteerConfig();
        await this.InitOrderCloudConfig();
        this.initShipPrintConfig();
    }

    async initChromePuppeteerConfig() {
        this.state.installChromeStatus = await OrderCloudDaemonService.getInstance().chromePuppeteerManager?.isChromiumInstalledWin();
    }

    async InitOrderCloudConfig() {
        if (this.state.orderCloudConfigInit) {
            return;
        }
        await this.getOrderCloudSetting(); // 非异步会被路由切换打断
        this.state.orderCloudConfigInit = true;
    }
    async initShipPrintConfig() {
        if (this.state.shipPrintConfigInit) return;
        await this.fetchShipPrintConfig();
        this.state.shipPrintConfigInit = true;
    }
    async fetchShipPrintConfig() {
        const res = await ECOrderAPI.fetchShipPrintConfig();
        this.state.shipPrintConfig = res;
    }
}
