import BaseAPI from 'MfBase/base-api';
export default class ECWayBillAPI extends BaseAPI {

    static async fetchEcWaybillInfo(params) {
        const res = await this.get('/api/ec/waybill/info', {
            params,
        });
        return res;
    }

    static async fetchEcWaybillTemplate(params) {
        const res = await this.get('/api/ec/waybill/templates/list', {
            params,
        });
        return res;
    }

    static async fetchEcWaybillStandardTemplates(params) {
        const res = await this.get('/api/ec/waybill/templates/type-list', {
            params,
        });
        return res;
    }

    static async createEcWaybillTemplate(data) {
        const res = await this.post('/api/ec/waybill/templates/add', data);
        return res;
    }
    static async updateEcWaybillTemplate(data) {
        const res = await this.put('/api/ec/waybill/templates/update', data);
        return res;
    }
    static async deleteEcWaybillTemplate(params) {
        const res = await this.del('/api/ec/waybill/templates/delete', {
            params,
        });
        return res;
    }
}
