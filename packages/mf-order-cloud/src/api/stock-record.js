import BaseAPI from 'MfBase/base-api';
export default class ECStockRecordAPI extends BaseAPI {
    /**
     * @desc 查询商品出库记录
     * <AUTHOR>
     * @date 2024/6/3 13:44
     */
    static async fetchOutRecordList(params) {
        const res = await this.get('/api/ec/goods/stock/record/list', {
            params,
        });
        return res;
    }

    // 发药出库
    /**
     * @desc 发药出库
     * <AUTHOR>
     * @date 2024-06-04 10:49:38
     * @param {string} recordId -
     * @param {object} data -
    */
    static async dispense(recordId, data) {
        const res = await this.put(`/api/ec/goods/stock/record/${recordId}/dispense`, data);
        return res;
    }

    /**
     * @desc 退药入库
     * <AUTHOR>
     * @date 2024-06-04 10:48:58
     * @param {string} recordId -
     * @param {object} data -
    */
    static async unDispense(recordId, data) {
        const res = await this.put(`/api/ec/goods/stock/record/${recordId}/undispense`, data);
        return res;
    }

    /**
     * Queries the details of a product inventory record.
     *
     * @param {string} recordId - The ID of the inventory record to retrieve details for.
     * @returns {Promise} - A Promise that resolves to the response object from the API.
     */
    static async getRecordDetail(recordId) {
        const res = await this.get(`/api/ec/goods/stock/record/${recordId}/detail`);
        return res;
    }

    /**
     * @desc 获取商品库存待同步列表
     * <AUTHOR>
     * @date 2025-07-15 11:41:58
     */
    static async getWaitSyncList(params) {
        const res = await this.get('/api/ec/mt/sku-stock/wait-sync', {
            params,
        });
        return res;
    }
    /**
     * @desc 修改商品库存同步标识
     * <AUTHOR>
     * @date 2025-07-15 11:41:58
     */
    static async updateWaitSyncFlag(data) {
        const res = await this.put('/api/ec/mt/sku-stock/sync-status', data);
        return res;
    }
}
