import BaseAPI, { fetch } from 'MfBase/base-api';

export default class ECAuthAPI extends BaseAPI {
    static async fetchAuthorizedEcList(params) {
        const res = await this.get('/api/ec/auth/list', {
            params,
        });
        return res;
    }
    static async fetchEcTypes(params) {
        const res = await this.get('/api/ec/types', {
            params,
        });
        return res;
    }
    static async authBind(params) {
        const res = await this.post('/api/ec/auth/bind', {
            ...params,
        });
        return res;
    }
    static async authEc(params) {
        const res = await this.post('/api/ec/auth', {
            ...params,
        });
        return res;
    }

    // 授权续期
    static async continueAuthEc(ecMallId, params) {
        const res = await this.post(`/api/ec/auth/${ecMallId}/continue`, {
            ...params,
        });
        return res;
    }
    static async chainClinicV3(params) {
        const res = await this.get('/api/v3/clinics/chain/clinics', {
            params,
        });
        return res;
    }

    // 删除授权
    static async deleteAuth(params) {
        const res = await this.post('/api/ec/auth/delete', {
            ...params,
        });
        return res;
    }

    // 取消门店绑定
    static async cancelBind(params) {
        const res = await this.post('/api/ec/auth/unbind', {
            ...params,
        });
        return res;
    }

    // 获取当前门店绑定的网点
    static async fetchBindAuthList(params) {
        const res = await this.get('/api/ec/auth/bind/list', {
            params,
        });
        const rows = res?.rows || [];
        return {
            rows,
        };
        // return res;
    }
    /**
     * 获取产品中心信息
     * <AUTHOR>
     * @date 2020-08-28
     * @returns {Promise}
     */
    static async fetchEditionCenter() {
        const res = await this.get('/api/v3/clinics/edition/center');
        return res;
    }

    // 获取拼多多授权页面code
    static async fetchPddIsvPageCode(data) {
        const res = await fetch({
            url: '/api/ec/pdd/isv/page-code',
            method: 'post',
            data,
            disabledCancel: true,
        });
        return res?.data?.data?.pageCode || '';
    }

    // 上传设备指纹信息
    static async pddFingerprint(data) {
        const res = await this.post('/api/ec/pdd/isv/device/fingerprint', data);
        return res;
    }

    /**
     * 美团修改前端同步标识
     * @param {Object} data - 参数
     * @param {string} data.mallId - ABC系统店铺id
     * @param {string} data.clientId - 客户端id
     * @param {number} data.opType - 操作类型； 0：添加标识；10：删除标识
     * @param {number} data.isForced - 是否强制操作。0：否；1：是
     */
    static async syncMTFlag(data) {
        const res = await this.put('/api/ec/mt/frontend/sync-flag', data);
        return res;
    }
    // 完成美团接入
    static async completeMTAccess(ecMallId) {
        const res = await fetch({
            url: `/api/ec/mall-id/${ecMallId}/access-complete`,
            method: 'put',
        });
        return res.data;
    }
    // 取消美团接入
    static async cancelMTAccess(ecMallId) {
        const res = await fetch({
            url: `/api/ec/mall-id/${ecMallId}/access-cancel`,
            method: 'delete',
        });
        return res;
    }
}
