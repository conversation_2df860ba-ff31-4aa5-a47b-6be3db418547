import BaseAPI, { fetch } from 'MfBase/base-api';
import Qs from 'qs';

export default class ECGoodsAPI extends BaseAPI {
    static async getEcGoods(params) {
        const res = await this.get('/api/ec/goods', {
            params,
        });
        return res;
    }
    // 获取商品类目
    static async getGoodsTypes(params) {
        const res = await this.get('/api/ec/goods/cats', {
            params,
        });
        return res;
    }
    // 绑定 his 商品
    static async bindHisGoods(goodsId, goodsSkuId, params) {
        const res = await this.put(`/api/ec/goods/${goodsId}/sku/${goodsSkuId}/his-goods`, {
            ...params,
        });
        return res;
    }

    // 查询某个药品库存详情
    static async getGoodsStockDetail(params) {
        const res = await this.get(`/api/v3/goods/${params.goodsId}/stocks/detail`, {
            method: 'get',
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { arrayFormat: 'comma' });
            },
        });
        return res;
    }

    static async updateAssignedStock(hisGoodsId, data) {
        const res = await fetch({
            url: `/api/ec/his-goods/${hisGoodsId}/related/assigned-stock`,
            method: 'put',
            data,
        });
        return res;
    }

    static async fetchRelatedSku(hisGoodsId, params) {
        const res = await fetch({
            url: `/api/ec/his-goods/${hisGoodsId}/related/goods-skus`,
            params,
        });
        return res?.data;
    }
}
