import BaseAPI from 'MfBase/base-api';

export default class ECDesktopAssistantStatAPI extends BaseAPI {

    /**
     * 桌面助手获取统计数据
     * @returns {Promise<*>}
     */
    static async getEcDesktopAssistantStat () {
        const res = await this.get('/api/ec/desktop-assistant/stat');
        return res;
    }

    /**
     * 是否安装了客户端
     * @returns {Promise<Response>}
     */
    static async getIsInstallClient() {
        const res = await fetch('http://127.0.0.1:8422/ping');
        return res;
    }

    /**
     * 打开客户端
     * @returns {Promise<Response>}
     */
    static async openClientByGet() {
        const res = await fetch('http://127.0.0.1:8422/process/create', {
            'method': 'POST',
            'body': JSON.stringify({
                'appName': 'C:\\Bytestream\\abcyun-clinic\\ABC数字医疗云.exe',
            }),
        });
        return res;
    }
}
