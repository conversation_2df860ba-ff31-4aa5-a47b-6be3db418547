import BaseAPI, { fetch } from 'MfBase/base-api';
import Qs from 'qs';

export default class TAGoodsAPI extends BaseAPI {
    // 查询商品列表
    static async getTaGoods (params) {
        const res = await this.get('/api/ec/mt/goods', {
            params,
            paramsSerializer(value) {
                return Qs.stringify(value);
            },
        });
        return res;
    }
    // 批量同步美团商品列表
    static async syncTaGoodsList (data) {
        const res = await fetch({
            url: '/api/ec/mt/batch/goods-stat',
            method: 'post',
            data,
            disabledCancel: true,
        });
        return res;
    }
    // 批量新增或修改美团商品
    static async syncTaGoodsDetail (data) {
        const res = await this.post('/api/ec/mt/batch/goods', data);
        return res;
    }
    // 批量新增或修改组包商品详情信息
    static async syncTaGoodsComposeDetail (data) {
        const res = await this.post('/api/ec/mt/batch/compose-goods', data);
        return res;
    }
    // 获取商品类目
    static async getGoodsTypes (params) {
        const res = await this.get('/api/ec/mt/goods/custom-cats/tree', {
            params,
        });
        return res;
    }
    // 绑定 his 商品
    static async bindHisGoods (goodsId, goodsSkuId, params) {
        const res = await this.put(`/api/ec/mt/goods/${goodsId}/sku/${goodsSkuId}/his-goods`, {
            ...params,
        });
        return res;
    }
    static async deleteMtGoods (data) {
        const res = await fetch({
            url: '/api/ec/mt/batch/goods',
            method: 'delete',
            data,
        });
        return res;
    }
}
