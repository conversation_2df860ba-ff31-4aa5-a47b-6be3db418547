import { ECTypeEnum } from '../constants.js';

export default class PDDPrint {
    constructor() {
        this.socket = null;
    }

    async connectPDDPrinter() {
        if (this.socket) {
            return this.socket;
        }
        return new Promise((resolve, reject) => {
            // ws://127.0.0.1:5000
            this.socket = new WebSocket('wss://127.0.0.1:18653');
            this.socket.addEventListener('open', () => {
                console.log('%c 连接PDD打印组件成功', 'background: #5199f8; padding: 4px; font-weight: bold; color: white'); // eslint-disable-next-line no-console
                resolve(this.socket);
            });
            this.socket.addEventListener('close', (e) => {
                reject(e);
            });
            this.socket.addEventListener('error', (e) => {
                e.__ecType__ = ECTypeEnum.PDD;
                reject(e);
            });
        });
    }

    async getPrinters() {
        return new Promise((resolve, reject) => {
            const sendData = {
                'cmd': 'getPrinters',
                'requestID': '123458976',
                'version': '1.0',
            };
            console.log(sendData);
            this.connectPDDPrinter().then((socket) => {
                socket.send(JSON.stringify(sendData));
                this.socket.addEventListener('message', (event) => {
                    const res = JSON.parse(event.data);
                    console.log(res);
                    if (res.cmd === 'getPrinters') {
                        resolve(res);
                    }
                });
            }).catch((err) => {
                reject(err);
            });
        });
    }

    static getSendData(data) {
        const {
            objectId,
            printData,
            waybillCode,
            shipper = {
                address: {
                    city: '',
                    detail: '',
                    district: '',
                    province: '',
                },
                mobile: '',
                name: '',
            },
            printer,
            customAreaStr,
        } = data;
        const _printData = JSON.parse(printData);
        return {
            requestID: objectId,
            'cmd': 'print',
            'task': {
                'documents': [
                    {
                        'contents': [
                            {
                                addData: {
                                    sender: shipper,
                                },
                                ..._printData,
                            },
                            {
                                'data': {
                                    'height': 240,
                                    'list': [
                                        {
                                            'fontSize': 50,
                                            'left': 2,
                                            'text': customAreaStr,
                                            'top': 2,
                                            'width': 540,
                                        },
                                    ],
                                    'waterdata': {
                                        'text': '',
                                    },
                                },
                                'templateURL': 'http://pinduoduoimg.yangkeduo.com/logistics/2019-07-14/5d7e8b5969d954539fcfba3268bbeb3a.xml',
                            },
                        ],
                        'documentID': waybillCode,
                    },
                ],
                'notifyType': [
                    'print',
                ],
                'preview': false,
                'previewType': 'image',
                printer,
                'taskID': `task_${Date.now()}`,
            },
            'version': '1.0',
        };
    }

    async print(data) {
        return new Promise((resolve, reject) => {
            const sendData = PDDPrint.getSendData(data);
            console.log(sendData);
            this.connectPDDPrinter().then((socket) => {
                socket.send(JSON.stringify(sendData));
                this.socket.addEventListener('message', (event) => {
                    const res = JSON.parse(event.data);
                    console.log(res);
                    if (res.status === 'failed') {
                        reject(res);
                        return;
                    }
                    if (res.cmd === 'PrintResultNotify') {
                        if (res.taskStatus === 'printed') {
                            resolve(res);
                            return;
                        }
                        reject(res);
                    }
                });
            }).catch((err) => {
                reject(err);
            });
        });
    }

    destroy() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
    }
}
