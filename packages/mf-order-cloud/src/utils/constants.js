import logoSTO from '@/assets/images/express-logo/img-shentong.png';
import logoHT from '@/assets/images/express-logo/img-baishi.png';
import logoSF from '@/assets/images/express-logo/img-shunfeng.png';
import logoDB from '@/assets/images/express-logo/img-debang.png';
import logoEMS from '@/assets/images/express-logo/img-ems.png';
import logoJD from '@/assets/images/express-logo/img-jingdong.png';
import logoTT from '@/assets/images/express-logo/img-tiantian.png';
import logoYZ from '@/assets/images/express-logo/img-youzheng.png';
import logoYT from '@/assets/images/express-logo/img-yuantong.png';
import logoYD from '@/assets/images/express-logo/img-yunda.png';
import logoZT from '@/assets/images/express-logo/img-zhongtong.png';
import logoMT from '@/assets/images/express-logo/img-meituan.png';
import logoELM from '@/assets/images/express-logo/img-eleme.png';
import logoPDD from '@/assets/images/express-logo/img-pdd.png';
export const WpCodeLogo = Object.freeze({
    'STO': logoSTO,
    'HT': logoHT,
    'SF': logoSF,
    'DEBANGWULIU': logoDB,
    'EMS': logoEMS,
    'JD': logoJD,
    'TT': logoTT,
    'YZXB': logoYZ,
    'YTO': logoYT,
    'YUNDA': logoYD,
    'ZTO': logoZT,
    'MT': logoMT,
    'ELE': logoELM,
    'PDD': logoPDD,
});
export const BindStatusEnum = Object.freeze({
    UNKNOWN: -1, // 未知
    BIND: 1, // 绑定
    EXPIRED: 2, // 过期
});
export const EcShopTypeEnum = Object.freeze({
    B2C: 0, // 电商
    O2O: 1, // 外卖
});
export const ECTypeEnum = Object.freeze({
    PDD: 1,
    ELM: 2,
    JD: 3,
    MT: 4,
});
export const MergeStatusEnum = Object.freeze({
    NO: 0, // 不可合并
    WAIT: 1, // 待合并
    MERGE_ED: 2, // 已合并
});
export const ECOrderStatusEnum = Object.freeze({
    ERROR: 0, // 不可合并
    WAIT: 1, // 待发货
    SHIPPED: 2, // 已发货待签收
    RECEIPT: 3, // 已签收
});
export const WaybillStatusEnum = Object.freeze({
    WAIT: 0, // 待打印
    PRINTED: 2, // 已打印
    CANCELED: 3, // 已回收
});
export const ECOrderRefundStatusEnum = Object.freeze({
    ALL: null, // 已签收
    NONE: 10, // 无售后或售后关闭
    HANDING: 20, // 售后中
    REFUND_SUCCESS: 30, // 售后成功
});
export const ECTypeText = Object.freeze({
    [ECTypeEnum.PDD]: '拼多多',
    [ECTypeEnum.ELM]: '饿了么',
    [ECTypeEnum.JD]: '京东',
    [ECTypeEnum.MT]: '美团',
});

// 库存处理结果类型 0:未知；10：退入库存；20：不退库存
export const AfterSaleItemStockDealResultType = Object.freeze({
    UNKNOWN: 0,
    RETURN_IN_STOCK: 10,
    NOT_RETURN_IN_STOCK: 20,
});
export const EcOrderAfterSaleStatus = Object.freeze({
    WAIT: 0, // 待处理
    DEAL_DONE: 10, // 已处理
});

export const ProvinceList = [
    {
        value: '110000',
        label: '北京',
        py: 'beijing',
        pyFirst: 'bj',
    },
    {
        value: '120000',
        label: '天津',
        py: 'tianjin',
        pyFirst: 'tj',
    },
    {
        value: '130000',
        label: '河北',
        py: 'hebei',
        pyFirst: 'hb',
    },

    {
        value: '140000',
        label: '山西',
        py: 'shanxi',
        pyFirst: 'sx',
    },

    {
        value: '150000',
        label: '内蒙古',
        py: 'neimenggu',
        pyFirst: 'nm',
    },

    {
        value: '210000',
        label: '辽宁',
        py: 'liaoning',
        pyFirst: 'ln',
    },

    {
        value: '220000',
        label: '吉林',
        py: 'jilin',
        pyFirst: 'jl',
    },

    {
        value: '230000',
        label: '黑龙江',
        py: 'heilongjiang',
        pyFirst: 'hlj',
    },

    {
        value: '310000',
        label: '上海',
        py: 'shanghai',
        pyFirst: 'sh',
    },

    {
        value: '320000',
        label: '江苏',
        py: 'jiangsu',
        pyFirst: 'js',
    },

    {
        value: '330000',
        label: '浙江',
        py: 'zhejiang',
        pyFirst: 'zj',
    },

    {
        value: '340000',
        label: '安徽',
        py: 'anhui',
        pyFirst: 'ah',
    },

    {
        value: '350000',
        label: '福建',
        py: 'fujian',
        pyFirst: 'fj',
    },

    {
        value: '360000',
        label: '江西',
        py: 'jiangxi',
        pyFirst: 'jx',
    },

    {
        value: '370000',
        label: '山东',
        py: 'shandong',
        pyFirst: 'sd',
    },

    {
        value: '410000',
        label: '河南',
        py: 'henan',
        pyFirst: 'hn',
    },

    {
        value: '420000',
        label: '湖北',
        py: 'hubei',
        pyFirst: 'hb',
    },

    {
        value: '430000',
        label: '湖南',
        py: 'hunan',
        pyFirst: 'hn',
    },

    {
        value: '440000',
        label: '广东',
        py: 'guangdong',
        pyFirst: 'gd',
    },

    {
        value: '450000',
        label: '广西',
        py: 'guangxi',
        pyFirst: 'gx',
    },

    {
        value: '460000',
        label: '海南',
        py: 'hainan',
        pyFirst: 'hn',
    },

    {
        value: '500000',
        label: '重庆',
        py: 'chongqing',
        pyFirst: 'cq',
    },

    {
        value: '510000',
        label: '四川',
        py: 'sichuan',
        pyFirst: 'sc',
    },

    {
        value: '520000',
        label: '贵州',
        py: 'guizhou',
        pyFirst: 'gz',
    },

    {
        value: '530000',
        label: '云南',
        py: 'yunnan',
        pyFirst: 'yn',
    },

    {
        value: '540000',
        label: '西藏',
        py: 'xizang',
        pyFirst: 'xz',
    },

    {
        value: '610000',
        label: '陕西',
        py: 'shanxi',
        pyFirst: 'sx',
    },

    {
        value: '620000',
        label: '甘肃',
        py: 'gansu',
        pyFirst: 'gs',
    },

    {
        value: '630000',
        label: '青海',
        py: 'qinghai',
        pyFirst: 'qh',
    },

    {
        value: '640000',
        label: '宁夏',
        py: 'ningxia',
        pyFirst: 'nx',
    },

    {
        value: '650000',
        label: '新疆',
        py: 'xinjiang',
        pyFirst: 'xj',
    },

    {
        value: '710000',
        label: '台湾',
        py: 'taiwan',
        pyFirst: 'tw',
    },

    {
        value: '810000',
        label: '香港',
        py: 'xianggang',
        pyFirst: 'xg',
    },

    {
        value: '820000',
        label: '澳门',
        py: 'aomen',
        pyFirst: 'am',
    },
];

export const OutType = Object.freeze({
    // 发药出库
    DISTRIBUTE: 10,
    // 退药入库
    RETURN: 20,
});

export const OutRecordStatus = Object.freeze({
    // 正常发货
    DELIVERED: 10,
    // 发货异常——未绑定goods
    UNBIND: 20,
    // 发货异常——库存不足
    SHORTAGE: 30,
    // 已退货
    RETURNED: 40,
});

export const EcOrderOutStatus = Object.freeze({
    // 未出库
    WAIT: 0,
    // 已出库
    OUT_STOCK: 10,
    // 已退库
    RETURN_STOCK: 90,
    // 已取消
    CANCELLED: 99,
});

export const AllocationStockType = Object.freeze({
    /**
     * 共享可售库存
     */
    SHARE: 0,
    /**
     * 按比例分配可售库存
     */
    RATIO: 10,
    /**
     * 自定义库存
     */
    CUSTOM: 20,
});
