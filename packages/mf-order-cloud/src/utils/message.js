export const ASSIST_MESSAGE = 'assist-message';
export const MAIN_MESSAGE = 'main-message';

/**
 * 监听桌面助手消息
 * @param event 消息事件
 * @param callBack 回调函数，（event，data）
 */
export function assistMessageOn(event, callBack) {
    if (!window.electron) {
        return;
    }
    window.electron.remote.ipcMain.on(event, callBack);
}

/**
 * 主窗口发送消息
 * @param browserWindow 发送的browserWindow对象
 * @param event 消息事件
 * @param data 传输的数据
 */
export function mainWindowSend(browserWindow, event, data) {
    if (!browserWindow) {
        return;
    }
    browserWindow.webContents.send(event, data);
}


/**
 * 取消监听桌面助手
 * @param event 消息事件
 * @param data 传输的数据
 */
export function removeAssistMessageOn(event) {
    if (!window.electron) {
        return;
    }
    window.electron.remote.ipcMain.removeAllListeners([event]);
}
