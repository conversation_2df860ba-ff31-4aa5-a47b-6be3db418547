/*
 * <AUTHOR>
 * @DateTime 2020-08-07 14:38:37
 */
// 正式环境
export const PROD_ENV = process.env.BUILD_ENV === 'prod';
// 灰度环境
export const GRAY_ENV = process.env.BUILD_ENV === 'gray';
// 预发布环境
export const PRE_ENV = process.env.BUILD_ENV === 'pre';
// 测试环境
export const TEST_ENV = process.env.BUILD_ENV === 'test';
// feature提测环境
export const FEATURE_ENV = process.env.BUILD_ENV === 'feature';
// 开发环境
export const DEV_ENV = process.env.BUILD_ENV === 'dev';
// own环境
export const OWN_ENV = process.env.BUILD_ENV === 'own';
// private环境
export const PRIVATE_ENV = process.env.BUILD_ENV === 'private';
// 本地环境
export const LOCAL_ENV = process.env.BUILD_ENV === undefined;

// 统一正式环境
export const isProd = PROD_ENV || GRAY_ENV || PRE_ENV;

// 统一测试环境
export const isTest = TEST_ENV || FEATURE_ENV;

// 统一开发环境
export const isDev = DEV_ENV || OWN_ENV || LOCAL_ENV || PRIVATE_ENV;

export const isPrintDev = DEV_ENV || LOCAL_ENV || PRIVATE_ENV;

// 本地环境
export const isLocal = LOCAL_ENV;

/**
 * 获取 own 和 ftest 环境的 No
 * @returns {string | undefined}
 */
export function getEnvNo() {
    const envNoReg = new RegExp(/https?:\/\/(\d+).(own|ftest).abczs.cn/);
    const matched = location.href.match(envNoReg);
    return matched?.[1];
}
