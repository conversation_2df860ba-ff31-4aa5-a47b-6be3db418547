export function getAddressStr(item) {
    const {
        city,
        detail,
        district,
        province,
    } = item || {};
    const _arr = [];
    if (province) {
        _arr.push(`${province}`);
    }
    if (city) {
        _arr.push(`${city}`);
    }
    if (district) {
        _arr.push(district);
    }
    if (detail) {
        _arr.push(detail);
    }
    return _arr.join('');
}

export function createGUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    }).replace(/-/g, '');
}

/**
 * @param {any} obj - 需要进行深度拷贝的对象
 * @returns {typeof obj} - 返回传入对象的类型的副本
 */

export function clone(obj) {
    if (null == obj || 'object' !== typeof obj) return obj;
    if (obj instanceof Array) {
        const copy = [];
        for (let i = 0, len = obj.length; i < len; ++i) {
            copy[i] = clone(obj[i]);
        }
        return copy;
    }
    if (Object.prototype.toString.call(obj) === '[object Object]') {
        const copy = {};
        for (const attr in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, attr)) copy[attr] = clone(obj[attr]);
        }
        return copy;
    }
}
