<template>
    <abc-flex vertical>
        <template v-for="(row, rowIndex) in visibleRows">
            <stream-renderer
                :key="rowIndex"
                :enable-stream="enableStream"
                :elements="row.elements"
                :custom-class="row.customClass"
                @render-complete="handleRowRenderComplete"
            >
                <template
                    #default="{
                        data, index
                    }"
                >
                    <slot :data="data" :row-index="rowIndex" :col-index="index"></slot>
                </template>
            </stream-renderer>
        </template>
    </abc-flex>
</template>

  <script>
    import { defineComponent } from 'vue';
    import StreamRenderer from './stream-renderer.vue';
    export default defineComponent({
        name: 'NestedStreamRenderer',
        components: {
            StreamRenderer,
        },
        props: {
            rows: {
                type: Array,
                required: true,
                default: () => [],
            },
            
            enableStream: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                visibleRows: [],
                currentRowIndex: 0,
            };
        },
        mounted() {
            this.startSchedule();
        },
        methods: {
            startSchedule() {
                if (!this.enableStream) {
                    this.visibleRows = this.rows;
                    return;
                }
                
                this.visibleRows = [];
                this.currentRowIndex = 0;

                this.visibleRows.push(this.rows[this.currentRowIndex]);
            },

            handleRowRenderComplete() {
                this.currentRowIndex++;

                if (this.currentRowIndex < this.rows.length) {
                    this.visibleRows.push(this.rows[this.currentRowIndex]);
                    return;
                }

                this.$emit('render-complete');
            },
        },
    });
  </script>
