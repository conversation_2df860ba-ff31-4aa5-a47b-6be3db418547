<template>
    <abc-button class="ai-gradient-button" v-bind="$attrs" v-on="$listeners">
        <slot></slot>
    </abc-button>
</template>

<style lang="scss">
.ai-gradient-button {
    color: var(--abc-color-T4);
    background: linear-gradient(102deg, #5acafd 0%, #5694fe 61.88%, #9055fe 100%) !important;
    border: none;
    border-radius: 50px;

    &:hover {
        background: linear-gradient(102deg, #79d5ff 0%, #8bb5ff 61.88%, #b892ff 100%) !important;
    }

    &:active {
        background: linear-gradient(102deg, #37bbf7 0%, #3e85ff 61.88%, #803ff8 100%) !important;
    }
}
</style>
