<template>
    <abc-flex :vertical="vertical" :class="customClass">
        <template
            v-for="(element, index) in visibleElements"
        >
            <slot :data="element" :index="index"></slot>
        </template>
    </abc-flex>
</template>

<script>
    import { defineComponent } from 'vue';

    export default defineComponent({
        name: 'StreamRenderer',
        props: {
            elements: {
                type: Array,
                required: true,
                default: () => [],
            },
            vertical: {
                type: Boolean,
                default: false,
            },
            customClass: {
                type: String,
                default: '',
            },
            enableStream: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                visibleElements: [],
                renderTimer: null,
            };
        },
        mounted() {
            this.startSchedule();
        },
        beforeDestroy() {
            this.cleanup();
        },
        methods: {
            startSchedule() {
                if (!this.enableStream) {
                    this.visibleElements = this.elements;
                    return;
                }
                
                this.visibleElements = [];

                let index = 0;
                let lastRenderTime = 0;
                const RENDER_INTERVAL = 20; // 控制渲染间隔，单位毫秒

                const renderNextItem = (timestamp) => {
                    // 如果组件已销毁，不继续渲染
                    if (!this.renderTimer) return;

                    // 计算时间差，控制渲染速率
                    const elapsed = timestamp - lastRenderTime;

                    if (elapsed >= RENDER_INTERVAL) {
                        lastRenderTime = timestamp;

                        if (index < this.elements.length) {
                            this.visibleElements.push(this.elements[index]);
                            index++;
                        } else {
                            this.$emit('render-complete');
                            return; // 渲染完成，不再继续
                        }
                    }

                    // 请求下一帧
                    this.renderTimer = requestAnimationFrame(renderNextItem);
                };

                // 启动渲染循环
                this.renderTimer = requestAnimationFrame(renderNextItem);
            },

            cleanup() {
                if (this.renderTimer) {
                    cancelAnimationFrame(this.renderTimer);
                    this.renderTimer = null;
                }
            },
        },
    });
</script>
