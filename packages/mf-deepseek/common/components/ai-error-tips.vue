<template>
    <abc-flex
        class="ai-error-tips"
        align="center"
        justify="center"
        :gap="6"
    >
        <abc-icon size="14" icon="s-alert-fill"></abc-icon>
        <div class="error-text">
            <slot>服务器繁忙，请稍后再试</slot>
        </div>
    </abc-flex>
</template>

<script>
    export default {
        name: 'AiErrorTips',
    };
</script>

<style lang="scss">
    .ai-error-tips {
        height: 32px;
        padding: var(--abc-border-radius-small, 6px) 0;
        margin-top: var(--abc-space-xl);
        color: var(--abc-color-T2);
        background: rgba(0, 0, 0, 0.04);
        border-radius: var(--abc-border-radius-small, 6px);
    }
</style>
