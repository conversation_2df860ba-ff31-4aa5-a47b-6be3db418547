{"name": "mf-deepseek", "uniqueName": "mf-deepseek", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development && webpack server --mode=development --config build/webpack.dev.config.js", "build": "cross-env NODE_ENV=production && export NODE_OPTIONS=\"--max-old-space-size=6144\" && webpack build --mode=production --config build/webpack.prod.config.js"}, "devDependencies": {"@babel/core": "^7.22.17", "@babel/eslint-parser": "^7.19.1", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/preset-env": "^7.22.15", "@vue/babel-helper-vue-jsx-merge-props": "1.2.1", "@vue/babel-preset-jsx": "1.2.4", "@vue/compiler-sfc": "3.2.30", "abc-fed-build-tool": "0.8.5", "babel-eslint": "^10.1.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^9.1.3", "babel-plugin-component": "^1.1.1", "chalk": "^2.0.1", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "css-loader": "^6.10.0", "external-remotes-plugin": "^1.0.0", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.8.1", "ora": "^1.2.0", "rimraf": "^2.6.0", "sass": "1.71.1", "sass-loader": "^14.1.1", "style-loader": "^3.3.1", "vue-loader": "^15", "vue-style-loader": "^4.1.3", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.3", "webpack-merge": "^5.8.0"}, "dependencies": {"@abc/constants": "1.4.177", "@abc/ui-pc": "1.365.3", "@abc/utils": "1.2.213", "@abc/utils-date": "1.2.214", "@abc/utils-dom": "1.2.214", "axios": "0.15.3", "crypto-js": "^4.2.0", "jquery": "^3.2.1", "jsonrepair": "^3.12.0", "marked": "^15.0.7", "vue": "2.7.14", "vue-router": "^3.5.4", "vuex": "^3.6.2", "lodash.clonedeep": "^4.5.0"}, "engines": {"node": ">= 8.0.0", "npm": ">= 3.0.0"}, "browserslist": ["last 2 Chrome versions"]}