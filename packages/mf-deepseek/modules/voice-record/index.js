import VoiceRecordDialog from './record-dialog';
import VoiceRecordTriggerButton from './record-v2/voice-record-trigger-button.vue';

let voiceRecordDialog = null;

function closeVoiceRecord() {
    if (voiceRecordDialog && !voiceRecordDialog._isDestroyed) {
        voiceRecordDialog.destroyDialog();
    }
}

function openVoiceRecord({
    outpatientSheetId,
    patientInfo,
    parent,
}) {
    closeVoiceRecord();
    voiceRecordDialog = new VoiceRecordDialog({
        outpatientSheetId,
        patientInfo,
    }).generateDialog({
        parent,
    });

    voiceRecordDialog.$on('close', () => {
        voiceRecordDialog = null;
    });
}

export default {
    openVoiceRecord,
    closeVoiceRecord,
    VoiceRecordTriggerButton,
};
