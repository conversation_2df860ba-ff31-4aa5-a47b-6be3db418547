<template>
    <abc-dialog
        v-if="visible"
        ref="dialog"
        v-model="visible"
        :custom-class="dialogClass"
        append-to-body
        :content-styles="contentStyles"
        :show-footer="false"
    >
        <abc-delete-icon
            class="voice-record-dialog__close-button"
            theme="dark"
            variant="outline-square"
            size="hugely"
            @delete="close"
        ></abc-delete-icon>
        <div class="voice-record-dialog__content">
            <!-- 初始状态 -->
            <div v-if="state === 'initial'" class="voice-record-initial">
                <abc-text theme="gray">
                    你可以口述速录的病历，直接记录与患者的问诊过程。
                </abc-text>
                <abc-text theme="gray" class="voice-record-subtitle">
                    AI自动生成病历
                </abc-text>
                <voice-record-button
                    class="start-button"
                    type="record"
                    circle
                    @click="startRecording"
                >
                </voice-record-button>
            </div>

            <!-- 录音状态 -->
            <div v-if="state === 'recording'" class="voice-record-recording">
                <div class="audio-wave">
                    <wave-visualization
                        :is-paused="false"
                        :waveform-data="waveformData"
                        style="width: 320px;"
                    ></wave-visualization>
                    <abc-text class="record-time" size="mini" theme="gray">
                        {{ formattedTime }}
                    </abc-text>
                    <div class="subtitle-container">
                        <div
                            class="subtitle-lines"
                            :class="{
                                'scrolling': isSubtitleScrolling,
                            }"
                        >
                            <abc-text
                                v-for="(line, index) in currentSubtitleLines"
                                :key="index"
                                class="subtitle-line"
                                :class="{
                                    'is-level-last1': index === currentSubtitleLines.length - 1,
                                    'is-level-last2': index === currentSubtitleLines.length - 2,
                                    'is-level-last3': index === currentSubtitleLines.length - 3,
                                }"
                                theme="gray"
                            >
                                {{ line }}
                            </abc-text>
                        </div>
                    </div>
                </div>
                <div class="record-controls">
                    <voice-record-button
                        class="pause-button"
                        type="pause"
                        @click="pauseRecording"
                    >
                    </voice-record-button>
                    <voice-record-button
                        class="end-button"
                        type="stop"
                        @click="stopRecording(true)"
                    >
                    </voice-record-button>
                </div>
            </div>

            <!-- 暂停状态 -->
            <div v-if="state === 'paused'" class="voice-record-paused">
                <div class="audio-wave">
                    <wave-visualization
                        :is-paused="true"
                        :amplitude="waveformData"
                    ></wave-visualization>
                    <abc-text class="record-time" theme="gray" size="mini">
                        {{ formattedTime }}
                    </abc-text>
                    <div class="subtitle-container">
                        <div class="subtitle-lines">
                            <abc-text
                                v-for="(line, index) in currentSubtitleLines"
                                :key="index"
                                class="subtitle-line"
                                :class="{
                                    'is-level-last1': index === currentSubtitleLines.length - 1,
                                    'is-level-last2': index === currentSubtitleLines.length - 2,
                                    'is-level-last3': index === currentSubtitleLines.length - 3,
                                }"
                                theme="gray"
                            >
                                {{ line }}
                            </abc-text>
                        </div>
                    </div>
                </div>
                <div class="record-controls">
                    <voice-record-button
                        class="continue-button"
                        type="continue"
                        @click="continueRecording"
                    >
                    </voice-record-button>
                    <voice-record-button
                        class="end-button"
                        type="stop"
                        @click="stopRecording(true)"
                    >
                    </voice-record-button>
                </div>
            </div>

            <!-- 生成病历状态 -->
            <result-panel
                v-if="state === 'generating'"
                style="z-index: 1;"
                :outpatient-sheet-id="outpatientSheetId"
                :audio-data="audioData"
                :asr-result="finalResult"
                :patient-info="patientInfo"
                :generating.sync="generatingMedicalRecord"
                @finish="close"
            ></result-panel>
        </div>
        <template v-if="state !== 'generating'">
            <div class="bg-circle-1"></div>
            <div class="bg-circle-2"></div>
            <div class="bg-circle-3"></div>
        </template>
    </abc-dialog>
</template>

<script>
    import WebAudioSpeechRecognizer from '../asr/webaudiospeechrecognizer';
    import config from './config';
    import WaveVisualization from '../components/wave-visualization-v2.vue';
    import VoiceRecordButton from '../components/voice-record-button.vue';
    import ResultPanel from './result-panel.vue';
    import { fetchTemporaryToken } from '../services/api';

    export default {
        name: 'VoiceRecordDialog',
        components: {
            WaveVisualization,
            VoiceRecordButton,
            ResultPanel,
        },
        props: {
            outpatientSheetId: {
                type: String,
                default: '',
            },
            patientInfo: {
                type: Object,
                required: false,
                default: null,
            },
        },
        data() {
            return {
                loading: false,
                visible: false,
                state: 'initial', // initial, recording, paused
                webAudioSpeechRecognizer: null,
                isRecording: false,
                waveformData: null,
                recordTimer: null,
                recordTime: 0,
                audioData: [], // 录音数据
                // 已完成识别的句子列表，每项包含文本内容和时间信息
                completedSentences: [],
                // 当前正在识别的句子
                currentSentence: '',
                // 所有的字幕行数组
                subtitleLines: [],
                displayLineCount: 2,
                // 当前显示的字幕行数组
                currentSubtitleLines: [],
                // 是否正在滚动
                isSubtitleScrolling: false,
                // 当前识别的起始时间
                currentStartTime: 0,
                // 当前识别的结束时间
                currentEndTime: 0,
                // 录音权限状态
                micPermissionStatus: 'unknown', // 'unknown', 'granted', 'denied', 'prompt'
                // 是否显示授权提示
                showPermissionTip: false,
                // 时间偏移量，用于计算暂停后继续录音的时间
                timeOffset: 0,
                // 是否是继续录音
                isContinueRecording: false,

                // 录音最终结果
                finalResult: [],
                // 生成病历中
                generatingMedicalRecord: false,

                waitingAsrLastMessage: false,
            };
        },
        computed: {
            dialogClass() {
                if (this.isRecording) {
                    return 'voice-record-dialog ai-effect-gradient-border animation';
                }
                if (this.state === 'generating') {
                    return 'voice-record-dialog result-generating';
                }
                return 'voice-record-dialog';
            },
            contentStyles() {
                if (this.state === 'generating') {
                    return 'transition: all 0.4s ease-in-out; height: 656px; width: 640px;';
                }
                return 'height: 340px; width: 560px;';
            },

            formattedTime() {
                const minutes = Math.floor(this.recordTime / 60).toString().padStart(2, '0');
                const seconds = (this.recordTime % 60).toString().padStart(2, '0');
                return `${minutes}:${seconds}`;
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.stopRecording();
                    this.destroyElement();
                }
            },
        },
        beforeDestroy() {
            this.stopRecording();
            this.audioData = [];
            if (this._subtitleScrollTimer) {
                clearTimeout(this._subtitleScrollTimer);
                this._subtitleScrollTimer = null;
            }
        },
        methods: {
            close() {
                this.$refs.dialog.handleClose();
                this.$emit('close');
            },
            // 检查麦克风权限
            checkMicPermission() {
                return new Promise((resolve, reject) => {
                    // 如果浏览器支持 navigator.permissions API
                    if (navigator.permissions && navigator.permissions.query) {
                        navigator.permissions.query({ name: 'microphone' })
                            .then((permissionStatus) => {
                                this.micPermissionStatus = permissionStatus.state;
                                console.log(`当前录音权限状态: ${permissionStatus.state}`);

                                // 添加状态变化监听
                                permissionStatus.onchange = () => {
                                    this.micPermissionStatus = permissionStatus.state;
                                    console.debug(`录音权限状态变化为: ${permissionStatus.state}`);

                                    if (permissionStatus.state === 'granted') {
                                        this.showPermissionTip = false;
                                    }
                                };

                                if (permissionStatus.state === 'granted') {
                                    resolve(true);
                                } else if (permissionStatus.state === 'prompt') {
                                    // 需要请求权限
                                    this.requestMicPermission(resolve, reject);
                                } else {
                                    // 权限被拒绝
                                    this.showPermissionTip = true;
                                    reject(new Error('麦克风权限被拒绝'));
                                }
                            })
                            .catch((error) => {
                                console.error('检查麦克风权限时出错:', error);
                                // 如果无法检查权限，尝试直接请求
                                this.requestMicPermission(resolve, reject);
                            });
                    } else {
                        // 如果浏览器不支持 permissions API，尝试直接请求权限
                        this.requestMicPermission(resolve, reject);
                    }
                });
            },

            // 请求麦克风权限
            requestMicPermission(resolve, reject) {
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then((stream) => {
                        // 获得权限后立即关闭流，因为我们只是检查权限
                        stream.getTracks().forEach((track) => track.stop());
                        this.micPermissionStatus = 'granted';
                        this.showPermissionTip = false;
                        resolve(true);
                    })
                    .catch((error) => {
                        console.error('请求麦克风权限失败:', error);
                        this.micPermissionStatus = 'denied';
                        this.showPermissionTip = true;
                        reject(error);
                    });
            },

            // 开始录音
            startRecording() {
                if (this.isRecording) {
                    return;
                }
                // 先检查录音权限
                this.checkMicPermission()
                    .then(() => {
                        // 权限获取成功，开始录音
                        this.startRecordingProcess();
                    })
                    .catch((error) => {
                        console.error('录音权限检查失败:', error);
                        // 权限检查失败，显示提示信息
                        this.showPermissionTip = true;
                        this.$Toast.error('录音权限检查失败');
                    });
            },

            // 录音处理过程
            async startRecordingProcess() {
                const token = await fetchTemporaryToken();

                if (!token) {
                    this.$Toast('获取录音凭证失败');
                    return;
                }

                const params = {
                    secretid: token.tmpSecretId,
                    secretkey: token.tmpSecretKey,
                    token: token.token,
                    ...config,
                };

                this.webAudioSpeechRecognizer = new WebAudioSpeechRecognizer(params, false);
                this.currentSentence = '';
                this.currentStartTime = 0;
                this.currentEndTime = 0;

                this.state = 'recording';
                this.isRecording = true;

                // 开始计时
                this.recordTimer = setInterval(() => {
                    this.recordTime++;
                }, 1000);

                // 设置振幅变化回调
                this.webAudioSpeechRecognizer.onWaveformUpdate = (data) => {
                    this.waveformData = data;
                };

                // 开始识别
                this.webAudioSpeechRecognizer.OnRecognitionStart = (res) => {
                    console.debug('OnRecognitionStart', res);
                };

                // 设置时间偏移量，只有在继续录音时才使用时间偏移量
                const timeOffsetToUse = this.isContinueRecording ? this.timeOffset : 0;
                console.debug(`当前录音时间偏移量: ${timeOffsetToUse}ms, 是否继续录音: ${this.isContinueRecording}`);
                // 重置继续录音标记
                this.isContinueRecording = false;

                // 一句话开始
                this.webAudioSpeechRecognizer.OnSentenceBegin = (res) => {
                    console.debug('OnSentenceBegin', res.result?.voice_text_str);
                    // 先重置当前句子
                    this.currentSentence = '';
                    // 如果有时间偏移量，则计算当前句子的开始时间
                    if (res.result && res.result.voice_text_str) {
                        // 使用上面已经定义的 timeOffsetToUse
                        this.currentStartTime = (res.result.start_time || 0) + timeOffsetToUse;
                        console.debug(`句子开始时间: ${this.currentStartTime}ms (原始: ${res.result.start_time || 0}ms, 偏移: ${timeOffsetToUse}ms)`);
                    }

                    // 更新字幕显示，新的句子开始
                    this.updateSubtitleDisplay();
                };

                // 识别变化时
                this.webAudioSpeechRecognizer.OnRecognitionResultChange = (res) => {
                    // console.log('OnRecognitionResultChange', res.result?.voice_text_str);
                    if (res.result && res.result.voice_text_str) {
                        // 更新当前正在识别的句子
                        this.currentSentence = res.result.voice_text_str;

                        // 更新字幕显示，当前句子有变化
                        this.updateSubtitleDisplay();

                        // 计算考虑偏移量的实际时间
                        const originalStartTime = res.result.start_time || 0;
                        const originalEndTime = res.result.end_time || 0;

                        // 更新时间信息，考虑时间偏移量
                        this.currentStartTime = originalStartTime + this.timeOffset;
                        this.currentEndTime = originalEndTime + this.timeOffset;

                        // console.log('实时识别时间更新', `原始: ${originalStartTime}-${originalEndTime}ms, 偏移后: ${this.currentStartTime}-${this.currentEndTime}ms`);
                    }
                };

                // 一句话结束
                this.webAudioSpeechRecognizer.OnSentenceEnd = (res) => {
                    console.debug('OnSentenceEnd', res.result?.voice_text_str);
                    if (res.result && res.result.voice_text_str) {
                        // 计算原始时间
                        const originalStartMs = res.result.start_time || 0;
                        const originalEndMs = res.result.end_time || 0;

                        // 计算考虑偏移量的实际时间
                        const startMs = originalStartMs + this.timeOffset;
                        const endMs = originalEndMs + this.timeOffset;

                        // 格式化时间显示
                        const startSec = Math.floor(startMs / 1000);
                        const startMinutes = Math.floor(startSec / 60).toString().padStart(2, '0');
                        const startSeconds = (startSec % 60).toString().padStart(2, '0');
                        const timeStr = `${startMinutes}:${startSeconds}`;

                        // console.log(`句子结束时间计算: 原始 ${originalStartMs}-${originalEndMs}ms, 偏移后 ${startMs}-${endMs}ms`);

                        // 将完成的句子添加到数组中，包含文本内容和考虑偏移量后的时间信息
                        const completedSentence = {
                            text: res.result.voice_text_str,
                            startTime: startMs,
                            endTime: endMs,
                            time: timeStr,
                            index: res.result.index,
                        };

                        this.completedSentences.push(completedSentence);

                        // 清空当前句子
                        this.currentSentence = '';

                        // 更新字幕显示，一个句子结束
                        this.updateSubtitleDisplay();
                    }
                };

                // 识别结束
                this.webAudioSpeechRecognizer.OnRecognitionComplete = (res) => {
                    console.debug('OnRecognitionComplete', res);
                    if (this.waitingAsrLastMessage) {
                        this.processAsrResult();
                        this.waitingAsrLastMessage = false;
                    }
                };

                // 识别错误
                this.webAudioSpeechRecognizer.OnError = (res) => {
                    console.warn('OnRecognitionError', res);
                    this.$Toast.error(`识别失败: ${res}`);
                    this.stopRecording();
                };

                // 每次录音结束，都存储录音数据，最后上传所有录音数据
                this.webAudioSpeechRecognizer.OnRecorderStop = (audioData) => {
                    console.debug('onRecordStop', audioData.length);
                    // this.audioData.push(...audioData);
                    this.audioData = this.audioData.concat(audioData);
                };

                this.webAudioSpeechRecognizer.start();
            },

            // 暂停录音
            pauseRecording() {
                if (this.webAudioSpeechRecognizer) {
                    this.webAudioSpeechRecognizer.stop();
                    this.webAudioSpeechRecognizer.destroyStream();
                    this.webAudioSpeechRecognizer = null;
                    clearInterval(this.recordTimer);
                    this.recordTimer = null;
                    this.state = 'paused';
                    this.isRecording = false;
                    this.waveformData = 0;

                    // 如果有已完成的句子，记录最后一句话的结束时间
                    if (this.completedSentences.length > 0) {
                        const lastSentence = this.completedSentences[this.completedSentences.length - 1];
                        // 更新时间偏移量，使用最后一句话的结束时间
                        this.timeOffset = lastSentence.endTime;
                        // console.log(`暂停录音，设置时间偏移量为: ${this.timeOffset}ms`);
                    } else if (this.currentEndTime > 0) {
                        // 如果没有完成的句子但有当前句子的结束时间
                        this.timeOffset = this.currentEndTime;
                        // console.log(`暂停录音，设置时间偏移量为: ${this.timeOffset}ms`);
                    }
                }
            },

            // 继续录音
            continueRecording() {
                // 标记为继续录音状态
                this.isContinueRecording = true;
                this.startRecording();
                this.isRecording = true;
            },

            // 停止录音
            stopRecording(isNormalEnd) {
                if (this.webAudioSpeechRecognizer) {
                    console.debug('停止录音');
                    this.webAudioSpeechRecognizer.stop();
                    this.webAudioSpeechRecognizer.destroyStream();
                    this.webAudioSpeechRecognizer = null;
                }

                this.resetRecording();
                if (isNormalEnd) {
                    if (this.state === 'paused') {
                        // 如果当前是暂停状态，这个时候停止需要处理识别结果
                        this.processAsrResult();
                    } else {
                        // 如果当前是继续状态，这个时候停止需要等待识别结果
                        this.waitingAsrLastMessage = true;
                    }
                } else {
                    this.state = 'initial';
                }
            },

            resetRecording() {
                clearInterval(this.recordTimer);
                this.recordTimer = null;
                this.recordTime = 0;
                this.waveformData = 0;
                this.isRecording = false;
                this.timeOffset = 0; // 重置时间偏移量
            },

            processAsrResult() {
                this.finalResult = this.completedSentences;
                this.completedSentences = [];
                this.currentSentence = '';
                this.currentStartTime = 0;
                this.currentEndTime = 0;
                this.subtitleLines = [];

                let fullText = '';
                // 如果有识别结果，可以在这里处理
                if (this.finalResult.length > 0) {
                    fullText = this.finalResult.map((item) => item.text).filter(Boolean).join('');
                    console.debug('processAsrResult识别结果:', fullText);
                }
                // 进入生成病历状态
                this.state = 'generating';
            },

            // 更新字幕显示
            updateSubtitleDisplay() {
                // 准备字幕行数据
                const lines = [];

                // 处理历史句子（已完成的句子）
                // 从最新的历史句子开始处理
                for (let i = 0; i < this.completedSentences.length; i++) {
                    const historyText = this.completedSentences[i].text;
                    // 将历史句子按每行20个字符分割
                    const historyLines = this.splitTextIntoLines(historyText);
                    lines.push(...historyLines);
                }

                // 处理当前正在识别的句子
                if (this.currentSentence) {
                    // 将当前句子按每行20个字符分割
                    const currentLines = this.splitTextIntoLines(this.currentSentence);
                    lines.push(...currentLines);
                }

                const newLines = lines;

                this.currentSubtitleLines = newLines.slice(-this.displayLineCount);
                // 如果行数发生变化并且大于displayLineCount行，触发滚动动画
                if (newLines.length > this.subtitleLines.length && newLines.length > this.displayLineCount) {
                    this.isSubtitleScrolling = true;
                    this.currentSubtitleLines = newLines.slice(-this.displayLineCount - 1);
                    this._subtitleScrollTimer = setTimeout(() => {
                        this.isSubtitleScrolling = false;
                        this.currentSubtitleLines = newLines.slice(-this.displayLineCount);
                    }, 300); // 动画持续时间
                }

                if (newLines.length <= this.displayLineCount) {
                    document.querySelector('.subtitle-lines').classList.add('need-init-anim');
                } else {
                    document.querySelector('.subtitle-lines').classList.remove('need-init-anim');
                }

                this.subtitleLines = newLines;
            },

            // 将文本按每行20个字符分割成多行
            splitTextIntoLines(text) {
                if (!text) return [];

                const lines = [];
                let remainText = text;

                while (remainText.length > 0) {
                    if (remainText.length <= 20) {
                        lines.push(remainText);
                        break;
                    }

                    // 每行最多20个字符
                    lines.push(remainText.substring(0, 20));
                    remainText = remainText.substring(20);
                }

                return lines;
            },

            destroyDialog() {
                this.stopRecording();
                this.visible = false;
            },

            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>

<style lang="scss">
@import "@/common/style/effect.scss";

// 以下是原有样式
.voice-record-dialog {
    position: relative;

    --abc-dialog-border-radius: 16px;
    --abc-dialog-translate-y: -40% !important;

    &.result-generating {
        background: linear-gradient(138deg, #cae1ff 0%, #e6e5ff 37.23%, #cbd8ff 97.84%);
    }

    .abc-dialog-body {
        padding: 24px !important;
        overflow: hidden;
        border-radius: var(--abc-dialog-border-radius);
    }

    &__close-button {
        position: absolute;
        top: 8px;
        right: 8px;

        &:hover {
            background-color: rgba(2, 10, 26, 0.04) !important;
        }
    }

    &__header {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 15px;
    }

    &__content {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
    }

    .voice-record-initial,
    .voice-record-recording,
    .voice-record-paused {
        position: relative;
        width: 100%;
        height: 100%;
        margin-top: 36px;
    }

    .voice-record-initial {
        display: flex;
        flex-direction: column;
        align-items: center;

        .voice-record-subtitle {
            margin-top: 4px;
        }

        .start-button {
            position: absolute;
            bottom: 40px;
            z-index: 1;
        }
    }

    .voice-record-result {
        width: 100%;
        margin-bottom: 20px;
    }

    .voice-record-recording,
    .voice-record-paused {
        .audio-wave {
            display: flex;
            flex-direction: column;
            align-items: center;

            .record-time {
                margin-top: 12px;
            }

            .subtitle-container {
                position: relative;
                width: 100%;
                height: 48px; /* 足够容纳三行字幕 */
                margin-top: 20px;
                overflow: hidden;
            }

            .subtitle-lines {
                display: flex;
                flex-direction: column;
                align-items: center;

                &.scrolling {
                    transition: transform 0.433s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.433s cubic-bezier(0, 0, 0.2, 1);
                    transform: translateY(-24px);

                    .subtitle-line {
                        transition: opacity 0.433s ease;
                    }
                }

                &.need-init-anim {
                    .subtitle-line {
                        transition: opacity 0.433s ease;
                    }
                }

                .subtitle-line {
                    width: 100%;
                    line-height: 24px;
                    text-align: center;

                    &.is-level-last1 {
                        opacity: 1;
                    }

                    &.is-level-last2 {
                        opacity: 0.3;
                    }

                    &.is-level-last3 {
                        opacity: 0.1;
                    }
                }
            }
        }

        .record-controls {
            position: absolute;
            bottom: 26px;
            z-index: 1;
            display: flex;
            gap: 24px;
            justify-content: center;
            width: 100%;

            .record-button {
                width: 56px;
                height: 56px;
                padding: 0;
            }

            .voice-record-button {
                width: 56px;
                height: 56px;
            }
        }
    }
}

.bg-circle-1,
.bg-circle-2,
.bg-circle-3 {
    position: absolute;
    width: 284px;
    height: 269px;
    pointer-events: none;
    border-radius: 50%;
}

.bg-circle-1 {
    top: 50%;
    left: -20%;
    background: #e5e1ff;
    filter: blur(60px); /* 关键模糊滤镜 */
    opacity: 0.5;
}

.bg-circle-2 {
    top: 70%;
    left: 20%;
    background: #dbf3ff;
    filter: blur(60px); /* 关键模糊滤镜 */
}

.bg-circle-3 {
    top: 60%;
    left: 55%;
    background: #e5edff;
    filter: blur(30px); /* 关键模糊滤镜 */
}
</style>
