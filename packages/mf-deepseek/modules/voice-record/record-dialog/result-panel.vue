<template>
    <abc-flex
        class="voice-record-result-panel"
        vertical
        style="width: 100%; height: 100%;"
    >
        <abc-text size="large" bold style="text-align: center;">
            生成病历
        </abc-text>
        <abc-card
            padding-size="small"
            :border="false"
            style="margin-top: 12px; border-radius: 12px;"
        >
            <abc-flex gap="8">
                <abc-icon
                    icon="s-microphone-line"
                    size="32"
                    style="margin-top: 3px;"
                    color="var(--abc-color-B3)"
                ></abc-icon>
                <abc-flex vertical style="width: 100%;">
                    <abc-text size="normal">
                        {{ patientName }}的问诊录音
                    </abc-text>
                    <abc-flex justify="space-between" style="width: 100%;">
                        <abc-text theme="gray" size="mini">
                            {{ createdTime }}
                        </abc-text>
                        <abc-text theme="gray" size="mini">
                            {{ recordDurationFormated }}
                        </abc-text>
                    </abc-flex>
                </abc-flex>
            </abc-flex>
        </abc-card>

        <abc-card class="voice-record-result__medical-record" :border="false" style="border-radius: 12px;">
            <abc-scrollbar padding-size="none" class="medical-record-scroll">
                <abc-flex
                    vertical
                    gap="6"
                    class="medical-record-result"
                >
                    <abc-space class="generate-status">
                        <ai-loading-spinner :loading="analyzeResult.loading"></ai-loading-spinner>
                        <abc-text v-if="analyzeResult.loading" theme="gray">
                            生成病历中...
                        </abc-text>
                        <abc-text v-else-if="analyzeResult.error" theme="gray">
                            生成失败
                        </abc-text>
                        <abc-text v-else theme="gray">
                            已生成病历
                        </abc-text>
                    </abc-space>
                    <markdown-renderer v-if="analyzeResult.content" :content="analyzeResult.content"></markdown-renderer>
                    <!-- 生成错误 -->
                    <ai-error-tips v-if="analyzeResult.error">
                        {{ analyzeResult.error }}
                    </ai-error-tips>
                </abc-flex>
            </abc-scrollbar>

            <transition name="fade">
                <abc-flex
                    v-show="analyzeResult.canRetry || (analyzeResult.content && !analyzeResult.error && !analyzeResult.loading)"
                    justify="end"
                    class="medical-record-actions"
                >
                    <ai-gradient-button
                        v-if="analyzeResult.canRetry"
                        size="large"
                        width="148"
                        @click="handleRetryClick"
                    >
                        重试
                    </ai-gradient-button>


                    <ai-gradient-button
                        v-if="analyzeResult.content && !analyzeResult.error && !analyzeResult.loading"
                        size="large"
                        width="148"
                        @click="handleAcceptClick"
                    >
                        采纳
                    </ai-gradient-button>
                </abc-flex>
            </transition>
        </abc-card>
    </abc-flex>
</template>

<script>
    import { storeToRefs } from 'MfBase/pinia';
    import { formatDate } from '@abc/utils-date';
    import AiGradientButton from '@/common/components/ai-gradient-button.vue';
    import {
        useVoiceAnalyzeStore,
    } from '../hooks/use-voice-analyze';
    import MarkdownRenderer from './markdown-renderer.vue';
    import AiErrorTips from '../../../common/components/ai-error-tips.vue';
    import { formatTime } from '@/common/utils';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import AiLoadingSpinner from '@/common/components/ai-loading-spinner.vue';
    import { saveAsrResult } from '../services/api';
    export default {
        name: 'VoiceRecordResultPanel',
        components: {
            AiErrorTips,
            MarkdownRenderer,
            AiGradientButton,
            AiLoadingSpinner,
        },
        props: {
            outpatientSheetId: {
                type: String,
                default: '',
            },
            patientInfo: {
                type: Object,
                required: false,
                default: null,
            },
            audioData: {
                type: Array,
                default: () => [],
            },
            asrResult: {
                type: Array,
                default: () => [],
            },
            generating: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const voiceAnalyzeStore = useVoiceAnalyzeStore();
            const {
                analyzeResult,
            } = storeToRefs(voiceAnalyzeStore);
            const {
                startVoiceAnalyze,
                destroy,
            } = voiceAnalyzeStore;
            return {
                analyzeResult,
                startVoiceAnalyze,
                destroyVoiceAnalyze: destroy,
            };
        },
        data() {
            return {
                uploadAudioUrl: '',
                createdTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
                generateError: '',
            };
        },
        computed: {
            patientName() {
                return this.patientInfo?.name || '匿名患者';
            },
            isGenerating: {
                get() {
                    return this.generating;
                },
                set(value) {
                    this.$emit('update:generating', value);
                },
            },
            asrContent() {
                if (this.asrResult.length > 0) {
                    return this.asrResult.map((item) => `[${item.time}]: ${item.text}`).filter(Boolean).join('\n');
                }
                return '';
            },
            recordDuration() {
                const duration = this.asrResult.length > 0 ? this.asrResult[this.asrResult.length - 1].endTime : 0;
                return duration;
            },
            recordDurationFormated() {
                return formatTime(this.recordDuration);
            },
        },
        async created() {
            await Promise.all([
                this.generateMedicalRecord(this.asrContent),
                this.uploadAudioToOSS(this.audioData),
            ]);
            // 存储到后台便于后续分析
            // uploadAudioUrl, asrResult, analyzeResult
            if (this.outpatientSheetId && this.uploadAudioUrl) {
                await saveAsrResult(this.outpatientSheetId, {
                    uploadAudioUrl: this.uploadAudioUrl,
                    asrResult: this.asrResult,
                    medicalRecord: this.analyzeResult.content || '',
                    duration: this.recordDuration,
                });
            }
        },
        beforeDestroy() {
            this.destroyVoiceAnalyze();
        },
        methods: {
            /**
             * 将录音数据转为文件并上传到OSS
             * @param {Array} audioData 录音数据
             */
            async uploadAudioToOSS(audioData) {
                if (!this.outpatientSheetId) {
                    return;
                }
                if (audioData.length === 0) {
                    return;
                }

                if (!this.asrContent) {
                    return;
                }
                try {
                    // 将Int8Array数据转换为WAV格式
                    const wavBlob = this.createWavFile(audioData);

                    // 创建File对象
                    const fileName = `voice_record_${new Date().getTime()}.wav`;
                    const audioFile = new File([wavBlob], fileName, { type: 'audio/wav' });

                    // 上传到OSS
                    const options = {
                        filePath: 'voice-records', // 业务路径
                    };

                    // 使用临时目录上传，避免占用空间
                    const result = await this.$abcPlatform.service.oss.uploadForTemp(options, audioFile, (percentage) => {
                        console.debug('上传进度：', percentage);
                    });

                    console.debug('录音文件上传成功：', result.url);
                    this.uploadAudioUrl = result.url;
                } catch (error) {
                    console.error('录音文件上传失败：', error);
                }
            },

            /**
             * 将录音数据转换为WAV格式文件
             * @param {Array} audioData 录音数据（Int8Array格式）
             * @returns {Blob} WAV格式的Blob对象
             */
            createWavFile(audioData) {
                // 采样率16000，单声道，16位
                const sampleRate = 16000;
                const numChannels = 1;
                const bitsPerSample = 16;

                // 创建WAV文件头
                const dataLength = audioData.length;
                const buffer = new ArrayBuffer(44 + dataLength);
                const view = new DataView(buffer);

                // RIFF标识
                this.writeString(view, 0, 'RIFF');
                // 文件长度
                view.setUint32(4, 36 + dataLength, true);
                // WAVE标识
                this.writeString(view, 8, 'WAVE');
                // fmt子块标识
                this.writeString(view, 12, 'fmt ');
                // fmt子块长度
                view.setUint32(16, 16, true);
                // 音频格式（PCM = 1）
                view.setUint16(20, 1, true);
                // 声道数
                view.setUint16(22, numChannels, true);
                // 采样率
                view.setUint32(24, sampleRate, true);
                // 字节率 = 采样率 * 通道数 * 位深 / 8
                view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);
                // 块对齐 = 通道数 * 位深 / 8
                view.setUint16(32, numChannels * bitsPerSample / 8, true);
                // 位深
                view.setUint16(34, bitsPerSample, true);
                // data子块标识
                this.writeString(view, 36, 'data');
                // data子块长度
                view.setUint32(40, dataLength, true);

                // 写入音频数据
                const dataView = new Uint8Array(buffer, 44);
                for (let i = 0; i < dataLength; i++) {
                    dataView[i] = audioData[i];
                }

                return new Blob([buffer], { type: 'audio/wav' });
            },

            /**
             * 在DataView中写入字符串
             * @param {DataView} view DataView对象
             * @param {number} offset 偏移量
             * @param {string} string 要写入的字符串
             */
            writeString(view, offset, string) {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            },

            // 生成病历
            async generateMedicalRecord(asrContent) {
                this.isGenerating = true;

                try {
                    await this.startVoiceAnalyze(asrContent);
                } catch (error) {
                    console.error('生成病历错误:', error);
                } finally {
                    this.isGenerating = false;
                }
            },

            handleAcceptClick() {
                const AllMRKey = [
                    {
                        key: 'chiefComplaint', label: '主诉', matchLabel: '主诉',
                    },
                    {
                        key: 'presentHistory', label: '现病史', matchLabel: '现病史',
                    },
                    {
                        key: 'pastHistory', label: '既往史', matchLabel: '既往史',
                    },
                    {
                        key: 'familyHistory', label: '家族史', matchLabel: '家族史',
                    },
                    {
                        key: 'allergicHistory', label: '过敏史', matchLabel: '过敏史',
                    },
                    {
                        key: 'personalHistory', label: '个人史', matchLabel: '个人史',
                    },
                    {
                        key: 'obstetricalHistory', label: '月经婚育史', matchLabel: '月经婚育史',
                    },
                    {
                        key: 'physicalExamination', label: '体格检查', matchLabel: '体格检查',
                    },
                    {
                        key: 'chineseExamination', label: '望闻切诊', matchLabel: '望闻切诊',
                    },
                    {
                        key: 'oralExamination', label: '口腔检查', matchLabel: '口腔检查',
                    },
                    {
                        key: 'auxiliaryExaminations', label: '辅助检查', matchLabel: '辅助检查',
                    },
                    {
                        key: 'diagnosis', label: '诊断', matchLabel: '诊断',
                    },
                    {
                        key: 'syndrome', label: '辨证', matchLabel: '辨证',
                    },
                    {
                        key: 'therapy', label: '治法', matchLabel: '治法',
                    },
                    {
                        key: 'disposals', label: '处置', matchLabel: '处置-口腔',
                    },
                ];

                try {
                    // 解析病历内容
                    const medicalRecordData = this.parseMedicalRecord(this.analyzeResult.content, AllMRKey);
                    console.log('解析后的病历数据:', { ...medicalRecordData });

                    // 发送解析后的病历数据
                    this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                        type: 'medicalRecord',
                        value: medicalRecordData,
                    });

                    // 关闭对话框
                    this.$emit('finish');
                } catch (error) {
                    console.error('采纳病历失败:', error);
                }
            },

            // 解析病历内容，根据 AllMRKey 中定义的字段提取对应内容
            parseMedicalRecord(medicalRecord, mrKeys) {
                const result = {};
                if (!medicalRecord) {
                    return result;
                }

                const trimedMedicalRecord = medicalRecord.replaceAll('**', '');

                // 遍历所有病历字段
                mrKeys.forEach((item) => {
                    const {
                        key, matchLabel,
                    } = item;
                    const regex = new RegExp(`${matchLabel}[：:](.*?)(?=\\n\\n|\\n[^\\n]|$)`, 's');
                    const match = trimedMedicalRecord?.match(regex);

                    if (match && match[1]?.trim()) {
                        result[key] = match[1].trim();
                    }
                });

                return result;
            },

            handleRetryClick() {
                this.generateMedicalRecord(this.asrContent);
            },
        },
    };
</script>

<style lang="scss">
.voice-record-result-panel {
    .recording-result {
        width: 100%;
        padding: 10px;

        h3 {
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: bold;
            text-align: left;
        }

        .recording-content {
            max-height: 300px;
            padding: 15px;
            margin-bottom: 15px;
            overflow-y: auto;
            text-align: left;
            background-color: #f9f9f9;
            border: 1px solid #eeeeee;
            border-radius: var(--abc-border-radius-small);

            .recording-item {
                display: flex;
                margin-bottom: 8px;

                .recording-time {
                    min-width: 40px;
                    margin-right: 10px;
                    color: #999999;
                }

                .recording-text {
                    flex: 1;
                }
            }
        }
    }

    .voice-record-result__medical-record {
        flex: 1;
        width: 100%;
        height: 100%;
        padding: var(--abc-paddingTB-xl) 0 var(--abc-paddingTB-xl) var(--abc-paddingLR-xl);
        margin-top: 12px;

        .medical-record-scroll {
            height: 411px;

            .medical-record-result {
                padding-right: 6px;
            }
        }
    }

    .medical-record-actions {
        display: flex;
        flex-shrink: 0;
        justify-content: center;
        height: 55px;
        padding-top: 14px;
        margin-right: var(--abc-paddingLR-xl);
        border-top: 1px solid var(--abc-color-P8);

        .ai-anim-fadein {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
    }
}
</style>
