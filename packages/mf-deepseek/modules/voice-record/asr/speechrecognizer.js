import * as CryptoJS from 'crypto-js';
const TAG = 'SpeechRecognizer';
// 识别需要过滤的参数
const needFiltrationParams = ['appid', 'secretkey', 'signCallback', 'echoCancellation'];

function formatSignString(query, params) {
    let strParam = '';
    let signStr = 'asr.cloud.tencent.com/asr/v2/';
    if (query.appid) {
        signStr += query.appid;
    }
    const keys = Object.keys(params);
    keys.sort();
    for (let i = 0, len = keys.length; i < len; i++) {
        strParam += `&${keys[i]}=${params[keys[i]]}`;
    }
    return `${signStr}?${strParam.slice(1)}`;
}
export const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};
async function createQuery(query) {
    let params = {};
    const time = new Date().getTime();

    async function getServerTime() {
        return new Promise((resolve, reject) => {
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', 'https://asr.cloud.tencent.com/server_time', true);
                xhr.send();
                xhr.onreadystatechange = function () {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        resolve(xhr.responseText);
                    }
                };
            } catch (error) {
                reject(error);
            }
        });
    }
    const serverTime = await getServerTime();
    params.secretid = query.secretid || '';
    params.engine_model_type = query.engine_model_type || '16k_zh';
    params.timestamp = parseInt(serverTime) || Math.round(time / 1000);
    params.expired = Math.round(time / 1000) + 24 * 60 * 60;
    params.nonce = Math.round(time / 100000);
    params.voice_id = guid();
    params.voice_format = query.voice_format || 1;

    const tempQuery = { ...query };
    for (let i = 0, len = needFiltrationParams.length; i < len; i++) {
        if (tempQuery.hasOwnProperty(needFiltrationParams[i])) {
            delete tempQuery[needFiltrationParams[i]];
        }
    }

    params = {
        ...tempQuery,
        ...params,
    };
    return params;
}

/** 获取签名 start */

function toUint8Array(wordArray) {
    // Shortcuts
    const { words } = wordArray;
    const { sigBytes } = wordArray;

    // Convert
    const u8 = new Uint8Array(sigBytes);
    for (let i = 0; i < sigBytes; i++) {
        u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    }
    return u8;
}

function Uint8ArrayToString(fileData) {
    let dataString = '';
    for (let i = 0; i < fileData.length; i++) {
        dataString += String.fromCharCode(fileData[i]);
    }
    return dataString;
}
// 签名函数示例
function signCallback(secretKey, signStr) {
    const hash = CryptoJS.HmacSHA1(signStr, secretKey);
    const bytes = Uint8ArrayToString(toUint8Array(hash));
    return window.btoa(bytes);
}

/** 获取签名 end */

// 获取签名原文
async function getUrl(self, params) {
    if (!params.appid || !params.secretid) {
        self.isLog && console.log(self.requestId, '请确认是否填入账号信息', TAG);
        return false;
    }
    const urlQuery = await createQuery(params);
    self.logger?.report('SpeechRecognizer鉴权参数', {
        requestId: self.requestId,
        voice_id: urlQuery.voice_id,
    });
    const queryStr = formatSignString(params, urlQuery);
    let signature = '';
    if (params.signCallback) {
        signature = params.signCallback(queryStr);
    } else {
        signature = signCallback(params.secretkey, queryStr);
    }
    return `wss://${queryStr}&signature=${encodeURIComponent(signature)}`;
}


export default class SpeechRecognizer {
    constructor(params, requestId, isLog, logger) {
        this.socket = null;
        this.isSignSuccess = false; // 是否鉴权成功
        this.isSentenceBegin = false; // 是否一句话开始
        this.query = {
            ...params,
        };
        this.isRecognizeComplete = false; // 当前是否识别结束
        this.requestId = requestId;
        this.isLog = isLog;
        this.sendCount = 0;
        this.getMessageList = [];
        this.logger = logger;
    }
    // 暂停识别，关闭连接
    stop() {
        this.isLog && console.log('SpeechRecognizer.stop', this.socket, this.socket?.readyState);
        if (this.resultChangeTimer) {
            clearTimeout(this.resultChangeTimer);
            this.resultChangeTimer = null;
        }
        if (this.socket && this.socket.readyState === 1) {
            this.socket.send(JSON.stringify({ type: 'end' }));
            this.isRecognizeComplete = true;
        } else {
            // this.OnError({ code : 6003, message: '连接未建立或连接已关闭' });
            if (this.socket && this.socket.readyState === 1) {
                this.socket.close();
            }
        }
    }
    // 建立websocket链接 data 为用户收集的音频数据
    async start() {
        this.socket = null;
        this.getMessageList = [];
        const url = await getUrl(this, this.query);
        if (!url) {
            this.isLog && console.log(this.requestId, '鉴权失败', TAG);
            this.logger?.report('SpeechRecognizer鉴权失败', {
                query: this.query,
            });
            this.OnError('启动识别引擎失败，鉴权信息不完整');
            return;
        }
        this.isLog && console.log(this.requestId, 'get ws url', url, TAG);
        if ('WebSocket' in window) {
            this.socket = new WebSocket(url);
        } else if ('MozWebSocket' in window) {
            this.socket = new window.MozWebSocket(url);
        } else {
            this.isLog && console.log(this.requestId, '浏览器不支持WebSocket', TAG);
            this.OnError('启动识别引擎失败，浏览器不支持WebSocket');
            return;
        }
        this.isLog && console.log(this.requestId, 'start, socket: ',this.socket, TAG);
        this.logger?.report('SpeechRecognizer启动', {
            requestId: this.requestId,
        });
        return new Promise((resolve) => {
            this.socket.onopen = (e) => { // 连接建立时触发
                resolve();
                this.isLog && console.log(this.requestId, '连接建立', e, TAG);
                this.logger?.report('SpeechRecognizerSocketOpen', {
                    requestId: this.requestId,
                    event: e,
                });
            };
            this.socket.onmessage = async (e) => { // 连接建立时触发
                try {
                    this.getMessageList.push(JSON.stringify(e));
                    const response = JSON.parse(e.data);
                    if (response.code !== 0) {
                        if (this.socket.readyState === 1) {
                            this.socket.close();
                        }
                        this.isLog && console.log(this.requestId, JSON.stringify(response), TAG);
                        this.OnError(response);
                    } else {
                        if (!this.isSignSuccess) {
                            this.OnRecognitionStart(response);
                            this.isSignSuccess = true;
                        }
                        if (response.final === 1) {
                            // 清除可能存在的延迟定时器
                            if (this.resultChangeTimer) {
                                clearTimeout(this.resultChangeTimer);
                                this.resultChangeTimer = null;
                            }
                            this.OnRecognitionComplete(response);
                            return;
                        }
                        if (response.result) {
                            if (response.result.slice_type === 0) {
                                this.OnSentenceBegin(response);
                                this.isSentenceBegin = true;
                            } else if (response.result.slice_type === 2) {
                                // 清除可能存在的延迟定时器
                                if (this.resultChangeTimer) {
                                    clearTimeout(this.resultChangeTimer);
                                    this.resultChangeTimer = null;
                                }
                                if (!this.isSentenceBegin) {
                                    this.OnSentenceBegin(response);
                                }
                                this.OnSentenceEnd(response);
                            } else {
                                // 为 OnRecognitionResultChange 添加 300ms 延迟
                                // 清除之前的定时器，避免多次触发
                                if (this.resultChangeTimer) {
                                    clearTimeout(this.resultChangeTimer);
                                    this.resultChangeTimer = null;
                                }

                                // 保存当前响应，用于延迟回调
                                const currentResponse = { ...response };

                                // 设置新的定时器
                                this.resultChangeTimer = setTimeout(() => {
                                    this.OnRecognitionResultChange(currentResponse);
                                    this.resultChangeTimer = null;
                                }, 350);
                            }
                        }
                        this.isLog && console.log(this.requestId, response, TAG);
                    }
                } catch (e) {
                    this.isLog && console.log(this.requestId, 'socket.onmessage catch error', JSON.stringify(e), TAG);
                    this.logger?.report('SpeechRecognizerMessageCatch', {
                        requestId: this.requestId,
                        err: e,
                    });
                }

            };
            this.socket.onerror = (e) => { // 通信发生错误时触发
                this.isLog && console.log(this.requestId, 'socket error callback', e, TAG);
                this.logger?.report('SpeechRecognizerSocketError', {
                    requestId: this.requestId,
                    err: e,
                });
                this.socket.close();
                this.OnError(e);
            };
            this.socket.onclose = (event) => {
                try {
                    if (!this.isRecognizeComplete) {
                        this.logger?.report('SpeechRecognizerSocketClose', {
                            requestId: this.requestId,
                            event,
                        });
                        this.isLog && console.log(this.requestId, 'socket is close and error', JSON.stringify(event), TAG);
                        this.OnError(event);
                    }
                } catch (e) {
                    this.isLog && console.log(this.requestId, `socket is onclose catch${this.sendCount}`, JSON.stringify(e), TAG);
                    this.logger?.report('SpeechRecognizerSocketCloseCatch', {
                        requestId: this.requestId,
                        event,
                        err: e,
                    });
                }
                this.logger = null;
            };
        });
    }
    close() {
        this.socket && this.socket.readyState === 1 && this.socket.close(1000);
    }
    // 发送数据
    write(data) {
        try {
            if (!this.socket || String(this.socket.readyState) !== '1') {
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    if (this.socket && this.socket.readyState === 1) {
                        this.socket.send(data);
                    }
                }, 100);
                return false;
            }
            this.sendCount += 1;
            this.socket.send(data);
        } catch (e) {
            this.isLog && console.log(this.requestId , '发送数据 error catch', e, TAG);
            this.logger?.report('SpeechRecognizerWriteCatch', {
                requestId: this.requestId,
                err: e,
            });
        }
    }
    // 开始识别的时候
    OnRecognitionStart(res) {
        this.isLog && console.log(this.requestId, 'OnRecognitionStart', JSON.stringify(res), TAG);
    }
    // 一句话开始的时候
    OnSentenceBegin(res) {
        this.isLog && console.log(this.requestId, 'OnSentenceBegin', JSON.stringify(res), TAG);
    }
    // 识别结果发生变化的时候
    OnRecognitionResultChange() {

    }
    // 一句话结束的时候
    OnSentenceEnd() {

    }
    // 识别结束的时候
    OnRecognitionComplete() {

    }
    // 识别失败
    OnError() {

    }
}
