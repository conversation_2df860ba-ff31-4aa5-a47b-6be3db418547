<template>
    <div class="wave-visualization" :class="{ 'paused-wave': isPaused }">
        <div
            v-for="(item, index) in waveItems"
            :key="index"
            class="wave-bar"
            :style="{ height: `${item.height}px` }"
        ></div>
    </div>
</template>

<script>
    export default {
        name: 'WaveVisualization',
        props: {
            // 是否暂停状态
            isPaused: {
                type: Boolean,
                default: false,
            },
            amplitude: {
                type: [Number, null],
                default: null,
            },
            barSize: {
                type: Number,
                default: 2,
            },
        },
        data() {
            return {
                waveItems: [],
                waveAnimationInterval: null,
            };
        },
        watch: {
            isPaused(newVal) {
                if (newVal) {
                    this.pauseWaveAnimation();
                } else {
                    this.startWaveAnimation();
                }
            },
            amplitude(newVal) {
                this.updateWaveVisualization(newVal);
            },
        },
        created() {
            this.createWaveVisualization();
        },
        mounted() {
            this.startWaveAnimation();
        },
        beforeDestroy() {
            this.pauseWaveAnimation();
        },
        methods: {
            // 创建波形可视化
            createWaveVisualization() {
                this.waveItems = [];
                // 创建波形条
                for (let i = 0; i < 44; i++) {
                    this.waveItems.push({
                        height: Math.floor(Math.random() * 3) + this.barSize, // 2-12px的随机高度
                    });
                }
            },

            // 更新波形图根据振幅数据
            updateWaveVisualization(amplitude) {
                // 当没有振幅数据或振幅很小时，设置一个最小高度
                const minHeight = this.barSize;
                // 当振幅为1（最大值）时，设置最大高度
                const maxHeight = 32;

                // 如果振幅数据有效
                if (amplitude !== undefined && amplitude !== null && amplitude > 0) {
                    // 将振幅映射到高度范围
                    const height = Math.max(minHeight, Math.floor(amplitude * maxHeight));

                    // 为每个波形条设置不同的高度，使得波形看起来更自然
                    this.waveItems = this.waveItems.map((_, index) => {
                        let multiplier = 1.0;
                        // 根据位置计算高度乘数，边缘的波形条高度较低
                        if (index < 8 || index >= this.waveItems.length - 8) {
                            multiplier = 0.1 + (Math.min(index, this.waveItems.length - index - 1) * 0.05);
                        }
                        const randomHeight = Math.max(minHeight, Math.floor(height * multiplier * (0.7 + Math.random() * 0.6)));
                        return {
                            height: randomHeight,
                        };
                    });
                } else {
                    this.updateWaveVisualization(0.001);
                }
            },

            // 启动波形动画
            startWaveAnimation() {
                this.pauseWaveAnimation();
                // 当没有振幅数据时，使用随机波形作为后备
                this.waveAnimationInterval = setInterval(() => {
                    if (!this.amplitude) {
                        this.waveItems = this.waveItems.map(() => ({
                            height: Math.floor(Math.random() * 3) + this.barSize, // 3-5px的随机高度
                        }));
                    }
                }, 200);
            },

            // 暂停波形动画
            pauseWaveAnimation() {
                clearInterval(this.waveAnimationInterval);
            },
        },
    };
</script>

<style lang="scss">
@use "sass:math";

.wave-visualization {
    position: relative;
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    width: 304px;
    height: 32px;

    // 使用 CSS 变量来控制每个波形条的颜色
    @for $i from 1 through 44 {
        $position: math.div($i - 1, 43); // 0 到 1 之间的位置

        .wave-bar:nth-child(#{$i}) {
            width: 2px;
            border-radius: 2px;
            transition: height 0.1s ease;
            will-change: height;

            // 从左到右的渐变色
            @if $position < 0.2 {
                background-color: #644efc; // 左侧紫色
            }

            @else if $position < 0.4 {
                background-color: #5c72fd; // 偏紫色
            }

            @else if $position < 0.6 {
                background-color: #50abff; // 紫蓝过渡色
            }

            @else if $position < 0.8 {
                background-color: #4eb8ff; // 偏蓝色
            }

            @else {
                background-color: #4eb8ff; // 右侧蓝色
            }

            // 边缘的波形条颜色更浅
            // @if $i <= 8 or $i >= 37 {
            //     opacity: 0.6;
            // }
        }
    }

    &.paused-wave .wave-bar {
        background-color: #909399 !important;
    }
}
</style>
