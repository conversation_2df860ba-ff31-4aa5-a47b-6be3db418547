<template>
    <div class="wave-visualization-v2" :class="{ 'paused-wave': isPaused }">
        <canvas ref="waveCanvas" class="wave-canvas"></canvas>
    </div>
</template>

<script>
    export default {
        name: 'WaveVisualizationV2',
        props: {
            // 是否暂停状态
            isPaused: {
                type: Boolean,
                default: false,
            },
            waveformData: {
                type: [Float32Array],
                default: null,
            },
            // 波形颜色
            waveColor: {
                type: String,
                default: 'rgba(30, 199, 97, 1)',
            },
            // 暂停时的颜色
            pausedColor: {
                type: String,
                default: '#909399',
            },
            width: {
                type: Number,
                default: 320,
            },
            height: {
                type: Number,
                default: 38,
            },
            sampleRate: {
                type: Number,
                default: 33,
            },
            barWidth: {
                type: Number,
                default: 1,
            },
            barMinHeight: {
                type: Number,
                default: 2,
            },
            barGap: {
                type: Number,
                default: 2,
            },
            maxDataPoints: {
                type: Number,
                default: 200, // 默认最多保存100个数据点
            },
        },
        data() {
            return {
                ctx: null,
                waveData: [], // 存储波形数据
                animationId: null,
                lastTime: 0,
                lastSampleTime: 0, // 上次采样时间
            };
        },
        computed: {
            canvasWidth() {
                return this.width;
            },
            canvasHeight() {
                return this.height;
            },
        },
        watch: {
            isPaused(newVal) {
                if (newVal) {
                    this.pauseAnimation();
                } else {
                    this.startAnimation();
                }
            },
        },
        mounted() {
            this.initCanvas();
            this.startAnimation();
        },
        beforeDestroy() {
            this.pauseAnimation();
        },
        methods: {
            initCanvas() {
                const canvas = this.$refs.waveCanvas;
                this.ctx = canvas.getContext('2d');

                // 设置canvas尺寸，考虑设备像素比以保证清晰度
                const dpr = window.devicePixelRatio || 1;

                canvas.width = this.canvasWidth * dpr;
                canvas.height = this.canvasHeight * dpr;
                canvas.style.width = `${this.canvasWidth}px`;
                canvas.style.height = `${this.canvasHeight}px`;

                this.ctx.scale(dpr, dpr);

                // 初始化波形数据
                this.waveData = [];
            },

            drawWave() {
                const {
                    ctx, canvasWidth, canvasHeight,
                } = this;
                const centerY = canvasHeight / 2;

                // 清空画布
                ctx.clearRect(0, 0, canvasWidth, canvasHeight);

                // 没有数据，直接返回
                if (this.waveData.length === 0) {
                    return;
                }

                // 计算每个数据点的宽度
                const {
                    barWidth, barGap,
                } = this; // 每个波形线宽度
                const barSpacing = barWidth + barGap; // 总间距
                const totalWidth = this.waveData.length * barSpacing;

                // 波形从最右侧开始出现
                let startX = 0;
                let displayData = this.waveData;

                if (totalWidth < canvasWidth) {
                    // 如果总宽度小于画布宽度，右对齐显示
                    startX = canvasWidth - totalWidth;
                } else {
                    // 如果总宽度大于画布宽度，显示最新的数据
                    const visibleBars = Math.floor(canvasWidth / barSpacing);
                    const startIndex = Math.max(0, this.waveData.length - visibleBars);
                    displayData = this.waveData.slice(startIndex);
                    startX = 0;
                }

                // 如果是暂停状态，使用暂停颜色
                ctx.fillStyle = this.isPaused ? this.pausedColor : this.waveColor;

                // 绘制每个波形线
                for (let i = 0; i < displayData.length; i++) {
                    const x = startX + i * barSpacing;
                    const amplitude = displayData[i] * canvasHeight;

                    // 添加小的随机波动，使波形看起来更自然
                    const jitter = 1 + (Math.random() * 0.05 - 0.025);
                    const jitteredAmplitude = amplitude * jitter;

                    // 绘制线条（从中心向上下对称延伸）
                    const barHeight = Math.max(this.barMinHeight, jitteredAmplitude); // 最小高2像素
                    const y = centerY - barHeight / 2;

                    // 绘制圆角矩形
                    this.drawRoundedBar(ctx, x, y, barWidth, barHeight);
                }
            },

            // 绘制波形线条
            drawRoundedBar(ctx, x, y, width, height) {
                // 直接使用矩形绘制线条，更简单高效
                ctx.fillRect(x, y, width, height);
            },

            sampleAmplitude() {
                if (!this.waveformData || this.waveformData.length === 0) {
                    // 如果没有振幅数据，使用固定的小值
                    this.waveData.push(0);
                } else {
                    // 采样当前振幅值
                    const dataArray = Array.from(this.waveformData);

                    // 找出当前帧的最大振幅，而不是平均振幅
                    // 这样可以更好地捕捉瞬时的峰值
                    let maxAmplitude = 0;
                    for (let i = 0; i < dataArray.length; i++) {
                        maxAmplitude = Math.max(maxAmplitude, Math.abs(dataArray[i]));
                    }

                    // 使用平方根函数增强小信号
                    const enhancedAmplitude = Math.sqrt(maxAmplitude);

                    // 应用缩放因子，使振幅更明显
                    const scaledAmplitude = Math.min(1, enhancedAmplitude);

                    // 将振幅值加入数据数组
                    this.waveData.push(scaledAmplitude);

                    // 限制数据点数量，当超过最大值时移除最早的数据点
                    if (this.waveData.length > this.maxDataPoints) {
                        this.waveData.shift();
                    }
                }
            },

            animate(timestamp) {
                if (!this.lastTime) this.lastTime = timestamp;
                this.lastTime = timestamp;

                // 每隔一定时间采样一次
                if (!this.lastSampleTime) this.lastSampleTime = timestamp;
                const sampleInterval = 1000 / this.sampleRate; // 采样间隔

                if (!this.isPaused && timestamp - this.lastSampleTime >= sampleInterval) {
                    this.sampleAmplitude();
                    this.lastSampleTime = timestamp;
                }

                this.drawWave();
                this.animationId = requestAnimationFrame(this.animate);
            },

            startAnimation() {
                if (!this.animationId) {
                    this.lastTime = 0;
                    this.lastSampleTime = 0;
                    this.animationId = requestAnimationFrame(this.animate);
                }
            },

            pauseAnimation() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                    this.animationId = null;
                }
            },
        },
    };
</script>

<style lang="scss">
.wave-visualization-v2 {
    position: relative;
    width: 100%;
    height: 100%;

    .wave-canvas {
        width: 100%;
        height: 100%;
    }
}
</style>
