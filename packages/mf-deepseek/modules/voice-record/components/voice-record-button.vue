<template>
    <div :class="['voice-record-button', `voice-record-button--${ type}`]">
        <!-- 默认状态 -->
        <template v-if="type === 'record'">
            <svg
                class="btn-default"
                width="80"
                height="80"
                viewBox="0 0 80 80"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <g clip-path="url(#clip0_1469_3162)">
                    <circle
                        cx="40"
                        cy="40"
                        r="39"
                        fill="white"
                    />
                    <circle
                        cx="40"
                        cy="40"
                        r="39"
                        stroke="white"
                        stroke-width="2"
                    />
                    <circle
                        cx="40"
                        cy="40"
                        r="39"
                        stroke="url(#paint0_linear_1469_3162)"
                        stroke-width="2"
                    />
                    <g clip-path="url(#clip1_1469_3162)">
                        <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="white" fill-opacity="0.85" />
                        <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint1_linear_1469_3162)" />
                        <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint2_linear_1469_3162)" />
                        <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint3_linear_1469_3162)" />
                    </g>
                </g>
                <defs>
                    <linearGradient
                        id="paint0_linear_1469_3162"
                        x1="0"
                        y1="0"
                        x2="93.1481"
                        y2="20.628"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1469_3162"
                        x1="55.5"
                        y1="20"
                        x2="27"
                        y2="54.5"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#FB6FB3" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                    <linearGradient
                        id="paint2_linear_1469_3162"
                        x1="48.3846"
                        y1="30.1675"
                        x2="20.0901"
                        y2="41.2769"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#634DFC" />
                        <stop offset="0.541172" stop-color="#4FD9F5" />
                        <stop offset="1" stop-color="#4DE9F4" />
                    </linearGradient>
                    <linearGradient
                        id="paint3_linear_1469_3162"
                        x1="27"
                        y1="22.5"
                        x2="59.6626"
                        y2="36.1084"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                    <clipPath id="clip0_1469_3162">
                        <rect width="80" height="80" fill="white" />
                    </clipPath>
                    <clipPath id="clip1_1469_3162">
                        <rect
                            width="26"
                            height="40"
                            fill="white"
                            transform="translate(27 20)"
                        />
                    </clipPath>
                </defs>
            </svg>

            <svg
                class="btn-hover"
                width="80"
                height="80"
                viewBox="0 0 80 80"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="40"
                    cy="40"
                    r="39"
                    fill="#D5EAFF"
                />
                <circle
                    cx="40"
                    cy="40"
                    r="39"
                    stroke="white"
                    stroke-width="2"
                />
                <circle
                    cx="40"
                    cy="40"
                    r="39"
                    stroke="url(#paint0_linear_1296_3695)"
                    stroke-width="2"
                />
                <g clip-path="url(#clip0_1296_3695)">
                    <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="white" fill-opacity="0.85" />
                    <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint1_linear_1296_3695)" />
                    <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint2_linear_1296_3695)" />
                    <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint3_linear_1296_3695)" />
                </g>
                <defs>
                    <linearGradient
                        id="paint0_linear_1296_3695"
                        x1="0"
                        y1="0"
                        x2="93.1481"
                        y2="20.628"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1296_3695"
                        x1="55.5"
                        y1="20"
                        x2="27"
                        y2="54.5"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#FB6FB3" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                    <linearGradient
                        id="paint2_linear_1296_3695"
                        x1="48.3846"
                        y1="30.1675"
                        x2="20.0901"
                        y2="41.2769"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#634DFC" />
                        <stop offset="0.541172" stop-color="#4FD9F5" />
                        <stop offset="1" stop-color="#4DE9F4" />
                    </linearGradient>
                    <linearGradient
                        id="paint3_linear_1296_3695"
                        x1="27"
                        y1="22.5"
                        x2="59.6626"
                        y2="36.1084"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                    <clipPath id="clip0_1296_3695">
                        <rect
                            width="26"
                            height="40"
                            fill="white"
                            transform="translate(27 20)"
                        />
                    </clipPath>
                </defs>
            </svg>

            <svg
                class="btn-active"
                width="80"
                height="80"
                viewBox="0 0 80 80"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="40"
                    cy="40"
                    r="39"
                    fill="#C6E9FF"
                />
                <circle
                    cx="40"
                    cy="40"
                    r="39"
                    stroke="white"
                    stroke-width="2"
                />
                <circle
                    cx="40"
                    cy="40"
                    r="39"
                    stroke="url(#paint0_linear_1296_3702)"
                    stroke-width="2"
                />
                <g clip-path="url(#clip0_1296_3702)">
                    <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="white" fill-opacity="0.85" />
                    <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint1_linear_1296_3702)" />
                    <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint2_linear_1296_3702)" />
                    <path d="M27 38.5196C27 45.3877 31.5872 50.2058 38.3352 50.7665V54.8489H31.7643C31.0205 54.8489 30.4183 55.4447 30.4183 56.1805C30.4183 56.9164 31.0205 57.4945 31.7643 57.4945H47.5805C48.3243 57.4945 48.9264 56.9164 48.9264 56.1805C48.9264 55.4447 48.3243 54.8489 47.5805 54.8489H41.0096V50.7665C47.7753 50.2058 52.3447 45.3877 52.3447 38.5196V34.9628C52.3447 34.2269 51.7602 33.6487 51.0164 33.6487C50.2725 33.6487 49.6704 34.2269 49.6704 34.9628V38.4144C49.6704 44.3891 45.7384 48.3487 39.6812 48.3487C33.6063 48.3487 29.6744 44.3891 29.6744 38.4144V34.9628C29.6744 34.2269 29.0899 33.6487 28.3283 33.6487C27.5845 33.6487 27 34.2269 27 34.9628V38.5196ZM33.2343 37.8888C33.2343 41.9186 35.8556 44.7569 39.6812 44.7569C43.4891 44.7569 46.1104 41.9186 46.1104 37.8888V26.8682C46.1104 22.8209 43.4891 20 39.6812 20C35.8556 20 33.2343 22.8209 33.2343 26.8682V37.8888ZM35.9087 37.8888V26.8682C35.9087 24.2751 37.4142 22.6106 39.6812 22.6106C41.9483 22.6106 43.436 24.2751 43.436 26.8682V37.8888C43.436 40.4818 41.9483 42.1464 39.6812 42.1464C37.4142 42.1464 35.9087 40.4818 35.9087 37.8888Z" fill="url(#paint3_linear_1296_3702)" />
                </g>
                <defs>
                    <linearGradient
                        id="paint0_linear_1296_3702"
                        x1="0"
                        y1="0"
                        x2="93.1481"
                        y2="20.628"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1296_3702"
                        x1="55.5"
                        y1="20"
                        x2="27"
                        y2="54.5"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#FB6FB3" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                    <linearGradient
                        id="paint2_linear_1296_3702"
                        x1="48.3846"
                        y1="30.1675"
                        x2="20.0901"
                        y2="41.2769"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#634DFC" />
                        <stop offset="0.541172" stop-color="#4FD9F5" />
                        <stop offset="1" stop-color="#4DE9F4" />
                    </linearGradient>
                    <linearGradient
                        id="paint3_linear_1296_3702"
                        x1="27"
                        y1="22.5"
                        x2="59.6626"
                        y2="36.1084"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                    <clipPath id="clip0_1296_3702">
                        <rect
                            width="26"
                            height="40"
                            fill="white"
                            transform="translate(27 20)"
                        />
                    </clipPath>
                </defs>
            </svg>
        </template>

        <template v-else-if="type === 'pause'">
            <svg
                class="btn-default"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    fill="white"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="#EAEDF1"
                    stroke-width="1.5"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="url(#paint0_linear_1296_3710)"
                    stroke-width="1.5"
                />
                <path d="M21.014 26.616V27.442C20.356 27.498 19.67 27.568 18.956 27.638V28.716H18.032V27.708C17.108 27.764 16.128 27.834 15.12 27.89L14.994 26.994C16.058 26.938 17.08 26.896 18.032 26.84V25.678H15.428L15.204 24.88C15.596 24.432 15.974 23.9 16.31 23.27H14.812V22.374H16.758C16.884 22.066 17.01 21.758 17.122 21.45L18.13 21.618C18.032 21.87 17.934 22.122 17.822 22.374H21.238V23.27H17.402C17.094 23.858 16.758 24.39 16.408 24.852H18.032V23.928H18.956V24.852H21.14V25.678H18.956V26.784C19.684 26.728 20.37 26.672 21.014 26.616ZM22.974 25.692C22.82 26.868 22.442 27.89 21.84 28.758L21.14 28.03C21.756 27.12 22.092 26.042 22.12 24.782V22.584C23.856 22.416 25.228 22.108 26.208 21.66L26.852 22.416C25.816 22.864 24.556 23.186 23.044 23.354V24.81H27.244V25.692H25.76V28.73H24.794V25.692H22.974ZM25.676 29.136V34.4H24.64V33.854H17.528V34.4H16.492V29.136H25.676ZM17.528 32.986H24.64V31.894H17.528V32.986ZM17.528 31.082H24.64V29.99H17.528V31.082ZM31.374 21.492L32.312 21.926C31.99 22.962 31.598 23.956 31.136 24.88V34.4H30.114V26.63C29.722 27.218 29.288 27.792 28.826 28.324L28.49 27.26C29.806 25.58 30.772 23.648 31.374 21.492ZM36.582 21.492C36.722 21.842 36.848 22.248 36.988 22.696H40.978V23.592H32.312V22.696H35.98C35.854 22.332 35.714 21.982 35.56 21.66L36.582 21.492ZM33.236 24.404H39.9V27.05H33.236V24.404ZM38.92 26.28V25.174H34.216V26.28H38.92ZM41.006 27.862V30.256H40.25V30.886H37.226V33.154C37.226 33.91 36.806 34.302 35.994 34.302H34.412L34.202 33.322C34.706 33.35 35.182 33.378 35.644 33.378C36.008 33.378 36.19 33.21 36.19 32.902V30.886H33.124V30.256H32.186V27.862H41.006ZM33.166 30.018H40.04V28.744H33.166V30.018Z" fill="#F04A3E" />
                <path d="M21.014 26.616V27.442C20.356 27.498 19.67 27.568 18.956 27.638V28.716H18.032V27.708C17.108 27.764 16.128 27.834 15.12 27.89L14.994 26.994C16.058 26.938 17.08 26.896 18.032 26.84V25.678H15.428L15.204 24.88C15.596 24.432 15.974 23.9 16.31 23.27H14.812V22.374H16.758C16.884 22.066 17.01 21.758 17.122 21.45L18.13 21.618C18.032 21.87 17.934 22.122 17.822 22.374H21.238V23.27H17.402C17.094 23.858 16.758 24.39 16.408 24.852H18.032V23.928H18.956V24.852H21.14V25.678H18.956V26.784C19.684 26.728 20.37 26.672 21.014 26.616ZM22.974 25.692C22.82 26.868 22.442 27.89 21.84 28.758L21.14 28.03C21.756 27.12 22.092 26.042 22.12 24.782V22.584C23.856 22.416 25.228 22.108 26.208 21.66L26.852 22.416C25.816 22.864 24.556 23.186 23.044 23.354V24.81H27.244V25.692H25.76V28.73H24.794V25.692H22.974ZM25.676 29.136V34.4H24.64V33.854H17.528V34.4H16.492V29.136H25.676ZM17.528 32.986H24.64V31.894H17.528V32.986ZM17.528 31.082H24.64V29.99H17.528V31.082ZM31.374 21.492L32.312 21.926C31.99 22.962 31.598 23.956 31.136 24.88V34.4H30.114V26.63C29.722 27.218 29.288 27.792 28.826 28.324L28.49 27.26C29.806 25.58 30.772 23.648 31.374 21.492ZM36.582 21.492C36.722 21.842 36.848 22.248 36.988 22.696H40.978V23.592H32.312V22.696H35.98C35.854 22.332 35.714 21.982 35.56 21.66L36.582 21.492ZM33.236 24.404H39.9V27.05H33.236V24.404ZM38.92 26.28V25.174H34.216V26.28H38.92ZM41.006 27.862V30.256H40.25V30.886H37.226V33.154C37.226 33.91 36.806 34.302 35.994 34.302H34.412L34.202 33.322C34.706 33.35 35.182 33.378 35.644 33.378C36.008 33.378 36.19 33.21 36.19 32.902V30.886H33.124V30.256H32.186V27.862H41.006ZM33.166 30.018H40.04V28.744H33.166V30.018Z" fill="url(#paint1_linear_1296_3710)" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1296_3710"
                        x1="0"
                        y1="0"
                        x2="65.2037"
                        y2="14.4396"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1296_3710"
                        x1="5"
                        y1="20.5"
                        x2="45.6845"
                        y2="39.7765"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                </defs>
            </svg>

            <svg
                class="btn-hover"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    fill="#D5EAFF"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="#EAEDF1"
                    stroke-width="1.5"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="url(#paint0_linear_1296_3714)"
                    stroke-width="1.5"
                />
                <path d="M21.014 26.616V27.442C20.356 27.498 19.67 27.568 18.956 27.638V28.716H18.032V27.708C17.108 27.764 16.128 27.834 15.12 27.89L14.994 26.994C16.058 26.938 17.08 26.896 18.032 26.84V25.678H15.428L15.204 24.88C15.596 24.432 15.974 23.9 16.31 23.27H14.812V22.374H16.758C16.884 22.066 17.01 21.758 17.122 21.45L18.13 21.618C18.032 21.87 17.934 22.122 17.822 22.374H21.238V23.27H17.402C17.094 23.858 16.758 24.39 16.408 24.852H18.032V23.928H18.956V24.852H21.14V25.678H18.956V26.784C19.684 26.728 20.37 26.672 21.014 26.616ZM22.974 25.692C22.82 26.868 22.442 27.89 21.84 28.758L21.14 28.03C21.756 27.12 22.092 26.042 22.12 24.782V22.584C23.856 22.416 25.228 22.108 26.208 21.66L26.852 22.416C25.816 22.864 24.556 23.186 23.044 23.354V24.81H27.244V25.692H25.76V28.73H24.794V25.692H22.974ZM25.676 29.136V34.4H24.64V33.854H17.528V34.4H16.492V29.136H25.676ZM17.528 32.986H24.64V31.894H17.528V32.986ZM17.528 31.082H24.64V29.99H17.528V31.082ZM31.374 21.492L32.312 21.926C31.99 22.962 31.598 23.956 31.136 24.88V34.4H30.114V26.63C29.722 27.218 29.288 27.792 28.826 28.324L28.49 27.26C29.806 25.58 30.772 23.648 31.374 21.492ZM36.582 21.492C36.722 21.842 36.848 22.248 36.988 22.696H40.978V23.592H32.312V22.696H35.98C35.854 22.332 35.714 21.982 35.56 21.66L36.582 21.492ZM33.236 24.404H39.9V27.05H33.236V24.404ZM38.92 26.28V25.174H34.216V26.28H38.92ZM41.006 27.862V30.256H40.25V30.886H37.226V33.154C37.226 33.91 36.806 34.302 35.994 34.302H34.412L34.202 33.322C34.706 33.35 35.182 33.378 35.644 33.378C36.008 33.378 36.19 33.21 36.19 32.902V30.886H33.124V30.256H32.186V27.862H41.006ZM33.166 30.018H40.04V28.744H33.166V30.018Z" fill="#F04A3E" />
                <path d="M21.014 26.616V27.442C20.356 27.498 19.67 27.568 18.956 27.638V28.716H18.032V27.708C17.108 27.764 16.128 27.834 15.12 27.89L14.994 26.994C16.058 26.938 17.08 26.896 18.032 26.84V25.678H15.428L15.204 24.88C15.596 24.432 15.974 23.9 16.31 23.27H14.812V22.374H16.758C16.884 22.066 17.01 21.758 17.122 21.45L18.13 21.618C18.032 21.87 17.934 22.122 17.822 22.374H21.238V23.27H17.402C17.094 23.858 16.758 24.39 16.408 24.852H18.032V23.928H18.956V24.852H21.14V25.678H18.956V26.784C19.684 26.728 20.37 26.672 21.014 26.616ZM22.974 25.692C22.82 26.868 22.442 27.89 21.84 28.758L21.14 28.03C21.756 27.12 22.092 26.042 22.12 24.782V22.584C23.856 22.416 25.228 22.108 26.208 21.66L26.852 22.416C25.816 22.864 24.556 23.186 23.044 23.354V24.81H27.244V25.692H25.76V28.73H24.794V25.692H22.974ZM25.676 29.136V34.4H24.64V33.854H17.528V34.4H16.492V29.136H25.676ZM17.528 32.986H24.64V31.894H17.528V32.986ZM17.528 31.082H24.64V29.99H17.528V31.082ZM31.374 21.492L32.312 21.926C31.99 22.962 31.598 23.956 31.136 24.88V34.4H30.114V26.63C29.722 27.218 29.288 27.792 28.826 28.324L28.49 27.26C29.806 25.58 30.772 23.648 31.374 21.492ZM36.582 21.492C36.722 21.842 36.848 22.248 36.988 22.696H40.978V23.592H32.312V22.696H35.98C35.854 22.332 35.714 21.982 35.56 21.66L36.582 21.492ZM33.236 24.404H39.9V27.05H33.236V24.404ZM38.92 26.28V25.174H34.216V26.28H38.92ZM41.006 27.862V30.256H40.25V30.886H37.226V33.154C37.226 33.91 36.806 34.302 35.994 34.302H34.412L34.202 33.322C34.706 33.35 35.182 33.378 35.644 33.378C36.008 33.378 36.19 33.21 36.19 32.902V30.886H33.124V30.256H32.186V27.862H41.006ZM33.166 30.018H40.04V28.744H33.166V30.018Z" fill="url(#paint1_linear_1296_3714)" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1296_3714"
                        x1="0"
                        y1="0"
                        x2="65.2037"
                        y2="14.4396"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1296_3714"
                        x1="5"
                        y1="20.5"
                        x2="45.6845"
                        y2="39.7765"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                </defs>
            </svg>

            <svg
                class="btn-active"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    fill="#B5D5F4"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="#EAEDF1"
                    stroke-width="1.5"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="url(#paint0_linear_1296_3718)"
                    stroke-width="1.5"
                />
                <path d="M21.014 26.616V27.442C20.356 27.498 19.67 27.568 18.956 27.638V28.716H18.032V27.708C17.108 27.764 16.128 27.834 15.12 27.89L14.994 26.994C16.058 26.938 17.08 26.896 18.032 26.84V25.678H15.428L15.204 24.88C15.596 24.432 15.974 23.9 16.31 23.27H14.812V22.374H16.758C16.884 22.066 17.01 21.758 17.122 21.45L18.13 21.618C18.032 21.87 17.934 22.122 17.822 22.374H21.238V23.27H17.402C17.094 23.858 16.758 24.39 16.408 24.852H18.032V23.928H18.956V24.852H21.14V25.678H18.956V26.784C19.684 26.728 20.37 26.672 21.014 26.616ZM22.974 25.692C22.82 26.868 22.442 27.89 21.84 28.758L21.14 28.03C21.756 27.12 22.092 26.042 22.12 24.782V22.584C23.856 22.416 25.228 22.108 26.208 21.66L26.852 22.416C25.816 22.864 24.556 23.186 23.044 23.354V24.81H27.244V25.692H25.76V28.73H24.794V25.692H22.974ZM25.676 29.136V34.4H24.64V33.854H17.528V34.4H16.492V29.136H25.676ZM17.528 32.986H24.64V31.894H17.528V32.986ZM17.528 31.082H24.64V29.99H17.528V31.082ZM31.374 21.492L32.312 21.926C31.99 22.962 31.598 23.956 31.136 24.88V34.4H30.114V26.63C29.722 27.218 29.288 27.792 28.826 28.324L28.49 27.26C29.806 25.58 30.772 23.648 31.374 21.492ZM36.582 21.492C36.722 21.842 36.848 22.248 36.988 22.696H40.978V23.592H32.312V22.696H35.98C35.854 22.332 35.714 21.982 35.56 21.66L36.582 21.492ZM33.236 24.404H39.9V27.05H33.236V24.404ZM38.92 26.28V25.174H34.216V26.28H38.92ZM41.006 27.862V30.256H40.25V30.886H37.226V33.154C37.226 33.91 36.806 34.302 35.994 34.302H34.412L34.202 33.322C34.706 33.35 35.182 33.378 35.644 33.378C36.008 33.378 36.19 33.21 36.19 32.902V30.886H33.124V30.256H32.186V27.862H41.006ZM33.166 30.018H40.04V28.744H33.166V30.018Z" fill="#F04A3E" />
                <path d="M21.014 26.616V27.442C20.356 27.498 19.67 27.568 18.956 27.638V28.716H18.032V27.708C17.108 27.764 16.128 27.834 15.12 27.89L14.994 26.994C16.058 26.938 17.08 26.896 18.032 26.84V25.678H15.428L15.204 24.88C15.596 24.432 15.974 23.9 16.31 23.27H14.812V22.374H16.758C16.884 22.066 17.01 21.758 17.122 21.45L18.13 21.618C18.032 21.87 17.934 22.122 17.822 22.374H21.238V23.27H17.402C17.094 23.858 16.758 24.39 16.408 24.852H18.032V23.928H18.956V24.852H21.14V25.678H18.956V26.784C19.684 26.728 20.37 26.672 21.014 26.616ZM22.974 25.692C22.82 26.868 22.442 27.89 21.84 28.758L21.14 28.03C21.756 27.12 22.092 26.042 22.12 24.782V22.584C23.856 22.416 25.228 22.108 26.208 21.66L26.852 22.416C25.816 22.864 24.556 23.186 23.044 23.354V24.81H27.244V25.692H25.76V28.73H24.794V25.692H22.974ZM25.676 29.136V34.4H24.64V33.854H17.528V34.4H16.492V29.136H25.676ZM17.528 32.986H24.64V31.894H17.528V32.986ZM17.528 31.082H24.64V29.99H17.528V31.082ZM31.374 21.492L32.312 21.926C31.99 22.962 31.598 23.956 31.136 24.88V34.4H30.114V26.63C29.722 27.218 29.288 27.792 28.826 28.324L28.49 27.26C29.806 25.58 30.772 23.648 31.374 21.492ZM36.582 21.492C36.722 21.842 36.848 22.248 36.988 22.696H40.978V23.592H32.312V22.696H35.98C35.854 22.332 35.714 21.982 35.56 21.66L36.582 21.492ZM33.236 24.404H39.9V27.05H33.236V24.404ZM38.92 26.28V25.174H34.216V26.28H38.92ZM41.006 27.862V30.256H40.25V30.886H37.226V33.154C37.226 33.91 36.806 34.302 35.994 34.302H34.412L34.202 33.322C34.706 33.35 35.182 33.378 35.644 33.378C36.008 33.378 36.19 33.21 36.19 32.902V30.886H33.124V30.256H32.186V27.862H41.006ZM33.166 30.018H40.04V28.744H33.166V30.018Z" fill="url(#paint1_linear_1296_3718)" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1296_3718"
                        x1="0"
                        y1="0"
                        x2="65.2037"
                        y2="14.4396"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1296_3718"
                        x1="5"
                        y1="20.5"
                        x2="45.6845"
                        y2="39.7765"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                </defs>
            </svg>
        </template>

        <template v-else-if="type === 'stop'">
            <svg
                class="btn-default"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="28"
                    fill="#F04A3E"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="28"
                    fill="url(#paint0_linear_1368_2996)"
                />
                <path d="M26.922 23.06V26.196H25.9V24.04H16.072V26.196H15.05V23.06H20.426C20.258 22.584 20.09 22.15 19.894 21.758L21.028 21.562C21.21 22.01 21.392 22.5 21.56 23.06H26.922ZM17.052 25.664H24.892V26.644H17.052V25.664ZM14.91 28.296H27.104V29.262H23.142V32.58C23.142 33 23.338 33.224 23.744 33.224H25.606C25.858 33.224 26.026 33.126 26.124 32.944C26.236 32.734 26.32 32.104 26.376 31.04L27.328 31.348C27.244 32.692 27.076 33.518 26.824 33.798C26.6 34.05 26.264 34.19 25.788 34.19H23.422C22.554 34.19 22.134 33.714 22.134 32.79V29.262H19.782V29.36C19.74 30.732 19.362 31.838 18.662 32.678C17.962 33.434 16.828 34.036 15.26 34.47L14.686 33.574C16.212 33.182 17.276 32.664 17.892 32.006C18.452 31.334 18.746 30.452 18.788 29.36V29.262H14.91V28.296ZM38.416 21.52C39.088 22.024 39.662 22.542 40.138 23.06L39.494 23.704H41.202V24.67H36.89C36.988 26.406 37.17 27.778 37.422 28.786C37.506 29.136 37.604 29.458 37.702 29.766C38.402 28.646 38.962 27.33 39.396 25.832L40.306 26.224C39.746 28.072 39.018 29.64 38.122 30.9C38.318 31.348 38.528 31.74 38.752 32.062C39.214 32.734 39.592 33.07 39.872 33.07C40.124 33.056 40.362 32.328 40.572 30.886L41.482 31.39C41.146 33.252 40.67 34.19 40.04 34.19C39.452 34.19 38.822 33.77 38.15 32.93C37.898 32.594 37.66 32.202 37.45 31.768C36.47 32.916 35.322 33.77 34.006 34.358L33.446 33.518C34.846 32.888 36.036 31.964 37.002 30.746C36.806 30.228 36.624 29.668 36.47 29.066C36.162 27.89 35.966 26.42 35.854 24.67H31.052V26.994H34.482C34.454 29.36 34.342 30.886 34.16 31.572C33.978 32.174 33.544 32.482 32.844 32.51C32.508 32.51 32.102 32.482 31.654 32.454L31.346 31.544C31.892 31.572 32.354 31.586 32.732 31.586C33.04 31.572 33.236 31.362 33.32 30.97C33.404 30.494 33.446 29.486 33.474 27.946H31.052V28.366C30.996 30.872 30.436 32.888 29.4 34.4L28.63 33.714C29.526 32.426 29.988 30.648 30.03 28.366V23.704H35.812C35.784 23.004 35.77 22.29 35.77 21.534H36.806C36.806 22.318 36.82 23.032 36.848 23.704H39.466C39.046 23.2 38.486 22.682 37.772 22.136L38.416 21.52Z" fill="white" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1368_2996"
                        x1="8.5"
                        y1="-8.5"
                        x2="71.512"
                        y2="15.8366"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                </defs>
            </svg>

            <svg
                class="btn-hover"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="28"
                    fill="#F04A3E"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="28"
                    fill="url(#paint0_linear_1368_3000)"
                />
                <path d="M26.922 23.06V26.196H25.9V24.04H16.072V26.196H15.05V23.06H20.426C20.258 22.584 20.09 22.15 19.894 21.758L21.028 21.562C21.21 22.01 21.392 22.5 21.56 23.06H26.922ZM17.052 25.664H24.892V26.644H17.052V25.664ZM14.91 28.296H27.104V29.262H23.142V32.58C23.142 33 23.338 33.224 23.744 33.224H25.606C25.858 33.224 26.026 33.126 26.124 32.944C26.236 32.734 26.32 32.104 26.376 31.04L27.328 31.348C27.244 32.692 27.076 33.518 26.824 33.798C26.6 34.05 26.264 34.19 25.788 34.19H23.422C22.554 34.19 22.134 33.714 22.134 32.79V29.262H19.782V29.36C19.74 30.732 19.362 31.838 18.662 32.678C17.962 33.434 16.828 34.036 15.26 34.47L14.686 33.574C16.212 33.182 17.276 32.664 17.892 32.006C18.452 31.334 18.746 30.452 18.788 29.36V29.262H14.91V28.296ZM38.416 21.52C39.088 22.024 39.662 22.542 40.138 23.06L39.494 23.704H41.202V24.67H36.89C36.988 26.406 37.17 27.778 37.422 28.786C37.506 29.136 37.604 29.458 37.702 29.766C38.402 28.646 38.962 27.33 39.396 25.832L40.306 26.224C39.746 28.072 39.018 29.64 38.122 30.9C38.318 31.348 38.528 31.74 38.752 32.062C39.214 32.734 39.592 33.07 39.872 33.07C40.124 33.056 40.362 32.328 40.572 30.886L41.482 31.39C41.146 33.252 40.67 34.19 40.04 34.19C39.452 34.19 38.822 33.77 38.15 32.93C37.898 32.594 37.66 32.202 37.45 31.768C36.47 32.916 35.322 33.77 34.006 34.358L33.446 33.518C34.846 32.888 36.036 31.964 37.002 30.746C36.806 30.228 36.624 29.668 36.47 29.066C36.162 27.89 35.966 26.42 35.854 24.67H31.052V26.994H34.482C34.454 29.36 34.342 30.886 34.16 31.572C33.978 32.174 33.544 32.482 32.844 32.51C32.508 32.51 32.102 32.482 31.654 32.454L31.346 31.544C31.892 31.572 32.354 31.586 32.732 31.586C33.04 31.572 33.236 31.362 33.32 30.97C33.404 30.494 33.446 29.486 33.474 27.946H31.052V28.366C30.996 30.872 30.436 32.888 29.4 34.4L28.63 33.714C29.526 32.426 29.988 30.648 30.03 28.366V23.704H35.812C35.784 23.004 35.77 22.29 35.77 21.534H36.806C36.806 22.318 36.82 23.032 36.848 23.704H39.466C39.046 23.2 38.486 22.682 37.772 22.136L38.416 21.52Z" fill="white" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1368_3000"
                        x1="8.5"
                        y1="-8.5"
                        x2="71.512"
                        y2="15.8366"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7AD5FF" />
                        <stop offset="0.618831" stop-color="#70A4FF" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                </defs>
            </svg>

            <svg
                class="btn-active"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="28"
                    fill="#F04A3E"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="28"
                    fill="url(#paint0_linear_1368_3004)"
                />
                <path d="M26.922 23.06V26.196H25.9V24.04H16.072V26.196H15.05V23.06H20.426C20.258 22.584 20.09 22.15 19.894 21.758L21.028 21.562C21.21 22.01 21.392 22.5 21.56 23.06H26.922ZM17.052 25.664H24.892V26.644H17.052V25.664ZM14.91 28.296H27.104V29.262H23.142V32.58C23.142 33 23.338 33.224 23.744 33.224H25.606C25.858 33.224 26.026 33.126 26.124 32.944C26.236 32.734 26.32 32.104 26.376 31.04L27.328 31.348C27.244 32.692 27.076 33.518 26.824 33.798C26.6 34.05 26.264 34.19 25.788 34.19H23.422C22.554 34.19 22.134 33.714 22.134 32.79V29.262H19.782V29.36C19.74 30.732 19.362 31.838 18.662 32.678C17.962 33.434 16.828 34.036 15.26 34.47L14.686 33.574C16.212 33.182 17.276 32.664 17.892 32.006C18.452 31.334 18.746 30.452 18.788 29.36V29.262H14.91V28.296ZM38.416 21.52C39.088 22.024 39.662 22.542 40.138 23.06L39.494 23.704H41.202V24.67H36.89C36.988 26.406 37.17 27.778 37.422 28.786C37.506 29.136 37.604 29.458 37.702 29.766C38.402 28.646 38.962 27.33 39.396 25.832L40.306 26.224C39.746 28.072 39.018 29.64 38.122 30.9C38.318 31.348 38.528 31.74 38.752 32.062C39.214 32.734 39.592 33.07 39.872 33.07C40.124 33.056 40.362 32.328 40.572 30.886L41.482 31.39C41.146 33.252 40.67 34.19 40.04 34.19C39.452 34.19 38.822 33.77 38.15 32.93C37.898 32.594 37.66 32.202 37.45 31.768C36.47 32.916 35.322 33.77 34.006 34.358L33.446 33.518C34.846 32.888 36.036 31.964 37.002 30.746C36.806 30.228 36.624 29.668 36.47 29.066C36.162 27.89 35.966 26.42 35.854 24.67H31.052V26.994H34.482C34.454 29.36 34.342 30.886 34.16 31.572C33.978 32.174 33.544 32.482 32.844 32.51C32.508 32.51 32.102 32.482 31.654 32.454L31.346 31.544C31.892 31.572 32.354 31.586 32.732 31.586C33.04 31.572 33.236 31.362 33.32 30.97C33.404 30.494 33.446 29.486 33.474 27.946H31.052V28.366C30.996 30.872 30.436 32.888 29.4 34.4L28.63 33.714C29.526 32.426 29.988 30.648 30.03 28.366V23.704H35.812C35.784 23.004 35.77 22.29 35.77 21.534H36.806C36.806 22.318 36.82 23.032 36.848 23.704H39.466C39.046 23.2 38.486 22.682 37.772 22.136L38.416 21.52Z" fill="white" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1368_3004"
                        x1="8.5"
                        y1="-8.5"
                        x2="71.512"
                        y2="15.8366"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#4FC8FE" />
                        <stop offset="0.618831" stop-color="#508FFB" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                </defs>
            </svg>
        </template>

        <template v-else-if="type === 'continue'">
            <svg
                class="btn-default"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    fill="white"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="#EAEDF1"
                    stroke-width="1.5"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="url(#paint0_linear_1296_3722)"
                    stroke-width="1.5"
                />
                <path d="M21.56 26.084H23.814V21.744H24.78V26.084H26.992V26.994H24.78V27.414C25.592 28.17 26.404 29.024 27.216 29.976L26.67 30.802C25.928 29.738 25.298 28.912 24.78 28.296V32.202H23.814V27.344C23.352 28.912 22.638 30.298 21.672 31.516L21.266 30.536C22.218 29.5 22.904 28.31 23.324 26.994H21.56V26.084ZM19.81 21.954H20.776V32.958H27.342V33.924H19.81V21.954ZM26.292 22.444L27.104 22.654C26.838 23.704 26.502 24.628 26.11 25.412L25.354 25.146C25.718 24.432 26.026 23.536 26.292 22.444ZM22.218 22.654C22.568 23.41 22.876 24.264 23.156 25.244L22.4 25.426C22.148 24.558 21.826 23.732 21.434 22.92L22.218 22.654ZM19.278 31.614V32.566C17.962 33.098 16.478 33.518 14.798 33.84L14.672 32.86C16.422 32.552 17.962 32.132 19.278 31.614ZM16.996 21.52L17.948 21.884C17.262 23.69 16.576 25.132 15.876 26.21C16.52 26.126 17.15 26.028 17.794 25.902C18.046 25.426 18.298 24.922 18.55 24.404L19.418 24.726C18.186 27.12 17.178 28.814 16.366 29.808C17.318 29.612 18.27 29.346 19.222 29.01V29.906C17.822 30.382 16.492 30.718 15.218 30.886L14.938 29.976C15.12 29.906 15.274 29.808 15.386 29.696C15.904 29.136 16.548 28.17 17.304 26.826C16.52 26.966 15.736 27.106 14.938 27.218L14.686 26.294C14.84 26.224 14.98 26.098 15.106 25.916C15.834 24.628 16.464 23.172 16.996 21.52ZM33.04 31.446V32.398C31.822 32.902 30.45 33.308 28.91 33.616L28.784 32.636C30.408 32.342 31.822 31.95 33.04 31.446ZM30.912 21.562L31.864 21.94C31.22 23.676 30.562 25.076 29.89 26.126C30.464 26.056 31.038 25.958 31.626 25.846C31.878 25.384 32.13 24.894 32.396 24.376L33.32 24.726C32.102 26.966 31.094 28.576 30.31 29.528C31.15 29.36 32.004 29.122 32.844 28.814V29.724C31.528 30.172 30.282 30.466 29.092 30.634L28.812 29.71C28.994 29.654 29.148 29.556 29.274 29.43C29.778 28.898 30.394 28.016 31.122 26.77C30.408 26.896 29.68 27.008 28.952 27.12L28.7 26.196C28.854 26.14 28.994 26.014 29.12 25.832C29.806 24.586 30.408 23.158 30.912 21.562ZM34.034 22.458H36.848V21.534H37.828V22.458H40.838V23.368H37.828V24.642H41.034V25.272C40.838 26.168 40.572 27.05 40.236 27.918L39.27 27.638C39.564 27.022 39.802 26.322 39.998 25.566H33.6V24.642H36.848V23.368H34.034V22.458ZM33.292 29.682H37.17C37.212 29.374 37.24 29.052 37.254 28.73V26.14H38.234V28.73C38.22 29.066 38.192 29.374 38.164 29.682H41.426V30.606H37.982C37.912 30.858 37.842 31.082 37.758 31.306C39.13 31.95 40.39 32.706 41.524 33.574L40.964 34.442C39.788 33.462 38.598 32.664 37.394 32.048C37.31 32.188 37.212 32.314 37.128 32.44C36.442 33.294 35.336 33.966 33.796 34.456L33.236 33.574C34.734 33.126 35.77 32.524 36.358 31.768C36.61 31.418 36.806 31.026 36.946 30.606H33.292V29.682ZM33.838 27.4C34.608 27.764 35.308 28.226 35.952 28.758L35.476 29.5C34.79 28.912 34.09 28.436 33.362 28.086L33.838 27.4ZM34.916 25.958C35.63 26.294 36.288 26.728 36.89 27.232L36.414 27.946C35.77 27.386 35.126 26.938 34.468 26.63L34.916 25.958Z" fill="#F04A3E" />
                <path d="M21.56 26.084H23.814V21.744H24.78V26.084H26.992V26.994H24.78V27.414C25.592 28.17 26.404 29.024 27.216 29.976L26.67 30.802C25.928 29.738 25.298 28.912 24.78 28.296V32.202H23.814V27.344C23.352 28.912 22.638 30.298 21.672 31.516L21.266 30.536C22.218 29.5 22.904 28.31 23.324 26.994H21.56V26.084ZM19.81 21.954H20.776V32.958H27.342V33.924H19.81V21.954ZM26.292 22.444L27.104 22.654C26.838 23.704 26.502 24.628 26.11 25.412L25.354 25.146C25.718 24.432 26.026 23.536 26.292 22.444ZM22.218 22.654C22.568 23.41 22.876 24.264 23.156 25.244L22.4 25.426C22.148 24.558 21.826 23.732 21.434 22.92L22.218 22.654ZM19.278 31.614V32.566C17.962 33.098 16.478 33.518 14.798 33.84L14.672 32.86C16.422 32.552 17.962 32.132 19.278 31.614ZM16.996 21.52L17.948 21.884C17.262 23.69 16.576 25.132 15.876 26.21C16.52 26.126 17.15 26.028 17.794 25.902C18.046 25.426 18.298 24.922 18.55 24.404L19.418 24.726C18.186 27.12 17.178 28.814 16.366 29.808C17.318 29.612 18.27 29.346 19.222 29.01V29.906C17.822 30.382 16.492 30.718 15.218 30.886L14.938 29.976C15.12 29.906 15.274 29.808 15.386 29.696C15.904 29.136 16.548 28.17 17.304 26.826C16.52 26.966 15.736 27.106 14.938 27.218L14.686 26.294C14.84 26.224 14.98 26.098 15.106 25.916C15.834 24.628 16.464 23.172 16.996 21.52ZM33.04 31.446V32.398C31.822 32.902 30.45 33.308 28.91 33.616L28.784 32.636C30.408 32.342 31.822 31.95 33.04 31.446ZM30.912 21.562L31.864 21.94C31.22 23.676 30.562 25.076 29.89 26.126C30.464 26.056 31.038 25.958 31.626 25.846C31.878 25.384 32.13 24.894 32.396 24.376L33.32 24.726C32.102 26.966 31.094 28.576 30.31 29.528C31.15 29.36 32.004 29.122 32.844 28.814V29.724C31.528 30.172 30.282 30.466 29.092 30.634L28.812 29.71C28.994 29.654 29.148 29.556 29.274 29.43C29.778 28.898 30.394 28.016 31.122 26.77C30.408 26.896 29.68 27.008 28.952 27.12L28.7 26.196C28.854 26.14 28.994 26.014 29.12 25.832C29.806 24.586 30.408 23.158 30.912 21.562ZM34.034 22.458H36.848V21.534H37.828V22.458H40.838V23.368H37.828V24.642H41.034V25.272C40.838 26.168 40.572 27.05 40.236 27.918L39.27 27.638C39.564 27.022 39.802 26.322 39.998 25.566H33.6V24.642H36.848V23.368H34.034V22.458ZM33.292 29.682H37.17C37.212 29.374 37.24 29.052 37.254 28.73V26.14H38.234V28.73C38.22 29.066 38.192 29.374 38.164 29.682H41.426V30.606H37.982C37.912 30.858 37.842 31.082 37.758 31.306C39.13 31.95 40.39 32.706 41.524 33.574L40.964 34.442C39.788 33.462 38.598 32.664 37.394 32.048C37.31 32.188 37.212 32.314 37.128 32.44C36.442 33.294 35.336 33.966 33.796 34.456L33.236 33.574C34.734 33.126 35.77 32.524 36.358 31.768C36.61 31.418 36.806 31.026 36.946 30.606H33.292V29.682ZM33.838 27.4C34.608 27.764 35.308 28.226 35.952 28.758L35.476 29.5C34.79 28.912 34.09 28.436 33.362 28.086L33.838 27.4ZM34.916 25.958C35.63 26.294 36.288 26.728 36.89 27.232L36.414 27.946C35.77 27.386 35.126 26.938 34.468 26.63L34.916 25.958Z" fill="url(#paint1_linear_1296_3722)" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1296_3722"
                        x1="0"
                        y1="0"
                        x2="65.2037"
                        y2="14.4396"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1296_3722"
                        x1="5"
                        y1="20.5"
                        x2="45.6845"
                        y2="39.7765"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                </defs>
            </svg>
            <svg
                class="btn-hover"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    fill="#D5EAFF"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="#EAEDF1"
                    stroke-width="1.5"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="url(#paint0_linear_1365_2987)"
                    stroke-width="1.5"
                />
                <path d="M21.56 26.084H23.814V21.744H24.78V26.084H26.992V26.994H24.78V27.414C25.592 28.17 26.404 29.024 27.216 29.976L26.67 30.802C25.928 29.738 25.298 28.912 24.78 28.296V32.202H23.814V27.344C23.352 28.912 22.638 30.298 21.672 31.516L21.266 30.536C22.218 29.5 22.904 28.31 23.324 26.994H21.56V26.084ZM19.81 21.954H20.776V32.958H27.342V33.924H19.81V21.954ZM26.292 22.444L27.104 22.654C26.838 23.704 26.502 24.628 26.11 25.412L25.354 25.146C25.718 24.432 26.026 23.536 26.292 22.444ZM22.218 22.654C22.568 23.41 22.876 24.264 23.156 25.244L22.4 25.426C22.148 24.558 21.826 23.732 21.434 22.92L22.218 22.654ZM19.278 31.614V32.566C17.962 33.098 16.478 33.518 14.798 33.84L14.672 32.86C16.422 32.552 17.962 32.132 19.278 31.614ZM16.996 21.52L17.948 21.884C17.262 23.69 16.576 25.132 15.876 26.21C16.52 26.126 17.15 26.028 17.794 25.902C18.046 25.426 18.298 24.922 18.55 24.404L19.418 24.726C18.186 27.12 17.178 28.814 16.366 29.808C17.318 29.612 18.27 29.346 19.222 29.01V29.906C17.822 30.382 16.492 30.718 15.218 30.886L14.938 29.976C15.12 29.906 15.274 29.808 15.386 29.696C15.904 29.136 16.548 28.17 17.304 26.826C16.52 26.966 15.736 27.106 14.938 27.218L14.686 26.294C14.84 26.224 14.98 26.098 15.106 25.916C15.834 24.628 16.464 23.172 16.996 21.52ZM33.04 31.446V32.398C31.822 32.902 30.45 33.308 28.91 33.616L28.784 32.636C30.408 32.342 31.822 31.95 33.04 31.446ZM30.912 21.562L31.864 21.94C31.22 23.676 30.562 25.076 29.89 26.126C30.464 26.056 31.038 25.958 31.626 25.846C31.878 25.384 32.13 24.894 32.396 24.376L33.32 24.726C32.102 26.966 31.094 28.576 30.31 29.528C31.15 29.36 32.004 29.122 32.844 28.814V29.724C31.528 30.172 30.282 30.466 29.092 30.634L28.812 29.71C28.994 29.654 29.148 29.556 29.274 29.43C29.778 28.898 30.394 28.016 31.122 26.77C30.408 26.896 29.68 27.008 28.952 27.12L28.7 26.196C28.854 26.14 28.994 26.014 29.12 25.832C29.806 24.586 30.408 23.158 30.912 21.562ZM34.034 22.458H36.848V21.534H37.828V22.458H40.838V23.368H37.828V24.642H41.034V25.272C40.838 26.168 40.572 27.05 40.236 27.918L39.27 27.638C39.564 27.022 39.802 26.322 39.998 25.566H33.6V24.642H36.848V23.368H34.034V22.458ZM33.292 29.682H37.17C37.212 29.374 37.24 29.052 37.254 28.73V26.14H38.234V28.73C38.22 29.066 38.192 29.374 38.164 29.682H41.426V30.606H37.982C37.912 30.858 37.842 31.082 37.758 31.306C39.13 31.95 40.39 32.706 41.524 33.574L40.964 34.442C39.788 33.462 38.598 32.664 37.394 32.048C37.31 32.188 37.212 32.314 37.128 32.44C36.442 33.294 35.336 33.966 33.796 34.456L33.236 33.574C34.734 33.126 35.77 32.524 36.358 31.768C36.61 31.418 36.806 31.026 36.946 30.606H33.292V29.682ZM33.838 27.4C34.608 27.764 35.308 28.226 35.952 28.758L35.476 29.5C34.79 28.912 34.09 28.436 33.362 28.086L33.838 27.4ZM34.916 25.958C35.63 26.294 36.288 26.728 36.89 27.232L36.414 27.946C35.77 27.386 35.126 26.938 34.468 26.63L34.916 25.958Z" fill="#F04A3E" />
                <path d="M21.56 26.084H23.814V21.744H24.78V26.084H26.992V26.994H24.78V27.414C25.592 28.17 26.404 29.024 27.216 29.976L26.67 30.802C25.928 29.738 25.298 28.912 24.78 28.296V32.202H23.814V27.344C23.352 28.912 22.638 30.298 21.672 31.516L21.266 30.536C22.218 29.5 22.904 28.31 23.324 26.994H21.56V26.084ZM19.81 21.954H20.776V32.958H27.342V33.924H19.81V21.954ZM26.292 22.444L27.104 22.654C26.838 23.704 26.502 24.628 26.11 25.412L25.354 25.146C25.718 24.432 26.026 23.536 26.292 22.444ZM22.218 22.654C22.568 23.41 22.876 24.264 23.156 25.244L22.4 25.426C22.148 24.558 21.826 23.732 21.434 22.92L22.218 22.654ZM19.278 31.614V32.566C17.962 33.098 16.478 33.518 14.798 33.84L14.672 32.86C16.422 32.552 17.962 32.132 19.278 31.614ZM16.996 21.52L17.948 21.884C17.262 23.69 16.576 25.132 15.876 26.21C16.52 26.126 17.15 26.028 17.794 25.902C18.046 25.426 18.298 24.922 18.55 24.404L19.418 24.726C18.186 27.12 17.178 28.814 16.366 29.808C17.318 29.612 18.27 29.346 19.222 29.01V29.906C17.822 30.382 16.492 30.718 15.218 30.886L14.938 29.976C15.12 29.906 15.274 29.808 15.386 29.696C15.904 29.136 16.548 28.17 17.304 26.826C16.52 26.966 15.736 27.106 14.938 27.218L14.686 26.294C14.84 26.224 14.98 26.098 15.106 25.916C15.834 24.628 16.464 23.172 16.996 21.52ZM33.04 31.446V32.398C31.822 32.902 30.45 33.308 28.91 33.616L28.784 32.636C30.408 32.342 31.822 31.95 33.04 31.446ZM30.912 21.562L31.864 21.94C31.22 23.676 30.562 25.076 29.89 26.126C30.464 26.056 31.038 25.958 31.626 25.846C31.878 25.384 32.13 24.894 32.396 24.376L33.32 24.726C32.102 26.966 31.094 28.576 30.31 29.528C31.15 29.36 32.004 29.122 32.844 28.814V29.724C31.528 30.172 30.282 30.466 29.092 30.634L28.812 29.71C28.994 29.654 29.148 29.556 29.274 29.43C29.778 28.898 30.394 28.016 31.122 26.77C30.408 26.896 29.68 27.008 28.952 27.12L28.7 26.196C28.854 26.14 28.994 26.014 29.12 25.832C29.806 24.586 30.408 23.158 30.912 21.562ZM34.034 22.458H36.848V21.534H37.828V22.458H40.838V23.368H37.828V24.642H41.034V25.272C40.838 26.168 40.572 27.05 40.236 27.918L39.27 27.638C39.564 27.022 39.802 26.322 39.998 25.566H33.6V24.642H36.848V23.368H34.034V22.458ZM33.292 29.682H37.17C37.212 29.374 37.24 29.052 37.254 28.73V26.14H38.234V28.73C38.22 29.066 38.192 29.374 38.164 29.682H41.426V30.606H37.982C37.912 30.858 37.842 31.082 37.758 31.306C39.13 31.95 40.39 32.706 41.524 33.574L40.964 34.442C39.788 33.462 38.598 32.664 37.394 32.048C37.31 32.188 37.212 32.314 37.128 32.44C36.442 33.294 35.336 33.966 33.796 34.456L33.236 33.574C34.734 33.126 35.77 32.524 36.358 31.768C36.61 31.418 36.806 31.026 36.946 30.606H33.292V29.682ZM33.838 27.4C34.608 27.764 35.308 28.226 35.952 28.758L35.476 29.5C34.79 28.912 34.09 28.436 33.362 28.086L33.838 27.4ZM34.916 25.958C35.63 26.294 36.288 26.728 36.89 27.232L36.414 27.946C35.77 27.386 35.126 26.938 34.468 26.63L34.916 25.958Z" fill="url(#paint1_linear_1365_2987)" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1365_2987"
                        x1="0"
                        y1="0"
                        x2="65.2037"
                        y2="14.4396"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1365_2987"
                        x1="5"
                        y1="20.5"
                        x2="45.6845"
                        y2="39.7765"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                </defs>
            </svg>
            <svg
                class="btn-active"
                width="56"
                height="56"
                viewBox="0 0 56 56"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    fill="#B5D5F4"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="#EAEDF1"
                    stroke-width="1.5"
                />
                <circle
                    cx="28"
                    cy="28"
                    r="27.25"
                    stroke="url(#paint0_linear_1365_2991)"
                    stroke-width="1.5"
                />
                <path d="M21.56 26.084H23.814V21.744H24.78V26.084H26.992V26.994H24.78V27.414C25.592 28.17 26.404 29.024 27.216 29.976L26.67 30.802C25.928 29.738 25.298 28.912 24.78 28.296V32.202H23.814V27.344C23.352 28.912 22.638 30.298 21.672 31.516L21.266 30.536C22.218 29.5 22.904 28.31 23.324 26.994H21.56V26.084ZM19.81 21.954H20.776V32.958H27.342V33.924H19.81V21.954ZM26.292 22.444L27.104 22.654C26.838 23.704 26.502 24.628 26.11 25.412L25.354 25.146C25.718 24.432 26.026 23.536 26.292 22.444ZM22.218 22.654C22.568 23.41 22.876 24.264 23.156 25.244L22.4 25.426C22.148 24.558 21.826 23.732 21.434 22.92L22.218 22.654ZM19.278 31.614V32.566C17.962 33.098 16.478 33.518 14.798 33.84L14.672 32.86C16.422 32.552 17.962 32.132 19.278 31.614ZM16.996 21.52L17.948 21.884C17.262 23.69 16.576 25.132 15.876 26.21C16.52 26.126 17.15 26.028 17.794 25.902C18.046 25.426 18.298 24.922 18.55 24.404L19.418 24.726C18.186 27.12 17.178 28.814 16.366 29.808C17.318 29.612 18.27 29.346 19.222 29.01V29.906C17.822 30.382 16.492 30.718 15.218 30.886L14.938 29.976C15.12 29.906 15.274 29.808 15.386 29.696C15.904 29.136 16.548 28.17 17.304 26.826C16.52 26.966 15.736 27.106 14.938 27.218L14.686 26.294C14.84 26.224 14.98 26.098 15.106 25.916C15.834 24.628 16.464 23.172 16.996 21.52ZM33.04 31.446V32.398C31.822 32.902 30.45 33.308 28.91 33.616L28.784 32.636C30.408 32.342 31.822 31.95 33.04 31.446ZM30.912 21.562L31.864 21.94C31.22 23.676 30.562 25.076 29.89 26.126C30.464 26.056 31.038 25.958 31.626 25.846C31.878 25.384 32.13 24.894 32.396 24.376L33.32 24.726C32.102 26.966 31.094 28.576 30.31 29.528C31.15 29.36 32.004 29.122 32.844 28.814V29.724C31.528 30.172 30.282 30.466 29.092 30.634L28.812 29.71C28.994 29.654 29.148 29.556 29.274 29.43C29.778 28.898 30.394 28.016 31.122 26.77C30.408 26.896 29.68 27.008 28.952 27.12L28.7 26.196C28.854 26.14 28.994 26.014 29.12 25.832C29.806 24.586 30.408 23.158 30.912 21.562ZM34.034 22.458H36.848V21.534H37.828V22.458H40.838V23.368H37.828V24.642H41.034V25.272C40.838 26.168 40.572 27.05 40.236 27.918L39.27 27.638C39.564 27.022 39.802 26.322 39.998 25.566H33.6V24.642H36.848V23.368H34.034V22.458ZM33.292 29.682H37.17C37.212 29.374 37.24 29.052 37.254 28.73V26.14H38.234V28.73C38.22 29.066 38.192 29.374 38.164 29.682H41.426V30.606H37.982C37.912 30.858 37.842 31.082 37.758 31.306C39.13 31.95 40.39 32.706 41.524 33.574L40.964 34.442C39.788 33.462 38.598 32.664 37.394 32.048C37.31 32.188 37.212 32.314 37.128 32.44C36.442 33.294 35.336 33.966 33.796 34.456L33.236 33.574C34.734 33.126 35.77 32.524 36.358 31.768C36.61 31.418 36.806 31.026 36.946 30.606H33.292V29.682ZM33.838 27.4C34.608 27.764 35.308 28.226 35.952 28.758L35.476 29.5C34.79 28.912 34.09 28.436 33.362 28.086L33.838 27.4ZM34.916 25.958C35.63 26.294 36.288 26.728 36.89 27.232L36.414 27.946C35.77 27.386 35.126 26.938 34.468 26.63L34.916 25.958Z" fill="#F04A3E" />
                <path d="M21.56 26.084H23.814V21.744H24.78V26.084H26.992V26.994H24.78V27.414C25.592 28.17 26.404 29.024 27.216 29.976L26.67 30.802C25.928 29.738 25.298 28.912 24.78 28.296V32.202H23.814V27.344C23.352 28.912 22.638 30.298 21.672 31.516L21.266 30.536C22.218 29.5 22.904 28.31 23.324 26.994H21.56V26.084ZM19.81 21.954H20.776V32.958H27.342V33.924H19.81V21.954ZM26.292 22.444L27.104 22.654C26.838 23.704 26.502 24.628 26.11 25.412L25.354 25.146C25.718 24.432 26.026 23.536 26.292 22.444ZM22.218 22.654C22.568 23.41 22.876 24.264 23.156 25.244L22.4 25.426C22.148 24.558 21.826 23.732 21.434 22.92L22.218 22.654ZM19.278 31.614V32.566C17.962 33.098 16.478 33.518 14.798 33.84L14.672 32.86C16.422 32.552 17.962 32.132 19.278 31.614ZM16.996 21.52L17.948 21.884C17.262 23.69 16.576 25.132 15.876 26.21C16.52 26.126 17.15 26.028 17.794 25.902C18.046 25.426 18.298 24.922 18.55 24.404L19.418 24.726C18.186 27.12 17.178 28.814 16.366 29.808C17.318 29.612 18.27 29.346 19.222 29.01V29.906C17.822 30.382 16.492 30.718 15.218 30.886L14.938 29.976C15.12 29.906 15.274 29.808 15.386 29.696C15.904 29.136 16.548 28.17 17.304 26.826C16.52 26.966 15.736 27.106 14.938 27.218L14.686 26.294C14.84 26.224 14.98 26.098 15.106 25.916C15.834 24.628 16.464 23.172 16.996 21.52ZM33.04 31.446V32.398C31.822 32.902 30.45 33.308 28.91 33.616L28.784 32.636C30.408 32.342 31.822 31.95 33.04 31.446ZM30.912 21.562L31.864 21.94C31.22 23.676 30.562 25.076 29.89 26.126C30.464 26.056 31.038 25.958 31.626 25.846C31.878 25.384 32.13 24.894 32.396 24.376L33.32 24.726C32.102 26.966 31.094 28.576 30.31 29.528C31.15 29.36 32.004 29.122 32.844 28.814V29.724C31.528 30.172 30.282 30.466 29.092 30.634L28.812 29.71C28.994 29.654 29.148 29.556 29.274 29.43C29.778 28.898 30.394 28.016 31.122 26.77C30.408 26.896 29.68 27.008 28.952 27.12L28.7 26.196C28.854 26.14 28.994 26.014 29.12 25.832C29.806 24.586 30.408 23.158 30.912 21.562ZM34.034 22.458H36.848V21.534H37.828V22.458H40.838V23.368H37.828V24.642H41.034V25.272C40.838 26.168 40.572 27.05 40.236 27.918L39.27 27.638C39.564 27.022 39.802 26.322 39.998 25.566H33.6V24.642H36.848V23.368H34.034V22.458ZM33.292 29.682H37.17C37.212 29.374 37.24 29.052 37.254 28.73V26.14H38.234V28.73C38.22 29.066 38.192 29.374 38.164 29.682H41.426V30.606H37.982C37.912 30.858 37.842 31.082 37.758 31.306C39.13 31.95 40.39 32.706 41.524 33.574L40.964 34.442C39.788 33.462 38.598 32.664 37.394 32.048C37.31 32.188 37.212 32.314 37.128 32.44C36.442 33.294 35.336 33.966 33.796 34.456L33.236 33.574C34.734 33.126 35.77 32.524 36.358 31.768C36.61 31.418 36.806 31.026 36.946 30.606H33.292V29.682ZM33.838 27.4C34.608 27.764 35.308 28.226 35.952 28.758L35.476 29.5C34.79 28.912 34.09 28.436 33.362 28.086L33.838 27.4ZM34.916 25.958C35.63 26.294 36.288 26.728 36.89 27.232L36.414 27.946C35.77 27.386 35.126 26.938 34.468 26.63L34.916 25.958Z" fill="url(#paint1_linear_1365_2991)" />
                <defs>
                    <linearGradient
                        id="paint0_linear_1365_2991"
                        x1="0"
                        y1="0"
                        x2="65.2037"
                        y2="14.4396"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#5ACAFD" />
                        <stop offset="0.618831" stop-color="#5694FE" />
                        <stop offset="1" stop-color="#9055FE" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_1365_2991"
                        x1="5"
                        y1="20.5"
                        x2="45.6845"
                        y2="39.7765"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#7FC5FF" />
                        <stop offset="1" stop-color="#634DFC" />
                    </linearGradient>
                </defs>
            </svg>
        </template>


        <div
            class="inner-button"
            @click="handleClick"
        ></div>
    </div>
</template>

<script>
    export default {
        name: 'VoiceRecordButton',

        props: {
            // 按钮类型：record（录音）, pause（暂停）, stop（停止）, continue（继续）
            type: {
                type: String,
                default: 'record',
                validator: (value) => ['stop', 'record', 'pause', 'continue'].includes(value),
            },
            // 按钮宽度，单位px
            width: {
                type: [Number, String],
                default: null,
            },
        },

        methods: {
            handleClick(event) {
                this.$emit('click', event);
            },
        },
    };
</script>

<style lang="scss">
.voice-record-button {
    position: relative;
    display: inline-block;
    width: 56px;
    height: 56px;

    &--record {
        width: 80px;
        height: 80px;
    }

    svg {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        opacity: 0;
    }

    .btn-default {
        opacity: 1;
    }

    &:hover {
        .btn-default {
            opacity: 0;
        }

        .btn-hover {
            opacity: 1;
        }
    }

    &:active {
        .btn-default,
        .btn-hover {
            opacity: 0;
        }

        .btn-active {
            opacity: 1;
        }
    }

    .inner-button {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }
}
</style>

