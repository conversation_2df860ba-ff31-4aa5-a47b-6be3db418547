import { defineStore } from 'MfBase/pinia';
import {
    fetchIsReadTips,
    markReadTips,
} from '../services/api';

/**
 * Pinia store for managing voice record state and logic
 */
export const useAsrTipsStore = defineStore('useAsrTips', {
    state: () => ({
        isReadTips: false,
    }),
    actions: {
        // 初始化读取提示状态
        async initReadTipsStatus() {
            this.isReadTips = await fetchIsReadTips();
        },
        
        // 标记已读提示
        async markReadTips() {
            this.isReadTips = await markReadTips();
            return this.isReadTips;
        },
    },
});
