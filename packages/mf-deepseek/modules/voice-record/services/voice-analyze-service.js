import streamService from '@/common/service/stream-service';

/**
 * 创建 语音分析 服务
 * @returns {Object} 语音分析 服务实例
 */
export const createVoiceAnalyzeService = () => {
    /**
   * 获取 语音分析 流式结果
   * @param {Object} params - 请求参数
   * @param {Function} onmessage - 数据回调
   * @param {Function} onClose - 关闭回调
   * @param {Function} onError - 错误回调
   * @returns {Object} 请求控制对象
   */
    const getVoiceAnalyzeStream = (params, onmessage, onClose, onError) => {
        // return streamService.fetchStream('/api/chat', {
        return streamService.fetchStream('/api/v2/ai/analysis/text', {
            method: 'POST',
            body: JSON.stringify(params),
            onmessage(chunk) {
                // 尝试解析 JSON
                try {
                    const data = JSON.parse(chunk.substring(5));
                    onmessage(data);
                } catch (e) {
                    console.error('Parse chunk error:', e, chunk);
                }
            },
            onerror(error) {
                onError && onError(error);
            },
            onclose() {
                onClose && onClose();
            },
        });
    };

    return {
        getVoiceAnalyzeStream,
    };
};

// 导出默认实例
export default createVoiceAnalyzeService();
