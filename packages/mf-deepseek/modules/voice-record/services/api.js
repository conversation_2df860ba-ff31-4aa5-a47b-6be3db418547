// api/deepseek-api.js
import { fetch } from 'MfBase/base-api';

const ASR_BUSINESS_TYPE = 40;
const SPEAKER_BUSINESS_TYPE = 0;

export const fetchIsReadTips = async () => {
    try {
        const res = await fetch.get('/api/v3/clinics/reminder/asr-medical-notice/read');
        return !!res.data?.data?.created;
    } catch (error) {
        console.error('Fetch is read tips error:', error);
        return false;
    }
};

export const markReadTips = async () => {
    try {
        await fetch.post('/api/v3/clinics/reminder/asr-medical-notice/read');
        return true;
    } catch (error) {
        console.error('Mark read tips error:', error);
        return false;
    }
};

export const fetchTemporaryToken = async () => {
    try {
        const res = await fetch.get('/api/v2/ai/speech/tencent/temporary-credentials');
        return res.data?.data;
    } catch (error) {
        console.error('fetchTemporaryToken error', error);
        return null;
    }
};

/**
 * type AsrResult = {
 *  duration: number;
 *  asrResult: Array<{ text: string; time: number; startTime: number; endTime: number; index: number }>;
 *  uploadAudioUrl: string;
 *  medicalRecord: string;
 * }
 */
/**
 *
 * @param {string} outpatientSheetId
 * @param {AsrResult} data
 * @returns
 */
export const saveAsrResult = async (outpatientSheetId, data, attachmentId) => {
    const payload = {
        'businessType': ASR_BUSINESS_TYPE,
        'businessInfo': {
            'voiceUrl': data.uploadAudioUrl,
            'duration': data.duration,
            'asrMetadataList': data.asrResult.map((item) => ({
                'startTime': item.startTime,
                'endTime': item.endTime,
                'text': item.text,
            })),
            'medicalRecord': data.medicalRecord,
        },
    };
    if (attachmentId) {
        payload.id = attachmentId;
    }
    try {
        const res = await fetch.put(`/api/v2/outpatients/${outpatientSheetId}/medical-record/attachments`, payload);
        return res.data;
    } catch (error) {
        console.error('Save asr result error:', error);
        return null;
    }
};

function resolveAsrResultDetail(res) {
    const {
        attachment,
        asrTask,
    } = res;
    if (attachment?.id) {
        const { businessInfo } = attachment;
        const asrItem = businessInfo;
        return {
            attachmentId: attachment.id,
            voiceUrl: asrItem.voiceUrl,
            duration: asrItem.duration,
            asrMetadataList: asrItem.asrMetadataList,
            medicalRecord: asrItem.medicalRecord,
            speakerRecognitionTaskId: asrTask?.taskId,
            speakerRecognitionTaskStatus: asrTask?.status,
            speakerRecognitionResult: asrTask?.taskResult,
        };
    }
    return null;
}

export const getAsrResultDetail = async (outpatientSheetId, attachmentId) => {
    try {
        const res = await fetch.get(`/api/v2/outpatients/${outpatientSheetId}/medical-record/${attachmentId}/asr`);
        return resolveAsrResultDetail(res.data?.data);
    } catch (error) {
        console.error('Get asr result detail error:', error);
        return null;
    }
};

export const getAsrResult = async (outpatientSheetId) => {
    try {
        const res = await fetch.get(`/api/v2/outpatients/${outpatientSheetId}/medical-record/asr`);

        const item = res.data?.data?.rows?.[0];
        if (!item) {
            return null;
        }
        /**{
    "id": "ffffffff0000000034f95e7a84a34000",
    "url": "",
    "fileName": "",
    "sort": 0,
    "fileSize": null,
    "businessType": 40,
    "businessInfo": {
        "duration": 6097.938,
        "voiceUrl": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-temp/fff730ccc5ee45d783d82a85b8a0e52d/voice-records/voice_record_1745371859506_TlrLCZlkoY7D.wav",
        "medicalRecord": "**主诉：** 头痛3天。",
        "asrMetadataList": [
            {
                "text": "嗯，没有什么问题，头有点痛，痛了大概多久了，3天左右。",
                "endTime": "5987",
                "startTime": "140"
            }
        ]
    }
}*/
        return resolveAsrResultDetail(item);

    } catch (error) {
        console.error('Get asr result error:', error);
        return null;
    }
};

/**
 * 查询说话人分离任务状态
 * @param {*} taskId
 * @param {*} businessType
 * @returns
 */
export const querySpeakerRecognitionStatus = async (taskId) => {
    try {
        const res = await fetch.get(`/api/v2/ai/speech/bytedance/asr/result/${taskId}/${SPEAKER_BUSINESS_TYPE}`);
        return {
            taskResult: res.data?.data?.taskResult,
            status: res.data?.data?.status,
        };
    } catch (error) {
        console.error('Get speaker recognition status error:', error);
        return null;
    }
};


/**
 * 提交说话人分离任务
 * @param {*} taskId 说话人分离任务 ID，使用附件的 id 作为任务 id
 * @param {{format: string, url: string}} audio
 * @returns
 */
export const submitSpeakerRecognitionTask = async (taskId, audio) => {
    try {
        const res = await fetch.post('/api/v2/ai/speech/bytedance/asr/async-task/submit', {
            businessId: taskId,
            businessType: SPEAKER_BUSINESS_TYPE,
            audio,
        });
        return res.data;
    } catch (error) {
        console.error('Submit speaker recognition task error:', error);
        return null;
    }
};

// 统一导出
export default {
    fetchIsReadTips,
    markReadTips,
    fetchTemporaryToken,
    saveAsrResult,
    getAsrResult,
    getAsrResultDetail,
    querySpeakerRecognitionStatus,
    submitSpeakerRecognitionTask,
};
