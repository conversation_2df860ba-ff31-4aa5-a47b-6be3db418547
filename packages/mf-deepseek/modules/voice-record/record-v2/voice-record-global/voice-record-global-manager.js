import Vue from 'vue';
import VoiceRecordGlobalContainer from './voice-record-global-container.vue';
import { defineStore } from 'MfBase/pinia';

export const useVoiceRecordGlobalStore = defineStore('voiceRecordGlobal', {
    state: () => ({
        instance: null,
        isMiniMode: false,
        propsData: null,
    }),
    
    actions: {
        open(props, callback) {
            if (this.instance) {
                return;
            }
            const Ctor = Vue.extend(VoiceRecordGlobalContainer);
            this.propsData = props;
            this.instance = new Ctor({
                propsData: props,
                parent: window._vue,
            });
            this.instance.$once('destroy', () => {
                console.log('useVoiceRecordGlobalStore destroy', callback);
                this.instance.$destroy();
                document.body.removeChild(this.instance.$el);
                this.instance = null;
                if (callback) {
                    callback();
                }
            });
            this.instance.$mount();
            document.body.appendChild(this.instance.$el);
            this.showRecordDialog();
            return () => {
                console.log('useVoiceRecordGlobalStore release callback');
                callback = null;
            };
        },
        showRecordDialog() {
            this.instance.showRecordDialog();
        },
        setMiniMode(isMiniMode) {
            this.isMiniMode = isMiniMode;
        },
    },
});
