<template>
    <div class="voice-record-trigger-button">
        <!-- 触发按钮 -->
        <abc-button
            ref="triggerButton"
            v-abc-check-electron="{
                customValidate: validateElectron,
                installTitle: '语音病历仅支持客户端，请下载后使用',
            }"
            variant="ghost"
            theme="primary"
            size="small"
            class="voice-record-trigger"
            :icon="attachmentId ? 's-mic-fill' : 's-microphone-line'"
            :disabled="isRecording"
            @click="handleButtonClick"
        >
            语音病历
        </abc-button>
    </div>
</template>

<script>
    import { getAsrResult } from '../services/api';
    import { useAsrTipsStore } from '../hooks/use-asr-tips';
    import { useVoiceRecordStore } from '../hooks/use-voice-record';
    import { mapGetters } from 'vuex';
    import {
        useVoiceRecordGlobalStore,
    } from './voice-record-global/voice-record-global-manager';
    import { storeToRefs } from 'MfBase/pinia';

    export default {
        name: 'VoiceRecordTriggerButton',
        props: {
            switchSetting: {
                type: Object,
            },
            outpatientInfo: {
                type: Object,
                required: true,
            },
            patientInfo: {
                type: Object,
                required: true,
            },
            hasAsrResult: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            // 使用 pinia store 获取所有响应式数据和方法
            const store = useAsrTipsStore();
            const {
                initReadTipsStatus,
            } = store;

            const voiceRecordGlobalStore = useVoiceRecordGlobalStore();
            const {
                open: openVoiceRecordGlobal,
            } = voiceRecordGlobalStore;

            const voiceRecordStore = useVoiceRecordStore();
            const {
                state: voiceRecordState,
            } = storeToRefs(voiceRecordStore);

            return {
                initReadTipsStatus,
                openVoiceRecordGlobal,
                voiceRecordState,
            };
        },
        data() {
            return {
                asrResult: null,
            };
        },
        computed: {
            ...mapGetters(['chainBasic']),
            isEnableAiVoiceMrWeb() {
                return this.chainBasic.deepseek?.voiceMrWeb === 1;
            },
            outpatientSheetId() {
                return this.outpatientInfo?.id;
            },
            attachmentId() {
                return this.asrResult?.attachmentId;
            },
            isRecording() {
                return this.voiceRecordState === 'recording' || this.voiceRecordState === 'paused';
            },
        },
        watch: {
            asrResult: {
                handler() {
                    this.$emit('update:hasAsrResult', !!this.attachmentId);
                },
                deep: true,
            },
        },
        created() {
            this.initReadTipsStatus();
            this.fetchAsrResult();
        },
        beforeDestroy() {
            if (this.unwatchVoiceRecord) {
                this.unwatchVoiceRecord();
            }
        },
        methods: {
            validateElectron() {
                if (this.isEnableAiVoiceMrWeb) {
                    return '';
                }
                return !window.$abcSocialSecurity?.isElectron ? '语音病历仅支持客户端，请下载后使用' : '';
            },
            async handleButtonClick() {
                this.unwatchVoiceRecord = this.openVoiceRecordGlobal({
                    outpatientInfo: this.outpatientInfo,
                    patientInfo: this.patientInfo,
                    switchSetting: this.switchSetting,
                    attachmentId: this.attachmentId,
                }, () => {
                    // 重新获取 ASR 结果
                    this.fetchAsrResult();
                });
            },
            async fetchAsrResult() {
                try {
                    const result = await getAsrResult(this.outpatientSheetId);
                    this.asrResult = result;
                } catch (e) {
                    // 忽略错误，继续录音
                    console.warn('获取 ASR 结果失败', e);
                }
            },
        },
    };
</script>
