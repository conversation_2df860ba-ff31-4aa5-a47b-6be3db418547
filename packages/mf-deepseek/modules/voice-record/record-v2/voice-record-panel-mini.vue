<template>
    <div
        ref="miniPlayer"
        :class="[
            'voice-record-panel-mini',
            {
                'voice-record-panel-mini--active': isActive,
            },
        ]"
        @mousedown="handleMiniPlayerMouseDown"
        @click.stop="handleMiniPlayerClick"
    >
        <div class="voice-record-panel-mini__content">
            <div class="voice-record-panel-mini__wave">
                <wave-visualization
                    class="mini-wave-visualization"
                    :is-paused="state === 'paused'"
                    :waveform-data="waveformData"
                    wave-color="rgba(255, 255, 255, 0.60)"
                    :width="226"
                    :height="16"
                    :bar-min-height="1"
                ></wave-visualization>
                <abc-flex gap="5" class="voice-record-panel-mini__info">
                    <abc-text size="mini">
                        {{ formattedTime }}
                    </abc-text>
                    <div class="subtitle-container">
                        <div class="subtitle-wrapper">
                            <div class="subtitle-content">
                                <abc-text size="mini">
                                    {{ fullSentence }}
                                </abc-text>
                            </div>
                        </div>
                    </div>
                </abc-flex>
            </div>
            <abc-space class="voice-record-panel-mini__controls" :size="12">
                <abc-button
                    v-if="state === 'recording'"
                    class="mini-control-btn"
                    shape="round"
                    variant="ghost"
                    icon="s-pause-fill"
                    theme="success"
                    @click.stop="handlePause"
                ></abc-button>
                <abc-button
                    v-else-if="state === 'paused'"
                    class="mini-control-btn"
                    shape="round"
                    variant="ghost"
                    icon="s-play-fill"
                    theme="success"
                    @click.stop="handleContinue"
                ></abc-button>
                <abc-button
                    class="mini-control-btn"
                    shape="round"
                    variant="ghost"
                    icon="s-stop-fill"
                    theme="success"
                    @click.stop="handleStop"
                ></abc-button>
            </abc-space>
        </div>
    </div>
</template>

<script>
    import WaveVisualization from '../components/wave-visualization-v2.vue';
    import { useVoiceRecordStore } from '../hooks/use-voice-record';
    import { storeToRefs } from 'MfBase/pinia';
    import LocalStore from 'MfBase/utils/localStorage-handler';

    export default {
        name: 'VoiceRecordPanelMini',
        components: {
            WaveVisualization,
        },
        setup() {
            const voiceRecordStore = useVoiceRecordStore();
            const {
                state,
                waveformData,
                formattedTime,
                fullSentence,
                isStarting,
            } = storeToRefs(voiceRecordStore);

            const {
                onFinishRecording,
                cleanup: cleanupRecording,
                pauseRecording,
                continueRecording,
                stopRecording,
            } = voiceRecordStore;

            return {
                state,
                waveformData,
                formattedTime,
                fullSentence,
                isStarting,
                onFinishRecording,
                cleanupRecording,
                pauseRecording,
                continueRecording,
                stopRecording,
            };
        },
        data() {
            return {
                recordData: null,
                miniPlayerPosition: {
                    x: 20,
                    y: 20,
                },
                isDragging: false,
                dragOffset: {
                    x: 0, y: 0,
                },
                recentlyDragged: false,
                dragStartPosition: null,
                hasMoved: false,
                isActive: false,
            };
        },
        computed: {
            // 移除 miniPlayerStyle computed 属性
        },
        mounted() {
            // 添加全局鼠标事件监听，用于拖拽迷你播放器
            window.addEventListener('mousemove', this.onMouseMove);
            window.addEventListener('mouseup', this.stopDrag);
            window.addEventListener('resize', this.handleWindowResize);

            // 尝试从 localStorage 读取保存的位置，如果没有则设置默认位置
            this.loadPositionFromLocalStorage();

            // 初始化时更新一次位置
            this.$nextTick(() => {
                this.updatePlayerPosition();
            });
        },
        beforeDestroy() {
            // 移除全局事件监听
            window.removeEventListener('mousemove', this.onMouseMove);
            window.removeEventListener('mouseup', this.stopDrag);
            window.removeEventListener('resize', this.handleWindowResize);
        },
        methods: {
            handlePause() {
                this.pauseRecording();
            },
            handleContinue() {
                if (this.isStarting) {
                    return;
                }
                this.continueRecording();
            },
            handleStop() {
                this.stopRecording(true);
            },
            handleMiniPlayerMouseDown(event) {
                if (!event.target.closest('.mini-control-btn')) {
                    this.isActive = true;
                }
                this.startDrag(event);
            },
            // 拖拽相关方法
            startDrag(event) {
                // 记录起始位置，用于判断是否是真正的拖拽
                this.dragStartPosition = {
                    x: event.clientX,
                    y: event.clientY,
                };

                this.isDragging = true;
                this.recentlyDragged = false;
                const rect = this.$refs.miniPlayer.getBoundingClientRect();
                this.dragOffset = {
                    x: event.clientX - rect.left,
                    y: event.clientY - rect.top,
                };
                event.preventDefault();
            },
            onMouseMove(event) {
                if (this.isDragging) {
                    // 计算移动距离
                    const moveDistance = Math.sqrt(
                        Math.pow(event.clientX - this.dragStartPosition.x, 2) +
                            Math.pow(event.clientY - this.dragStartPosition.y, 2),
                    );

                    // 如果移动距离超过阈值，标记为真正的拖拽
                    if (moveDistance > 5) {
                        this.hasMoved = true;
                    }

                    // 计算新位置
                    const newX = event.clientX - this.dragOffset.x;
                    const newY = event.clientY - this.dragOffset.y;

                    // 限制在视窗内
                    const maxX = window.innerWidth - 350; // 迷你播放器宽度
                    const maxY = window.innerHeight - 64; // 迷你播放器高度

                    this.miniPlayerPosition = {
                        x: Math.max(0, Math.min(newX, maxX)),
                        y: Math.max(0, Math.min(newY, maxY)),
                    };
                    this.updatePlayerPosition();
                }
            },
            stopDrag() {
                console.log('stopDrag', this.isDragging, this.recentlyDragged);
                if (this.isDragging) {
                    // 只有真正拖拽过才设置 recentlyDragged 为 true
                    if (this.hasMoved) {
                        this.recentlyDragged = true;

                        // 保存位置到 localStorage
                        this.savePositionToLocalStorage();

                        // 设置一个短暂的延时，防止拖拽后立即触发点击事件
                        // eslint-disable-next-line abc/no-timer-id
                        setTimeout(() => {
                            this.recentlyDragged = false;
                        }, 300);
                    }

                    this.isDragging = false;
                    this.hasMoved = false;
                    this.isActive = false;
                }
            },
            handleMiniPlayerClick() {
                console.log('handleMiniPlayerClick', this.isDragging, this.recentlyDragged);
                // 使用一个标志和时间戳来判断是否是拖拽后的点击
                if (!this.isDragging && !this.recentlyDragged) {
                    this.$emit('maximize');
                }
            },
            // 设置默认位置
            setDefaultPosition() {
                // 计算水平居中位置
                const windowWidth = window.innerWidth;
                const playerWidth = 350; // 迷你播放器宽度

                this.miniPlayerPosition = {
                    x: (windowWidth - playerWidth) / 2,
                    y: 52, // 距离顶部52px
                };
                this.updatePlayerPosition();
            },

            // 处理窗口大小变化
            handleWindowResize() {
                // 如果当前有保存的位置百分比，重新计算绝对位置
                const position = LocalStore.get('voice_mini_player_position', true);
                if (position && position.xPercent !== undefined && position.yPercent !== undefined) {
                    const windowWidth = window.innerWidth;
                    const windowHeight = window.innerHeight;
                    const playerWidth = 350; // 迷你播放器宽度
                    const playerHeight = 64; // 迷你播放器高度

                    // 计算绝对位置，确保不超出屏幕
                    const x = Math.max(0, Math.min(position.xPercent * windowWidth, windowWidth - playerWidth));
                    const y = Math.max(0, Math.min(position.yPercent * windowHeight, windowHeight - playerHeight));

                    this.miniPlayerPosition = {
                        x, y,
                    };
                    this.updatePlayerPosition();
                }
            },

            // 从 localStorage 加载位置信息
            loadPositionFromLocalStorage() {
                try {
                    const position = LocalStore.get('voice_mini_player_position', true);
                    if (position && position.xPercent !== undefined && position.yPercent !== undefined) {
                        // 将百分比转换为实际坐标
                        const windowWidth = window.innerWidth;
                        const windowHeight = window.innerHeight;
                        const playerWidth = 350; // 迷你播放器宽度
                        const playerHeight = 64; // 迷你播放器高度

                        // 计算绝对位置，确保不超出屏幕
                        const x = Math.max(0, Math.min(position.xPercent * windowWidth, windowWidth - playerWidth));
                        const y = Math.max(0, Math.min(position.yPercent * windowHeight, windowHeight - playerHeight));

                        this.miniPlayerPosition = {
                            x, y,
                        };
                        this.updatePlayerPosition();
                    } else {
                        this.setDefaultPosition();
                    }
                } catch (error) {
                    console.error('加载播放器位置信息失败:', error);
                    this.setDefaultPosition();
                }
            },

            // 保存位置信息到 localStorage
            savePositionToLocalStorage() {
                try {
                    // 将绝对坐标转换为百分比
                    const windowWidth = window.innerWidth;
                    const windowHeight = window.innerHeight;

                    // 计算百分比位置
                    const positionToSave = {
                        x: this.miniPlayerPosition.x,
                        y: this.miniPlayerPosition.y,
                        xPercent: this.miniPlayerPosition.x / windowWidth,
                        yPercent: this.miniPlayerPosition.y / windowHeight,
                    };

                    LocalStore.set('voice_mini_player_position', positionToSave);
                } catch (error) {
                    console.error('保存播放器位置信息失败:', error);
                }
            },

            // 更新播放器位置
            updatePlayerPosition() {
                if (this.$refs.miniPlayer) {
                    const {
                        x, y,
                    } = this.miniPlayerPosition;
                    this.$refs.miniPlayer.style.transform = `translate(${x}px, ${y}px)`;
                    this.$refs.miniPlayer.style.cursor = this.isDragging ? 'grabbing' : 'grab';
                }
            },
        },
    };
</script>

<style lang="scss">
// 迷你播放器样式
.voice-record-panel-mini {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 350px;
    height: 64px;
    padding: 14px 20px;
    overflow: hidden;
    cursor: grab;
    background-color: var(--abc-color-G2);
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 16px;
    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.16);
    //transition: transform 0.2s ease;

    --subtitle-mask-color-before: linear-gradient(270deg, rgba(30, 199, 97, 0) 0%, rgba(30, 199, 97, 0.8) 100%);
    --subtitle-mask-color-after: linear-gradient(270deg, rgba(30, 199, 97, 0.8) 0%, rgba(30, 199, 97, 0) 100%);

    &:not(.voice-record-panel-mini--active):hover {
        background-color: var(--abc-color-G3);

        --subtitle-mask-color-before: linear-gradient(270deg, rgba(35, 207, 103, 0) 0%, rgba(35, 207, 103, 0.8) 100%);
        --subtitle-mask-color-after: linear-gradient(270deg, rgba(35, 207, 103, 0.8) 0%, rgba(35, 207, 103, 0) 100%);
    }

    &--active {
        background-color: var(--abc-color-G1);

        --subtitle-mask-color-before: linear-gradient(270deg, rgba(8, 164, 70, 0) 0%, rgba(8, 164, 70, 0.8) 100%);
        --subtitle-mask-color-after: linear-gradient(270deg, rgba(8, 164, 70, 0.8) 0%, rgba(8, 164, 70, 0) 100%);
    }

    &__content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;
    }

    &__wave {
        display: flex;
        flex-direction: column;
        width: 226px;
        height: 100%;
    }

    &__info {
        line-height: var(--abc-line-height-small);
        color: rgba(255, 255, 255, 0.9);
    }

    .subtitle-container {
        position: relative;
        width: 100%;
        overflow: hidden;
    }

    .subtitle-wrapper {
        position: relative;
        display: flex;
        justify-content: flex-end;
        width: 100%;
        height: 16px;
        overflow: hidden;

        .subtitle-content {
            overflow: visible;
            text-align: right;
            white-space: nowrap;
        }
    }

    .mini-wave-visualization {
        height: 20px;

        canvas {
            height: 100%;
        }
    }

    .mini-wave-visualization::before,
    .subtitle-wrapper::before {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        width: 8px;
        height: 100%;
        pointer-events: none;
        content: "";
        background: var(--subtitle-mask-color-before);
    }

    .mini-wave-visualization::after,
    .subtitle-wrapper::after {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 1;
        width: 8px;
        height: 100%;
        pointer-events: none;
        content: "";
        background: var(--subtitle-mask-color-after);
    }

    &__controls {
        .mini-control-btn.abc-button-only-icon {
            width: 28px !important;
            min-width: 28px !important;
            height: 28px !important;
            background: rgba(255, 255, 255, 0.9);
            border: none;

            &:hover {
                background: #ffffff;
            }

            &:active {
                background: rgba(255, 255, 255, 0.6);
            }
        }
    }
}
</style>
