<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        append-to-body
        :show-footer="false"
        :on-close="handleClose"
        :show-close="false"
        disabled-keyboard
    >
        <abc-flex vertical gap="24" align="center">
            <h4 class="build-in-h4" style=" margin-bottom: 0; text-align: center;">
                语音病历使用须知
            </h4>
            <div style="width: 412px; height: 148px; text-align: justify;">
                <p style="margin-top: 0; line-height: 22px; color: #000000;">
                    欢迎使用 AI 语音病历，您已获得限时免费体验资格。
                </p>
                <p style=" display: flex; gap: 8px; margin-top: 12px; line-height: 22px; color: #000000;">
                    <label>1. </label>
                    <span>为获得较好的收音效果，提升识别准确率，推荐你购买专业收音设备：<abc-link @click="handleMallClick">商品链接</abc-link></span>
                </p>
                <p style=" display: flex; gap: 8px; margin-top: 6px; line-height: 22px; color: #000000;">
                    <label>2. </label>普通话效果更佳，最长录音不超过 15 分钟。
                </p>
            </div>
            <div class="ai-diagnosis-tips-footer">
                <abc-button
                    theme="success"
                    size="large"
                    width="148"
                    @click="handleClose"
                >
                    我知道了
                </abc-button>
            </div>
        </abc-flex>
    </abc-modal>
</template>

<script>
    export default {
        name: 'VoiceRecordTipsModal',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            visible: {
                get() {
                    return this.value;
                },
                set(value) {
                    this.$emit('input', value);
                },
            },
        },
        methods: {
            handleMallClick() {
                window.open('https://item.jd.com/10154312755928.html', '_blank', 'width=1200,height=680');
            },
            handleClose() {
                this.visible = false;
                this.$emit('close');
            },
        },
    };
</script>
