<template>
    <div>
        <!-- 正常录音对话框 -->
        <abc-dialog
            v-show="visible && !isMiniMode"
            ref="dialog"
            v-model="visible"
            :title="isResultView ? '语音病历' : ''"
            :show-close="isResultView"
            :custom-class="dialogClass"
            append-to-body
            :content-styles="contentStyles"
            :show-footer="false"
            :before-close="handleBeforeClose"
            :disabled-keyboard="!isResultView"
        >
            <template v-if="!isResultView">
                <abc-delete-icon
                    class="voice-record-dialog-v2__close-button"
                    theme="dark"
                    variant="outline-square"
                    size="hugely"
                    @delete="handleClose"
                ></abc-delete-icon>
                <voice-record-panel
                    class="voice-record-panel"
                    :outpatient-sheet-id="outpatientSheetId"
                    :patient-info="patientInfo"
                    @minimize="handleMinimize"
                ></voice-record-panel>
                <!-- 收起按钮 -->
                <abc-button
                    v-show="isInRecordProcess && isUnDiagnosis"
                    class="voice-record-dialog-v2__minimize-button"
                    shape="square"
                    theme="default"
                    variant="text"
                    size="large"
                    icon="s-b-minimize-line"
                    @click="handleMinimize"
                >
                    收起
                </abc-button>
            </template>
            <voice-record-result-panel
                v-else
                :switch-setting="switchSetting"
                :patient-info="patientInfo"
                :outpatient-info="outpatientInfo"
                :attachment-id="attachmentId"
                :record-data="recordData"
                @re-record="handleReRecord"
                @finish="handleClose"
            >
            </voice-record-result-panel>
        </abc-dialog>

        <!-- 迷你播放器模式 -->
        <voice-record-panel-mini
            v-show="visible && isMiniMode"
            ref="miniPlayer"
            :class="isAnimating ? 'voice-record-panel-mini-is-animation' : ''"
            @maximize="handleMaximize"
        >
        </voice-record-panel-mini>
    </div>
</template>

<script>
    import VoiceRecordPanel from './voice-record-panel.vue';
    import VoiceRecordResultPanel from './voice-record-result-panel.vue';
    import { useVoiceRecordStore } from '../hooks/use-voice-record';
    import { storeToRefs } from 'MfBase/pinia';
    import VoiceRecordPanelMini from './voice-record-panel-mini.vue';
    import {
        useVoiceRecordGlobalStore,
    } from './voice-record-global/voice-record-global-manager';

    export default {
        name: 'VoiceRecordDialog',
        components: {
            VoiceRecordPanelMini,
            VoiceRecordPanel,
            VoiceRecordResultPanel,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            /**
             * 病历开关设置
             */
            switchSetting: {
                type: Object,
            },
            /**
             * 患者信息
             */
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
            /**
             * 门诊信息
             */
            outpatientInfo: {
                type: Object,
                default: () => ({}),
            },
            attachmentId: {
                type: String,
                default: '',
            },
        },
        setup() {
            const voiceRecordStore = useVoiceRecordStore();
            const {
                state,
            } = storeToRefs(voiceRecordStore);

            const {
                onFinishRecording,
                cleanup: cleanupRecording,
            } = voiceRecordStore;

            const voiceRecordGlobalStore = useVoiceRecordGlobalStore();

            const {
                isMiniMode,
            } = storeToRefs(voiceRecordGlobalStore);

            const {
                setMiniMode,
            } = voiceRecordGlobalStore;

            return {
                state,
                onFinishRecording,
                cleanupRecording,
                isMiniMode,
                setMiniMode,
            };
        },
        data() {
            return {
                isResultView: this.attachmentId !== '',
                recordData: null,
                // 播放器相关数据
                isDestroy: false,
                isAnimating: false,
            };
        },
        computed: {
            visible: {
                get() {
                    return this.value;
                },
                set(value) {
                    this.$emit('input', value);
                },
            },
            // 是否是待诊
            isUnDiagnosis() {
                return this.outpatientInfo?.status !== 1;
            },
            isInRecordProcess() {
                return this.state === 'recording' || this.state === 'paused';
            },
            dialogClass() {
                const classNames = ['voice-record-dialog-v2'];

                if (this.isResultView) {
                    classNames.push('result-view');
                }
                if (this.isAnimating) {
                    classNames.push('voice-record-dialog-is-animation');
                }
                return classNames.join(' ');
            },
            customStyles() {
                if (this.isResultView) {
                    return {
                        transition: 'width .4s, height .4s ease-in-out',
                        height: '100%',
                        width: '100%',
                        'min-width': '640px',
                        'min-height': '440px',
                    };
                }
                return {
                    height: '440px',
                    width: '640px',
                };
            },
            contentStyles() {
                if (this.isResultView) {
                    const height = Math.round(window.innerHeight * 0.76);
                    return `transition: width .4s, height .4s ease-in-out; min-width: 640px; min-height: 440px; height: ${height}px; width: 1200px;`;
                }
                return 'height: 440px; width: 640px;';
            },
            outpatientSheetId() {
                return this.outpatientInfo?.id;
            },
        },
        watch: {
            visible(value) {
                if (!value) {
                    // console.log('voice-record-dialog visible false, emit destroy');
                    this.$emit('destroy');
                    this.destroyElement();
                }
            },
        },
        created() {
            const unWatchFinish = this.onFinishRecording(({
                audioData,
                asrResult,
                logger,
            }) => {
                console.log('onFinishRecording asrResult', asrResult);
                // 结果视图时退出迷你模式
                if (this.isMiniMode) {
                    this.handleMaximize();
                }
                this.isResultView = true;
                this.recordData = {
                    audioData,
                    asrResult: [...asrResult],
                    logger,
                };
            });
            this.$on('hook:beforeDestroy', () => {
                unWatchFinish();
                this.cleanupRecording();
            });
        },
        methods: {
            handleReRecord() {
                this.isResultView = false;
                this.$emit('re-record');
            },
            handleClose() {
                this.visible = false;
            },
            handleBeforeClose(fn) {
                if (this.isResultView) {
                    this.$alert({
                        type: 'info',
                        title: '提示',
                        content: '已保存本次录音，可再次打开回听录音或采纳病历',
                        closeAfterConfirm: true,
                        showClose: false,
                        onConfirm: () => fn(true),
                    });
                } else {
                    fn(true);
                }
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode?.removeChild(this.$el);
            },
            // 迷你播放器相关方法
            async handleMinimize() {
                this.isAnimating = true;
                // 第一步：获取起始位置和尺寸（First）
                const dialogEl = this.$refs.dialog.$refs.dialog;
                const dialogRect = dialogEl.getBoundingClientRect();

                // 创建对话框克隆元素用于动画
                const dialogClone = dialogEl.cloneNode(true);
                dialogClone.style.position = 'fixed';
                dialogClone.style.zIndex = '9999';
                dialogClone.style.left = `${dialogRect.left}px`;
                dialogClone.style.top = `${dialogRect.top}px`;
                dialogClone.style.width = `${dialogRect.width}px`;
                dialogClone.style.height = `${dialogRect.height}px`;
                dialogClone.style.transition = 'none';
                dialogClone.style.pointerEvents = 'none'; // 防止克隆元素接收鼠标事件
                dialogClone.style.transformOrigin = 'top left';

                // 添加到文档中
                document.body.appendChild(dialogClone);

                // 设置迷你模式
                this.setMiniMode(true);

                // 等待迷你播放器渲染完成
                await this.$nextTick();

                // 第二步：获取结束位置和尺寸（Last）
                const miniPlayerEl = this.$refs.miniPlayer.$el;
                const miniPlayerRect = miniPlayerEl.getBoundingClientRect();

                // 创建迷你播放器克隆元素
                const miniPlayerClone = miniPlayerEl.cloneNode(true);
                miniPlayerClone.style.position = 'fixed';
                miniPlayerClone.style.zIndex = '9998'; // 略低于对话框克隆
                miniPlayerClone.style.left = `${miniPlayerRect.left}px`;
                miniPlayerClone.style.top = `${miniPlayerRect.top}px`;
                miniPlayerClone.style.width = `${miniPlayerRect.width}px`;
                miniPlayerClone.style.height = `${miniPlayerRect.height}px`;
                miniPlayerClone.style.transition = 'none';
                miniPlayerClone.style.pointerEvents = 'none';
                miniPlayerClone.style.transformOrigin = 'top left';
                miniPlayerClone.style.opacity = '0';

                // 添加到文档中
                document.body.appendChild(miniPlayerClone);

                // 第三步：计算变换（Invert）
                const scaleX = miniPlayerRect.width / dialogRect.width;
                const scaleY = miniPlayerRect.height / dialogRect.height;
                const translateX = miniPlayerRect.left - dialogRect.left;
                const translateY = miniPlayerRect.top - dialogRect.top;

                // 第四步：执行动画（Play）
                // 对话框淡出动画
                const dialogAnimation = dialogClone.animate([
                    {
                        transform: 'none',
                        borderRadius: '12px',
                        opacity: 1,
                    },
                    {
                        transform: `translate(${translateX}px, ${translateY}px) scale(${scaleX}, ${scaleY})`,
                        borderRadius: '16px',
                        opacity: 0,
                    },
                ], {
                    duration: 300,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    fill: 'forwards',
                });

                // 迷你播放器淡入动画
                const miniPlayerAnimation = miniPlayerClone.animate([
                    {
                        transform: `translate(${-translateX}px, ${-translateY}px) scale(${1 / scaleX}, ${1 / scaleY})`,
                        borderRadius: '12px',
                        opacity: 0,
                    },
                    {
                        opacity: 0.2,
                        offset: 0.5,
                    },
                    {
                        transform: 'none',
                        borderRadius: '16px',
                        opacity: 1,
                    },
                ], {
                    duration: 300,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    fill: 'forwards',
                });

                // 动画结束后移除克隆元素
                dialogAnimation.onfinish = () => {
                    document.body.removeChild(dialogClone);
                };

                miniPlayerAnimation.onfinish = () => {
                    document.body.removeChild(miniPlayerClone);
                    this.isAnimating = false;
                };
            },
            async handleMaximize() {
                this.isAnimating = true;

                // 第一步：获取起始位置和尺寸（First）
                const miniPlayerEl = this.$refs.miniPlayer.$el;
                const miniPlayerRect = miniPlayerEl.getBoundingClientRect();

                // 创建迷你播放器克隆元素
                const miniPlayerClone = miniPlayerEl.cloneNode(true);
                miniPlayerClone.style.position = 'fixed';
                miniPlayerClone.style.zIndex = '9999';
                miniPlayerClone.style.left = `${miniPlayerRect.left}px`;
                miniPlayerClone.style.top = `${miniPlayerRect.top}px`;
                miniPlayerClone.style.width = `${miniPlayerRect.width}px`;
                miniPlayerClone.style.height = `${miniPlayerRect.height}px`;
                miniPlayerClone.style.transition = 'none';
                miniPlayerClone.style.pointerEvents = 'none';
                miniPlayerClone.style.transformOrigin = 'top left';

                // 添加到文档中
                document.body.appendChild(miniPlayerClone);

                // 设置为非迷你模式
                this.setMiniMode(false);

                // 等待对话框渲染完成
                await this.$nextTick();
                // 第二步：获取结束位置和尺寸（Last）
                const dialogEl = this.$refs.dialog.$refs.dialog;
                const dialogRect = dialogEl.getBoundingClientRect();

                // 创建对话框克隆元素
                const dialogClone = dialogEl.cloneNode(true);
                dialogClone.style.position = 'fixed';
                dialogClone.style.zIndex = '9998';
                dialogClone.style.left = `${dialogRect.left}px`;
                dialogClone.style.top = `${dialogRect.top}px`;
                dialogClone.style.width = `${dialogRect.width}px`;
                dialogClone.style.height = `${dialogRect.height}px`;
                dialogClone.style.minWidth = `${dialogRect.width}px`;
                dialogClone.style.minHeight = `${dialogRect.height}px`;
                dialogClone.style.transition = 'none';
                dialogClone.style.pointerEvents = 'none';
                dialogClone.style.transformOrigin = 'top left';
                dialogClone.style.opacity = '0';

                const dialogWrapper = document.createElement('div');
                dialogWrapper.classList.add('abc-dialog-wrapper');
                dialogWrapper.appendChild(dialogClone);

                // 添加到文档中
                document.body.appendChild(dialogWrapper);

                // 第三步：计算变换（Invert）
                const scaleX = dialogRect.width / miniPlayerRect.width;
                const scaleY = dialogRect.height / miniPlayerRect.height;
                const translateX = dialogRect.left - miniPlayerRect.left;
                const translateY = dialogRect.top - miniPlayerRect.top;

                // 第四步：执行动画（Play）
                // 迷你播放器淡出动画
                const miniPlayerAnimation = miniPlayerClone.animate([
                    {
                        transform: 'none',
                        borderRadius: '16px',
                        opacity: 1,
                    },
                    {
                        opacity: 0, offset: 0.5,
                    },
                    {
                        transform: `translate(${translateX}px, ${translateY}px) scale(${scaleX}, ${scaleY})`,
                        borderRadius: '12px',
                        opacity: 0,
                    },
                ], {
                    duration: 300,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    fill: 'both',
                });

                // 对话框淡入动画
                const dialogAnimation = dialogClone.animate([
                    {
                        transform: `translate(${-translateX}px, ${-translateY}px) scale(${1 / scaleX}, ${1 / scaleY})`,
                        borderRadius: '16px',
                        opacity: 0,
                    },
                    {
                        transform: 'none',
                        borderRadius: '12px',
                        opacity: 1,
                    },
                ], {
                    duration: 300,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    fill: 'both',
                });

                // 动画结束后移除克隆元素
                miniPlayerAnimation.onfinish = () => {
                    document.body.removeChild(miniPlayerClone);
                };

                dialogAnimation.onfinish = () => {
                    document.body.removeChild(dialogWrapper);
                    this.isAnimating = false;
                };
            },
        },
    };
</script>

<style lang="scss">
// 弹窗样式覆盖
.voice-record-dialog-v2 {
    padding: 0;
    overflow: hidden;
    border-radius: 12px;
    will-change: width, height;

    --abc-dialog-translate-y: -40% !important;

    &.result-view {
        .abc-dialog-body {
            padding: 0 !important;
            background: transparent;
        }
    }

    .abc-dialog-header {
        padding: 0 0 0 24px;

        .abc-dialog-title {
            font-size: 16px;
            font-weight: 600;
            color: #000000;
        }
    }

    .abc-dialog-body {
        position: relative;
        padding: 40px 86px 68px 86px !important;
        margin: 0;
        overflow-x: hidden;
        overflow-y: hidden;
        background: linear-gradient(0deg, #ffffff 0%, var(--abc-color-theme8, #e0efff) 100%);
    }

    &__close-button {
        position: absolute;
        top: 4px;
        right: 4px;
    }

    &__minimize-button {
        position: absolute;
        right: 12px;
        bottom: 12px;
    }

    &__header {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 15px;
    }

    &__content {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
    }
}

.voice-record-panel-mini-is-animation {
    opacity: 0;
}

.voice-record-dialog-is-animation {
    background: transparent;
    opacity: 0;
}
</style>
