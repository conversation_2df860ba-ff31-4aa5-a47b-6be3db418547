import { marked } from 'marked';

/**
 * MarkdownRenderer - 可扩展的 Markdown 渲染器
 * 提供了一个插件系统，允许自定义 Markdown 渲染规则
 */
class MarkdownRenderer {
    constructor() {
        // 创建一个基础渲染器实例
        this.renderer = new marked.Renderer();
        
        // 保存原始渲染方法的引用
        this.baseRenderers = {
            heading: marked.Renderer.prototype.heading,
            paragraph: marked.Renderer.prototype.paragraph,
            code: marked.Renderer.prototype.code,
            blockquote: marked.Renderer.prototype.blockquote,
            list: marked.Renderer.prototype.list,
            listitem: marked.Renderer.prototype.listitem,
            table: marked.Renderer.prototype.table,
            hr: marked.Renderer.prototype.hr,
        };
        
        // 插件列表
        this.plugins = [];
        
        // 全局状态
        this.state = {
            isFirstMarkdownFirstHeading: true,
        };
        
        // 内容清理规则 { pattern: 匹配模式, replacement: 替换内容 }
        this.cleanupRules = [
            {
                pattern: /```/g, replacement: '', 
            },
        ];
    }
    
    /**
     * 添加插件
     * @param {Object} plugin - 插件对象
     * @param {string} plugin.type - 插件类型 (heading, paragraph, code 等)
     * @param {Function} plugin.renderer - 渲染函数
     * @param {number} [plugin.priority=10] - 优先级，数字越小优先级越高
     * @returns {MarkdownRenderer} - 返回实例本身，支持链式调用
     */
    addPlugin(plugin) {
        if (!plugin || !plugin.type || typeof plugin.renderer !== 'function') {
            console.warn('Invalid plugin:', plugin);
            return this;
        }
        
        this.plugins.push({
            ...plugin,
            priority: plugin.priority || 10,
        });
        
        // 按优先级排序插件
        this.plugins.sort((a, b) => a.priority - b.priority);
        
        // 更新渲染器方法
        this._updateRenderer();
        
        return this;
    }
    
    /**
     * 移除插件
     * @param {string} pluginId - 插件ID
     * @returns {MarkdownRenderer} - 返回实例本身，支持链式调用
     */
    removePlugin(pluginId) {
        this.plugins = this.plugins.filter((plugin) => plugin.id !== pluginId);
        this._updateRenderer();
        return this;
    }
    
    /**
     * 添加内容清理规则
     * @param {RegExp} pattern - 匹配模式
     * @param {string} replacement - 替换内容
     * @returns {MarkdownRenderer} - 返回实例本身，支持链式调用
     */
    addCleanupRule(pattern, replacement = '') {
        this.cleanupRules.push({
            pattern, replacement, 
        });
        return this;
    }
    
    /**
     * 更新渲染器方法
     * @private
     */
    _updateRenderer() {
        // 为每种类型的插件创建渲染方法
        const rendererTypes = [...new Set(this.plugins.map((plugin) => plugin.type))];
        
        rendererTypes.forEach((type) => {
            if (!this.baseRenderers[type]) {
                console.warn(`No base renderer found for type: ${type}`);
                return;
            }
            
            // 获取此类型的所有插件
            const typePlugins = this.plugins.filter((plugin) => plugin.type === type);
            
            // 创建渲染方法
            this.renderer[type] = (data) => {
                let result = null;
                let processed = false;
                
                // 按优先级应用插件
                for (const plugin of typePlugins) {
                    const pluginResult = plugin.renderer.call(this, {
                        data,
                        state: this.state,
                        baseRenderer: this.baseRenderers[type],
                    });
                    
                    if (pluginResult !== undefined) {
                        result = pluginResult;
                        processed = true;
                        break;
                    }
                }
                
                // 如果没有插件处理，使用基础渲染器
                if (!processed) {
                    result = this.baseRenderers[type].call(this.renderer, data);
                }
                
                return result;
            };
        });
    }
    
    /**
     * 清理内容
     * @param {string} content - 原始内容
     * @returns {string} - 清理后的内容
     * @private
     */
    _cleanContent(content) {
        if (!content) return '';
        
        let cleanedContent = content;
        this.cleanupRules.forEach((rule) => {
            cleanedContent = cleanedContent.replace(rule.pattern, rule.replacement);
        });
        
        return cleanedContent;
    }
    
    /**
     * 渲染 Markdown 内容
     * @param {string} content - Markdown 内容
     * @returns {string} - 渲染后的 HTML
     */
    render(content) {
        try {
            const cleanedContent = this._cleanContent(content);
            return marked.parse(cleanedContent, { renderer: this.renderer });
        } catch (e) {
            console.error('Error rendering markdown:', e);
            return content || '';
        }
    }
    
    /**
     * 重置渲染器状态
     * @returns {MarkdownRenderer} - 返回实例本身，支持链式调用
     */
    reset() {
        this.state.isFirstMarkdownFirstHeading = true;
        return this;
    }
}

// 创建默认实例
const defaultRenderer = new MarkdownRenderer();

// 添加标题渲染插件
defaultRenderer.addPlugin({
    id: 'heading-top-divider',
    type: 'heading',
    priority: 1,
    renderer({
        data, state, baseRenderer, 
    }) {
        const { depth } = data;
        
        if (depth === 3) {
            let result = '';
            
            // 只有第一个 markdown 的第一个标题不添加分割线
            if (!state.isFirstMarkdownFirstHeading) {
                result += '<hr class="markdown-custom-renderer-heading-top-divider" />';
            } else {
                // 标记第一个标题已经渲染过
                state.isFirstMarkdownFirstHeading = false;
            }
            
            result += baseRenderer.call(this.renderer, data);
            
            return result;
        }
        
        // 返回 undefined 表示不处理，让其他插件或基础渲染器处理
        return undefined;
    },
});

// 添加段落渲染插件
defaultRenderer.addPlugin({
    id: 'paragraph-top-divider',
    type: 'paragraph',
    priority: 1,
    renderer({
        data, 
    }) {
        const { text } = data;
        
        if (text.includes('加减建议')) {
            let result = '';
            result += '<div class="markdown-custom-renderer-paragraph-top-divider"></div>';
            result += `<div class="markdown-custom-renderer-add-sub-suggestion">${text}</div>`;
            return result;
        }
        
        return undefined;
    },
});

// 添加水平线渲染插件
defaultRenderer.addPlugin({
    id: 'custom-hr',
    type: 'hr',
    priority: 1,
    renderer() {
        // 将默认的 <hr> 替换为自定义的 div
        return '<div class="markdown-custom-hr-divider"></div>';
    },
});

// 导出默认实例的方法
export const {
    render, reset, 
} = {
    render: defaultRenderer.render.bind(defaultRenderer),
    reset: defaultRenderer.reset.bind(defaultRenderer),
};
