import {
    TASK_STATUS, CUSTOM_STREAM_EVENT_TYPE_TITLE, CUSTOM_EXTERNAL_STREAM_EVENT_TYPE,
} from './constant';

class StreamController {
    constructor() {
        this.queue = []; // 任务队列
        this.timer = null;
        this.chunkSize = 10; // 每次处理的字符数
        this.interval = 10; // 每次处理的间隔
        this.replenishKeys = [];
        
        this.inited = false;
    }
    
    initQueue(keys) {
        if (!keys || !Array.isArray(keys) || keys.length === 0) {
            throw new Error('Invalid keys');
        }
        this.queue = keys.map((key) => ({
            key,
            status: TASK_STATUS.PENDING,
            content: '',
        }));
        
        this.inited = true;
    }
    
    push(payload) {
        const {
            key, content, done, 
        } = payload;
        const taskItem = this.queue.find((task) => task.key === key);
        if (!taskItem) {
            throw new Error(`Task with key ${key} not found`);
        }
        
        taskItem.content += content || '';
        
        if (done) {
            taskItem.status = TASK_STATUS.COMPLETED;
        } else {
            taskItem.status = TASK_STATUS.IN_PROGRESS;
        }
    }
    
    start(onPending, onMessage, onCompleted) {
        if (!this.inited) {
            throw new Error('StreamController must be initialized first');
        }
        
        if (this.timer) {
            clearInterval(this.timer);
        }
        
        this.timer = setInterval(() => {
            if (this.queue.length === 0) {
                this.destroy();
                onCompleted && onCompleted();
                return;
            }
            
            const currentTask = this.queue[0];
            
            if (currentTask.status === TASK_STATUS.PENDING) {
                // 处理 pending 状态的任务
                onPending && onPending(currentTask.key);
                return;
            } 
            
            // 处理 in_progress 或 completed 状态的任务
            if (currentTask.content.length > 0) {
                // 取出前 chunkSize 个字符
                const chunk = currentTask.content.substring(0, this.chunkSize);
                // 更新 content，移除已处理的部分
                currentTask.content = currentTask.content.substring(this.chunkSize);
                
                
                if (!this.replenishKeys.includes(currentTask.key) && Object.keys(CUSTOM_STREAM_EVENT_TYPE_TITLE).includes(currentTask.key)) {
                    if (this.replenishKeys.some((key) => CUSTOM_EXTERNAL_STREAM_EVENT_TYPE.includes(key)) &&
                    CUSTOM_EXTERNAL_STREAM_EVENT_TYPE.includes(currentTask.key)) {
                        const splitChunk = `\n\n --- \n\n${chunk}`;
                        onMessage && onMessage(currentTask.key, splitChunk);
                        this.replenishKeys.push(currentTask.key);
                        return;
                    }
                    
                    const titleChunk = `\n\n ### ${CUSTOM_STREAM_EVENT_TYPE_TITLE[currentTask.key]}\n\n${chunk}`;
                    onMessage && onMessage(currentTask.key, titleChunk);
                    this.replenishKeys.push(currentTask.key);
                    return;
                }
                
                // 调用 onMessage 回调
                onMessage && onMessage(currentTask.key, chunk);
            } else if (currentTask.status === TASK_STATUS.COMPLETED) {
                // 当内容已全部处理完且任务状态为 completed 时，移除该任务
                this.queue.shift();
            } 
            
        }, this.interval); 
    }
    
    destroy() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        
        this.queue = [];
        this.replenishKeys = [];
        this.inited = false;
    }
}

export default new StreamController();