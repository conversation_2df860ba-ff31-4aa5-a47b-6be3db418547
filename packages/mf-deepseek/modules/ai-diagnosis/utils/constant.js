export const PROCESSING_ENUM = ['先煎', '后下', '包煎', '另煎', '先炒', '烊化', '冲服', '捣碎', '打粉', '另包'];

export const MATADATA_RENDER_STATUS = {
    NOT_STARTED: 1, // 未开始
    IN_PROGRESS: 2, // 进行中
    COMPLETED: 3, // 已完成
};


export const ANALYSIS_EXTEND_FLAG = {
    DIALECTICAL: 1, // 诊断
    PRESCRIPTION: 2, // 处方
    SUGGESTION: 4, // 建议
    CHINESE_PRESCRIPTION: 8, // 中医处方
    WESTERN_PRESCRIPTION: 16, // 西医处方
    EXTERNAL_PRESCRIPTION: 32, // 外治处方
};

export const MASSAGE_PR = 'massage_pr';
export const GUASHA_PR = 'guasha_pr';
export const CUPPING_PR = 'cupping_pr';
export const MOXIBUSTION_PR = 'moxibustion_pr';
export const ACUPUNCTURE_PR = 'acupuncture_pr';
export const TRANSDERMAL_PR = 'transdermal_pr';

export const COMPONENT_NAME = {
    DIALECTICAL: 'dialectical', // 证型 (已废弃)
    CHINESE_PRESCRIPTION: 'ch_pr', // 中医处方
    SUGGESTION: 'suggestion', // 建议
    UNKNOWN: 'unknown',
    TREATMENT: 'treatment', // 治法
    WESTERN_PRESCRIPTION: 'w_pr', // 西医处方
    DIAGNOSIS: 'diagnosis', // 初步诊断/诊断
    WESTERN_DIAGNOSTIC_BASIS: 'w_db', // 西医诊断依据
    CHINESE_DIAGNOSTIC_BASIS: 'c_db', // 中医辩证要点
    EXTERNAL_MASSAGE_PR: MASSAGE_PR, // 外治推拿
    EXTERNAL_GUASHA_PR: GUASHA_PR, // 外治刮痧
    EXTERNAL_MOXIBUSTION_PR: MOXIBUSTION_PR, // 外治艾灸
    EXTERNAL_CUPPING_PR: CUPPING_PR, // 外治拔罐
    EXTERNAL_ACUPUNCTURE_PR: ACUPUNCTURE_PR, // 外治针灸
    EXTERNAL_TRANSDERMAL_PR: TRANSDERMAL_PR, // 外治贴敷
    MEDICAL_RECORD_REVIEW: 'medical_record_review', // 病史回顾
    CHINESE_ADD_SUB_SUGGESTION: 'chinese_add_sub_suggestion', // 中医加减建议
};

export const EXTERNAL_PRESCRIPTION_TYPE = {
    [TRANSDERMAL_PR]: '贴敷',
    [ACUPUNCTURE_PR]: '针灸',
    [MOXIBUSTION_PR]: '艾灸',
    [CUPPING_PR]: '拔罐',
    [GUASHA_PR]: '刮痧',
    [MASSAGE_PR]: '推拿',
};

export const ExternalPRUsageTypeEnum = Object.freeze({
    [TRANSDERMAL_PR]: 0, // 贴敷
    [ACUPUNCTURE_PR]: 1, // 针刺
    [MOXIBUSTION_PR]: 2, // 艾灸
    [CUPPING_PR]: 3, // 拔罐
    [GUASHA_PR]: 4, // 刮痧
    [MASSAGE_PR]: 5, // 推拿
});


export const STREAM_EVENT_TYPE = Object.freeze({
    METADATA: 'metadata', // 元数据
    THINKING: 'thinking', // 深度思考
    CHINESE_MEDICAL_HISTORY_REVIEW: 'chinese-medical-history-review', // 中医病史回顾
    WESTERN_MEDICAL_HISTORY_REVIEW: 'western-medical-history-review', // 西医病史回顾
    SUGGESTION_CHINESE_DIAGNOSE: 'suggestion-chinese-diagnose', // 推荐中医诊断
    SUGGESTION_WESTERN_DIAGNOSE: 'suggestion-western-diagnose', // 推荐西医诊断
    SUGGESTION_WESTERN_ANNOUNCEMENTS: 'suggestion-western-announcements', // 西医建议
    SUGGESTION_WESTERN_PRESCRIPTION: 'suggestion-western-prescription', // 成药处方
    SUGGESTION_CHINESE_PRESCRIPTION: 'suggestion-chinese-prescription', // 中药处方
    SUGGESTION_CHINESE_ANNOUNCEMENTS: 'suggestion-chinese-announcements', // 中医建议
    SUGGESTION_EXTERNAL_CUPPING_PRESCRIPTION: 'suggestion-external-cupping-prescription', // 外治拔罐处方
    SUGGESTION_EXTERNAL_MASSAGE_PRESCRIPTION: 'suggestion-external-massage-prescription', // 外治推拿处方
    SUGGESTION_EXTERNAL_GUASHA_PRESCRIPTION: 'suggestion-external-guasha-prescription', // 外治刮痧处方
    SUGGESTION_EXTERNAL_MOXIBUSTION_PRESCRIPTION: 'suggestion-external-moxibustion-prescription', // 外治艾灸处方
    SUGGESTION_EXTERNAL_ACUPUNCTURE_PRESCRIPTION: 'suggestion-external-acupuncture-prescription', // 外治针灸处方
    SUGGESTION_EXTERNAL_TRANSDERMAL_PRESCRIPTION: 'suggestion-external-transdermal-prescription', // 外治贴敷处方
    ALL: 'all', // 所有
});

export const CUSTOM_STREAM_EVENT_TYPES = Object.freeze([
    STREAM_EVENT_TYPE.CHINESE_MEDICAL_HISTORY_REVIEW,
    STREAM_EVENT_TYPE.WESTERN_MEDICAL_HISTORY_REVIEW,
    STREAM_EVENT_TYPE.SUGGESTION_CHINESE_DIAGNOSE,
    STREAM_EVENT_TYPE.SUGGESTION_WESTERN_DIAGNOSE,
    STREAM_EVENT_TYPE.SUGGESTION_CHINESE_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_WESTERN_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_TRANSDERMAL_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_ACUPUNCTURE_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_MOXIBUSTION_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_CUPPING_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_GUASHA_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_MASSAGE_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_CHINESE_ANNOUNCEMENTS,
    STREAM_EVENT_TYPE.SUGGESTION_WESTERN_ANNOUNCEMENTS,
]);


export const CUSTOM_STREAM_EVENT_TYPE_TITLE = Object.freeze({
    [STREAM_EVENT_TYPE.SUGGESTION_CHINESE_PRESCRIPTION]: '中药处方',
    [STREAM_EVENT_TYPE.SUGGESTION_WESTERN_PRESCRIPTION]: '成药处方',
    [STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_TRANSDERMAL_PRESCRIPTION]: '外治处方',
    [STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_ACUPUNCTURE_PRESCRIPTION]: '外治处方',
    [STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_MOXIBUSTION_PRESCRIPTION]: '外治处方',
    [STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_CUPPING_PRESCRIPTION]: '外治处方',
    [STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_GUASHA_PRESCRIPTION]: '外治处方',
    [STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_MASSAGE_PRESCRIPTION]: '外治处方',
    [STREAM_EVENT_TYPE.SUGGESTION_CHINESE_ANNOUNCEMENTS]: '注意事项',
    [STREAM_EVENT_TYPE.SUGGESTION_WESTERN_ANNOUNCEMENTS]: '注意事项',
    [STREAM_EVENT_TYPE.CHINESE_MEDICAL_HISTORY_REVIEW]: '病史回顾',
    [STREAM_EVENT_TYPE.WESTERN_MEDICAL_HISTORY_REVIEW]: '病史回顾',
});

export const CUSTOM_EXTERNAL_STREAM_EVENT_TYPE = Object.freeze([
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_TRANSDERMAL_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_ACUPUNCTURE_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_MOXIBUSTION_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_CUPPING_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_GUASHA_PRESCRIPTION,
    STREAM_EVENT_TYPE.SUGGESTION_EXTERNAL_MASSAGE_PRESCRIPTION,
]);

export const TASK_STATUS = {
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
};

export const DIAGNOSIS_MODEL_TYPE = {
    CHINESE: 1, // 中医大模型
    WESTERN: 2, // 西医大模型
};
