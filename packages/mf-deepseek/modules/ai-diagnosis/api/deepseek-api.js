// api/deepseek-api.js
import { fetch } from 'MfBase/base-api';

export const fetchIsReadTips = async () => {
    try {
        const res = await fetch.get('/api/v3/clinics/reminder/ai-use-notice/read');
        return !!res.data?.data?.created;
    } catch (error) {
        console.error('Fetch is read tips error:', error);
        return false;
    }
};

export const markReadTips = async () => {
    try {
        await fetch.post('/api/v3/clinics/reminder/ai-use-notice/read');
        return true;
    } catch (error) {
        console.error('Mark read tips error:', error);
        return false;
    }
};

export const fetchPrompt = async (data) => {
    try {
        const res = await fetch.post('/api/v2/ai/business/outpatient/medical-record-diagnosis/prompt', data);
        return res.data;
    } catch (error) {
        console.error('Fetch error:', error);
        return null;
    }
};

export const checkDeepseekResult = async (params) => {
    try {
        const res = await fetch.post('/api/v2/ai/business/outpatient/medical-record-diagnosis/result', params);
        if (res.data?.data) {
            return {
                thinking: res.data.data.reasoningContent,
                content: res.data.data.content,
                metadata: res.data.data.metadata,
                id: res.data.data.id,
                request: res.data.data.request,
            };
        }
    } catch (error) {
        console.error('Check deepseek result error:', error);
    }
    return null;
};

export const analysisDeepseekResult = async (id, extendFlag, extendInfo = null) => {
    try {
        return fetch.put(`/api/v2/ai/analysis-result/extend-flag/${id}`, {
            extendFlag,
            extendInfo,
        });
    } catch (error) {
        console.error('Analysis deepseek result error:', error);
    }
};

export const saveDeepseekResult = async (params) => {
    try {
        return fetch.put('/api/v2/ai/business/outpatient/medical-record-diagnosis/result', params);
    } catch (error) {
        console.error('Save deepseek result error:', error);
    }
};

export const getDeepseekReqAndResult = async (businessId, businessType = 1) => {
    try {
        return await fetch({
            url: `/api/v2/ai/analysis/result/list-by-businessId/${businessId}`,
            method: 'get',
            params: {
                businessType,
            },
        });
    } catch (error) {
        console.error('Get deepseek req error:', error);
    }
};

// 统一导出
export default {
    fetchIsReadTips,
    markReadTips,
    fetchPrompt,
    checkDeepseekResult,
    analysisDeepseekResult,
    saveDeepseekResult,
    getDeepseekReqAndResult,
};
