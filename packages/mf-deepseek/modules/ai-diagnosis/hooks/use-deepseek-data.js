import { defineStore } from 'MfBase/pinia';

export const useDeepseekDataStore = defineStore('deepSeekData', {
    state: () => ({
        medicalRecord: null,
        medicalRecordSwitch: null,
        patientInfo: null,
        extendInfo: null,

        // Deepseek result ID
        resultId: '',

        rule: { // 定制化规则
            dialecticalSchool: 1,
            treatmentMethods: 1,
            chineseMedicineFlavors: 16,
            diagnosisModel: 1,
        },

        lastReq: null,
    }),

    actions: {
        setMedicalRecord(data) {
            this.medicalRecord = data;
        },

        setMedicalRecordSwitch(data) {
            this.medicalRecordSwitch = data;
        },

        setPatientInfo(data) {
            this.patientInfo = data;
        },

        setExtendInfo(data) {
            this.extendInfo = data;
        },

        setResultId(data) {
            this.resultId = data;
        },

        setRule(data) {
            this.rule = data;
        },

        // 重置 store 状态到初始值
        resetState() {
            this.$reset();
        },

        setLastReq(data) {
            this.lastReq = data;
        },
    },
});
