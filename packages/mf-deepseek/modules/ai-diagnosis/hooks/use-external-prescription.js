import { defineStore } from 'MfBase/pinia';
import { EXTERNAL_PRESCRIPTION_TYPE } from '../utils/constant';
import { isString } from '@/common/utils/index';
import {
    numberRegex, numberPrefixRegex,
} from '@/common/utils/regex';

export const useExternalPrescriptionStore = defineStore('externalPrescription', {
    state: () => ({}),

    actions: {
        handleTitle(key) {
            return EXTERNAL_PRESCRIPTION_TYPE[key] || '';
        },

        handlePlace(place) {
            if (!place) return '';

            if (!Array.isArray(place)) {
                return '';
            }

            return place.map((o) => {
                const {
                    name, position,
                } = o;

                return `${name || ''}${name && position ? `(${position})` : ''}`;
            }).filter(Boolean).join('，');
        },


        handleUsage(o, duration = false) {
            if (!o) return '';

            const {
                // eslint-disable-next-line camelcase
                frequency, duration_per_time, total_count, total_times,
            } = o;

            // eslint-disable-next-line camelcase
            const total = total_count || total_times;

            if (!duration) {
                // eslint-disable-next-line camelcase
                return `${frequency || ''}${total ? `，共${total}` : ''}`;
            }

            // eslint-disable-next-line camelcase
            return `${frequency || ''}${duration_per_time ? `，${this.handleDurationTime(duration_per_time)}` : ''}${total ? `，共${total}` : ''}`;
        },

        handleHerbs(herbs) {
            if (!herbs) return '';

            return herbs.map((o) => {
                const {
                    name, dosage,
                } = o;

                if (dosage && isString(dosage) && numberRegex.test(dosage)) {
                    return `${name} ${dosage}g`;
                }

                return `${name || ''}${dosage || ''}`;
            }).join('，');
        },

        handleCount(count) {
            if (!count || !isString(count)) {
                return '';
            }

            if (numberRegex.test(count)) {
                return parseInt(count, 10);
            }

            const match = count.match(numberPrefixRegex);
            if (match) {
                return parseInt(match[1], 10);
            }

            return '';
        },

        isArrayComplete(data) {
            if (!data || !Array.isArray(data)) return false;

            return data.length > 0;
        },

        handleAcupointName(type, data) {
            if (type === 'guasha_pr') {
                return data?.name || '';
            }

            if (data?.type === 'main') {
                return '主穴';
            }

            if (data?.type === 'subordinate') {
                return '配穴';
            }

            return '';
        },

        handleDurationTime(duration) {
            if (!duration || !isString(duration)) {
                return '';
            }


            if (duration.includes('每次')) {
                return duration;
            }

            return `每次${duration}`;
        },
    },
});
