import { defineStore } from 'MfBase/pinia';

/**
 * Pinia store for managing DeepSeek enable state
 */
export const useDeepseekEnableStore = defineStore('deepSeekEnable', {
    state: () => ({
        isDeepseekEnable: false,
    }),
    
    actions: {
        setIsDeepseekEnable(data) {
            this.isDeepseekEnable = data;
        },
        
        // 重置 store 状态到初始值
        resetState() {
            this.$reset();
        },
    },
});
