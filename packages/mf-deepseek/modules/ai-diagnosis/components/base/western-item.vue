<template>
    <div class="western-item-wrapper">
        <abc-flex align="center">
            <abc-text
                v-if="name"
                tag="div"
                style="flex: 1; font-size: 13px; font-weight: 600;"
                class="ellipsis"
                :title="name"
            >
                {{ index + 1 }}. {{ name }}
            </abc-text>

            <abc-text
                v-if="doseVisible"
                tag="div"
                size="small"
                style="flex-shrink: 0; margin-left: 8px;"
            >
                Sig：{{ dose }} {{ route }} {{ frequency }}
            </abc-text>
        </abc-flex>
        
        <abc-text
            v-if="remarks"
            tag="div"
            size="small"
            style="padding-left: 14px; margin-top: 2px;"
            theme="gray"
        >
            {{ remarks }}
        </abc-text>
    </div>
</template>

<script>
    import { defineComponent } from 'vue';

    export default defineComponent({
        name: 'WesternItem',

        props: {
            index: {
                type: Number,
                default: 0,
            },

            name: {
                type: String,
                default: '',
            },

            dose: {
                type: String,
                default: '',
            },

            route: {
                type: String,
                default: '',
            },

            frequency: {
                type: String,
                default: '',
            },

            remarks: {
                type: String,
                default: '',
            },
        },

        computed: {
            doseVisible() {
                return this.dose || this.frequency || this.route;
            },
        },
    });
</script>

<style lang="scss" scoped>
.western-item-wrapper {
    line-height: 20px;
}
</style>
