<template>
    <div class="western-diagnostic-basis-comp-wrapper">
        <div
            v-if="isParseError"
            class="deepseek-markdown-renderer"
            v-html="errorContent"
        ></div>

        <template v-else>
            <abc-flex
                v-for="(group, index) in parsedGroups"
                :key="index"
                vertical
                class="western-diagnostic-basis-item"
            >
                <abc-text
                    tag="div"
                    :class="[
                        'western-diagnostic-basis__title',
                        {
                            'is-wait-check': isWait<PERSON><PERSON>ck(group.status)
                        }
                    ]"
                >
                    {{ group.title }}{{ isWaitCheck(group.status) ? ' ?' : '' }}
                </abc-text>
                
                <abc-text
                    v-if="isWaitCheck(group.status)"
                    theme="warning"
                    tag="div"
                    style="padding-left: 12px; font-size: 13px;"
                >
                    {{ handleWaitCheckStr(group.status) }}
                </abc-text>
                
                <template v-if="group.content">
                    <abc-flex 
                        v-for="(line, lineIndex) in handleContent(group.content)" 
                        :key="lineIndex" 
                        tag="div"
                        size="small"
                        class="western-diagnostic-basis__content"
                    >
                        {{ line }}
                    </abc-flex>
                </template>
            </abc-flex>
        </template>
    </div>
</template>

<script>
    import { defineComponent } from 'vue';
    import { marked } from 'marked';
    import { markdownToText } from '@/common/utils/markdown-to-text';
    export default defineComponent({
        name: 'WesternDiagnosticBasis',

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },
            loaded: {
                type: Boolean,
            },
        },

        computed: {
            content() {
                return this.data?.content || '';
            },

            parsedGroups() {
                if (!this.content) return [];

                // 使用正则表达式匹配所有的 **标题** 位置
                const titleRegex = /\*\*(.*?)\*\*/g;
                const titleMatches = [...this.content.matchAll(titleRegex)];

                if (!titleMatches.length) {
                    // 如果没有匹配到任何标题，但包含 ** 符号，可能是不完整的格式
                    if (this.content.includes('**')) {
                        return [{
                            title: this.content.replace(/\*/g, '').trim(),
                            status: '',
                            content: '',
                        }];
                    }
                    return [];
                }

                // 收集所有标题的位置和内容
                const titlePositions = titleMatches.map((match) => {
                    return {
                        index: match.index,
                        endIndex: match.index + match[0].length,
                        title: match[1].trim(),
                        originalMatch: match[0],
                    };
                });

                // 过滤掉可能是状态的标题（包含 * 的标题通常是状态的一部分）
                const validTitlePositions = titlePositions.filter((item) => !item.title.includes('*'));

                const groups = [];

                for (let i = 0; i < validTitlePositions.length; i++) {
                    const currentTitle = validTitlePositions[i];
                    const nextTitle = validTitlePositions[i + 1];

                    // 确定内容结束的位置（默认到下一个标题或文本结束）
                    const contentEndIndex = nextTitle ? nextTitle.index : this.content.length;

                    // 提取当前标题后的文本片段
                    const segmentText = this.content.substring(currentTitle.endIndex, contentEndIndex);

                    // 检查是否有状态标记 (***状态***)
                    const statusMatch = segmentText.match(/^\s*\*\*\*(.*?)\*\*\*/);
                    let status = '';
                    let contentStartIndex = currentTitle.endIndex;

                    // 如果有状态标记，更新状态和内容起始位置
                    if (statusMatch && statusMatch[1]) {
                        status = statusMatch[1].trim();
                        // 状态结束位置
                        contentStartIndex = currentTitle.endIndex + statusMatch[0].length;
                    }

                    // 提取内容部分
                    let content = '';

                    // 只有当状态标记完整时，才提取内容
                    if (!segmentText.includes('***') || (statusMatch && statusMatch[0].endsWith('***'))) {
                        // 提取从状态结束到内容结束的文本
                        const rawContent = this.content.substring(contentStartIndex, contentEndIndex).trim();

                        // 检查内容中是否有新标题的开始（**开头的内容）
                        const nextTitleStartMatch = rawContent.match(/\*\*/);
                        if (nextTitleStartMatch) {
                            // 如果找到了新标题的开始，截断内容
                            content = rawContent.substring(0, nextTitleStartMatch.index).trim();
                        } else {
                            content = rawContent;
                        }
                    }

                    groups.push({
                        title: currentTitle.title,
                        status,
                        content,
                    });
                }

                return groups;
            },

            isParseError() {
                return this.loaded &&
                    this.content &&
                    !this.parsedGroups.length;
            },

            errorContent() {
                return this.processContent(this.content);
            },
        },

        methods: {
            processContent(content) {
                try {
                    return marked.parse(content);
                } catch (e) {
                    console.error('Error processing content:', e);
                    return content;
                }
            },

            isWaitCheck(str) {
                if (!str) return false;

                return str.startsWith('待查');
            },

            handleWaitCheckStr(str) {
                if (!str) return '';

                const arr = str.split('|');

                return arr[1] || '未知';
            },
            
            handleContent(content) {
                const cleanedContent = content.replace(/^\s*\d+\.\s*/gm, '');
                return markdownToText(cleanedContent).split('\n');
            },
        },
    });
</script>

<style lang="scss">
.western-diagnostic-basis-comp-wrapper {
    line-height: 20px;

    .western-diagnostic-basis-item + .western-diagnostic-basis-item {
        margin-top: 8px;
    }

    .western-diagnostic-basis__title {
        position: relative;
        padding: 4px 0 4px 12px;
        font-size: 13px;
        font-weight: 600;
        line-height: 20px;

        &.is-wait-check {
            padding-bottom: 0;
        }

        &::before {
            position: absolute;
            top: 5px;
            left: 0;
            font-size: 18px;
            line-height: 1;
            content: "•";
        }
    }

    .western-diagnostic-basis__content {
        position: relative;
        padding: 4px 0 4px 12px;
        font-size: 13px;
        line-height: 20px;
    }
}
</style>
