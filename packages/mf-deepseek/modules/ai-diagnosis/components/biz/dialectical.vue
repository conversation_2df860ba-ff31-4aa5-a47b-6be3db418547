<template>
    <base-card :loaded="loaded" @click="handleClick">
        <abc-text bold tag="div" style="padding: 4px 0 0 0; line-height: 20px;">
            {{ content }}
        </abc-text>
    </base-card>
</template>

<script>
    import { defineComponent } from 'vue';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import { getMarkdownText } from '@/common/utils/index';
    import BaseCard from '@/common/components/base-card.vue';
    import deepseekService from '../../services/deepseek-service';
    import { ANALYSIS_EXTEND_FLAG } from '../../utils/constant';
    

    export default defineComponent({
        name: 'Dialectical',

        components: {
            BaseCard,
        },

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },

            loaded: {
                type: Boolean,
                default: false,
            },
        },

        computed: {
            content() {
                return getMarkdownText(this.data?.content);
            },
        },

        methods: {
            handleClick() {
                this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                    type: 'medicalRecord',
                    value: {
                        syndrome: this.content,
                    },
                });
                
                deepseekService.analysisDeepseekResult(ANALYSIS_EXTEND_FLAG.DIALECTICAL);
            },
        },
    });
</script>
