<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
    <abc-flex
        class="external-prescription-comp-wrapper"
        vertical
    >
        <div v-if="formatData.type" class="external-prescription-comp__title">
            {{ handleTitle(formatData.type) }}
        </div>

        <template v-if="isArrayComplete(formatData.categories)">
            <abc-flex
                v-for="(category, index) in formatData.categories"
                :key="index"
                class="external-prescription-content-wrapper"
                vertical
            >
                <div
                    v-if="category.name"
                    style="margin-bottom: 6px;"
                    :class="['external-prescription-content__name', {
                        'is-not-first': index !== 0
                    }]"
                >
                    {{ category.name }}
                </div>

                <template v-if="isArrayComplete(category.operations)">
                    <abc-flex
                        v-for="(operation, subKey) in category.operations"
                        :key="subKey"
                        class="external-prescription-content__info"
                        vertical
                    >
                        <abc-text tag="div" bold style="font-size: 13px;">
                            {{ operation.operation ? `${operation.operation}：` : '' }}{{ handlePlace(operation.place) }}
                        </abc-text>

                        <span
                            v-if="operation.time || operation.operation_detail"
                            style="margin-top: 2px; font-size: 13px; color: var(--abc-color-T2);"
                        >
                            {{ operation.time || '' }}{{ operation.operation_detail ? `，${operation.operation_detail}` : '' }}
                        </span>
                    </abc-flex>
                </template>
            </abc-flex>
        </template>

        <div v-if="handleUsage(formatData, true)" style=" padding-left: 12px; margin-top: 8px;">
            <span style="font-weight: 600;">用法：</span>{{ handleUsage(formatData, true) }}
        </div>

        <abc-flex
            v-if="loaded"
            style="padding-top: 8px;"
            align="center"
            justify="flex-end"
        >
            <accept-button @click="handleClick(formatData)"></accept-button>
        </abc-flex>
    </abc-flex>
</template>

<script>
    import { defineComponent } from 'vue';
    import {
        ExternalPRUsageTypeEnum,
    } from '../../utils/constant';
    import AcceptButton from '@/common/components/accept-button.vue';
    import deepseekService from '../../services/deepseek-service';
    import { ANALYSIS_EXTEND_FLAG } from '../../utils/constant';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import { useExternalPrescriptionStore } from '../../hooks/use-external-prescription';

    export default defineComponent({
        name: 'ExternalMassagePrescription',

        components: {
            AcceptButton,
        },

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },

            loaded: {
                type: Boolean,
                default: false,
            },
        },

        setup() {
            const externalPrescriptionStore = useExternalPrescriptionStore();
            const {
                handleTitle, handlePlace, handleUsage, handleCount, isArrayComplete, handleDurationTime,
            } = externalPrescriptionStore;
            return {
                handleTitle,
                handlePlace,
                handleUsage,
                handleCount,
                isArrayComplete,
                handleDurationTime,
            };
        },

        computed: {
            formatData() {
                return this.data || {};
            },
        },

        methods: {
            handleCustomPlace(categories) {
                if (!categories || !Array.isArray(categories)) return [];

                return categories.reduce((res, cur) => {
                    const _c = (cur.operations || []).map((o) => {
                        return o.place || [];
                    }).flat();

                    res = [...res, ..._c];

                    return res;
                }, []);
            },

            handleClick(o) {
                const {
                    // eslint-disable-next-line camelcase
                    type, frequency, duration_per_time, categories, total_count, total_times,
                } = o;

                const payload = {
                    usageType: ExternalPRUsageTypeEnum[type],
                    freq: frequency,
                    // eslint-disable-next-line camelcase
                    specialRequirement: duration_per_time ? `${this.handleDurationTime(duration_per_time)}` : '',
                    // eslint-disable-next-line camelcase
                    dosage: this.handleCount(total_count || total_times),
                    acupoints: this.handleCustomPlace(categories),
                    externalGoodsItems: [],
                };


                this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                    type: 'prescriptionExternalForms',
                    value: payload,
                });

                deepseekService.analysisDeepseekResult(ANALYSIS_EXTEND_FLAG.EXTERNAL_PRESCRIPTION);
            },
        },
    });
</script>

<style lang="scss" scoped>
.external-prescription-comp-wrapper {
    font-size: 13px;
    line-height: 20px;

    .external-prescription-comp__title {
        padding: 4px 0 8px 12px;
        font-weight: 500;
        color: var(--abc-color-B8);
    }

    .external-prescription-content__name {
        position: relative;
        padding-left: 12px;

        &.is-not-first {
            margin-top: 8px;
        }

        &::before {
            position: absolute;
            top: 1px;
            left: 0;
            font-size: 18px;
            line-height: 1;
            content: "•";
        }
    }

    .external-prescription-content__info {
        padding-left: 12px;
    }

    .external-prescription-content__info + .external-prescription-content__info {
        margin-top: 8px;
    }
}
</style>

