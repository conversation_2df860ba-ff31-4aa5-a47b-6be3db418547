<template>
    <base-card :loaded="loaded" @click="handleClick">
        <div 
            class="deepseek-markdown-renderer card-stream-renderer"
            v-html="content"
        ></div>
    </base-card>
</template>

<script>
    import { defineComponent } from 'vue';
    import { marked } from 'marked';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import { convertOrderedToUnorderedList } from '@/common/utils/index';
    import { markdownToText } from '@/common/utils/markdown-to-text';
    import BaseCard from '@/common/components/base-card.vue';

    export default defineComponent({
        name: 'Suggestion',

        components: {
            BaseCard,
        },

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },

            loaded: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                content: '',
            };
        },

        watch: {
            data: {
                handler(val) {
                    const str = val?.content || '';

                    if (!str) {
                        this.content = '';
                        return;
                    }
                    
                    try {
                        this.content = marked(convertOrderedToUnorderedList(str));
                    } catch (e) {
                        this.content = str;
                    }
                },
                immediate: true,
                deep: true,
            },
        },

        methods: {
            startsWithNumber(line) {
                const ref = /^\d+\./;
                return ref.test(line);
            },

            handleClick() {
                const text = markdownToText(this.data.content || '').trim();
                const value = text.split('\n').map((line, index) => {
                    // 如果已经有编号，就保留
                    if (this.startsWithNumber(line)) {
                        return line;
                    }

                    return `${index + 1}. ${line}`;
                }).join('<br>');

                this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                    type: 'doctorAdvice',
                    value,
                });
            },
        },
    });
</script>

<style lang="scss">
.card-stream-renderer {
    width: 100%;
    margin-top: 0 !important;
}

.card-stream-renderer > *:first-child {
    margin-top: 0 !important;
}

.card-stream-renderer > *:last-child {
    margin-bottom: 0 !important;
}
</style>
