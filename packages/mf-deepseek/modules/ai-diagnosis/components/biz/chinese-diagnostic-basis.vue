<template>
    <div
        class="deepseek-markdown-renderer chinese-diagnostic-basis-stream-renderer"
        v-html="content"
    ></div>
</template>

<script>
    import { defineComponent } from 'vue';
    import { marked } from 'marked';
    

    export default defineComponent({
        name: 'ChineseDiagnosticBasis',

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            content() {
                return this.render(this.data?.content || '');
            },
        },
        
        methods: {
            render(text) {
                try {
                    return marked.parse(text);
                } catch (e) {
                    console.error('Error processing content:', e);
                    return text;
                }
            },
        },
    });
</script>

<style lang="scss">
.chinese-diagnostic-basis-stream-renderer {
    li {
        font-size: 13px;
        line-height: 18px !important;

        ul {
            padding-left: 0;
            margin: 0;

            li::marker {
                display: none;
                content: "";
            }
        }
    }

    li + li {
        margin-top: 2px;
    }
}

.chinese-diagnostic-basis-stream-renderer > *:first-child {
    margin-top: 0 !important;
}

.chinese-diagnostic-basis-stream-renderer > *:last-child {
    margin-bottom: 0 !important;
}
</style>
