<template>
    <div
        class="deepseek-markdown-renderer medical-record-review-stream-renderer"
        v-html="content"
    ></div>
</template>

<script>
    import { defineComponent } from 'vue';
    import { marked } from 'marked';
    

    export default defineComponent({
        name: 'MedicalRecordReview',

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            content() {
                return this.render(this.data?.content || '');
            },
        },
        
        methods: {
            render(text) {
                try {
                    return marked.parse(text);
                } catch (e) {
                    console.error('Error processing content:', e);
                    return text;
                }
            },
        },
    });
</script>

<style lang="scss">
.medical-record-review-stream-renderer {
    p {
        padding-left: 12px;
        line-height: 20px;
    }

    li {
        line-height: 20px;
    }
}

.medical-record-review-stream-renderer > *:first-child {
    margin-top: 0 !important;
}

.medical-record-review-stream-renderer > *:last-child {
    margin-bottom: 0 !important;
}
</style>
