<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
    <abc-flex
        class="external-prescription-comp-wrapper"
        vertical
    >
        <div v-if="formatData.type" class="external-prescription-comp__title">
            {{ handleTitle(formatData.type) }}
        </div>

        <template v-if="isArrayComplete(formatData.categories)">
            <abc-flex
                v-for="(category, index) in formatData.categories"
                :key="index"
                class="external-prescription-content-wrapper"
                vertical
            >
                <abc-text tag="div" bold style="font-size: 13px;">
                    {{ handleAcupointName(formatData.type, category) }}：{{ handlePlace(category.place) }}
                </abc-text>

                <span
                    v-if="category.purpose"
                    style="margin-top: 2px; font-size: 13px; color: var(--abc-color-T2);"
                >
                    {{ category.purpose }}
                </span>
            </abc-flex>
        </template>

        <div v-if="handleUsage(formatData, true)" style="margin-top: 8px;">
            <span style="font-weight: 600;">用法：</span>{{ handleUsage(formatData, true) }}
        </div>

        <div v-if="formatData.operation" style="margin-top: 4px;">
            <span style="font-weight: 600;">操作：</span>{{ formatData.operation }}
        </div>

        <abc-flex
            v-if="loaded"
            style="padding-top: 8px;"
            align="center"
            justify="flex-end"
        >
            <accept-button @click="handleClick(formatData)"></accept-button>
        </abc-flex>
    </abc-flex>
</template>

<script>
    import { defineComponent } from 'vue';
    import {
        ExternalPRUsageTypeEnum,
    } from '../../utils/constant';
    import AcceptButton from '@/common/components/accept-button.vue';
    import deepseekService from '../../services/deepseek-service';
    import { ANALYSIS_EXTEND_FLAG } from '../../utils/constant';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import { useExternalPrescriptionStore } from '../../hooks/use-external-prescription';

    export default defineComponent({
        name: 'ExternalGeneralPrescription',

        components: {
            AcceptButton,
        },

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },

            loaded: {
                type: Boolean,
                default: false,
            },
        },

        setup() {
            const externalPrescriptionStore = useExternalPrescriptionStore();
            const {
                handleTitle, handlePlace, handleUsage, handleHerbs, handleCount, isArrayComplete, handleAcupointName, handleDurationTime,
            } = externalPrescriptionStore;
            return {
                handleTitle,
                handlePlace,
                handleUsage,
                handleHerbs,
                handleCount,
                isArrayComplete,
                handleAcupointName,
                handleDurationTime,
            };
        },

        computed: {
            formatData() {
                return this.data || {};
            },
        },

        methods: {
            handleClick(o) {
                const {
                    // eslint-disable-next-line camelcase
                    type, frequency, duration_per_time, categories, total_count, total_times,
                } = o;

                const payload = {
                    usageType: ExternalPRUsageTypeEnum[type],
                    freq: frequency,
                    // eslint-disable-next-line camelcase
                    specialRequirement: duration_per_time ? `${this.handleDurationTime(duration_per_time)}` : '',
                    // eslint-disable-next-line camelcase
                    dosage: this.handleCount(total_count || total_times),
                    acupoints: (categories || []).map((c) => c.place || []).flat(),
                    externalGoodsItems: [],
                };

                this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                    type: 'prescriptionExternalForms',
                    value: payload,
                });

                deepseekService.analysisDeepseekResult(ANALYSIS_EXTEND_FLAG.EXTERNAL_PRESCRIPTION);
            },
        },
    });
</script>

<style lang="scss" scoped>
.external-prescription-comp-wrapper {
    padding-left: 12px;
    font-size: 13px;
    line-height: 20px;

    .external-prescription-comp__title {
        padding: 4px 0 8px 0;
        font-weight: 500;
        color: var(--abc-color-B8);
    }

    .external-prescription-content-wrapper + .external-prescription-content-wrapper {
        margin-top: 8px;
    }
}
</style>

