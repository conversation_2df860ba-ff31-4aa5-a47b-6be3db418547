<template>
    <div class="deepseek-thinking-wrapper">
        <abc-flex class="thinking-header" align="center">
            <abc-space :size="6">
                <abc-button
                    variant="fill"
                    theme="default"
                    icon="s-ai-thinking-line"
                    icon-color="var(--abc-color-T2)"
                    size="small"
                    @click="handleExpandClick"
                >
                    <template v-if="loading">
                        深度思考中...
                    </template>
                    <template v-else>
                        已深度思考
                    </template>
                    <abc-icon :icon="isExpand ? 's-b-upline-medium' : 's-b-downline-medium'" size="14"></abc-icon>
                </abc-button>
            </abc-space>
        </abc-flex>

        <div
            class="thinking-content with-transition"
            :class="{
                'is-expand': isExpand,
                'is-completed': true
            }"
        >
            <pre-render v-if="preRenderVisible" :metadata="metadata"></pre-render>

            <div v-if="thinkingRenderVisible" style="margin-top: 16px;" v-html="renderedContent"></div>
        </div>
    </div>
</template>

<script>
    import { marked } from 'marked';
    import {
        defineComponent,
    } from 'vue';
    import PreRender from './pre-render.vue';
    import { useDeepseekStore } from '../../hooks/use-deepseek';
    import { MATADATA_RENDER_STATUS } from '../../utils/constant';
    import { storeToRefs } from 'MfBase/pinia';

    export default defineComponent({
        name: 'ThinkingRenderer',

        components: {
            PreRender,
        },

        props: {
            content: {
                type: String,
                default: '',
            },
            loading: {
                type: Boolean,
                default: false,
            },
            metadata: {
                type: Object,
                default: () => ({}),
            },
        },

        setup() {
            const store = useDeepseekStore();

            const {
                isCache,
                metadataRenderStatus,
                deepseekResult,
            } = storeToRefs(store);

            return {
                isCache,
                metadataRenderStatus,
                deepseekResult,
            };
        },

        data() {
            return {
                isExpand: false,
            };
        },

        computed: {
            preRenderVisible() {
                return (this.metadataRenderStatus === MATADATA_RENDER_STATUS.IN_PROGRESS ||
                    this.metadataRenderStatus === MATADATA_RENDER_STATUS.COMPLETED) &&
                    !this.deepseekResult.isPreparing;
            },

            thinkingRenderVisible() {
                return this.metadataRenderStatus === MATADATA_RENDER_STATUS.COMPLETED;
            },

            renderedContent() {
                const content = this.content.replace(/\s*json\s*/gi, '约定');

                try {
                    return marked(content);
                } catch (e) {
                    console.error('Markdown parsing error:', e);
                    return content;
                }
            },
        },

        watch: {
            content() {
                this.checkExpand();
            },

            loading() {
                this.checkExpand();
            },

            preRenderVisible(val) {
                if (val && !this.isCache) {
                    this.$nextTick(() => {
                        this.isExpand = true;
                    });
                }
            },
        },

        methods: {
            checkExpand() {
                if (this.loading === false && this.content) {
                    this.isExpand = false;
                }
            },
            handleExpandClick() {
                this.isExpand = !this.isExpand;
            },
        },
    });
</script>

<style lang="scss">
    .deepseek-thinking-wrapper {
        color: var(--abc-color-T2);

        .thinking-header {
            min-height: 26px;

            .thinking-title {
                line-height: 16px;
            }
        }

        .thinking-content {
            max-height: 0;
            padding: 0 var(--abc-paddingLR-l, 12px);
            overflow: hidden;
            font-size: 13px;
            border-left: 2px solid var(--abc-color-P1, #e0e2eb);
            opacity: 0;

            p {
                position: relative;
                margin: var(--abc-space-xl, 16px) 0;
                font-size: 13px;
                line-height: 20px;

                &:first-child {
                    margin-top: 0;
                }

                &:last-child {
                    margin-bottom: 0;

                    &::after {
                        position: absolute;
                        width: 200px;
                        height: 1.5em;
                        margin-left: -200px;
                        content: '';
                        background: linear-gradient(90deg, transparent, var(--abc-color-cp-grey2));
                    }
                }
            }

            &.is-completed {
                p {
                    &:last-child {
                        &::after {
                            display: none;
                        }
                    }
                }
            }

            &.with-transition {
                transition: all 0.25s ease-in-out;
            }

            &.is-expand {
                max-height: 10000px;
                padding: 2px var(--abc-paddingLR-l, 12px);
                margin-top: var(--abc-space-ml);
                opacity: 1;
            }
        }
    }
</style>
