<template>
    <div>
        <template v-for="(block, index) in blocks">
            <!-- 自定义协议内容 -->
            <template v-if="block.type === NODE_TYPE.CUSTOM">
                <component
                    :is="getComponent(block.componentName)"
                    :key="index"
                    :data="block.data"
                    :loaded="block.loaded"
                ></component>
            </template>

            <!-- Markdown 内容 -->
            <div
                v-else
                :key="index"
                class="deepseek-markdown-renderer"
                v-html="block.content"
            ></div>
        </template>
        
        <v3-loading v-if="v3LoadingVisible"></v3-loading>
    </div>
</template>

<script>
    import {
        defineComponent,
    } from 'vue';
    import {
        COMPONENT_NAME,
    } from '../../utils/constant';
    import Loading from '@/common/components/loading.vue';
    import Dialectical from '../biz/dialectical.vue';
    import Suggestion from '../biz/suggestion.vue';
    import ChinesePrescription from '../biz/chinese-prescription.vue';
    import Treatment from '../biz/treatment.vue';
    import WesternPrescription from '../biz/western-prescription.vue';
    import Diagnosis from '../biz/diagnosis.vue';
    import WesternDiagnosticBasis from '../biz/western-diagnostic-basis.vue';
    import ExternalTransdermalPrescription from '../biz/external-transdermal-prescription.vue';
    import ExternalGeneralPrescription from '../biz/external-general-prescription.vue';
    import ExternalMassagePrescription from '../biz/external-massage-prescription.vue';
    import V3Loading from '../biz/v3-loading.vue';
    import ChineseDiagnosticBasis from '../biz/chinese-diagnostic-basis.vue';
    import MedicalRecordReview from '../biz/medical-record-review.vue';
    import ChineseAddSubSuggestion from '../biz/chinese-add-sub-suggestion.vue';
    import {
        StreamParser, rules,
    } from '@/core/index';
    import {
        NODE_TYPE,
    } from '@/common/utils/constant';
    import {
        render, reset,
    } from '../../utils/markdown-renderer';
    import { useDeepseekStore } from '../../hooks/use-deepseek';
    import { storeToRefs } from 'MfBase/pinia';
    import Logger from '@/common/utils/logger';

    export default defineComponent({
        name: 'MarkdownRenderer',
        
        components: {
            V3Loading,
        },
        
        props: {
            content: {
                type: String,
                required: true,
            },
            loading: {
                type: Boolean,
                default: false,
            },
        },
        
        setup() {
            const deepseekStore = useDeepseekStore();
            const { deepseekResult } = storeToRefs(deepseekStore);
            const { getDeepseekParams } = deepseekStore;
            return {
                deepseekResult,
                getDeepseekParams,
            };
        },

        data() {
            return {
                blocks: [],
                pendingContent: '',

                NODE_TYPE,
                COMPONENT_NAME,

                parser: null,
                jsonType: [
                    COMPONENT_NAME.CHINESE_PRESCRIPTION, 
                    COMPONENT_NAME.WESTERN_PRESCRIPTION, 
                    COMPONENT_NAME.EXTERNAL_ACUPUNCTURE_PR,
                    COMPONENT_NAME.EXTERNAL_MASSAGE_PR,
                    COMPONENT_NAME.EXTERNAL_TRANSDERMAL_PR,
                    COMPONENT_NAME.EXTERNAL_CUPPING_PR,
                    COMPONENT_NAME.EXTERNAL_MOXIBUSTION_PR,
                    COMPONENT_NAME.EXTERNAL_GUASHA_PR,
                ],
            };
        },
        
        computed: {
            v3LoadingVisible() {
                return this.deepseekResult.v3Loading;
            },            
        },
        watch: {
            content: {
                immediate: true,
                handler(newContent) {
                    this.processContent(newContent);
                },
            },

            loading: {
                handler(val) {
                    if (!val) {
                        this.blocks.forEach((block) => {
                            block.loaded = true;
                        });
                    }
                },
                immediate: true,
            },
        },

        methods: {
            getComponent(name) {
                switch (name) {
                    // 证型 (已废弃)
                    case COMPONENT_NAME.DIALECTICAL:
                        return Dialectical;
                    case COMPONENT_NAME.CHINESE_PRESCRIPTION:
                        return ChinesePrescription;
                    case COMPONENT_NAME.SUGGESTION:
                        return Suggestion;
                    case COMPONENT_NAME.TREATMENT:
                        return Treatment;
                    case COMPONENT_NAME.WESTERN_PRESCRIPTION:
                        return WesternPrescription;
                    case COMPONENT_NAME.DIAGNOSIS:
                        return Diagnosis;
                    case COMPONENT_NAME.WESTERN_DIAGNOSTIC_BASIS:
                        return WesternDiagnosticBasis;
                    case COMPONENT_NAME.EXTERNAL_TRANSDERMAL_PR:
                        return ExternalTransdermalPrescription;
                    case COMPONENT_NAME.EXTERNAL_MASSAGE_PR:
                        return ExternalMassagePrescription;
                    case COMPONENT_NAME.CHINESE_DIAGNOSTIC_BASIS:
                        return ChineseDiagnosticBasis;
                    case COMPONENT_NAME.EXTERNAL_MOXIBUSTION_PR:
                    case COMPONENT_NAME.EXTERNAL_GUASHA_PR:
                    case COMPONENT_NAME.EXTERNAL_CUPPING_PR:
                    case COMPONENT_NAME.EXTERNAL_ACUPUNCTURE_PR:
                        return ExternalGeneralPrescription;
                    case COMPONENT_NAME.MEDICAL_RECORD_REVIEW:
                        return MedicalRecordReview;
                    case COMPONENT_NAME.CHINESE_ADD_SUB_SUGGESTION:
                        return ChineseAddSubSuggestion;
                    default:
                        return Loading;
                }
            },

            createParser() {
                const {
                    createJsonRule, createTextRule,
                } = rules;

                this.parser = new StreamParser({
                    rules: [
                        // json - 中医、西医处方、外治处方
                        createJsonRule({
                            callback: (data) => {
                                const {
                                    complete, data: jsonData, error, content,
                                } = data;
                                
                                if (error) {
                                    this.blocks.push({
                                        type: NODE_TYPE.CUSTOM,
                                        componentName: COMPONENT_NAME.UNKNOWN,
                                        data: jsonData,
                                        loaded: complete,
                                    });
                                    
                                    const {
                                        medicalRecord, patient, 
                                    } = this.getDeepseekParams() || {};
                                    
                                    Logger.report({
                                        scene: 'deepseek_report_scene',
                                        data: {
                                            info: 'json string parse error',
                                            content,
                                            medicalRecordId: medicalRecord?.id,
                                            outpatientSheetId: medicalRecord?.outpatientSheetId,
                                            patientOrderId: medicalRecord?.patientOrderId,
                                            patientId: patient?.id,
                                            patientName: patient?.name,
                                        },
                                    });
                                    
                                    return;
                                }

                                if (!jsonData || !jsonData.type || !this.jsonType.includes(jsonData.type)) {
                                    return;
                                }

                                this.blocks.push({
                                    type: NODE_TYPE.CUSTOM,
                                    componentName: jsonData.type,
                                    data: jsonData,
                                    loaded: complete,
                                });
                            },
                        }),
                        // 治法
                        createTextRule({
                            startMarker: /### 治法/,
                            endMarker: '###',
                            callback: (data) => {
                                const {
                                    complete, content,
                                } = data;

                                this.blocks.push({
                                    type: NODE_TYPE.CUSTOM,
                                    componentName: COMPONENT_NAME.TREATMENT,
                                    data: {
                                        content,
                                    },
                                    loaded: complete,
                                });
                            },
                        }),
                        // 建议
                        createTextRule({
                            startMarker: /### 调护建议|### 注意事项/,
                            endMarker: '###',
                            callback: (data) => {
                                const {
                                    complete, content,
                                } = data;

                                this.blocks.push({
                                    type: NODE_TYPE.CUSTOM,
                                    componentName: COMPONENT_NAME.SUGGESTION,
                                    data: {
                                        content,
                                    },
                                    loaded: complete,
                                });
                            },
                        }),
                        // 初步诊断（中医、西医）
                        createTextRule({
                            startMarker: /### 初步诊断/,
                            endMarker: '###',
                            callback: (data) => {
                                const {
                                    complete, content,
                                } = data;

                                this.blocks.push({
                                    type: NODE_TYPE.CUSTOM,
                                    componentName: COMPONENT_NAME.DIAGNOSIS,
                                    data: {
                                        content,
                                    },
                                    loaded: complete,
                                });
                            },
                        }),
                        // 西医辨证依据
                        createTextRule({
                            startMarker: /### 诊断依据/,
                            endMarker: '###',
                            callback: (data) => {
                                const {
                                    complete, content,
                                } = data;

                                this.blocks.push({
                                    type: NODE_TYPE.CUSTOM,
                                    componentName: COMPONENT_NAME.WESTERN_DIAGNOSTIC_BASIS,
                                    data: {
                                        content,
                                    },
                                    loaded: complete,
                                });
                            },
                        }),
                        // 中医辩证要点
                        createTextRule({
                            startMarker: /### 辨证要点/,
                            endMarker: '###',
                            callback: (data) => {
                                const {
                                    complete, content,
                                } = data;

                                this.blocks.push({
                                    type: NODE_TYPE.CUSTOM,
                                    componentName: COMPONENT_NAME.CHINESE_DIAGNOSTIC_BASIS,
                                    data: {
                                        content,
                                    },
                                    loaded: complete,
                                });
                            },
                        }),
                        // 病史回顾
                        createTextRule({
                            startMarker: /### 病史回顾/,
                            endMarker: '###',
                            callback: (data) => {
                                const {
                                    complete, content,
                                } = data;

                                this.blocks.push({
                                    type: NODE_TYPE.CUSTOM,
                                    componentName: COMPONENT_NAME.MEDICAL_RECORD_REVIEW,
                                    data: {
                                        content,
                                    },
                                    loaded: complete,
                                });
                            },
                        }),
                        // 病史回顾
                        createTextRule({
                            // 加减建议后接中文冒号或者英文冒号
                            startMarker: /加减建议[：:]/,
                            endMarker: '###',
                            callback: (data) => {
                                const {
                                    complete, content,
                                } = data;

                                this.blocks.push({
                                    type: NODE_TYPE.CUSTOM,
                                    componentName: COMPONENT_NAME.CHINESE_ADD_SUB_SUGGESTION,
                                    data: {
                                        content,
                                    },
                                    loaded: complete,
                                });
                            },
                        }),
                    ],
                    defaultProcessor: (data) => {
                        const { content } = data;

                        this.blocks.push({
                            type: NODE_TYPE.MARKDOWN,
                            content: render(content),
                        });
                    },
                });
            },

            processContent(newContent) {
                // 避免重复处理相同内容
                if (newContent === this.pendingContent) {
                    return;
                }

                if (!this.parser) {
                    this.createParser();
                }

                reset();
                this.blocks = [];
                this.parser.parse(newContent);
                this.pendingContent = newContent;
            },
        },
    });
</script>
