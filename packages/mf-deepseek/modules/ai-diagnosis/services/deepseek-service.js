// services/deepseek-service.js
import streamService from '@/common/service/stream-service';
import deepseekApi from '../api/deepseek-api';
import { useDeepseekDataStore } from '../hooks/use-deepseek-data';

/**
 * 创建 Deepseek 服务
 * @returns {Object} Deepseek 服务实例
 */
export const createDeepseekService = () => {
    /**
   * 获取 Deepseek 推理结果（流式）
   * @param {Object} params - 请求参数
   * @param {Function} onmessage - 数据回调
   * @param {Function} onClose - 关闭回调
   * @param {Function} onError - 错误回调
   * @returns {Object} 请求控制对象
   */
    const getDeepseekStream = (params, onmessage, onClose, onError) => {
        // return streamService.fetchStream('/api/chat', {
        return streamService.fetchStream('/api/v2/ai/business/outpatient/medical-record-diagnosis', {
            method: 'POST',
            body: JSON.stringify(params),
            onmessage(chunk) {
                // 尝试解析 JSON
                try {
                    const data = JSON.parse(chunk.substring(5));
                    onmessage(data);
                } catch (e) {
                    console.error('Parse chunk error:', e, chunk);
                }
            },
            onerror(error) {
                onError && onError(error);
            },
            onclose() {
                onClose && onClose();
            },
        });
    };

    const analysisDeepseekResult = (extendFlag) => {
        const deepseekDataStore = useDeepseekDataStore();
        const {
            resultId,
        } = deepseekDataStore;

        if (!resultId) {
            return;
        }

        return deepseekApi.analysisDeepseekResult(resultId, extendFlag);
    };

    return {
        getDeepseekStream,
        fetchIsReadTips: deepseekApi.fetchIsReadTips,
        markReadTips: deepseekApi.markReadTips,
        fetchPrompt: deepseekApi.fetchPrompt,
        checkDeepseekResult: deepseekApi.checkDeepseekResult,
        analysisDeepseekResult,
        saveDeepseekResult: deepseekApi.saveDeepseekResult,
        getDeepseekReqAndResult: deepseekApi.getDeepseekReqAndResult,
    };
};

// 导出默认实例
export default createDeepseekService();
