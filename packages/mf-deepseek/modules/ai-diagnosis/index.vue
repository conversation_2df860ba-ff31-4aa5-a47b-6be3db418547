<script>
    import MarkdownRenderer from './components/renderers/markdown-renderer.vue';
    import ThinkingRenderer from './components/renderers/thinking-renderer.vue';
    import {
        useDeepseekStore,
    } from './hooks/use-deepseek';
    import { isProd } from '@/common/utils/constant';
    import { storeToRefs } from 'MfBase/pinia';

    export default {
        name: 'DeepseekPage',

        components: {
            MarkdownRenderer,
            ThinkingRenderer,
        },

        setup() {
            const store = useDeepseekStore();
            const {
                deepseekResult,
                isCache,
                deepseekParamsChangeInfo,
            } = storeToRefs(store);

            const {
                retryDeepseek, getDeepseekPromptAndResult,
            } = store;

            return {
                content: deepseekResult,
                retryDeepseek,
                getDeepseekPromptAndResult,
                isCache,
                deepseekParamsChangeInfo,
            };
        },

        data() {
            return {
                isGodMode: false,
                isProd,
                shouldScrollToBottom: true, // Default to true to enable scrolling
                lastScrollTop: 0, // Track the last scroll position
            };
        },

        computed: {
            enableScrollToBottom() {
                if (this.isCache) {
                    return false;
                }

                return this.shouldScrollToBottom;
            },
        },

        watch: {
            'content.thinking': {
                handler() {
                    if (this.enableScrollToBottom) {
                        this.$nextTick(this.scrollToBottom);
                    }
                },
            },
            'content.content': {
                handler() {
                    if (this.enableScrollToBottom) {
                        this.$nextTick(this.scrollToBottom);
                    }
                },
            },
        },

        created() {
            try {
                const mode = localStorage.getItem('deepseekGodMode');
                this.isGodMode = mode === 'true';
            } catch (e) {
                console.error(e);
                this.isGodMode = false;
            }
        },

        mounted() {
            // Add scroll event listener to detect manual scrolling
            this.$nextTick(() => {
                const contentEl = this.$refs.contentRef;
                if (contentEl && contentEl.$el) {
                    contentEl.$el.addEventListener('scroll', this.handleScroll);
                }
            });
        },

        beforeDestroy() {
            // Clean up the event listener
            const contentEl = this.$refs.contentRef;
            if (contentEl && contentEl.$el) {
                contentEl.$el.removeEventListener('scroll', this.handleScroll);
            }
        },

        methods: {
            scrollToBottom() {
                const contentEl = this.$refs.contentRef;
                if (contentEl) {
                    contentEl.$el.scrollTop = contentEl.$el.scrollHeight;
                }
            },

            handleScroll(event) {
                const {
                    scrollTop, scrollHeight, clientHeight,
                } = event.target;

                // Calculate how close to the bottom we are (in pixels)
                const scrollBottomPosition = scrollHeight - (scrollTop + clientHeight);

                // If scrolling up (current position is less than last position)
                if (scrollTop < this.lastScrollTop) {
                    this.shouldScrollToBottom = false;
                }

                // If user has scrolled to the bottom (or very close to it, within 5px tolerance)
                // Re-enable automatic scrolling
                if (scrollBottomPosition <= 5) {
                    this.shouldScrollToBottom = true;
                }

                // Update the last scroll position
                this.lastScrollTop = scrollTop;
            },

            handleClick() {
                this.getDeepseekPromptAndResult();
            },
        },
    };
</script>

<template>
    <abc-flex vertical class="deepseek-page">
        <abc-flex class="deepseek-header" align="center" justify="center">
            <abc-button
                class="back-btn"
                variant="text"
                theme="default"
                icon="s-b-left-line-medium"
                @click="$emit('back')"
            >
                返回
            </abc-button>
            <div class="deepseek-title"></div>
            <abc-button
                v-if="content.error"
                class="retry-btn"
                variant="text"
                theme="primary"
                icon="s-b-refresh-line-medium"
                @click="retryDeepseek"
            ></abc-button>

            <abc-button
                v-if="!isProd && isGodMode"
                class="retry-btn"
                variant="text"
                theme="primary"
                icon="s-paperinvoice-color"
                @click="handleClick"
            ></abc-button>
        </abc-flex>

        <abc-tips-card-v2
            v-if="deepseekParamsChangeInfo.changed"
            theme="primary"
            :border-radius="false"
            custom-class="deepseek-params-change-info"
        >
            <abc-tooltip
                placement="top-start"
                :content="deepseekParamsChangeInfo.info"
                :disabled="!deepseekParamsChangeInfo.isExtraLongInfo"
            >
                <abc-flex flex="1" style="overflow: hidden;">
                    <span class="ellipsis" style="max-width: calc(100% - 70px);">{{ deepseekParamsChangeInfo.info }}</span>

                    <span>有编辑更新</span>
                </abc-flex>
            </abc-tooltip>

            <abc-button
                variant="text"
                size="small"
                theme="primary"
                style=" flex-shrink: 0; margin: 0 0 0 12px;"
                @click="$emit('back')"
            >
                重新思考
            </abc-button>
        </abc-tips-card-v2>

        <abc-flex ref="contentRef" vertical class="deepseek-content">
            <thinking-renderer
                v-if="!content.error"
                :content="content.thinking"
                :loading="content.loading"
                :metadata="content.metadata"
            ></thinking-renderer>

            <markdown-renderer v-if="content.content && !content.error" :content="content.content" :loading="content.loading"></markdown-renderer>

            <template v-if="content.error">
                <abc-flex
                    class="deepseek-error"
                    align="center"
                    justify="center"
                    :gap="6"
                >
                    <abc-icon size="14" icon="s-alert-fill"></abc-icon>
                    <div class="error-text">
                        服务器繁忙，请稍后再试
                    </div>
                </abc-flex>
            </template>
        </abc-flex>
    </abc-flex>
</template>

<style lang="scss">
    @import '../../common/style/mixin.scss';
    @import '../../common/style/render.css';

    .deepseek-page {
        height: 100%;

        .deepseek-header {
            position: relative;
            height: 48px;
            padding: 12px 16px;
            border-bottom: 1px solid #e4e7ed;

            .back-btn {
                position: absolute;
                left: 8px;
            }

            .retry-btn {
                position: absolute;
                right: 16px;
            }

            .deepseek-title {
                width: 158px;
                height: 14px;
                background-image: url('./assets/image/logo-deepseek-thinking.svg');
                background-repeat: no-repeat;
            }
        }

        .deepseek-content {
            flex: 1;
            // 右 padding - 10px，预留滚动条宽度
            padding: var(--abc-paddingTB-ml) 6px 120px var(--abc-paddingLR-xl);
            overflow-y: scroll;
            background: var(--abc-color-cp-grey2);

            @include scrollBar(false, 10px);

            .deepseek-error {
                height: 32px;
                padding: var(--abc-border-radius-small, 6px) 0;
                margin-top: var(--abc-space-xl);
                color: var(--abc-color-T2);
                background: rgba(0, 0, 0, 0.04);
                border-radius: var(--abc-border-radius-small, 6px);
            }
        }

        .deepseek-params-change-info {
            padding: 0 16px;
            border-top: 0;
            border-right: 0;
            border-left: 0;

            .abc-tips-card-v2__description {
                align-items: center;
                width: 100%;
                height: 40px;
                overflow: hidden;

                .abc-icon {
                    margin-top: 0;
                }
            }
        }
    }

    @media screen and (max-width: 1439px) {
        .deepseek-page {
            .deepseek-header {
                .deepseek-title {
                    width: 97px;
                    background-image: url('./assets/image/logo-deepseek-thinking-small.svg');
                }
            }
        }
    }
</style>
