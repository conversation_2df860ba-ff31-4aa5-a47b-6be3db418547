'use strict'
const path = require('path')
const config = require('../config')
const MiniCssExtractPlugin = require("mini-css-extract-plugin")
const packageConfig = require('../package.json')
const Tool = require("abc-fed-build-tool");
const HtmlWebpackPlugin = require("html-webpack-plugin");

exports.assetsPath = function (_path) {
    const assetsSubDirectory = (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test')
        ? config.build.assetsSubDirectory
        : config.devonline.assetsSubDirectory

    return path.posix.join(assetsSubDirectory, _path)
}

exports.cssLoaders = function (options) {
    options = options || {}

    const cssLoader = {
        loader: 'css-loader',
        options: {
            sourceMap: options.sourceMap
        }
    }

    const postcssLoader = {
        loader: 'postcss-loader',
        options: {
            sourceMap: options.sourceMap
        }
    }


    // generate loader string to be used with extract text plugin
    function generateLoaders(loader, loaderOptions) {
        const loaders = options.usePostCSS ? [cssLoader, postcssLoader] : [cssLoader]
        // 如果 sass-loader 版本 = 8，这里使用 `prependData` 字段
        // 如果 sass-loader 版本 < 8，这里使用 `data` 字段
        const data = options.data ? options.data : ''
        const prependData = options.prependData ? options.prependData : ''
        const additionalData = options.additionalData ? options.additionalData : ''
        if (loader) {
            /**
             * @desc 注入全局变量
             * <AUTHOR>
             * @date 2022/5/13 11:16
             */
            if (additionalData || prependData || data) {
                if (!(typeof loaderOptions === 'object' && loaderOptions !== null)) {
                    loaderOptions = {}
                }
                if (data) loaderOptions.data = data
                if (prependData) loaderOptions.prependData = prependData
                if (additionalData) loaderOptions.additionalData = additionalData
            }
            loaders.push({
                loader: loader + '-loader',
                options: Object.assign({}, loaderOptions, {
                    sourceMap: options.sourceMap,
                })
            })
        }

        if (options.extract) {
            return [{ loader: MiniCssExtractPlugin.loader }].concat(loaders);
        } else {
            return [{
                loader: 'vue-style-loader'
            }].concat(loaders)
        }
    }

    // https://vue-loader.vuejs.org/en/configurations/extract-css.html
    return {
        css: generateLoaders(),
        postcss: generateLoaders(),
        // less: generateLoaders('less'),
        sass: generateLoaders('sass', { indentedSyntax: true }),
        scss: generateLoaders('sass'),
        // stylus: generateLoaders('stylus'),
        // styl: generateLoaders('stylus')
    }
}

// Generate loaders for standalone style files (outside of .vue)
exports.styleLoaders = function (options) {
    const output = []
    const loaders = exports.cssLoaders(options)

    for (const extension in loaders) {
        const loader = loaders[extension]
        output.push({
            test: new RegExp('\\.' + extension + '$'),
            use: loader
        })
    }

    return output
}

exports.createNotifierCallback = () => {
    const notifier = require('node-notifier')

    return (severity, errors) => {
        if (severity !== 'error') return

        const error = errors[0]
        const filename = error.file && error.file.split('!').pop()

        notifier.notify({
            title: packageConfig.name,
            message: severity + ': ' + error.name,
            subtitle: filename || '',
            icon: path.join(__dirname, 'logo.png')
        })
    }
}

exports.getIPAddress = function () {
    var interfaces = require('os').networkInterfaces();
    for (var devName in interfaces) {
        var iface = interfaces[devName];
        for (var i = 0; i < iface.length; i++) {
            var alias = iface[i];
            if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
                return alias.address;
            }
        }
    }
}

exports.redirectLogin = function (pathname, derServer) {
    const targetDomain = process.env.LOGIN_DEV ? `http://localhost:8999` : `https://global-dev.abczs.cn`;
    derServer.app.get(pathname, (req, res) => {
        const { host } = req.headers;
        const port = host.split(':')[1];
        const from = `${exports.getIPAddress()}:${port}`;
        const redirectURL = `${targetDomain}${pathname}?from=http://${from}`;

        res.writeHead(302, { Location: redirectURL });
        res.end();
    })
}

exports.AbcEngineLoaderPlugin = function (options) {
    const engineLoaderUrl = process.env.ABC_FE_ENGINE_URL ? process.env.ABC_FE_ENGINE_URL : (process.env.ABC_FE_ENGINE === 'true' || process.env.NODE_ENV === 'test')
        ? config.build.abcFeEngineLoaderUrl
        : config.devonline.abcFeEngineLoaderUrl
    return new Tool.AbcFeEngineLoadPlugin({
        onInjectHTML: options.onInjectHTML,
        remotes: {
            'abc-fe-engine': `AbcFeEngine@${engineLoaderUrl}`
        }
    });
}

exports.AbcMedicalImagingViewerSDKLoaderPlugin = function (options) {
    const medicalImagingViewerSDKLoaderUrl = process.env.ABC_MEDICAL_IMAGING_VIEWER_SDK_URL ? process.env.ABC_FE_MEDICAL_IMAGING_VIEWER_SDK_URL : (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test')
        ? config.build.abcMedicalImagingViewerSDKLoader
        : config.devonline.abcMedicalImagingViewerSDKLoader
    return new class AbcMedicalImagingViewerSDKLoaderPlugin {
        apply(compiler) {
            compiler.hooks.compilation.tap('AbcMedicalImagingViewerSDKLoaderPlugin', (compilation) => {
                const HtmlWebpackPlugin = require('html-webpack-plugin');
                const hooks = HtmlWebpackPlugin.getHooks(compilation);
                hooks.alterAssetTags.tapAsync('AbcMedicalImagingViewerSDKLoaderPlugin', (htmlPluginData, callback) => {
                    const onInjectHTML = options.onInjectHTML;
                    if (!onInjectHTML || onInjectHTML(htmlPluginData)) {
                        htmlPluginData.assetTags.scripts.push({
                            tagName: 'script',
                            closeTag: true,
                            attributes: {
                                type: "text/javascript",
                            },
                            innerHTML: `document.write("<script defer src='${medicalImagingViewerSDKLoaderUrl}?t=\"+Date.now()+\"'><\\/script>");`
                        });
                    } else {
                        console.warn(`AbcMedicalImagingViewerSDKLoaderPlugin: ${htmlPluginData.plugin.options.filename} is not need inject`);
                    }
                    if (callback) {
                        callback(null, htmlPluginData);
                    } else {
                        return Promise.resolve(htmlPluginData);
                    }
                });
            })
        }
    }
}

exports.AbcEmrEditorSDKLoaderPlugin = function (options) {
    const emrEditorSDKLoaderUrl = process.env.ABC_EMR_EDITOR_SDK_URL ? process.env.ABC_EMR_EDITOR_SDK_URL : (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test')
        ? config.build.abcEmrEditorSDKLoader
        : config.devonline.abcEmrEditorSDKLoader
    return new class AbcEmrEditorSDKLoaderPlugin {
        apply(compiler) {
            compiler.hooks.compilation.tap('AbcEmrEditorSDKLoaderPlugin', (compilation) => {
                const HtmlWebpackPlugin = require('html-webpack-plugin');
                const hooks = HtmlWebpackPlugin.getHooks(compilation);
                hooks.alterAssetTags.tapAsync('AbcEmrEditorSDKLoaderPlugin', (htmlPluginData, callback) => {
                    const onInjectHTML = options.onInjectHTML;
                    if (!onInjectHTML || onInjectHTML(htmlPluginData)) {
                        htmlPluginData.assetTags.scripts.push({
                            tagName: 'script',
                            closeTag: true,
                            attributes: {
                                type: "text/javascript",
                            },
                            innerHTML: `document.write("<script defer src='${emrEditorSDKLoaderUrl}?t=\"+Date.now()+\"'><\\/script>");`
                        });
                    } else {
                        console.warn(`AbcEmrEditorSDKLoaderPlugin: ${htmlPluginData.plugin.options.filename} is not need inject`);
                    }
                    if (callback) {
                        callback(null, htmlPluginData);
                    } else {
                        return Promise.resolve(htmlPluginData);
                    }
                });
            })
        }
    }
}

exports.AbcRumLoaderPlugin = function (options) {
    return new class AbcRumLoaderPlugin {
        apply(compiler) {
            compiler.hooks.compilation.tap('AbcRumLoaderPlugin', (compilation) => {
                const HtmlWebpackPlugin = require('html-webpack-plugin');
                const hooks = HtmlWebpackPlugin.getHooks(compilation);
                hooks.alterAssetTags.tapAsync('AbcRumLoaderPlugin', (htmlPluginData, callback) => {
                    const onInjectHTML = options.onInjectHTML;
                    const rumConfig = getRumConfig();
                    if (rumConfig && (!onInjectHTML || onInjectHTML(htmlPluginData))) {
                        htmlPluginData.assetTags.scripts.push({
                            tagName: 'script',
                            closeTag: true,
                            attributes: {
                                type: "text/javascript",
                                src: rumConfig.url,
                            },
                        });
                        htmlPluginData.assetTags.scripts.push({
                            tagName: 'script',
                            closeTag: true,
                            attributes: {
                                type: "text/javascript",
                            },
                            innerHTML: `window.top === window && window.AbcRumWebClient && window.AbcRumWebClient.AbcRumWebClientV2.init(${JSON.stringify(rumConfig.options)});`
                        })
                    } else {
                        console.warn(`AbcRumLoaderPlugin: ${htmlPluginData.plugin.options.filename} is not need inject`);
                    }
                    if (callback) {
                        callback(null, htmlPluginData);
                    } else {
                        return Promise.resolve(htmlPluginData);
                    }
                });
            })
        }
    }
}

/**
 * @desc 获取前端监控配置
 * @returns {{options: {workspace: string, enableRequest: boolean, enableResourcePerf: boolean, project: string, env: string, trackCountThreshold: number, service: string, ignoreResourceConfig: string[], host: string, enablePerf: boolean, enableTrace: boolean, logstore: string, enableRuntimeError: boolean}, url: string}}
 */


const getRumConfig = function () {
    let rumConfig;

    const buildEnv = process.env.BUILD_ENV || 'dev';

    // n is not defined - 浏览器插件报错
    const ignoreRuntimeErrorConfig = [
        '中断切换路由之前的请求',
        'testLogin不支持该操作',
        'timeout',
        '没有网络连接',
        'ResizeObserver loop limit exceeded',
        '没找到相关内容',
        'read ECONNRESET',
        'ResizeObserver loop completed with undelivered notifications.',
        'Empty reason',
        'downloadFile md5 not match',
        'n is not defined'
    ]

    if (['dev', 'test', 'pre', 'gray', 'prod'].includes(buildEnv)) {
        const envFix = ['pre', 'gray', 'prod'].includes(buildEnv) ? 'prod' : buildEnv;
        rumConfig = {
            url: '//static-common-cdn.abcyun.cn/js/abc-rum/0.0.30/web/index.0712ed23.umd.js',
            options: {
                host: 'cn-shanghai.log.aliyuncs.com',
                project: 'abc-fed-log',
                logstore: `${envFix}-metrics-raw`,
                workspace: `${envFix}-metrics`,
                env: buildEnv,
                namespace: 'PC',
                version: process.env.BUILD_TAG || 'latest',
                enableRuntimeError: true,
                enableResourcePerf: false,
                enableWhiteScreenCheck: true,
                whiteBoxElements: ['html', 'body', '#app', '.window-loading__square', '#window-loading', '.window-loading'],
                enableRequest: false,
                enablePerf: false,
                enableTrace: false,
                enableVueError: true,
                enableDomClick: false,
                enableWindowMessage: true,
                ignoreResourceConfig: [
                    'CLodopfuncs.js'
                ],
                trackCountThreshold: 10,
                ignoreRuntimeErrorConfig,
            }
        }
    }
    return rumConfig
}

exports.AbcIconfontSvgInjectPlugin = function () {
    return new class AbcIconfontSvgInjectPlugin {
        apply(compiler) {
            compiler.hooks.compilation.tap('AbcIconfontSvgInjectPlugin', (compilation) => {
                const HtmlWebpackPlugin = require('html-webpack-plugin');
                const hooks = HtmlWebpackPlugin.getHooks(compilation);
                hooks.alterAssetTags.tapAsync('AbcIconfontSvgInjectPlugin', (htmlPluginData, callback) => {
                    htmlPluginData.assetTags.scripts.push({
                        tagName: 'script',
                        closeTag: true,
                        attributes: {
                            type: "text/javascript",
                            src: 'https://static-common-cdn.abcyun.cn/iconfont/pc/font_4448419_mkeeprmb5o9/iconfont.js',
                            async: true
                        },
                    });

                    if (callback) {
                        callback(null, htmlPluginData);
                    } else {
                        return Promise.resolve(htmlPluginData);
                    }
                });
            })
        }
    }
}

exports.AbcFedConfigLoaderPlugin = function (options) {
    return new class AbcFedConfigLoaderPlugin {
        apply(compiler) {
            compiler.hooks.compilation.tap('AbcFedConfigLoaderPlugin', (compilation) => {
                const HtmlWebpackPlugin = require('html-webpack-plugin');
                const hooks = HtmlWebpackPlugin.getHooks(compilation);
                hooks.alterAssetTags.tapAsync('AbcFedConfigLoaderPlugin', (htmlPluginData, callback) => {
                    const onInjectHTML = options.onInjectHTML;
                    if (!onInjectHTML || onInjectHTML(htmlPluginData)) {
                        htmlPluginData.assetTags.scripts.unshift({
                            tagName: 'script',
                            closeTag: true,
                            attributes: {
                                type: "text/javascript",
                            },
                            innerHTML: config.getAbcFedConfigInnerHtml(),
                        })
                    } else {
                        console.warn(`AbcFedConfigLoaderPlugin: ${htmlPluginData.plugin.options.filename} is not need inject`);
                    }
                    if (callback) {
                        callback(null, htmlPluginData);
                    } else {
                        return Promise.resolve(htmlPluginData);
                    }
                });
            })
        }
    }
}

exports.isLocal = !process.env.BUILD_ENV;
exports.isOwn = process.env.BUILD_ENV === 'own';
exports.isFeature = process.env.BUILD_ENV === 'feature';
