<template>
    <abc-dialog
        v-model="showDialog"
        title="批量打印费用清单"
        append-to-body
        content-styles="width: 672px"
        class="cost-print-batch-dialog"
    >
        <abc-form
            ref="form"
            label-position="left"
            :label-width="72"
        >
            <abc-form-item label="费用时间">
                <abc-date-picker
                    v-model="selectDate"
                    :focus-show-options="false"
                    type="daterange"
                >
                </abc-date-picker>
            </abc-form-item>
        </abc-form>
        <abc-form ref="form2" item-block>
            <abc-form-item
                class="table-item"
                label="患者范围"
            >
                <abc-table
                    type="pro"
                    theme="white"
                    :render-config="renderConfig"
                    :data-list="tableList"
                    style="height: 400px;"
                >
                </abc-table>
            </abc-form-item>
        </abc-form>
        <footer slot="footer" class="dialog-footer">
            <abc-button
                :loading="btnLoading"
                @click="handleSubmit"
            >
                确定
            </abc-button>
            <abc-button
                type="blank"
                @click="showDialog = false"
            >
                取消
            </abc-button>
        </footer>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import PrintAPI from 'api/hospital/print';
    import AbcPrinter from '@/printer';
    import { getAbcPrintOptions } from '@/printer/print-handler';
    import { getViewDistributeConfig } from '@/views-distribute/utils';
    import Vue from 'vue';
    import { parseTime } from '@/utils';
    import { prevDate } from '@abc/utils-date';
    import { PrintMode } from '@/printer/constants';

    export default {
        name: 'CostPrintBatchDialog',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: Boolean,
        },
        data() {
            const lastDay = parseTime(prevDate(new Date()), 'y-m-d', true);

            return {
                selectDate: [lastDay, lastDay],
                btnLoading: false,
                renderConfig: {
                    'list': [{
                        'isCheckbox': true,
                        'label': ' ',
                        'style': {
                            'flex': 'none',
                            'width': '36px',
                            'maxWidth': '',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'left',
                        },
                    },{
                        'key': 'bedNo',
                        'label': '床号',
                        'testValue': '01',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'flex': '10',
                            'width': '',
                            'maxWidth': '56px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'center',
                        },
                    },{
                        'key': 'name',
                        'label': '姓名',
                        'testValue': '李思思',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'flex': '28',
                            'width': '',
                            'maxWidth': '140px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'left',
                        },
                    },{
                        'key': 'sex',
                        'label': '性别',
                        'testValue': '女',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'flex': '20',
                            'width': '',
                            'maxWidth': '100px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'left',
                        },
                    },{
                        'key': 'age',
                        'label': '年龄',
                        'testValue': '23',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'flex': '20',
                            'width': '',
                            'maxWidth': '100px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'left',
                        },
                    },{
                        'key': 'inpatientTime',
                        'label': '入院时间',
                        'testValue': '2023-12-09',
                        'colType': 'time',
                        'isCheckbox': false,
                        'style': {
                            'flex': '28',
                            'width': '',
                            'maxWidth': '140px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'left',
                        },
                    },{
                        'key': 'inpatientDays',
                        'label': '在院天数',
                        'testValue': '32',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'flex': '20',
                            'width': '',
                            'maxWidth': '100px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'center',
                        },
                    }],
                },
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            printOptions() {
                return getViewDistributeConfig().Print.printOptions;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            quickList() {
                return this.$abcPage.$store.quickList;
            },
            tableList() {
                return this.quickList.map((x) => {
                    const {
                        patient,
                    } = x;
                    const {
                        name,
                        sex,
                        age,
                    } = patient || {};

                    return Vue.observable({
                        ...x,
                        name,
                        sex,
                        age: this.parseAge(age),
                        checked: false,
                    });
                });
            },
        },
        methods: {
            async handleSubmit() {
                try {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            const beginDate = this.selectDate[0];
                            const endDate = this.selectDate[1];
                            const patientOrderIds = this.tableList
                                .filter((x) => x.checked)
                                .map((x) => x.id);
                            AbcPrinter.abcPrint((async () => {
                                const printPropsList = [];
                                const printData = await this.fetchPrintData({
                                    beginDate,
                                    endDate,
                                    patientOrderIds,
                                });
                                console.log(printData);
                                const printOptions = getAbcPrintOptions(this.printOptions.CHARGE_LIST_BATCH.label, printData);
                                console.log(printOptions);
                                if (printOptions) {
                                    printPropsList.push({
                                        ...printOptions,
                                        data: printOptions.data ?? {},
                                        mode: PrintMode.Electron,
                                    });
                                }
                                return printPropsList;
                            }));
                        }
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            parseAge(age) {
                const {
                    year,
                    month,
                    day,
                } = age || {};
                if (year > 0) {
                    return `${year}岁`;
                }
                if (month > 0) {
                    return `${month}月`;
                }
                if (day > 0) {
                    return `${day}天`;
                }
                return '';
            },
            async fetchPrintData(printParams) {
                const printLoading = this.$Loading({
                    text: '准备打印...',
                    customClass: 'print-loading-wrapper',
                });
                try {
                    const { data } = await PrintAPI.getFeeListBatch(printParams);
                    console.log(data);
                    return {
                        clinicName: this.currentClinic.shortName || this.currentClinic.clinicName,
                        nationalCode: this.currentClinic.nationalCode,
                        ...data,
                    };
                } catch (e) {
                    console.error('获取打印数据失败\n', e);
                } finally {
                    printLoading.close();
                }
            },
        },
    };
</script>

<style lang="scss">
    .cost-print-batch-dialog {
        .abc-form-item.table-item {
            margin-bottom: 0;

            .abc-form-item-content {
                font-size: 14px;
            }
        }
    }
</style>
