<template>
    <abc-table-tr
        class="biz-pharmacy-charge-table-tr"
        :data-cy="`goods-tr-${item.name}`"
        :order-index="orderIndex"
        :custom-order-render="customOrderRender"
        :support-delete-tr="!disabled"
        @delete-tr="handleDeleteTr"
        @click="handleClickTableTr"
    >
        <abc-table-td class="name" :style="tableTdStyle.goodsName">
            <goods-filed
                :show-cost-price="isCanSeeGoodsCostPriceInRetail"
                size="large"
                :item="item"
                :goods="item.productInfo"
            ></goods-filed>
        </abc-table-td>
        <abc-table-td :style="tableTdStyle.batchNo">
            <batches-infos
                size="large"
                :charge-item="item"
                :disabled="disabledBatchSelect"
                @click="handleSelectBatches(item)"
            ></batches-infos>
        </abc-table-td>
        <abc-table-td align="center" :style="tableTdStyle.unitCount" class="unit-count">
            <abc-space
                is-compact
                compact-block
                class="unit-count-with-dose-count"
            >
                <abc-tooltip
                    custom-popper-class="charge-table-tr-abc-tooltip"
                    :visible-arrow="false"
                    :disabled="!needInitialStockIn"
                >
                    <abc-form-item
                        ref="unitCountFormItem"
                        :key="item.unit"
                        required
                        trigger="change"
                        placement="top"
                        :validate-event="validateUnitCount"
                    >
                        <abc-input
                            v-model="totalCount"
                            v-abc-focus-selected
                            :placeholder="isChineseMedicinePieces && !isGift ? '单剂' : '数量'"
                            data-cy="unit-count-input"
                            :width="96"
                            :disabled="disabled || disabledUnitCount"
                            size="large"
                            type="number"
                            :input-custom-style="{
                                textAlign: 'center', borderTopLeftRadius: '8px', borderBottomLeftRadius: '8px', borderTopRightRadius: 0, borderBottomRightRadius: 0
                            }"
                            :config="{
                                max: 100000,
                                supportZero: false,
                                formatLength: supportDecimal ? 2 : undefined
                            }"
                            @change="handleChangeCount"
                            @enter="handleEnter"
                            @focus="handleUnitCountFocus"
                            @blur="handleUnitCountBlur"
                        >
                        </abc-input>
                    </abc-form-item>
                    <abc-flex
                        slot="content"
                        align="center"
                        justify="space-between"
                        style="width: 100%; height: 100%;"
                    >
                        <abc-text theme="white">
                            未入库
                        </abc-text>
                        <abc-button
                            size="small"
                            variant="ghost"
                            theme="warning"
                            style="border: none;"
                            @click.stop="handleOpenStockInDialog()"
                        >
                            去入库
                        </abc-button>
                    </abc-flex>
                </abc-tooltip>
                <abc-form-item>
                    <charge-item-unit-select
                        v-model="item.unit"
                        :disabled="disabled || disabledUnitCount"
                        :product-info="item.productInfo"
                        :width="40"
                        :inner-width="40"
                        :no-icon="true"
                        size="large"
                        :input-style="chargeItemUnitSelectInputStyle"
                        @change="handleUnitChange"
                        @focus="handleUnitCountFocus"
                        @blur="handleUnitCountBlur"
                    ></charge-item-unit-select>
                    <abc-text
                        v-if="isChineseMedicinePieces && unitCountFocus && !isGift"
                        size="mini"
                        theme="gray"
                        class="dose-count-bottom"
                    >
                        × {{ item.doseCount || 1 }} 剂
                    </abc-text>
                </abc-form-item>
                <!-- <div v-if="isChineseMedicinePieces && unitCountFocus && !isGift" class="dose-count">
                    ×{{ item.doseCount || 1 }}剂
                </div> -->
            </abc-space>
        </abc-table-td>
        <abc-table-td :style="tableTdStyle.unitPrice" align="left">
            <abc-text v-if="goodsIsUnPriced" theme="warning-light" size="large">
                未定价
            </abc-text>
            <span v-else style="font-size: 16px;">
                {{ item.sourceUnitPrice | formatMoney(false) }}
            </span>
        </abc-table-td>
        <abc-table-td :style="tableTdStyle.realPrice">
            <div
                v-if="curIsEdit"
                class="unit-price-wrapper"
                :class="{ 'is-edit': isEditedRealPrice }"
                @mouseenter="hoverUnitPrice = true"
                @mouseleave="hoverUnitPrice = false"
            >
                <abc-space is-compact compact-block>
                    <abc-form-item required>
                        <abc-input
                            ref="unitPriceRef"
                            v-model="item.unitPrice"
                            v-abc-focus-selected
                            v-abc-adjust-price-popper="{
                                adjustmentFormatLength: 4,
                                methods: val => adjustUnitPriceChange(item, val),
                                originTotalFee: item.singlePromotionedUnitPrice,
                            }"
                            :width="75"
                            :input-custom-style="{ textAlign: 'right' }"
                            :tabindex="-1"
                            type="money"
                            :disabled="disabledPrice"
                            :config="getUnitPriceConfig"
                            @focus="focusUnitPrice(item)"
                            @change="changeUnitPrice"
                        >
                        </abc-input>
                        <abc-loading v-if="loading && showLoading === 'unitPrice'" small no-cover></abc-loading>
                    </abc-form-item>
                    <abc-form-item>
                        <price-radio-popover
                            ref="priceRadioPopover"
                            :disabled="!item.unitCount"
                            @change-price-radio="val => changePriceRadio(item, val)"
                        >
                            <abc-input
                                ref="priceRadioInput"
                                v-abc-focus-selected
                                class="total-price-ratio"
                                :value="displayTotalPriceRatio"
                                :width="79"
                                placeholder="折扣"
                                type="money"
                                :disabled="!item.unitCount || disabledPrice"
                                :config="{
                                    supportZero: true,
                                    max: 100,
                                }"
                                @up="(event) => handlePriceRatioKeyDown(event, 'up')"
                                @down="(event) => handlePriceRatioKeyDown(event, 'down')"
                                @left="(event) => handlePriceRatioKeyDown(event, 'left')"
                                @right="(event) => handlePriceRatioKeyDown(event, 'right')"
                                @enter="(event) => handlePriceRatioKeyDown(event, 'enter')"
                                @input="val => inputPriceRadioHandler(item, val)"
                                @change="changeTotalPriceRatio(item)"
                            >
                                <span slot="appendInner">%</span>
                            </abc-input>
                        </price-radio-popover>
                    </abc-form-item>
                </abc-space>

                <div v-if="isEditedRealPrice && hoverUnitPrice" class="bargain-origin-price-popover">
                    议价前：<abc-money :value="item.singlePromotionedUnitPrice"></abc-money>
                </div>
            </div>
            <template v-else>
                <abc-flex align="center" style="width: 100%;">
                    <div class="table-real-price" @click="handleClickPrice">
                        <span style="font-size: 16px;">{{ item.unitPrice | formatMoney(false) }}</span>
                    </div>
                    <abc-popover
                        v-if="!curIsEdit && item.singlePromotions && item.singlePromotions.length"
                        ref="promotionsPopover"
                        theme="white"
                        placement="bottom"
                        trigger="click"
                        :visible-arrow="false"
                        style="flex: 1; overflow: hidden;"
                    >
                        <div slot="reference" class="tag-group">
                            <abc-tag-v2
                                ellipsis
                                variant="outline"
                                shape="round"
                                size="mini"
                                style="margin-left: 4px;"
                                :title="getPromotionStr(checkedPromotion) || ''"
                                :theme="checkedPromotion ? 'danger' : 'default'"
                            >
                                {{ checkedPromotion ? getPromotionStr(checkedPromotion) : '有可用优惠' }}
                            </abc-tag-v2>
                        </div>

                        <div class="charge-tr-promotion-popover">
                            <abc-flex justify="space-between">
                                <abc-text bold>
                                    可使用优惠
                                </abc-text>
                            </abc-flex>
                            <abc-divider margin="small"></abc-divider>
                            <abc-tips-card-v2 v-if="hasEditPrice" theme="primary" style="margin-bottom: 8px;">
                                商品将基于原价优惠，清空议价
                            </abc-tips-card-v2>
                            <template v-if="item.singlePromotions">
                                <abc-flex
                                    v-for="promotion in item.singlePromotions"
                                    :key="promotion.id"
                                    align="center"
                                    class="promotion-item"
                                    :class="{
                                        'is-checked': promotion.checked
                                    }"
                                    :title="promotion.name || ''"
                                    @click="handleClickPromotion(promotion)"
                                >
                                    <abc-tag-v2
                                        variant="outline"
                                        theme="danger"
                                        shape="round"
                                        margin="small"
                                        size="mini"
                                    >
                                        {{ getPromotionTagStr(promotion) }}
                                    </abc-tag-v2>
                                    <div class="name">
                                        {{ getPromotionStr(promotion) }}
                                    </div>
                                    <div class="price">
                                        <abc-money :value="promotion.displayDiscountPrice"></abc-money>
                                    </div>
                                    <abc-flex class="choose-icon-wrapper" align="center" justify="center">
                                        <abc-icon v-if="promotion.checked" icon="n-check-circle-fill"></abc-icon>
                                    </abc-flex>
                                </abc-flex>
                                <abc-flex
                                    align="center"
                                    class="promotion-item no-promotion"
                                    :class="{
                                        'is-checked': !checkedPromotion
                                    }"
                                    @click="handleClickPromotion()"
                                >
                                    <div class="name">
                                        不使用优惠
                                    </div>
                                    <abc-flex class="choose-icon-wrapper" align="center" justify="center">
                                        <abc-icon v-if="!checkedPromotion" icon="n-check-circle-fill"></abc-icon>
                                    </abc-flex>
                                </abc-flex>
                            </template>
                        </div>
                    </abc-popover>
                </abc-flex>
            </template>
        </abc-table-td>
        <abc-table-td :width="108" align="right" style="padding-right: 8px;">
            <div
                v-if="curIsEdit"
                class="total-price-wrapper"
                :class="{ 'is-edit': isEditedTotalPrice }"
                @mouseenter="hoverTotalPrice = true"
                @mouseleave="hoverTotalPrice = false"
            >
                <abc-form-item required>
                    <abc-input
                        v-model="item.totalPrice"
                        v-abc-focus-selected
                        v-abc-adjust-price-popper="{
                            methods: val => adjustPriceChange(item, val),
                            originTotalFee: item.singlePromotionedTotalPrice,
                        }"
                        :width="84"
                        type="money"
                        :tabindex="-1"
                        :disabled="!item.unitCount || disabledPrice"
                        :input-custom-style="{ textAlign: 'right' }"
                        :config="{
                            formatLength: 2, supportZero: true, max: 9999999
                        }"
                        @focus="focusTotalPrice(item)"
                        @change="changeTotalPrice"
                    >
                    </abc-input>
                    <abc-loading v-if="loading && showLoading === 'totalPrice'" small no-cover></abc-loading>
                </abc-form-item>

                <div
                    v-if="(isEditedTotalPrice || item.unitAdjustmentFeeLastModifiedByName) && hoverTotalPrice"
                    class="bargain-origin-price-popover"
                >
                    <abc-flex v-if="isEditedTotalPrice && !item.unitAdjustmentFeeLastModifiedByName" align="center">
                        议价前：<abc-money :value="item.singlePromotionedTotalPrice"></abc-money>
                    </abc-flex>
                    <template v-else-if="item.unitAdjustmentFeeLastModifiedByName">
                        <abc-flex align="center">
                            <span class="title">原价</span>：
                            <abc-money :value="item.singlePromotionedTotalPrice"></abc-money>
                        </abc-flex>
                        <abc-flex align="center">
                            <span class="title">议价人</span>：
                            {{ item.unitAdjustmentFeeLastModifiedByName }}
                        </abc-flex>
                    </template>
                </div>
            </div>

            <span v-else style="font-size: 16px; font-weight: 500;" @click="handleClickPrice">
                {{ item.totalPrice | formatMoney }}
            </span>

            <abc-text v-if="isCanSeeGoodsCostPriceInRetail && !isGift && typeof item.grossProfitRate === 'number'" :theme="item.grossProfitRate >= 0 ? 'gray' : 'warning-light'" class="gross-profit-rate">
                {{ item.grossProfitRate * 100 | formatMoney }}%
            </abc-text>
        </abc-table-td>
    </abc-table-tr>
</template>

<script type="text/ecmascript-6">
    import GoodsFiled from '@/views-pharmacy/charge/components/goods-filed.vue';
    import BatchesInfos from '@/views-pharmacy/charge/components/batches-infos.vue';
    import Common from 'views/cashier/table/common.js';
    import { mapGetters } from 'vuex';
    import ChargeItemUnitSelect from '@/views-pharmacy/charge/components/charge-item-unit-select.vue';
    import { ActivityTypeLabel } from 'views/marketing/constants';
    import DialogBatchesSelector from '@/views-pharmacy/charge/components/dialog-batches-selector';
    import GoodsStockInDialog from '@/components/goods-stock-in-dialog';
    import { isShortage } from 'utils/validate';
    import {
        isChineseMedicine, isGoods, isSupportDecimalsFourMedicine,
    } from '@/filters';
    import RuleAdapter from 'views/marketing/sales-promotion/rule-adapter.js';
    import { PromotionTypeStr } from 'views/marketing/sales-promotion/constants.js';
    import { GoodsTypeIdEnum } from '@abc/constants';
    import Big from 'big.js';
    import GoodsModel from 'views/common/goods-model';
    import GoodsApi from 'api/goods';
    import cloneDeep from 'lodash.clonedeep';
    import TraceCode, {
        ShebaoTraceableCodeDismountingFlagEnum, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import { OutpatientChargeTypeEnum } from '@/common/constants/outpatient';
    import { PharmacyTypeEnum } from 'views/common/enum';
    import { isNotNull } from '@/utils';

    export default {
        name: 'ChargeTableTr',
        components: {
            ChargeItemUnitSelect,
            GoodsFiled,
            BatchesInfos,
        },
        mixins: [Common],
        props: {
            orderIndex: {
                type: Number,
            },
            item: {
                type: Object,
                required: true,
            },
            tableTdStyle: {
                type: Object,
                required: true,
            },
            isEdit: {
                type: Boolean,
                default: false,
            },
            loading: Boolean,
            disabled: Boolean,
            disabledUnitCount: Boolean,
            disabledPrice: Boolean,
            disabledBatchSelect: Boolean,
            customOrderRender: Function,
            canSingleBargain: [Boolean, Number],
        },
        data() {
            return {
                ActivityTypeLabel,
                PromotionTypeStr,
                hoverUnitPrice: false,
                hoverTotalPrice: false,
                showLoading: '',
                unitCountMessage: '',
                unitCountFocus: false,
                unitCountBlurTimer: null,
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'isChainSubStore',
                'isCanSeeGoodsCostPriceInRetail',
                'traceCodeConfig',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            goodsIsUnPriced() {
                const goodsModel = new GoodsModel(this.item.productInfo);
                return goodsModel.goodsIsUnPriced;
            },
            checkedPromotion() {
                return this.item.singlePromotions?.find((it) => it.checked);
            },
            displayTotalPriceRatio() {
                const { totalPriceRatio } = this.item;
                if (!totalPriceRatio || totalPriceRatio > 1) return '';
                return Math.round((totalPriceRatio ?? 1) * 100);
            },
            isChineseMedicine() {
                const {
                    productType,
                    productSubType,
                } = this.item;
                return isChineseMedicine({
                    type: productType, subType: productSubType,
                });
            },
            // 拆零销售的商品支持2位小数
            goodsSupportDecimal() {
                const {
                    productType,
                    productSubType,
                    productInfo,
                } = this.item;
                const {
                    pieceNum,
                } = productInfo || {};
                return pieceNum === 1 && isGoods({
                    type: productType, subType: productSubType,
                });
            },
            supportDecimal() {
                return this.isChineseMedicine || this.goodsSupportDecimal;
            },
            getUnitPriceConfig() {
                const {
                    productType,
                    productSubType,
                } = this.item;
                if (this.isChineseMedicine || isSupportDecimalsFourMedicine({
                    type: productType, subType: productSubType,
                })) {
                    return {
                        formatLength: 4, supportZero: true, max: 9999999,
                    };
                }
                return {
                    formatLength: 2, supportZero: true, max: 9999999,
                };
            },
            curIsEdit: {
                get() {
                    return this.isEdit;
                },
                set(val) {
                    this.$emit('update-price', val);
                },
            },
            stockCountLimit() {
                const {
                    stockPackageCount,
                    stockPieceCount,
                    useDismounting,
                    productInfo,
                } = this.item;
                let totalStock = stockPackageCount;
                if (useDismounting) {
                    const {
                        pieceNum,
                    } = productInfo;
                    totalStock = pieceNum * stockPackageCount + stockPieceCount;
                }
                return totalStock;
            },
            isChineseMedicinePieces() {
                return this.item.productInfo && this.item.productInfo.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES;
            },
            totalCount: {
                get() {
                    if (this.isChineseMedicinePieces && !this.unitCountFocus) {
                        // 使用 Big.js 处理精度问题
                        const unitCount = this.item.unitCount || 1;
                        const doseCount = this.item.doseCount || 1;

                        // 使用 Big.js 计算 unitCount * doseCount，并保留 2 位小数
                        return Big(unitCount).times(doseCount).toNumber();
                    } if (this.isChineseMedicinePieces && this.unitCountFocus && this.item.retailFirstSelect) {
                        return '';
                    }
                    return this.item.unitCount;
                },
                set(val) {
                    this.item.retailFirstSelect = false;
                    this.item.unitCount = val ?? 1;
                },
            },
            isGift() {
                return this.item?.isGift;
            },
            // 是否需要去入库
            needInitialStockIn() {
                return this.isChainSubStore && !this.isStockIn;
            },
            isStockIn() {
                return this.item?.productInfo?.pharmacyGoodsStockList?.length > 0 &&
                    this.item?.productInfo?.pharmacyGoodsStockList?.some((it) => it.esInorder > 0);
            },
            chargeItemUnitSelectInputStyle() {
                const config = {
                    height: '40px',
                    borderRadius: 0,
                    borderTopRightRadius: '8px',
                    borderBottomRightRadius: '8px',
                };
                return config;
            },
            hasEditPrice() {
                return this.isEditedUnitPrice || this.isEditedTotalPrice || this.isEditedRealPrice;
            },
        },
        created() {
        },

        mounted() {
        },

        methods: {
            /**
             * @desc 处理单位数量输入框聚焦
             */
            handleUnitCountFocus(e) {
                // 记录当前的聚焦状态
                this._isFocused = true;

                // 清除可能存在的定时器
                if (this.unitCountBlurTimer) {
                    clearTimeout(this.unitCountBlurTimer);
                    this.unitCountBlurTimer = null;
                }

                // 立即设置聚焦状态
                this.unitCountFocus = true;

                // 选中 input 内容
                this.$nextTick(() => {
                    // 再次检查是否仍然处于聚焦状态
                    if (this._isFocused && e && e.target && e.target.tagName === 'INPUT') {
                        e.target.select();
                    }
                });
            },

            /**
             * @desc 处理单位数量输入框失焦
             */
            handleUnitCountBlur() {
                // 标记当前不再聚焦
                this._isFocused = false;

                // 重置首次选中标记
                if (this.item.retailFirstSelect) {
                    this.item.retailFirstSelect = false;
                }

                // 清除可能存在的定时器
                if (this.unitCountBlurTimer) {
                    clearTimeout(this.unitCountBlurTimer);
                    this.unitCountBlurTimer = null;
                }

                // 使用延时器处理失焦，避免在两个组件间切换时状态不一致
                this.unitCountBlurTimer = setTimeout(() => {
                    // 再次检查是否仍然处于非聚焦状态
                    if (!this._isFocused) {
                        this.unitCountFocus = false;
                    }
                    this.unitCountBlurTimer = null;
                }, 150); // 增加延时时间，确保在切换时有足够的时间处理
            },

            getPromotionTagStr(promotion) {
                if (!promotion) return '';
                const {
                    hitRuleDetail,
                } = promotion;
                return PromotionTypeStr[hitRuleDetail?.discountWay] || '折扣';
            },
            getPromotionStr(promotion) {
                if (!promotion) return '';
                const {
                    hitRuleDetail = {},
                } = promotion;
                return RuleAdapter.transCalc2promotionStr({
                    ...hitRuleDetail,
                    displayPackageUnit: this.item.unit,
                    goodsId: this.item.productId,
                    goodsTypeName: this.transGoodsClassificationName(hitRuleDetail.goodsTypeName),
                }) || promotion.name;
            },
            handleSelectBatches(item) {
                if (this.disabledBatchSelect) return;
                this.handleClickTableTr(item);
                this._batcherDialog = new DialogBatchesSelector({
                    chargeItem: this.item,
                    onConfirm: this.handleBatchesChange,
                    minUnitCount: this.minUnitCount,
                    disabledUnitCount: this.disabledUnitCount,
                    isCanSeeGoodsCostPrice: this.isCanSeeGoodsCostPriceInRetail,
                });
                this._batcherDialog.generateDialog();
            },
            handleBatchesChange(data) {
                Object.assign(this.item, data);
                if (this.item.isExpectedBatch) {
                    this.clearSinglePromotions();
                } else {
                    this.item.chargeFormItemBatchInfos = null;
                }
                // true表示是批次变化
                this.$emit('change', true);
            },
            handleClickPrice() {
                if (this.disabled) return;
                if (this.disabledPrice) return;
                if (!this.canSingleBargain) return;
                this.curIsEdit = true;
                this.$nextTick(() => {
                    this.$refs.unitPriceRef?.$el?.querySelector('input').focus();
                });
            },
            focusUnitPrice() {
                this.hoverUnitPrice = false;
            },

            focusTotalPrice() {
                this.hoverTotalPrice = false;
            },
            handleClickTableTr(item) {
                this.$emit('select', item);
            },
            validateUnitCount(value, callback) {
                if (!value) {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                    this.unitCountMessage = '不能为空';
                    return;
                }
                if (+value === 0) {
                    callback({
                        validate: false,
                        message: '不能为0',
                    });
                    this.unitCountMessage = '不能为0';
                    return;
                }
                const res = isShortage(this.item);
                if (res.flag) {
                    callback({
                        validate: false,
                        message: !this.needInitialStockIn ? (res.tips || '无库存') : '',
                    });
                    this.unitCountMessage = !this.needInitialStockIn ? (res.tips || '无库存') : '';
                    return;
                }
                callback({
                    validate: true,
                });
                this.unitCountMessage = '';
            },
            clearSinglePromotions() {
                if (this.item.singlePromotions) {
                    this.item.singlePromotions.forEach((it) => {
                        it.expectedChecked = null;
                        it.checked = false;
                    });
                }
            },
            handleChangeCount() {
                if (this.item.expectedTotalPrice) {
                    this.item.expectedTotalPrice = null;
                    if (this.item.singlePromotionedUnitPrice !== +this.item.unitPrice) {
                        this.item.expectedUnitPrice = this.item.unitPrice;
                    }
                }
                if (!this.item.unitCount) {
                    this.item.unitCount = 1;
                }
                this.$set(this.item, 'expectedTotalPriceRatio', null);
                this.$nextTick(() => {
                    if (this.$refs.unitCountFormItem) {
                        this.$refs.unitCountFormItem.validate();
                    }
                });
                this.$emit('change');

                this.changeShebaoDismountingFlag();
            },
            handleUnitChange() {
                const {
                    dismounting,
                    pieceUnit,
                    packageUnit,
                    piecePrice,
                    packagePrice,
                } = this.item.productInfo || {};
                const useDismounting = +(dismounting && this.item.unit === pieceUnit && this.item.unit !== packageUnit);
                this.item.useDismounting = useDismounting;
                this.item.unitPrice = useDismounting ? piecePrice : packagePrice;
                this.clearSinglePromotions();
                this.$emit('change');

                this.changeShebaoDismountingFlag();
            },
            changeUnitPrice() {
                this.showLoading = 'totalPrice';
                this.$set(this.item, 'expectedTotalPrice', null);
                this.$set(this.item, 'expectedUnitPrice', this.item.unitPrice);

                this.$emit('change');
                this._timer = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },
            changeTotalPrice() {
                this.showLoading = 'unitPrice';
                this.$set(this.item, 'expectedUnitPrice', null);
                this.$set(this.item, 'expectedTotalPriceRatio', null);
                this.$set(this.item, 'expectedTotalPrice', this.item.totalPrice);
                this.$set(this.item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(this.item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);

                this.$emit('change');
                this._timer = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },
            handleEnter(e) {
                if (!this.item.unitCount) {
                    this.item.unitCount = 1;
                }
                this.$emit('enter', e, this.item);
            },
            handleDeleteTr() {
                if (this.disabled) return;
                this.$emit('delete-tr');
            },
            inputPriceRadioHandler(item, val) {
                this.$set(item, 'totalPriceRatio', val / 100);
            },
            changeTotalPriceRatio(item) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPriceRatio', item.totalPriceRatio);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.$emit('change');
            },

            /**
             * 处理打开入库弹窗
             */
            handleOpenStockInDialog() {
                if (!this.item || !this.item.productInfo || !this.item.productInfo.id) {
                    this.$Toast.warning('无法获取商品信息');
                    return;
                }

                // 打开入库弹窗
                new GoodsStockInDialog({
                    goodsId: this.item.productInfo.id,
                    successCallback: async () => {
                        // 入库成功后手动更新入库状态
                        const { data } = await GoodsApi.fetchGoods(this.item.productInfo.id, {
                            pharmacyNo: this.item.pharmacyNo,
                        });
                        this.$set(this.item, 'productInfo', data);
                        this.$emit('init-stock-in-callback');
                    },
                }).generateDialogAsync({
                    parent: this,
                });
            },
            changePriceRadio(item, val) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'totalPriceRatio', val);
                this.$set(item, 'expectedTotalPriceRatio', val);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.$emit('change');
            },

            adjustUnitPriceChange(item, val) {
                item.unitPrice = val;
                this.changeUnitPrice();
            },

            adjustPriceChange(item, val) {
                item.totalPrice = val;
                this.changeTotalPrice();
            },
            handleClickPromotion(promotion) {
                if (promotion?.checked) return;
                this.item.singlePromotions.forEach((it) => {
                    it.expectedChecked = false;
                });
                if (promotion) {
                    promotion.expectedChecked = !promotion.checked;
                }

                this.$set(this.item, 'expectedUnitPrice', null);
                this.$set(this.item, 'expectedTotalPrice', null);
                this.$set(this.item, 'expectedTotalPriceRatio', null);
                this.$set(this.item, 'unitAdjustmentFeeLastModifiedBy', null);
                this.$set(this.item, 'unitAdjustmentFeeLastModifiedByName', null);

                this.$emit('change');
                this.$refs.promotionsPopover?.doClose();
            },

            handlePriceRatioKeyDown(event, direction) {
                if (event.preventDefault) event.preventDefault();
                if (event.stopPropagation) event.stopPropagation();
                if (direction === 'up') {
                    this.$refs.priceRadioPopover.up();
                } else if (direction === 'down') {
                    this.$refs.priceRadioPopover.down();
                } else if (direction === 'left') {
                    this.$refs.priceRadioPopover.left();
                } else if (direction === 'right') {
                    this.$refs.priceRadioPopover.right();
                } else if (direction === 'enter') {
                    this.$refs.priceRadioPopover.enter();
                    this.$refs.priceRadioInput.$el.querySelector('input').blur();
                }
            },
            /**
             * 重新计算追溯码拆零标识
             */
            changeShebaoDismountingFlag() {
                if (
                    this.traceCodeConfig?.collectionCheck &&
                    this.item.checked &&
                    this.item.unitCount &&
                    this.item.sourceItemType !== OutpatientChargeTypeEnum.NO_CHARGE &&
                    this.item.pharmacyType !== PharmacyTypeEnum.AIR_PHARMACY &&
                    TraceCode.formItemSupportTraceCode(this.item)
                ) {
                    TraceCode.getMaxTraceCountList({
                        dataList: [this.item],
                        scene: TraceCodeScenesEnum.PHARMACY,
                    }).then((res) => {
                        const resultItemInfo = res[0] ?? {};
                        if (isNotNull(resultItemInfo.shebaoDismountingFlag)) {
                            this.$set(this.item, 'shebaoDismountingFlag', resultItemInfo.shebaoDismountingFlag);
                        }
                        // 如果开启了拆零不采集模式
                        if (TraceCode.hasEnableDismountingMode) {
                            if (
                                [ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_NO_EDIT, ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_EDIT].includes(this.item.shebaoDismountingFlag) &&
                                this.item.traceableCodeList?.length
                            ) {
                                // 如果变为了拆零，并且已经采集了追溯码，则暂存并清空
                                this.item._cacheTraceableCodeList = cloneDeep(this.item.traceableCodeList);
                                this.$set(this.item, 'traceableCodeList', []);
                            } else if (
                                [ShebaoTraceableCodeDismountingFlagEnum.NO_DISMOUNTING_NO_EDIT, ShebaoTraceableCodeDismountingFlagEnum.NO_DISMOUNTING_EDIT].includes(this.item.shebaoDismountingFlag) &&
                                this.item._cacheTraceableCodeList?.length
                            ) {
                                // 如果变为了不拆零，并且之前采集过追溯码，则恢复已采集的追溯码
                                this.$set(this.item, 'traceableCodeList', cloneDeep(this.item._cacheTraceableCodeList));
                                this.item._cacheTraceableCodeList = [];
                            }
                        }
                    }).catch((e) => {
                        console.error(e);
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';
    @import 'src/components/excel-table/bargain-origin-price-popover.scss';

    .unit-count {
        .unit-count-with-dose-count {
            display: flex;
            border-radius: var(--abc-border-radius-small);

            .unit-count-wrapper:not(.unit-count-wrapper-disabled):hover {
                .abc-input-wrapper .abc-input__inner {
                    z-index: 1;
                    border-color: $theme3;
                }

                .abc-select-wrapper .abc-input__inner {
                    background-color: var(--abc-color-B4) !important;
                    border-color: $theme3;
                }
            }

            .unit-count-wrapper {
                display: flex;
                align-items: center;
                height: 40px;
                background-color: #ffffff;
                border: 1px solid var(--abc-color-P7);
                border-radius: var(--abc-border-radius-small);
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;

                .abc-input-wrapper {
                    margin-right: -1px;

                    .abc-input__inner {
                        padding: 3px 5px 3px 3px;
                        font-size: 16px;
                        text-align: center;
                        //background-color: transparent !important;
                        //border-color: transparent;
                        //box-shadow: none;
                    }
                }

                .abc-select-wrapper {
                    .abc-input__inner {
                        padding: 0;
                        text-align: center;
                        background-color: transparent !important;
                        border-color: transparent;
                        box-shadow: none;
                    }

                    .iconfont {
                        display: none;
                    }
                }

                &:focus,
                &:active {
                    z-index: 2;
                }

                &.is-error {
                    .count-center .abc-input__inner {
                        border-color: $Y2;
                    }
                }
            }

            .unit-count-wrapper.unit-count-wrapper-disabled {
                background-color: var(--abc-color-bg-disabled);
            }

            .dose-count {
                min-width: 48px;
                max-width: 48px;
                height: 40px;
                margin-left: -1px;
                line-height: 40px;
                color: var(--abc-color-T2);
                text-align: center;
                background-color: var(--abc-color-P5);
                border: 1px solid var(--abc-color-P7);
                border-top-right-radius: var(--abc-border-radius-small);
                border-bottom-right-radius: var(--abc-border-radius-small);
            }

            .dose-count-bottom {
                position: absolute;
                top: 50%;
                width: 100px;
                font-size: 12px;
                text-align: left;
                text-indent: 8px;
                transform: translateY(-50%);
            }
        }

        .unit-count-hide-dose-count {
            .unit-count-wrapper {
                border-radius: var(--abc-border-radius-small);
            }

            .dose-count {
                display: none;
            }
        }
    }

    .charge-table-tr-abc-tooltip {
        width: 136px;
        height: 42px;
        background: var(--abc-color-Y2);
        border: none;
        border-radius: var(--abc-border-radius-medium);
        box-shadow: none;

        &::after {
            position: absolute;
            bottom: -4px;
            left: 50%;
            width: 0;
            height: 0;
            content: '';
            border-top: 5px solid var(--abc-color-Y2);
            border-right: 5px solid transparent;
            border-left: 5px solid transparent;
            transform: translateX(-50%);
        }
    }

    .biz-pharmacy-charge-table-tr {
        border-bottom: 1px solid var(--abc-color-card-divider-color) !important;

        .tag-group {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            height: 22px;
            overflow-y: hidden;
            cursor: pointer;

            > div {
                cursor: pointer;
            }

            .iconfont {
                position: absolute;
                right: 0;
                color: $T3;
            }
        }

        .unit-price-wrapper .bargain-origin-price-popover {
            right: unset;
            left: 12px;

            > div {
                line-height: 20px;
            }
        }

        .total-price-wrapper .bargain-origin-price-popover {
            right: 12px;

            > div {
                line-height: 20px;
            }
        }

        .gross-profit-rate {
            position: absolute;
            bottom: 4px;
        }
    }

    .charge-tr-promotion-popover {
        width: 352px;

        .promotion-item {
            min-height: 32px;
            padding: 0 6px;
            cursor: pointer;
            border-radius: var(--abc-border-radius-small);

            &:hover {
                background: var(--abc-color-cp-grey4);
            }

            &.is-checked {
                background: var(--abc-color-B4);
            }

            &.no-promotion {
                padding-left: 48px;
                color: var(--abc-color-T2);
            }

            & + .promotion-item {
                margin-top: 2px;
            }

            .name {
                flex: 1;
                margin-left: 8px;
            }

            .price {
                width: 88px;
                padding-right: 12px;
                text-align: right;
            }

            .choose-icon-wrapper {
                width: 32px;
                height: 32px;
            }
        }

        .iconfont {
            color: $G2;
        }
    }
</style>

