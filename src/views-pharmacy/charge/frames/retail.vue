<template>
    <abc-layout v-abc-loading:page="pageLoading" has-sidebar class="charge-retail-wrapper">
        <abc-layout-content class="charge-retail-content" data-cy="charge-retail-content">
            <div ref="retailContent" class="retail-content">
                <abc-form
                    ref="postDataForm"
                    item-no-margin
                    :need-scroll-to-first-error="needScrollToFirstError"
                >
                    <abc-section>
                        <abc-tips-card-v2
                            v-if="lockedInfo"
                            style="margin-bottom: 16px;"
                            theme="warning"
                            align="center"
                        >
                            {{ lockedTips }}
                            <template v-if="showCancelPay" #operate>
                                <abc-button
                                    variant="text"
                                    size="small"
                                    @click="onClickCancelPay"
                                >
                                    支付遇到问题？
                                </abc-button>
                            </template>
                        </abc-tips-card-v2>
                        <abc-flex justify="space-between" align="center">
                            <div style="width: 252px;">
                                <cooperation-detail v-if="cooperationDetail" :value="cooperationDetail" :charge-sheet-id="chargeSheetId"></cooperation-detail>
                                <abc-button-group v-else-if="postData.registerInfoId">
                                    <abc-button
                                        shape="square"
                                        variant="ghost"
                                        theme="default"
                                        size="large"
                                        icon-color="var(--abc-color-theme1)"
                                        icon="s-rxt-fill"
                                        disabled
                                        style="color: var(--abc-color-T1);"
                                    >
                                        纸质处方
                                    </abc-button>

                                    <abc-button
                                        variant="ghost"
                                        size="large"
                                        icon="s-accessory-line"
                                        style="min-width: 110px;"
                                        @click="showPrescriptionRegisDialog = true"
                                    >
                                        登记信息
                                    </abc-button>
                                </abc-button-group>
                                <abc-dropdown
                                    v-else
                                    size="large"
                                    placement="bottom-start"
                                    @change="handlePRChange"
                                >
                                    <div slot="reference">
                                        <abc-button
                                            shape="square"
                                            variant="ghost"
                                            size="large"
                                            icon="s-rxt-fill"
                                        >
                                            获取流转处方
                                            <abc-badge
                                                v-if="coCashierTodo"
                                                :value="coCashierTodo"
                                                class="co-cashier-todo"
                                                variant="round"
                                                theme="danger"
                                            ></abc-badge>
                                        </abc-button>
                                    </div>
                                    <abc-dropdown-item value="registerPrescription">
                                        登记纸质处方 <abc-text theme="gray">
                                            Ctrl + F
                                        </abc-text>
                                    </abc-dropdown-item>
                                    <abc-dropdown-item v-if="isOpenSocial" value="socialPrescriptionOut">
                                        医保电子流转处方 <abc-text theme="gray">
                                            Ctrl + Y
                                        </abc-text>
                                    </abc-dropdown-item>
                                    <abc-dropdown-item value="extractPrescription">
                                        合作诊所流转处方 <abc-text theme="gray">
                                            Ctrl + H
                                        </abc-text>
                                        <abc-badge
                                            v-if="coCashierTodo"
                                            :value="coCashierTodo"
                                            variant="round"
                                            theme="danger"
                                        ></abc-badge>
                                    </abc-dropdown-item>
                                </abc-dropdown>
                            </div>

                            <abc-space class="search-header">
                                <pharmacy-goods-autocomplete
                                    ref="pharmacyGoodsAutocomplete"
                                    :profit-classification-list="profitClassificationList"
                                    :disabled="disabledChangeGoods"
                                    :show-piece-search="showPieceSearch"
                                    :split-by-not-initial-stock="isChainSubStore"
                                    @handle-search-range-switch="handleSearchRangeSwitch"
                                    @focus="stopBarcodeDetect"
                                    @blur="startBarcodeDetect"
                                    @select="handleSelect"
                                    @enter="handleGoodsAutocompleteEnter"
                                ></pharmacy-goods-autocomplete>
                            </abc-space>
                            <abc-form-item-group label-position="inner">
                                <abc-space style="width: 252px;" align="end">
                                    <abc-form-item label="销售" :required="requiredSeller" hidden-red-dot>
                                        <employee-department-selector
                                            :department-id.sync="postData.sellerDepartmentId"
                                            :doctor-id.sync="postData.sellerId"
                                            :width="76"
                                            :inner-width="160"
                                            :no-icon="false"
                                            size="medium"
                                            :allow-option-name="false"
                                            :tabindex="-1"
                                            data-cy="pharmacy-goods-selector-salesman"
                                            @change="changeSeller"
                                        ></employee-department-selector>
                                    </abc-form-item>
                                    <abc-form-item label="药师">
                                        <employee-selector
                                            v-model="postData.pharmacistId"
                                            :width="76"
                                            :inner-width="100"
                                            size="medium"
                                            clearable
                                            :employees="pharmacistEmployees"
                                            data-cy="pharmacy-goods-selector-pharmacist"
                                        >
                                        </employee-selector>
                                    </abc-form-item>
                                </abc-space>
                            </abc-form-item-group>
                        </abc-flex>
                    </abc-section>
                    <abc-section>
                        <abc-table
                            ref="retailTable"
                            custom
                            class="retail-table"
                            :support-delete-tr="!disabledChangeGoods"
                            show-order
                            fill-height
                            :fill-bottom-offset="240"
                        >
                            <abc-flex
                                v-if="isCooperationOrder || isPrescriptionOut"
                                class="abc-table-top-header"
                                justify="space-between"
                                align="center"
                                style="background-color: var(--abc-color-cp-grey3);"
                            >
                                <abc-space>
                                    <abc-tag-v2 variant="outline" size="small" theme="success">
                                        {{ isCooperationOrder ? '诊所流转处方' : '电子流转处方' }}
                                    </abc-tag-v2>
                                    <abc-text bold>
                                        <template v-if="isCooperationOrder">
                                            {{ sourcePatientInfo?.name || '' }}
                                            {{ sourcePatientInfo?.sex || '' }}
                                            {{ sourcePatientInfo?.mobile || '' }}
                                        </template>
                                        <template v-if="isPrescriptionOut">
                                            {{ postData.prescriptionPatient?.name || '' }}
                                            {{ postData.prescriptionPatient?.sex || '' }}
                                            {{ postData.prescriptionPatient?.mobile || '' }}
                                        </template>
                                    </abc-text>
                                </abc-space>
                                <template v-if="warningTips">
                                    <abc-tips theme="warning">
                                        {{ warningTips }}
                                    </abc-tips>
                                </template>
                            </abc-flex>
                            <abc-table-header>
                                <abc-table-td :style="tableTdStyle.goodsName">
                                    商品
                                </abc-table-td>
                                <abc-table-td :style="tableTdStyle.batchNo">
                                    批号/有效期
                                </abc-table-td>
                                <abc-table-td :style="tableTdStyle.unitCount" style="text-indent: 37px;">
                                    数量
                                </abc-table-td>
                                <abc-table-td :style="tableTdStyle.unitPrice" align="left">
                                    单价
                                </abc-table-td>
                                <abc-table-td :style="tableTdStyle.realPrice">
                                    实价
                                </abc-table-td>
                                <abc-table-td :width="108" align="right" style="padding-right: 8px;">
                                    金额
                                </abc-table-td>
                            </abc-table-header>
                            <abc-table-body ref="retailTableBody" data-cy="pharmacy-goods-item-list" style="margin-bottom: -1px;">
                                <div v-abc-click-outside="onClickOutside" class="charge-form-wrapper">
                                    <div
                                        v-for="(group, index) in expandChargeForms"
                                        :key="`group-${index}`"
                                        class="charge-form group-charge-form"
                                        :class="{ 'has-chinese-medicine': chineseMedicinePiecesFormItems.length > 0 && index === expandChargeForms.length - 1 }"
                                    >
                                        <charge-table-tr
                                            v-for="(item, itemIndex) in group.items"
                                            :id="`_${item.keyId}`"
                                            :key="`${item.keyId }${ itemIndex}`"
                                            ref="chargeTableTr"
                                            :item="item"
                                            :order-index="item.trIndex"
                                            :is-edit="editItemKeyId === item.keyId"
                                            :class="{
                                                'is-selected': getIsSelected(item)
                                            }"
                                            :loading="calcLoading"
                                            :disabled-unit-count="disabledChangeUnitCount || item._disabledChangeUnitCount"
                                            :table-td-style="tableTdStyle"
                                            delete-icon-variant="dark"
                                            :can-single-bargain="!!chargeConfig.singleBargainSwitch"
                                            :dose-count="userInputFormDoseCount"
                                            @update-price="handleUpdatePrice"
                                            @enter="handleTrEnter"
                                            @delete-tr="handleDeleteTr(item)"
                                            @select="handleSelectTableTr(item)"
                                            @change="handleItemChange(item, $event)"
                                            @init-stock-in-callback="initStockInCallback"
                                        >
                                        </charge-table-tr>

                                        <div
                                            v-if="group.form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE && chineseMedicinePiecesFormItems.length > 0"
                                            class="charge-form chinese-medicine-pieces charge-table-tr"
                                            :class="{ 'is-selected': curSelectedChargeItem && curSelectedChargeItem.keyId === 'doseCountTr' }"
                                            @click="handleSelectTableTr({ keyId: 'doseCountTr' })"
                                        >
                                            <abc-flex align="center" style="padding: 8px 16px;">
                                                <abc-text size="normal">
                                                    配方饮片
                                                </abc-text>
                                                <abc-form-item required>
                                                    <abc-input
                                                        :ref="`userInputFormDoseCountRetail_${index}`"
                                                        v-model="userInputFormDoseCount"
                                                        v-abc-focus-selected
                                                        size="small"
                                                        type="number"
                                                        :width="60"
                                                        :input-custom-style="{ textAlign: 'center' }"
                                                        :config="{
                                                            max: 9999,
                                                            supportZero: false,
                                                        }"
                                                        :disabled="disabledChangeUnitCount || disabledChineseMedicinePiecesDose"
                                                        style="margin: 0 8px;"
                                                        @enter="handleDoseInputEnter"
                                                        @focus="handleDoseInputFocus"
                                                        @blur="handleDoseInputBlur"
                                                    ></abc-input>
                                                </abc-form-item>
                                                <abc-flex align="center" gap="small">
                                                    <abc-text size="normal">
                                                        剂
                                                    </abc-text>
                                                </abc-flex>
                                                <chinese-stat-popover v-if="chineseMedicineForm && doseTotalObj.total > 0" :form="chineseMedicineForm">
                                                    <abc-text
                                                        size="normal"
                                                        theme="gray"
                                                        style="margin-left: 8px;"
                                                    >
                                                        （ {{ doseTotalObj.str }} ）
                                                    </abc-text>
                                                </chinese-stat-popover>
                                                <abc-text
                                                    v-else
                                                    size="normal"
                                                    theme="gray"
                                                    style="margin-left: 8px;"
                                                >
                                                    （ {{ doseTotalObj.str }} ）
                                                </abc-text>
                                            </abc-flex>
                                        </div>
                                    </div>
                                </div>
                                <div class="charge-form">
                                    <charge-table-tr
                                        v-for="(item, index) in promotionFormItems"
                                        :id="`_${item.keyId}`"
                                        :key="`${item.keyId }${ index}`"
                                        :item="item"
                                        disabled
                                        disabled-unit-count
                                        disabled-price
                                        :table-td-style="tableTdStyle"
                                        delete-icon-variant="dark"
                                        :custom-order-render="customOrderRender"
                                        @change="handleItemChange(item, $event)"
                                        @enter="handleTrEnter"
                                        @delete-tr="handleDeletePromotionGift(item)"
                                    >
                                    </charge-table-tr>
                                </div>
                                <!--手动标记的赠品-->
                                <div class="charge-form">
                                    <charge-table-tr
                                        v-for="(item, index) in markGiftFormItems"
                                        :id="`_${item.keyId}`"
                                        :key="`${item.keyId }${ index}`"
                                        :item="item"
                                        disabled-price
                                        :table-td-style="tableTdStyle"
                                        delete-icon-variant="dark"
                                        :custom-order-render="customOrderRender"
                                        @change="handleItemChange(item, $event)"
                                        @enter="handleTrEnter"
                                        @delete-tr="handleDeleteItem(item)"
                                    >
                                    </charge-table-tr>
                                </div>
                                <abc-content-empty
                                    v-if="!loading && expandChargeFormItems.length === 0 && markGiftFormItems.length === 0"
                                    value="待添加商品"
                                    size="small"
                                ></abc-content-empty>
                            </abc-table-body>
                            <abc-table-footer style="padding: 0 16px;">
                                <div class="retail-verify" data-cy="abc-space-wrapper-智能审方">
                                    <retail-verify
                                        v-if="!noChargeForms"
                                        :post-data="postData"
                                        :verify-outpatient="verifyOutpatient"
                                    ></retail-verify>
                                </div>
                            </abc-table-footer>
                        </abc-table>
                    </abc-section>
                </abc-form>
            </div>
            <abc-flex class="retail-settle" gap="16">
                <div class="customer-info-wrapper">
                    <abc-flex class="search-wrapper" gap="8">
                        <customer-autocomplete
                            ref="customerAutocomplete"
                            :patient.sync="postData.patient"
                            :member-info="postData.memberInfo"
                            :disabled="disabledChangeCustomer"
                            :show-balance="false"
                            :show-points="false"
                            size="medium"
                            data-cy="patient-autocomplete-retail-搜索会员"
                            style="flex: 1;"
                            show-f2
                            @focus="stopBarcodeDetect"
                            @blur="startBarcodeDetect"
                            @select="handleSelectCustomer"
                            @update-customer-promotion="calcFee"
                            @update-customer="handleUpdateCustomer"
                            @get-customer-extend-info="handleCustomerExtendInfo"
                        ></customer-autocomplete>
                        <abc-button
                            v-if="!hasPatient"
                            icon="s-b-user-add-line"
                            icon-color="var(--abc-color-T1)"
                            shape="square"
                            variant="ghost"
                            size="large"
                            theme="default"
                            style="min-width: 70px;"
                            data-cy="quick-operation-wrapper-办会员"
                            @click="handleClickAddMember"
                        >
                            F4
                        </abc-button>
                    </abc-flex>
                    <abc-divider margin="none" theme="light"></abc-divider>
                    <abc-flex justify="space-around" class="customer-info">
                        <abc-flex
                            align="center"
                            justify="center"
                            vertical
                            gap="6"
                        >
                            <abc-text theme="gray">
                                会员余额
                            </abc-text>
                            <abc-text v-if="hasPatient" size="large">
                                {{ customerExtendInfo.balanceTotal | formatMoney }}
                            </abc-text>
                            <abc-text v-else size="large">
                                -
                            </abc-text>
                        </abc-flex>
                        <abc-flex
                            align="center"
                            justify="center"
                            vertical
                            gap="6"
                        >
                            <abc-text theme="gray">
                                积分
                            </abc-text>
                            <abc-text v-if="hasPatient" size="large">
                                {{ customerExtendInfo.points || 0 }}
                            </abc-text>
                            <abc-text v-else size="large">
                                -
                            </abc-text>
                        </abc-flex>
                        <abc-flex
                            align="center"
                            justify="center"
                            vertical
                            gap="6"
                        >
                            <abc-text theme="gray">
                                优惠券
                            </abc-text>
                            <abc-text v-if="hasPatient" size="large">
                                {{ customerExtendInfo.couponsCount || 0 }}
                            </abc-text>
                            <abc-text v-else size="large">
                                -
                            </abc-text>
                        </abc-flex>
                        <abc-flex
                            align="center"
                            justify="center"
                            vertical
                            gap="6"
                        >
                            <abc-text theme="gray">
                                历史消费
                            </abc-text>
                            <abc-text v-if="hasPatient" size="large">
                                {{ customerExtendInfo.cumulativeAmount | formatMoney }}
                            </abc-text>
                            <abc-text v-else size="large">
                                -
                            </abc-text>
                        </abc-flex>
                    </abc-flex>
                </div>

                <abc-flex class="settle-info-wrapper">
                    <abc-flex class="discount-wrapper" vertical gap="2">
                        <discount-list
                            class="discount-list"
                            :post-data="postData"
                            :charge-sheet-id="chargeSheetId"
                            :charge-forms="postData.chargeForms"
                            :gift-rule-promotions.sync="postData.giftRulePromotions"
                            :coupon-promotions.sync="postData.couponPromotions"
                            :patient-points-info.sync="postData.patientPointsInfo"
                            :summary="chargeSheetSummary"
                            @change="handleChangeDiscount"
                        ></discount-list>
                        <abc-popover
                            v-if="postData.remarks"
                            width="500px"
                            placement="top-start"
                            trigger="hover"
                            theme="yellow"
                            show-on-overflow
                            style="padding: 0 8px;"
                        >
                            <template slot="reference">
                                <abc-text
                                    size="mini"
                                    theme="gray"
                                    class="remark-info"
                                    data-cy="abc-space-item-remark"
                                >
                                    备注：{{ postData.remarks }}
                                </abc-text>
                            </template>
                            <abc-text>
                                {{ postData.remarks }}
                            </abc-text>
                        </abc-popover>
                    </abc-flex>

                    <abc-flex class="total-info" vertical>
                        <abc-flex vertical class="price-info-wrapper">
                            <abc-flex
                                class="price-info"
                                align="flex-start"
                                justify="space-between"
                                gap="8"
                            >
                                <abc-flex vertical gap="8">
                                    <abc-space class="price-item">
                                        <abc-text class="label" theme="gray">
                                            合计
                                        </abc-text>
                                        <abc-text
                                            class="money"
                                            bold
                                            size="large"
                                            data-cy="pharmacy-goods-price-合计"
                                        >
                                            {{ chargeSheetSummary.sourceTotalPrice | formatMoney }}
                                        </abc-text>
                                    </abc-space>

                                    <abc-space class="price-item">
                                        <abc-text class="label" theme="gray">
                                            优惠
                                        </abc-text>
                                        <abc-text
                                            class="money"
                                            bold
                                            size="large"
                                            data-cy="pharmacy-goods-price-优惠"
                                        >
                                            {{ chargeSheetSummary.discountTotalFee | formatMoney }}
                                        </abc-text>
                                    </abc-space>
                                </abc-flex>
                                <abc-space class="price-item">
                                    <abc-text class="label" style="font-size: 20px;">
                                        应收
                                    </abc-text>
                                    <abc-text
                                        bold
                                        theme="danger"
                                        class="money total-amount"
                                        data-cy="pharmacy-goods-price-应收"
                                    >
                                        <abc-money
                                            :symbol-icon-size="20"
                                            :value="chargeSheetSummary.needPayFee"
                                            :symbol-style="{
                                                'margin-right': '4px',
                                                'line-height': '18px',
                                                'align-self': 'flex-end'
                                            }"
                                        ></abc-money>
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </abc-flex>

                        <div class="btn-wrapper">
                            <abc-space>
                                <abc-button
                                    size="large"
                                    class="huge-abc-button"
                                    type="blank"
                                    :disabled="!hasUpdate"
                                    data-cy="pharmacy-goods-button-删单"
                                    title="Ctrl + Delete"
                                    @click="handleClickClear"
                                >
                                    删单
                                    <span class="quick-key gray">Ctrl + Del</span>
                                </abc-button>
                                <abc-check-access v-if="!noChargeForms">
                                    <abc-button
                                        title="F9"
                                        class="huge-abc-button"
                                        :loading="calcLoading || buttonLoading"
                                        size="large"
                                        data-cy="pharmacy-goods-button-结账"
                                        @click="handleClickCharge"
                                    >
                                        结账
                                        <span class="quick-key">F9</span>
                                    </abc-button>
                                </abc-check-access>
                                <abc-tooltip v-else content="没有收费项目">
                                    <abc-button class="huge-abc-button" size="large" disabled>
                                        结账
                                        <span class="quick-key">F9</span>
                                    </abc-button>
                                </abc-tooltip>
                            </abc-space>
                        </div>
                    </abc-flex>
                </abc-flex>
            </abc-flex>

            <quick-operation
                ref="quick-operation"
                :post-data="postData"
                :cooperation-detail="cooperationDetail"
                :charge-sheet-id="chargeSheetId"
                :charge-sheet-summary="chargeSheetSummary"
                :is-prescription-out="isPrescriptionOut"
                data-cy="pharmacy-goods-quick-operation-wrapper"
                @update-patient="handleUpdatePatient"
                @update-price="handleUpdatePrice"
                @update-price-ratio="handleUpdatePriceRatio"
                @update-batches="handleUpdateBatches"
                @calc-fee="calcFee"
                @hangup="hangUpOrder"
                @use-hangup="handleHangup"
                @clear="clearChargeSheet"
                @open-trace-code-dialog="handleOpenTraceCodeDialog"
                @set-gift="handleSetGift"
                @fetchChargeSheet="fetchChargeSheet"
                @handle-search-range-switch="handleSearchRangeSwitch"
                @handle-chinese-medicine-dose="handleChineseMedicineDose"
            ></quick-operation>
        </abc-layout-content>
        <abc-layout-sidebar :width="340" class="charge-retail-sidebar">
            <retail-sidebar @select-goods="handleSidebarSelectGoods"></retail-sidebar>
        </abc-layout-sidebar>
        <points-need-open-notice-dialog
            v-if="showPointsNeedOpenNoticeDialog"
            v-model="showPointsNeedOpenNoticeDialog"
            @confirm="confirm"
        ></points-need-open-notice-dialog>
        <prescription-registration-dialog
            v-if="showPrescriptionRegisDialog"
            v-model="showPrescriptionRegisDialog"
            :register-info-id="postData.registerInfoId"
            :charge-sheet-id="chargeSheetId"
            :cooperation-id="cooperationDetail && cooperationDetail.id"
            :charge-sheet-type="postData.type"
            :default-urls="postData.prescriptionPDFUrls"
            :default-doctor-name="PRRegistrationDefaultDoctor"
            :patient="PRRegistrationDefaultPatient"
            :cache-diagnosis="cacheDiagnosis"
            :is-prescription-out="isPrescriptionOut"
            @cache-register-info="handleCacheRegisterInfo"
            @cache-diagnosis="handleCacheDiagnosis"
            @success="onRegisterPrescriptionSuccess"
        >
        </prescription-registration-dialog>
        <extract-prescription-dialog
            v-if="showExtractPRDialog"
            v-model="showExtractPRDialog"
            @extract="handleExtract"
            @edit="initCopyChargeSheet"
        >
        </extract-prescription-dialog>
        <dialog-info-patient
            v-if="showCreateMemberDialog"
            v-model="showCreateMemberDialog"
            :default-data="sourcePatientInfo"
            @success="createMemberSuccess"
        ></dialog-info-patient>
    </abc-layout>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';

    import RetailSidebar from '@/views-pharmacy/charge/components/retail-sidebar.vue';
    import QuickOperation from '@/views-pharmacy/charge/components/quick-operation.vue';
    import PharmacyGoodsAutocomplete from '@/views-pharmacy/charge/components/pharmacy-goods-autocomplete.vue';
    import DiscountList from '@/views-pharmacy/charge/components/discount/list.vue';
    import ChargeTableTr from '@/views-pharmacy/charge/components/charge-table-tr.vue';
    import CustomerAutocomplete from '@/views-pharmacy/charge/components/customer-autocomplete.vue';
    import CooperationDetail from '@/views-pharmacy/charge/components/cooperation-detail.vue';
    import RetailVerify from '@/views-pharmacy/charge/components/retail-verify.vue';
    import PointsNeedOpenNoticeDialog from '@/views-pharmacy/charge/frames/points-need-open-notice-dialog.vue';
    import {
        ChargeSheetTypeEnum,
        ChargeStatusEnum,
        CreateCashierPostData,
        CreateChargeSheetSummary,
        PayModeEnum,
        SourceFormTypeEnum,
    } from '@/service/charge/constants.js';
    import {
        getChargeItemStruct,
        getWarnChargeItemGroup,
        setChargeForm,
        getTraceCodeChargeItems,
    } from 'views/cashier/utils/index.js';
    import {
        ChargeFormItemGiftType,
        getCustomerInfo, getPharmacyWarnChargeItemGroup,
    } from 'src/views-pharmacy/charge/utils/index.js';
    import EmployeeDepartmentSelector from 'views/common/components/employee-department-selector/index.vue';
    import ChargeAPI from 'api/charge.js';
    import enterEvent from 'views/cashier/mixins/enter-event.js';
    import keyboardEvent from 'views/cashier/mixins/keyup-keydown-event.js';
    import AbcChargeService from '@/service/charge/index.js';
    import AbcChargeDialog from '@/service/charge/components/dialog-charge/index.js';
    import ChargeNoticeDialog from 'views/cashier/components/charge-notice-dialog/index.js';
    import {
        debounce,
    } from 'utils/lodash.js';
    import GoodsApi from 'api/goods';
    import GoodsAPIV3 from 'api/goods/index-v3.js';
    import { SearchSceneTypeEnum } from 'views/common/enum';
    import {
        off, on,
    } from 'utils/dom';
    import Printer from 'views/print';
    import AbcPrinter from '@/printer';
    import TpsAPI from 'api/tps';
    import QRCode from 'qrcode';
    import AbcAccess from '@/access/utils';
    import clone from 'utils/clone';
    import { TagV2 as AbcTagV2 } from '@abc/ui-pc';
    import { PharmacyChargeRouterNameKeys } from '@/views-pharmacy/charge/core/routes';
    import BarcodeDetectorV2 from 'utils/barcode-detector-v2';
    import EmployeeSelector from 'views/layout/employee-selector/employee-selector.vue';
    import { ROLE_PHARMACY_DOCTOR } from 'utils/constants';
    import localStorage from 'utils/localStorage-handler';
    import CollectionTraceCodeDialog from '@/service/trace-code/dialog-collection-trace-code';
    import {
        createGUID,
        isBarcode,
        checkHasAbcDialog,
    } from '@/utils';
    import {
        ANONYMOUS_ID, GoodsTypeIdEnum,
    } from '@abc/constants';
    import { CooperationOrderStatus } from '@/views-pharmacy/charge/utils/constants';
    import CdssAPI from 'api/cdss';
    import { IngredientObj } from 'views/common/inventory/constants';
    import TraceCode, {
        AliPackageLevelEnum, AliPackageLevelLabel,
        SceneTypeEnum, ShebaoTraceableCodeDismountingFlagEnum, TraceableCodeListErrorType, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';
    import useChargeLock from '@/views/cashier/hooks/useChargeLock.js';
    import AbcSocket from 'views/common/single-socket';
    import { LockBusinessKeyEnum } from '@/common/constants/business-lock';
    import { isChineseMedicine } from '@/filters';
    import { CashierMeanwhilePrintPayModeEnum } from 'views/print/store';
    import Logger from 'utils/logger';
    import { ABCPrintConfigKeyMap } from '@/printer/constants';
    import { pdfLodopPrint } from '@/printer/utils';
    import { CashierPrintApi } from '@/printer/print-api/cashier';
    import {
        InvoiceBusinessScene, InvoiceCategory, InvoiceSupplierId, InvoiceViewType,
    } from 'views/cashier/invoice/constants.js';
    import { getOrigin } from 'views/settings/micro-clinic/decoration/config';
    import ShortUrlAPI from 'api/short-url';
    import ChineseStatPopover from '../components/chinese-stat-popover.vue';
    import TraceCodeLimitDialog from '@/service/trace-code/dialog-limit-trace-code';

    import MarketingAPI from 'api/marketing';
    import Big from 'big.js';

    const PrescriptionRegistrationDialog = () => import('../components/dialog-prescription-registration.vue');
    const ExtractPrescriptionDialog = () => import('../components/dialog-extract-prescription/index.vue');
    const DialogInfoPatient = () => import('views/crm/common/package-info/dialog-info-patient.vue');

    import ShebaoRestrictAPI from 'api/shebao-restrict';
    import { ShebaoPayMode } from 'views/inventory/goods/archives/components/social-code-autocomplete/constant';
    import { ShebaoPayModeByTypeEnum } from 'views/outpatient/constants';
    import { compareShebaoPayMode } from '@/views-hospital/medical-prescription/utils/config';

    import TraceCodeSelectGoodsDialog from '@/service/trace-code/dialog-trace-code-select-goods';
    import PrintManager from '@/printer/manager/print-manager';
    import CrmAPI from 'api/crm';
    export default {
        name: 'PharmacyChargeMain',
        components: {
            EmployeeSelector,
            RetailSidebar,
            QuickOperation,
            PharmacyGoodsAutocomplete,
            EmployeeDepartmentSelector,
            DiscountList,
            ChargeTableTr,
            CustomerAutocomplete,
            CooperationDetail,
            RetailVerify,
            PointsNeedOpenNoticeDialog,
            ChineseStatPopover,
            PrescriptionRegistrationDialog,
            ExtractPrescriptionDialog,
            DialogInfoPatient,
        },
        mixins: [
            enterEvent,
            keyboardEvent,
        ],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        setup() {
            const {
                isDisabledScanBarcode,
            } = useBarcodeScanner();

            const {
                lockedInfo,
                lockedTips,
                showCancelPay,
                onConfirmCancelPay,
                getLockInfo,
                setLockInfo,
            } = useChargeLock();

            return {
                isDisabledScanBarcode,
                lockedInfo,
                lockedTips,
                showCancelPay,
                onConfirmCancelPay,
                getLockInfo,
                setLockInfo,
            };
        },
        data() {
            return {
                // 记录上一个聚焦的元素
                lastSelectedChargeItemKeyId: null,
                SourceFormTypeEnum,
                needScrollToFirstError: false,
                showPointsNeedOpenNoticeDialog: false,
                showExtractPRDialog: false,
                showPrescriptionRegisDialog: false,
                showCreateMemberDialog: false,
                remindFlag: false,
                chargeSheetId: '',
                shebaoSettlePrintSheetId: '',
                chargeStatus: ChargeStatusEnum.RETAIL,
                showBatchesDialog: false,
                fromAutocomplete: false,
                calcLoading: false,
                buttonLoading: false,
                viewLoading: false,
                pageLoading: false,
                showPieceSearch: false,
                userInputFormDoseCount: 1,
                lastUserInputFormDoseCount: 1,

                editItemKeyId: null,
                curSelectedChargeItem: null,
                loading: false,
                filterParams: {
                    keyword: '',
                    patient: '',
                },
                postData: CreateCashierPostData(),
                cooperationDetail: null,
                chargeSheetSummary: CreateChargeSheetSummary(),
                verifyOutpatient: null,
                tableTdStyle: {
                    goodsName: {
                        flex: 5,
                        width: 0,
                        minWidth: '200px',
                    },
                    batchNo: {
                        flex: 2,
                        minWidth: '133px',
                        maxWidth: '133px',
                    },
                    unitCount: {
                        flex: 2,
                        minWidth: '200px',
                        maxWidth: '200px',
                    },
                    unitPrice: {
                        flex: 2,
                        minWidth: '80px',
                        maxWidth: '140px',
                        paddingLeft: '20px',
                    },
                    realPrice: {
                        flex: 3,
                        minWidth: '164px',
                        maxWidth: '200px',
                    },
                },
                customerExtendInfo: {
                    balanceTotal: '',
                    points: '',
                    cumulativeAmount: '',
                    couponsCount: '',
                },
                cacheDiagnosis: {},
                hasInterceptAliPackageLevel: false, //是否开启中码、大码拦截
                hasCodeSafeOpened: false,
                hasEnableDismountingMode: false, //是否启用拆零不采模式
                traceCodeCallbackQueue: [], // 处理采集后追溯码回调队列
            };
        },
        computed: {
            ...mapGetters([
                'chainBasic',
                'currentClinic',
                'currentRegion',
                'chargeConfig',
                'employeeList',
                'userInfo',
                'draftCashierNews',
                'traceCodeConfig',
                'allDepartmentDoctors',
                'pointsConfig',
                'coCashierTodo',
                'isChainSubStore',
            ]),
            ...mapGetters('shebaoRestrict', ['restrictSwitch']),
            isOpenSocial() {
                return this.$abcSocialSecurity.isOpenSocial;
            },
            hasPatient() {
                return !!this.postData.patient?.id;
            },
            sourcePatientInfo() {
                const {
                    sourcePatientInfo,
                } = this.cooperationDetail || {};
                return sourcePatientInfo;
            },
            // 没有开营销积分，则禁用多倍积分
            disabledMultiPoint() {
                return !this.pointsConfig.enable;
            },
            requiredSeller() {
                return this.chainBasic && !!this.chainBasic.chargeRequiredSeller;
            },
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            // 追溯码提醒
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            needTraceCodeFormItems() {
                // 避免 getTraceCodeChargeItems 方法中的排序操作影响原始数据
                const chargeFormsClone = clone(this.postData.chargeForms);
                return getTraceCodeChargeItems(chargeFormsClone);
            },
            pharmacistEmployees() {
                return this.employeeList?.filter((it) => it.roleIds?.includes(ROLE_PHARMACY_DOCTOR)) || [];
            },
            noChargeForms() {
                return this.postData.chargeForms.filter((form) => {
                    return form.chargeFormItems.length;
                }).length === 0;
            },
            // 平铺的正常收费项目
            expandChargeFormItems() {
                let formItems = [];
                this.postData.chargeForms.forEach((form) => {
                    // 非营销赠品
                    if (
                        [
                            SourceFormTypeEnum.GIFT,
                            SourceFormTypeEnum.SINGLE_PROMOTION_GIFT,
                        ].indexOf(form.sourceFormType) === -1
                    ) {
                        // 非手动赠品
                        formItems = formItems.concat(form.chargeFormItems.filter((item) => {
                            return item.isGift !== ChargeFormItemGiftType.MARKED_GIFT;
                        }));
                    }
                });
                return formItems;
            },
            // 分组的收费项目，每一组对应一个 form
            expandChargeForms() {
                // 创建分组数组
                const groupedItems = [];
                let trIndex = 0; // 添加序号计数器

                this.postData.chargeForms.forEach((form) => {
                    // 非营销赠品
                    if (
                        [
                            SourceFormTypeEnum.GIFT,
                            SourceFormTypeEnum.SINGLE_PROMOTION_GIFT,
                        ].indexOf(form.sourceFormType) === -1
                    ) {
                        // 非手动赠品
                        const filteredItems = form.chargeFormItems.filter((item) => {
                            return item.isGift !== ChargeFormItemGiftType.MARKED_GIFT;
                        });

                        // 为每个项目添加 trIndex
                        const itemsWithIndex = filteredItems.map((item) => {
                            item.trIndex = ++trIndex; // 递增序号
                            return item;
                        });

                        // 如果过滤后还有项目，则添加到分组中
                        if (itemsWithIndex.length > 0) {
                            groupedItems.push({
                                form,
                                items: itemsWithIndex,
                            });
                        }
                    }
                });

                return groupedItems;
            },
            // 中药饮片收费项目
            chineseMedicinePiecesFormItems() {
                return this.expandChargeFormItems.filter((item) => (
                    item.productInfo &&
                    item.productInfo.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES
                ));
            },
            // 中药处方-饮片类
            chineseMedicineForm() {
                const form = this.postData.chargeForms.find(($form) => $form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE);
                if (form) {
                    // 创建一个新对象，避免修改原始数据
                    return {
                        ...form,
                        chargeFormItems: form.chargeFormItems.filter((item) =>
                            item.productInfo && item.productInfo.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                        ),
                    };
                }
                return null;
            },
            // userInputFormDoseCount 已移至 data 中
            // 单剂重量（克）
            doseTotalObj() {
                const chargeFormItems = this.chineseMedicinePiecesFormItems;
                const count = this.doseTotal(chargeFormItems)?.count?.toFixed(2);
                const total = (count * this.userInputFormDoseCount).toFixed(2);
                return {
                    total,
                    str: `${this.doseTotal(chargeFormItems).kind} 味，单剂 ${count}g，合计 ${total}g`,
                };
            },
            // 营销活动产生的赠品
            promotionFormItems() {
                let formItems = [];
                this.postData.chargeForms.forEach((form) => {
                    if (
                        [
                            SourceFormTypeEnum.GIFT,
                            SourceFormTypeEnum.SINGLE_PROMOTION_GIFT,
                        ].indexOf(form.sourceFormType) > -1
                    ) {
                        formItems = formItems.concat(form.chargeFormItems);
                    }
                });
                return formItems;
            },
            // 手动标记的赠品
            markGiftFormItems() {
                const formItems = [];
                this.postData.chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        if (item.isGift === ChargeFormItemGiftType.MARKED_GIFT) {
                            formItems.push(item);
                        }
                    });
                });
                return formItems;
            },
            warningTips() {
                if (this.isPrescriptionOut) {
                    return '本单来自医保电子处方购药，销售品种、数量需遵照处方，避免医保报销风险';
                }
                return '';
            },
            oncomingGiftRulePromotionStr() {
                if (!this.postData.oncomingGiftRulePromotionStr) return '';
                const {
                    onlyOriginalPrice,
                    name,
                } = this.postData.oncomingGiftRulePromotionStr || {};

                return `差50元可参与“${name || ''}”${onlyOriginalPrice ? '（限原价购买活动商品）' : ''}`;
            },
            profitClassificationList() {
                return this.$abcPage.$store.profitClassificationList || [];
            },
            hasUpdate() {
                if (this.isCooperationOrder) return true;
                const {
                    patient,
                    chargeForms,
                    sellerId,
                } = this.postData;
                return patient?.id ||
                    chargeForms.length ||
                    sellerId;
            },
            // 处方外购
            isPrescriptionOut() {
                const {
                    type,
                } = this.postData;
                return type === ChargeSheetTypeEnum.PRESCRIPTION_OUT;
            },
            // 合作订单
            isCooperationOrder() {
                const {
                    type,
                } = this.postData;
                return type === ChargeSheetTypeEnum.COOPERATION_ORDER;
            },
            // 判断配方饮片剂量是否可编辑
            disabledChineseMedicinePiecesDose() {
                const items = this.chineseMedicinePiecesFormItems;
                if (!items || items.length === 0) {
                    return true;
                }
                // 检查所有饮片是否都可以修改数量，如果有任何一项不可修改，则禁用剂量输入
                return items.some((item) => item._disabledChangeUnitCount === true);
            },
            // 禁止添加商品
            disabledChangeGoods() {
                return this.isPrescriptionOut ||
                    this.postData.lockStatus > 1;
            },
            // 禁止修改患者
            disabledChangeCustomer() {
                return this.postData.lockStatus > 1;
            },
            // 禁止改数量
            disabledChangeUnitCount() {
                return this.isPrescriptionOut ||
                    this.postData.lockStatus > 1;
            },
            /**
             * @desc 构建包含配方饮片行的完整列表
             * @returns {Array} 包含配方饮片行的完整列表
             */
            itemsWithDoseCountTr() {
                // 获取所有商品项并按照实际的 DOM 结构构建包含配方饮片行的完整列表
                const allItems = [];

                // 先获取所有商品项
                const normalItems = [...this.expandChargeFormItems];

                // 如果没有商品项或没有配方饮片，直接返回原始商品项
                if (!normalItems.length || !this.chineseMedicinePiecesFormItems || !this.chineseMedicinePiecesFormItems.length) {
                    return normalItems;
                }

                // 如果有配方饮片，找到中药处方组的最后一个商品项的索引
                let insertIndex = normalItems.length;

                // 找到中药处方组
                const chineseMedicineGroup = this.expandChargeForms.find(
                    (group) => group.form.sourceFormType === this.SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                );

                if (chineseMedicineGroup && chineseMedicineGroup.items.length) {
                    // 找到该组最后一个商品项在 normalItems 中的索引
                    const lastItemInGroup = chineseMedicineGroup.items[chineseMedicineGroup.items.length - 1];
                    if (lastItemInGroup) {
                        insertIndex = normalItems.findIndex((item) => item.keyId === lastItemInGroup.keyId);
                        if (insertIndex !== -1) {
                            insertIndex += 1; // 插入到该项目之后
                        } else {
                            insertIndex = normalItems.length; // 如果找不到，插入到最后
                        }
                    }
                }

                // 构建包含配方饮片行的完整列表
                for (let i = 0; i < normalItems.length; i++) {
                    allItems.push(normalItems[i]);

                    // 在插入点处添加配方饮片行
                    if (i === insertIndex - 1) {
                        allItems.push({
                            keyId: 'doseCountTr',
                            name: '配方饮片',
                            isDoseCountTr: true,
                            trIndex: normalItems[i].trIndex + 0.5,
                        });
                    }
                }

                return allItems;
            },

            /**
             * @desc 合作诊所订单
             * <AUTHOR> Yang
             * @date 2024-08-14 19:21:32
            */
            cooperationOrderItems() {
                const {
                    orderItems,
                } = this.cooperationDetail || {};
                return orderItems || [];
            },
            PRRegistrationDefaultPatient() {
                const {
                    type,
                    patient,
                } = this.postData;
                if (type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                    return this.sourcePatientInfo;
                }
                return patient;
            },
            PRRegistrationDefaultDoctor() {
                const {
                    type,
                } = this.postData;
                if (type !== ChargeSheetTypeEnum.COOPERATION_ORDER) return '';
                const {
                    sourceDoctorName,
                } = this.cooperationDetail || {};
                return sourceDoctorName || '';
            },
            isNewPharmacyCashierVersion() {
                return PrintManager.getInstance().isNewPharmacyCashierVersion();
            },

        },
        watch: {
            isDisabledScanBarcode(v) {
                if (v) {
                    this.stopBarcodeDetect();
                } else {
                    this.startBarcodeDetect();
                }
            },
        },
        async created() {
            this._key = `${this.currentClinic.clinicId}_${this.currentClinic.userId}`;
            await Promise.all([
                this.$store.dispatch('initChargeConfig'),
                this.$store.dispatch('fetchPointsConfig'),
                this.$store.dispatch('shebaoRestrict/initRestrictConfig'),
            ]);
            this.fetchRemindFlag();
            this.$store.dispatch('initAllDepartmentDoctors', {
                allEmployee: 1,
            });

            // 检查URL参数中是否有chargeSheetId，用于重新收费功能
            const {
                copyChargeSheetId, chargeStatus,
            } = this.$route.query;
            if (copyChargeSheetId) {
                this.initCopyChargeSheet(copyChargeSheetId, chargeStatus);
            } else {
                this.initRetail();
            }

            this._calcFee = debounce(this.calcFee, 200, true);
            this._printOptions = this.viewDistributeConfig.Print.printOptions;
            this.hasInterceptAliPackageLevel = TraceCode.hasInterceptAliPackageLevel();
            this.hasCodeSafeOpened = TraceCode.hasCodeSafeOpened;
            this.hasEnableDismountingMode = TraceCode.hasEnableDismountingMode;

            // 设置socket监听
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._handleSocketLock = (data) => {
                this.handleOnLockSocket(data, 'lock');
            };
            this._handleSocketUnlock = (data) => {
                this.handleOnLockSocket(data, 'unlock');
            };
            this._socket.on('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.on('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },
        beforeDestroy() {
            this.stopBarcodeDetect();
            off(document, 'keydown', this.keydownHandle);
            this.createDraft();

            // 移除socket监听
            this._socket.off('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.off('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },
        mounted() {
            this._openAllCallbackQueueDialog = debounce(() => {
                if (this.traceCodeCallbackQueue.length > 0) {
                    this.processDialogQueue();
                }
            }, 300, true);
            this.startBarcodeDetect();
            on(document, 'keydown', this.keydownHandle);
        },
        methods: {
            /**
             * @desc 点击"支付遇到问题？"按钮
             * <AUTHOR> Yang
             * @date 2024-08-15
             */
            onClickCancelPay() {
                // 后台的数据可能存在有锁信息但是没有lockPayTransactionInfo
                if (!this.postData.lockPayTransactionInfo) return;
                this.onConfirmCancelPay({
                    chargePayTransactionId: this.postData.lockPayTransactionInfo.id,
                });
            },
            /**
             * @desc 处理锁单socket事件
             * <AUTHOR> Yang
             * @date 2024-08-15
             */
            async handleOnLockSocket(data, type) {
                const {
                    businessKey,
                    key: patientOrderId,
                    value,
                } = data || {};
                if (businessKey !== LockBusinessKeyEnum.CHARGE) return;
                if (this.postData.patientOrderId !== patientOrderId) return;
                if (type === 'lock') {
                    const { businessDetail } = value || {};
                    const { chargePayTransactionId } = businessDetail || {};
                    this.setLockInfo(data || null);
                    if (!this.postData.lockPayTransactionInfo) {
                        this.postData.lockPayTransactionInfo = {};
                    }
                    Object.assign(this.postData.lockPayTransactionInfo, {
                        id: chargePayTransactionId,
                    });
                } else {
                    // 解锁时重新获取数据
                    this.fetchChargeSheet(this.chargeSheetId);
                }
            },
            handleClickAddMember() {
                this.showCreateMemberDialog = true;
            },
            handlePRChange(val) {
                if (val === 'registerPrescription') {
                    this.showPrescriptionRegisDialog = true;
                } else if (val === 'extractPrescription') {
                    this.showExtractPRDialog = true;
                } else if (val === 'socialPrescriptionOut') {
                    this.handleClickSocial();
                }
            },
            handleCacheRegisterInfo(data) {
                if (this.postData.id === data.chargeSheetId) {
                    this.postData.prescriptionPDFUrls = data.prescriptionPDFUrls;
                }
            },
            handleCacheDiagnosis(data) {
                if (this.postData.id === data.chargeSheetId) {
                    this.cacheDiagnosis = data;
                }
            },
            onRegisterPrescriptionSuccess(val) {
                this.postData.registerInfoId = val;
                this.postData.isRegisteredPrescription = true;
            },
            // 医保电子处方
            async handleClickSocial() {
                if (!window.electronFlag) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '当前为浏览器环境，不能操作医保业务，请在装有医保网络的ABC客户端操作',
                    });
                    return;
                }
                const {
                    isOpenPharmacyPrescriptionOutside,
                } = this.$abcSocialSecurity;
                if (!isOpenPharmacyPrescriptionOutside) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '本店未接入医保电子处方，如需接入请向医保局提交申请，通过后联系客服接入',
                    });
                    return;
                }
                const prescriptionOutsideResult = await this.$abcSocialSecurity.pharmacyPrescriptionOutside();
                if (prescriptionOutsideResult?.data) {
                    const { chargeSheetId } = prescriptionOutsideResult.data;
                    this.fetchChargeSheet(chargeSheetId);
                }
            },
            createMemberSuccess(patient) {
                this.showCreateMemberDialog = false;
                if (this.hasPatient) return;
                this.$emit('update-patient', patient);
            },
            scrollToIndex(index, height = 0) {
                // 先检查引用是否存在
                if (this.$refs.retailTableBody?.$el) {
                    this.$refs.retailTableBody.$el.scrollTop = Math.ceil((index + 1) * 78) + height;
                }
            },
            async fetchRemindFlag() {
                try {
                    const { data } = await MarketingAPI.referrer.getRemindReadStatus('patient-points-not-enable');
                    this.remindFlag = data.created || false;
                } catch (err) {
                    console.log(err);
                }
            },
            handleTrEnter(e, item) {
                if (!this.fromAutocomplete) {
                    // 找到当前选中项的索引
                    const currentIndex = this.expandChargeFormItems.findIndex((it) => it.keyId === item.keyId);
                    if (currentIndex !== this.expandChargeFormItems.length - 1) {
                        this.setSelectedChargeItem(this.expandChargeFormItems[currentIndex + 1]);
                    }
                }
                this.enterEvent(e);
            },
            // 按追溯码查询药品对应批次数据
            async updateChargeFormItemBatchInfos(chargeItem) {
                try {
                    let originalChargeItem = chargeItem;

                    this.postData.chargeForms.forEach((form) => {
                        form.chargeFormItems.forEach((item) => {
                            if (item.keyId === chargeItem.keyId) {
                                // 找到表单实际渲染的item
                                originalChargeItem = item;
                            }
                        });
                    });

                    await this.updateChargeFormsBatchInfos([{
                        chargeFormItems: [originalChargeItem],
                    }]);

                } catch (e) {
                    console.error(e);
                }
            },
            async updateChargeFormsBatchInfos(chargeForms) {
                try {
                    const chargeFormItems = [];

                    chargeForms.map((form) => {
                        return form.chargeFormItems || [];
                    }).flat().forEach((chargeItem) => {
                        // 有追溯码数据才需要更新对应批次
                        if (chargeItem.traceableCodeList?.length) {
                            chargeFormItems.push(chargeItem);
                        } else {
                            // 其他处理。。。
                        }
                    });

                    // 没有数据，不触发查询
                    if (!chargeFormItems.length) return;

                    const chargeItemKeyIdMap = {};


                    const res = await GoodsAPIV3.getChargeFormItemBatchInfos({
                        queryGoodsList: [
                            {
                                goodsIds: chargeFormItems.map((chargeItem) => {
                                    const {
                                        productId,
                                        keyId,
                                        unitCount,
                                        unit,
                                        productInfo,
                                        traceableCodeList,
                                    } = chargeItem;
                                    let packageCount;
                                    let pieceCount;

                                    if (isChineseMedicine(productInfo)) {
                                        pieceCount = unitCount;
                                    } else {
                                        if (unit === productInfo.packageUnit) {
                                            packageCount = unitCount;
                                        } else {
                                            pieceCount = unitCount;
                                        }
                                    }

                                    // 方便后续更新批次信息
                                    chargeItemKeyIdMap[keyId] = {
                                        chargeItem,
                                        usePackageCount: !!packageCount,
                                    };

                                    return {
                                        goodsId: productId,
                                        keyId,
                                        packageCount,
                                        pieceCount,
                                        noList: traceableCodeList?.map((e) => e.no) ?? [],
                                    };
                                }),
                            },
                        ],
                    });
                    if (res?.list) {
                        res.list.forEach((item) => {
                            const {
                                chargeItem: originalChargeItem, usePackageCount,
                            } = chargeItemKeyIdMap[item.keyId] || {};

                            originalChargeItem.chargeFormItemBatchInfos = item.goodsBatchInfoList?.map((batch) => {
                                return {
                                    'unitCount': usePackageCount ? batch.cutPackageCount : batch.cutPieceCount,
                                    'batchId': batch.batchId,
                                    'batchNo': batch.batchNo,
                                    'expiryDate': batch.expiryDate,
                                    'expiredWarnFlag': batch.expiredWarnFlag,
                                };
                            }) || [];
                            originalChargeItem.isExpectedBatch = originalChargeItem.chargeFormItemBatchInfos.length ? 1 : 0;
                        });
                    }
                } catch (e) {
                    console.error('updateChargeFormsBatchInfos', e);
                }
            },
            onClickOutside() {
                const hasPriceRadioPopover = this.$refs.chargeTableTr?.some((it) => {
                    return it.$refs.priceRadioPopover?.showPopover;
                });
                if (hasPriceRadioPopover) return;
                if (document.querySelector('#adjustment-popover-wrapper')) return;
                this.editItemKeyId = null;
            },
            startBarcodeDetect() {
                if (this.startCharging) return;
                if (this._barcodeDetector) return;
                this._barcodeDetector = BarcodeDetectorV2.getInstance();
                this._barcodeDetector.startDetect(this.handleBarcode, true);
            },
            stopBarcodeDetect() {
                if (!this._barcodeDetector) return;
                this._barcodeDetector.stopDetect(this.handleBarcode);
                this._barcodeDetector = null;
            },
            handleGoodsAutocompleteEnter(e) {
                const barcode = e.target.value;
                if (isBarcode(barcode) || TraceCode.isTraceableCode(barcode,false)) {
                    this.handleBarcode(e, barcode);
                    this.$refs.pharmacyGoodsAutocomplete.clearKeyword();
                }
            },
            async handleBarcode(e, barcode) {
                const $quickOperation = this.$refs['quick-operation'];
                if ($quickOperation && $quickOperation.dialogVisible) return;
                let res = null;
                this.postData.chargeForms.forEach((form) => {
                    [
                        SourceFormTypeEnum.GIFT,
                        SourceFormTypeEnum.SINGLE_PROMOTION_GIFT,
                    ].indexOf(form.sourceFormType) === -1 && form.chargeFormItems.forEach((item) => {
                        const {
                            barCode: itemBarCode,
                        } = item.productInfo || {};
                        if (barcode && itemBarCode && itemBarCode === barcode) {
                            res = item;
                        }
                    });
                });
                // 提前找一下存在不，存在直接数量+1，不存在再去请求
                if (res) {
                    res.unitCount = +res.unitCount + 1;
                    this.focusUnitCountInput(res.keyId);
                    this._calcFee();
                    return;
                }
                const { data } = await ChargeAPI.fetchAllGoods({
                    clinicId: this.currentClinic.clinicId,
                    key: barcode,
                });
                if (barcode !== data.query.key) return;
                const goods = data.list[ 0 ];
                if (!goods) {
                    if (TraceCode.isTraceableCode(barcode)) {
                        if (this._showTraceableCodeModal) return;
                        this._showTraceableCodeModal = true;
                        await new TraceCodeSelectGoodsDialog({
                            value: true,
                            title: '请选择追溯码关联的商品',
                            keywordTraceableCodeNoInfo: data.keywordTraceableCode,
                            onConfirm: this.handleConfirmBindGoods,
                            onClose: () => {
                                this._showTraceableCodeModal = false;
                            },
                        }).generateDialogAsync({
                            parent: this,
                        });
                    } else {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: [ '该商品未建档' ],
                            onClose: () => {
                                this.focusGoodsAutocomplete();
                            },
                        });
                    }
                    return;
                }
                const {
                    noStocks,
                    stockPackageCount = 0,
                    stockPieceCount = 0,
                } = goods || {};
                if (noStocks || stockPackageCount + stockPieceCount <= 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: [ '该商品无库存' ],
                        onClose: () => {
                            this.focusGoodsAutocomplete();
                        },
                    });
                    return;
                }

                this.handleSelect({
                    goods,
                    fromScan: true,
                    keywordTraceableCode: data.keywordTraceableCode,
                });

                if (location.href.indexOf('biz-pharmacy/charge/retail') === -1) {
                    Logger.report({
                        scene: 'error_active_barcode_detect',
                        data: {
                            info: '收费扫码检测异常',
                            data: {
                                href: location.href,
                                event: e,
                            },
                        },
                    });
                    this.stopBarcodeDetect();
                }

            },

            // 药品标识码绑定goods成功后的回调
            handleConfirmBindGoods(goods, keywordTraceableCodeNoInfo) {
                this.handleSelect({
                    goods,
                    fromScan: true,
                    keywordTraceableCode: keywordTraceableCodeNoInfo,
                });
            },

            focusGoodsAutocomplete() {
                this.$refs.pharmacyGoodsAutocomplete.$el.querySelector('input').focus();
            },

            initRetail() {
                this.needScrollToFirstError = false;
                this.postData = CreateCashierPostData();
                this.chargeSheetSummary = CreateChargeSheetSummary();
                if (this.draftCashierNews.length) {
                    this.postData = clone(this.draftCashierNews[0]);
                    if (this.postData.id) {
                        this.initDataHandler(this.postData);
                        return;
                    }
                }

                this.initRoleHandler();
                this.setSelectedChargeItem(this.expandChargeFormItems[0]);
                this.initChargeService();
                this.calcFee();
                this.fetchOutpatientVerify();

                this._timer = setTimeout(() => {
                    this.$refs.postDataForm?.validate();
                }, 150);
            },

            initRoleHandler() {
                if (!this.postData.pharmacistId) {
                    // 若登录账号为药师角色，药师默认值为当前药师；若非药师记忆上次开单药师
                    if (this.userInfo?.roleIds.includes(ROLE_PHARMACY_DOCTOR)) {
                        this.postData.pharmacistId = this.userInfo.id;
                    } else {
                        const res = localStorage.get('last_selected_pharmacist', true) || '';
                        if (res) {
                            this.postData.pharmacistId = res;
                        }
                    }
                    // 药师不在药师列表中，清空药师
                    if (!this.pharmacistEmployees.some((it) => it.id === this.postData.pharmacistId)) {
                        this.postData.pharmacistId = '';
                    }
                }

                if (!this.postData.sellerId) {
                    const res = localStorage.getObj('last_selected_sellerId', this._key, true);
                    if (res) {
                        this.postData.sellerDepartmentId = res.sellerDepartmentId;
                        this.postData.sellerId = res.sellerId;
                    }
                }
            },

            createDraft() {
                if (!AbcAccess.check()) return false;
                if (!this.hasUpdate) return;
                this.$store.dispatch('SetDraft', {
                    key: 'cashier',
                    record: {
                        ...this.postData,
                        draftId: `${Date.now()}`,
                    },
                });
            },
            clearDraft() {
                this.$store.dispatch('ClearDraft', {
                    key: 'cashierAll',
                });
            },
            validateChargeItems() {
                if (this.postData.chargeForms.length === 0) {
                    if (!this._showTips) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '未添加商品',
                            onClose: () => {
                                this._showTips = false;
                            },
                        });
                        this._showTips = true;
                    }
                    return false;
                }
                return true;
            },
            keydownHandle(event) {

                const hasDialog = checkHasAbcDialog();
                if (hasDialog) return;

                if (this.isDisabledScanBarcode) return;

                const KEY_F9 = 'F9';
                const KEY_DEL = 'Delete';
                const KEY_UP = 'ArrowUp';
                const KEY_DOWN = 'ArrowDown';
                const KEY_ENTER = 'Enter';
                const KEY_F = 'KeyF';
                const KEY_H = 'KeyH';
                const KEY_Y = 'KeyY';
                const KEY_F4 = 'F4';
                const KEY_F2 = 'F2';

                const isCtrlKey = event.ctrlKey;

                const keyCode = event.code;
                if (keyCode === KEY_F9) {
                    if (this.calcLoading) return;
                    if (this.noChargeForms) return;
                    if (this._chargeDialogInstance) return;
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.handleClickCharge();
                } else if (keyCode === KEY_DEL && event.ctrlKey) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.handleClickClear();
                } else if (keyCode === KEY_DEL && !event.ctrlKey) {
                    // 单个商品删除
                    if (this.curSelectedChargeItem && !this.disabledChangeGoods) {
                        if (event.preventDefault) event.preventDefault();
                        if (event.stopPropagation) event.stopPropagation();
                        this.handleDeleteTr(this.curSelectedChargeItem);
                    }
                } else if (keyCode === KEY_ENTER) {
                    // 回车键处理：当存在选中项且当前没有聚焦到input时，默认聚焦到数量input
                    if (this.curSelectedChargeItem && document.activeElement === document.body && !this.disabledChangeUnitCount) {
                        if (event.preventDefault) event.preventDefault();
                        if (event.stopPropagation) event.stopPropagation();
                        const { keyId } = this.curSelectedChargeItem;
                        if (keyId) {
                            this.focusUnitCountInput(keyId);
                        }
                    }
                } else if (keyCode === KEY_UP || keyCode === KEY_DOWN) {
                    // 上下键切换选中状态
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();

                    const items = this.itemsWithDoseCountTr;
                    if (!items.length) return;

                    // 如果没有选中项，则选中第一个
                    if (!this.curSelectedChargeItem) {
                        this.setSelectedChargeItem(items[0]);
                        return;
                    }

                    // 找到当前选中项的索引
                    const currentIndex = items.findIndex((item) => item.keyId === this.curSelectedChargeItem.keyId);
                    if (currentIndex === -1) {
                        this.setSelectedChargeItem(items[0]);
                        return;
                    }

                    // 上键选择上一个，下键选择下一个
                    let nextIndex;
                    if (keyCode === KEY_UP) {
                        nextIndex = currentIndex > 0 ? currentIndex - 1 : 0;
                    } else {
                        nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : items.length - 1;
                    }

                    this.setSelectedChargeItem(items[nextIndex]);

                    // 如果选中的是配方饮片行，自动聚焦到剂量输入框
                    if (items[nextIndex].keyId === 'doseCountTr') {
                        this.focusDoseCountInput();
                    }

                    const { keyId } = this.curSelectedChargeItem;
                    if (document.activeElement.tagName === 'INPUT' && keyId) {
                        this.focusUnitCountInput(keyId);
                    }
                } else if (isCtrlKey && keyCode === KEY_F) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    if (!this.validateChargeItems()) {
                        return;
                    }
                    // 处方登记
                    this.showPrescriptionRegisDialog = true;
                } else if (isCtrlKey && keyCode === KEY_H) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.showExtractPRDialog = true;
                } else if (isCtrlKey && keyCode === KEY_Y) {
                    // 医保电子处方
                    if (!this.isOpenSocial) return;
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.handleClickSocial();
                } else if (keyCode === KEY_F4) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    // 加会员
                    this.handleClickAddMember();
                } else if (keyCode === KEY_F2) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    // 会员搜索聚焦
                    this.$refs.customerAutocomplete?.$refs.customerAutocomplete?.focus();
                }
            },
            handleSelectCustomer(data) {
                if (!data.patient.id) {
                    this.customerExtendInfo = {
                        balanceTotal: '',
                        points: '',
                        cumulativeAmount: '',
                        couponsCount: '',
                    };
                }
                this.postData.patient = data.patient;
                this.postData.memberInfo = data.memberInfo;
                this.postData.memberId = data.memberInfo?.patientId || '';
                this.calcFee();
                this.fetchOutpatientVerify();
                this.focusGoodsAutocomplete();
            },
            handleUpdateCustomer(data) {
                Object.assign(this.postData.patient, data);
            },
            handleCustomerExtendInfo(data) {
                Object.assign(this.customerExtendInfo, data);
            },
            /**
             * @desc 切换搜索范围（全部/饮片）并聚焦到搜索框
             * <AUTHOR>
             */
            handleSearchRangeSwitch() {
                // 切换搜索方式
                this.showPieceSearch = !this.showPieceSearch;

                // 聚焦到搜索框
                this.$nextTick(() => {
                    // 清空搜索框内容
                    this.$refs?.pharmacyGoodsAutocomplete?.clearKeyword();
                    // 聚焦到搜索框
                    this.$refs?.pharmacyGoodsAutocomplete?.focus();
                });
            },
            /**
             * @desc 快捷键聚焦到配方饮片剂量输入框
             * <AUTHOR>
             */
            handleChineseMedicineDose() {
                if (this.chineseMedicinePiecesFormItems.length === 0) {
                    this.$Toast({
                        message: '请先添加饮片后，再配中药剂数',
                        duration: 1500,
                    });
                    return;
                }
                const { activeElement } = document;
                // 检查当前聚焦元素是否在表格内或搜索框
                if (activeElement && activeElement.tagName === 'INPUT') {
                    const parentElement = document.getElementById('pharmacy-goods-autocomplete-wrapper');
                    const isFromGoodsAutoComplete = document.activeElement.closest(`#${parentElement.id}`) === parentElement;

                    if (isFromGoodsAutoComplete) {
                        this.lastSelectedChargeItemKeyId = 'isFromGoodsAutoComplete';
                    } else {
                        // 或者在 charge-table-tr || retail-table 容器内
                        const isInChargeTableTr = activeElement.closest('.charge-table-tr') !== null;
                        const isInRetailTable = activeElement.closest('.retail-table') !== null;

                        if (isInChargeTableTr || isInRetailTable) {
                            this.lastSelectedChargeItemKeyId = this.curSelectedChargeItem ? this.curSelectedChargeItem.keyId : null;
                        }
                    }
                }

                // 设置配方饮片行为选中状态
                this.setSelectedChargeItem({ keyId: 'doseCountTr' });
                this.focusDoseCountInput();
            },

            /**
             * @desc 聚焦到剂量输入框
             */
            focusDoseCountInput() {
                // 找到中药处方的索引
                const chineseMedicineFormIndex = this.expandChargeForms.findIndex(
                    (group) => group.form.sourceFormType === this.SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                );

                if (chineseMedicineFormIndex !== -1) {
                    this.$nextTick(() => {
                        const refKey = `userInputFormDoseCountRetail_${chineseMedicineFormIndex}`;
                        const inputEl = this.$refs[refKey];

                        // 处理 inputEl 可能是数组的情况
                        if (inputEl) {
                            if (Array.isArray(inputEl)) {
                                // 如果是数组，取第一个元素
                                if (inputEl[0] && typeof inputEl[0].focus === 'function') {
                                    inputEl[0].focus();
                                }
                            } else {
                                // 如果是单个元素
                                if (typeof inputEl.focus === 'function') {
                                    inputEl.focus();
                                }
                            }
                        }
                    });
                }
            },

            /**
             * @desc 处理剂量输入框聚焦
             */
            handleDoseInputFocus() {
                // 设置配方饮片行为选中状态
                this.setSelectedChargeItem({ keyId: 'doseCountTr' });
            },

            /**
             * @desc 处理剂量输入框失焦，只有实际变更才重新计算
             */
            handleDoseInputBlur() {
                this.$nextTick(() => {
                    // 只有值变化时才重新计算
                    if (this.userInputFormDoseCount !== this.lastUserInputFormDoseCount) {
                        this.handleDosageCountChange(this.userInputFormDoseCount, true);
                        this.lastUserInputFormDoseCount = this.userInputFormDoseCount;
                    }
                });
            },

            /**
             * @desc 处理剂量输入框回车事件，返回到上一个选中项或下一项
             */
            handleDoseInputEnter() {
                this.$nextTick(() => {
                    try {
                        this.curSelectedChargeItem = null;

                        const items = this.itemsWithDoseCountTr;

                        // 如果是从搜索框跳转过来，则返回搜索框
                        if (this.lastSelectedChargeItemKeyId === 'isFromGoodsAutoComplete') {
                            this.$refs?.pharmacyGoodsAutocomplete?.$el?.querySelector('input')?.focus();
                            this.lastSelectedChargeItemKeyId = null;
                            return;
                        }

                        // 如果有上一个选中项的 keyId，则返回到该项
                        if (this.lastSelectedChargeItemKeyId) {
                            const keyId = this.lastSelectedChargeItemKeyId;
                            let targetItem = null;

                            // 在列表中查找目标项
                            if (keyId === 'doseCountTr') {
                                targetItem = { keyId: 'doseCountTr' };
                            } else {
                                targetItem = items.find((item) => item.keyId === keyId);
                            }

                            if (targetItem) {
                                this.setSelectedChargeItem(targetItem);

                                // 如果是普通商品项，尝试聚焦到数量输入框
                                if (keyId !== 'doseCountTr') {
                                    this.focusUnitCountInput(keyId);
                                }

                                // 清空上一个选中项的 keyId
                                this.lastSelectedChargeItemKeyId = null;
                                return;
                            }

                            // 清空上一个选中项的 keyId
                            this.lastSelectedChargeItemKeyId = null;
                        }

                        // 如果没有商品项，则直接聚焦到搜索框
                        if (!items || items.length === 0) {
                            this.$refs?.pharmacyGoodsAutocomplete?.$el?.querySelector('input')?.focus();
                            return;
                        }

                        // 找到配方饮片行的索引
                        const doseCountTrIndex = items.findIndex((item) => item.keyId === 'doseCountTr');

                        // 如果找不到配方饮片行，则直接聚焦到搜索框
                        if (doseCountTrIndex === -1) {
                            this.$refs?.pharmacyGoodsAutocomplete?.$el?.querySelector('input')?.focus();
                            return;
                        }

                        // 如果配方饮片行后面还有商品项，则选中下一行
                        if (doseCountTrIndex < items.length - 1) {
                            const nextItem = items[doseCountTrIndex + 1];
                            this.setSelectedChargeItem(nextItem);
                            this.focusUnitCountInput(nextItem.keyId);
                        } else {
                            // 如果配方饮片行是最后一行，则聚焦到搜索框
                            this.$refs?.pharmacyGoodsAutocomplete?.$el?.querySelector('input')?.focus();
                        }
                    } catch (e) {
                        console.warn('处理剂量输入框回车事件失败', e);
                        // 如果出错，将聚点转移到搜索框
                        this.$refs?.pharmacyGoodsAutocomplete?.$el?.querySelector('input')?.focus();
                    }
                });
            },
            /**
             * @desc 参数字段的变动都必须触发init
             * <AUTHOR>
             * @date 2024-01-22 17:08:43
             */
            initChargeService() {
                this._chargeService = new AbcChargeService({
                    chargeSheetId: this.chargeSheetId,
                    chargeStatus: this.chargeStatus, // 收费状态
                    chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                    postData: this.postData, // 提交的收费单数据
                });
            },
            changeSeller(seller) {
                this.postData.sellerName = seller.name;
            },
            handleSidebarSelectGoods(goods) {
                this.handleSelect({
                    goods,
                    fromSidebar: true,
                });
            },
            async handleSelect(options) {
                const {
                    goods,
                    fromSidebar = false,
                    keywordTraceableCode,
                } = options;

                if (!goods) return false;
                if (goods.disabled) return false;
                this.fromAutocomplete = true;

                const isTraceableCode = TraceCode.isTraceableCode(keywordTraceableCode?.no);
                if (!isTraceableCode && keywordTraceableCode?.no) {
                    if (TraceCode.hasInterceptTraceCodeFormatLength()) {
                        this.$Toast({
                            type: 'error',
                            message: '药品添加成功，但无法采集追溯码（医保要求药品追溯码必须为20位或30位，且不应存在汉字或其他符号）',
                        });
                    }
                }
                const traceCodeInfo = isTraceableCode && {
                    no: keywordTraceableCode.no,
                    traceableCodeNoInfo: keywordTraceableCode.traceableCodeNoInfo,
                    traceableCodeList: keywordTraceableCode.traceableCodeList,
                    count: 1,
                };

                const chargeItem = getChargeItemStruct(goods, traceCodeInfo && {
                    traceableCodeList: [traceCodeInfo],
                });

                // 该动作不能阻塞流程
                traceCodeInfo && TraceCode.getMaxTraceCountList({
                    scene: TraceCodeScenesEnum.PHARMACY,
                    dataList: [chargeItem],
                }).then((res) => {
                    const [maxCountObj] = res;
                    const {
                        list,shebaoDismountingFlag,
                    } = maxCountObj;
                    this.postData.chargeForms.forEach((form) => {
                        form.chargeFormItems.forEach((formItem) => {
                            if (formItem.keyId === chargeItem.keyId) {
                                formItem.shebaoDismountingFlag = shebaoDismountingFlag;
                            }
                        });
                    });
                    const traceItem = list[0];
                    /**
                     * 医保拆零标志 shebaoDismountingFlag 二进制
                     * 第0位：是否允许拆零 0-不拆零 1-拆零 在10中0是第0位
                     * 第1位：是否允许编辑是否拆零 0-不允许 1-允许 在10中1是第1位
                     * 10->2 不拆零允许编辑; 11->3 拆零允许编辑;00->0 不拆零不允许编辑; 01->1 拆零不允许编辑
                     */
                    if (this.hasEnableDismountingMode && shebaoDismountingFlag === ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_NO_EDIT) {
                        //拆零不允许编辑
                        this.postData.chargeForms.forEach((form) => {
                            form.chargeFormItems.forEach((formItem) => {
                                if (formItem.keyId === chargeItem.keyId) {
                                    formItem.traceableCodeList = [];
                                }
                            });
                        });
                        this.$Toast({
                            type: 'error',
                            message: '根据医保最新要求，拆零追溯码无需采集',
                        });
                    } else if (this.hasCodeSafeOpened && [AliPackageLevelEnum.MEDIUM,AliPackageLevelEnum.BIG].includes(traceItem?.aliPackageLevel) && this.hasInterceptAliPackageLevel) {
                        this.postData.chargeForms.forEach((form) => {
                            form.chargeFormItems.forEach((formItem) => {
                                if (formItem.keyId === chargeItem.keyId) {
                                    formItem.traceableCodeList = formItem.traceableCodeList.filter((code) => code.no !== traceItem.no);
                                }
                            });
                        });
                        this.$Toast({
                            type: 'error',
                            message: `商品已添加，但本次采码为${AliPackageLevelLabel[traceItem?.aliPackageLevel]}，请在发药前检查并采集小包装上的追溯码`,
                        });
                    } else {
                        const isDismounting = TraceCode.isShouldApplyPieceUnit(traceItem);
                        traceCodeInfo.unit = chargeItem.unit;
                        traceCodeInfo.count = traceItem ? (isDismounting ? traceItem.hisPieceCount : traceItem.hisPackageCount) : 1;
                        traceCodeInfo.hisPackageCount = traceItem.hisPackageCount;
                        traceCodeInfo.hisPieceCount = traceItem.hisPieceCount;
                        traceCodeInfo.hisLeftTotalPieceCount = traceItem.hisLeftTotalPieceCount;

                        //首次采集追溯码，并且从码上放心转换规格失败的
                        if (traceItem?.newTraceCodeNo) {
                            if (!this.traceCodeCallbackQueue.some((item) => item.id === traceCodeInfo.no)) {
                                this.traceCodeCallbackQueue.push({
                                    id: traceCodeInfo.no,
                                    callback: () => {
                                        this.openLimitSetDialog(chargeItem, traceCodeInfo);
                                    },
                                });
                            }
                        }
                    }
                }).catch((e) => {
                    console.error(e);
                });
                chargeItem.unitCount = 1;
                chargeItem.retailFirstSelect = true;
                /**
                 * @desc 添加的药品能在合作处方中找到，需要覆盖数量，并且禁止修改
                 * <AUTHOR> Yang
                 * @date 2024-08-15 09:33:29
                 */
                if (this.postData.type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                    const orderItem = this.cooperationOrderItems.find((it) => goods.id === it.goodsId);
                    if (orderItem) {
                        chargeItem.unitCount = orderItem.unitCount;
                        chargeItem._disabledChangeUnitCount = true;
                    }
                }

                setChargeForm(this.postData.chargeForms, goods, chargeItem, true);

                this.handleChangePostDataSort();

                // 扫的追溯码有相关批次信息才更新批次信息
                if (keywordTraceableCode?.no && keywordTraceableCode.traceableCodeList?.length) {
                    await this.updateChargeFormItemBatchInfos(chargeItem);
                }

                this.setSelectedChargeItem(chargeItem);

                if (fromSidebar || TraceCode.hasInterceptTraceCodeFormatLength()) {
                    try {
                        const { data } = await GoodsApi.fetchGoods(chargeItem.productId, {
                            pharmacyNo: chargeItem.pharmacyNo,
                            sceneType: SearchSceneTypeEnum.outpatient,
                            departmentId: this.postData.sellerDepartmentId,
                        });
                        chargeItem.productInfo = data;
                        chargeItem.unitPrice = data.packagePrice || 0;
                        chargeItem.stockPieceCount = data.stockPieceCount || 0;
                        chargeItem.stockPackageCount = data.stockPackageCount || 0;
                        chargeItem.sanMaHeYiIdentificationCodeList = data.sanMaHeYiIdentificationCodeList ?? [];
                    } catch (err) {
                        console.error(err);
                    }
                }
                this.calcFee();
                this.fetchOutpatientVerify();
                this.focusUnitCountInput(chargeItem.keyId);
                this.$nextTick(() => {
                    if (chargeItem?.productInfo?.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES) {
                        this.scrollToIndex(this.expandChargeFormItems?.length || 0, 48);
                    } else {
                        this.scrollToIndex(this.expandChargeFormItems?.findIndex((item) => {
                            return item.id === chargeItem?.id;
                        }) || 0, 0);
                    }
                });
                this._openAllCallbackQueueDialog && this._openAllCallbackQueueDialog();
            },
            focusUnitCountInput(keyId) {
                this._focusTimer = setTimeout(() => {
                    const $input = this.$el.querySelector(`#_${keyId}`)?.querySelector('.unit-count input');
                    if ($input) {
                        $input.focus();
                        $input.selectionStart = 0;
                        $input.selectionEnd = $input.value.length;
                    }
                    this.$refs.pharmacyGoodsAutocomplete?.clearKeyword();

                    const curVM = this.$refs.chargeTableTr.find((vm) => vm.$el.id === `_${keyId}`);
                    curVM?.$refs?.unitCountFormItem.validate();
                }, 1);
            },
            getIsSelected(item) {
                if (!this.curSelectedChargeItem) return false;
                return this.curSelectedChargeItem.keyId === item.keyId;
            },
            handleSelectTableTr(item) {
                this.setSelectedChargeItem(item);

                // 如果选中的是配方饮片行，自动聚焦到剂量输入框
                if (item.keyId === 'doseCountTr') {
                    this.focusDoseCountInput();
                }
            },
            // 删除商品
            handleDeleteTr(target) {
                const deleteItem = this.handleDeleteItem(target);
                if (this.curSelectedChargeItem?.keyId === deleteItem?.keyId) {
                    this.setSelectedChargeItem(this.expandChargeFormItems[0]);
                }
                this.calcFee();
                this.fetchOutpatientVerify();
            },
            customOrderRender() {
                return (
                    <AbcTagV2 shape="square" size="small" theme="danger" variant="dark">赠</AbcTagV2>
                );
            },
            // 删除赠品
            handleDeletePromotionGift(target) {
                const deleteItem = this.handleDeleteItem(target);
                // 从列表中删除chargeItem，需要将 giftRulePromotions 中 expectedChecked 变成false
                this.postData.giftRulePromotions.forEach((gift) => {
                    gift.giftGoodItems?.forEach((it) => {
                        if (it.chargeFormItemId === deleteItem?.id) {
                            it.expectedChecked = false;
                        }
                    });
                });
                this.calcFee();
                this.fetchOutpatientVerify();
            },
            handleDeleteItem(target) {
                let deleteItem = null;
                this.postData.chargeForms = this.postData.chargeForms.filter((form) => {
                    const index = form.chargeFormItems.findIndex((item) => item.keyId === target.keyId);
                    if (index > -1) {
                        deleteItem = form.chargeFormItems[index];
                        form.chargeFormItems.splice(index, 1);
                    }
                    return form.chargeFormItems.length;
                });
                return deleteItem;
            },
            // 处理中药剂量变化
            handleDosageCountChange(inputDoseCount, needCalcFee = false) {
                if (!inputDoseCount || inputDoseCount <= 0) {
                    return;
                }

                inputDoseCount = Number(inputDoseCount);

                // 直接使用 chineseMedicinePiecesFormItems 计算属性获取中药饮片数据
                this.chineseMedicinePiecesFormItems.forEach((item) => {
                    item.doseCount = inputDoseCount;
                    item.expectedDoseCount = inputDoseCount;
                    if (item.expectedTotalPrice) {
                        item.expectedTotalPrice = null;
                        if (item.singlePromotionedUnitPrice !== +item.unitPrice) {
                            item.expectedUnitPrice = item.unitPrice;
                        }
                    }
                });

                // 更新表单上的剂量数据
                this.postData.chargeForms.forEach((form) => {
                    if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                        form.expectedDoseCount = inputDoseCount;
                        form.doseCount = inputDoseCount;
                        if (form.expectedTotalPrice) {
                            form.expectedTotalPrice = null;
                            if (form.singlePromotionedUnitPrice !== +form.unitPrice) {
                                form.expectedUnitPrice = form.unitPrice;
                            }
                        }
                        if (form.usageInfo) {
                            form.usageInfo.doseCount = inputDoseCount;
                        }
                    }
                });

                if (needCalcFee) {
                    this._calcFee();

                    this.$Toast({
                        message: '饮片剂数修改成功',
                        type: 'success',
                    });
                }
            },
            handleChangeDiscount() {
                // 单据上的优惠需要清空药品上的议价+单品优惠
                this.postData.giftRulePromotions.forEach((it) => {
                    if (it.expectedChecked) {
                        it.productItems.forEach((product) => {
                            const chargeItem = this.expandChargeFormItems.find((item) => item.keyId === product.keyId);
                            if (chargeItem) {
                                chargeItem.singlePromotions?.forEach((p) => {
                                    p.expectedChecked = false;
                                });
                                chargeItem.expectedUnitPrice = null;
                                chargeItem.expectedTotalPrice = null;
                                chargeItem.expectedTotalPriceRatio = null;
                                chargeItem.unitAdjustmentFeeLastModifiedBy = null;
                                chargeItem.unitAdjustmentFeeLastModifiedByName = null;
                            }
                        });
                    }
                });
                this.handleItemChange();
            },
            async handleItemChange(item, isBatchesChange = false) {
                console.log('handleItemChange', item, isBatchesChange);

                if (item?.traceableCodeList?.length && !isBatchesChange) {
                    await this.updateChargeFormItemBatchInfos(item);
                }
                this._calcFee();
                this.fetchOutpatientVerify();
            },

            setSelectedChargeItem(item) {
                if (this.curSelectedChargeItem?.keyId === item?.keyId) return;
                this.curSelectedChargeItem = item;
                this.editItemKeyId = null;
            },

            handleClickClear(needConfirm = true) {
                if (!this.hasUpdate) return;
                if (needConfirm) {
                    if (!this._showTips) {
                        let content = '删除后不能恢复，是否确定删除？';
                        if (this.postData.type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                            content = '删除后不可恢复，只能重新提取合作诊所处方，是否确定删除？';
                        } else if (this.postData.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT) {
                            content = '删除后不可恢复，只能重新提取医保电子处方，是否确定删除？';
                        }
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content,
                            onConfirm: () => {
                                this.clearChargeSheet();
                            },
                            onClose: () => {
                                this._showTips = false;
                            },
                        });
                        this._showTips = true;
                    }
                } else {
                    this.clearChargeSheet();
                }
            },

            async deleteServerDraftSubmit() {
                try {
                    await ChargeAPI.deleteChargeSheet(this.chargeSheetId);
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                } catch (err) {
                    console.error(err);
                }
            },
            handleUpdatePatient(patient) {
                const data = getCustomerInfo(patient);
                this.handleSelectCustomer(data);
            },
            handleUpdatePrice() {
                if (!this.curSelectedChargeItem) return;
                if (this.curSelectedChargeItem.isGift) {
                    this.$Toast({
                        message: '赠品不支持议价',
                        type: 'error',
                    });
                    return;
                }
                this.editItemKeyId = this.curSelectedChargeItem.keyId;
                this._focusTimer = setTimeout(() => {
                    const $input = this.$el.querySelector(`#_${this.editItemKeyId}`)?.querySelector('.unit-price-wrapper input');
                    if ($input) {
                        $input.focus();
                        $input.selectionStart = 0;
                        $input.selectionEnd = $input.value.length;
                    }
                }, 1);
            },
            handleUpdatePriceRatio() {
                if (!this.curSelectedChargeItem) return;
                if (this.curSelectedChargeItem.isGift) {
                    this.$Toast({
                        message: '赠品不支持改折率',
                        type: 'error',
                    });
                    return;
                }
                this.editItemKeyId = this.curSelectedChargeItem.keyId;
                this._focusTimer = setTimeout(() => {
                    const $input = this.$el.querySelector(`#_${this.editItemKeyId}`)?.querySelector('.total-price-ratio input');
                    if ($input) {
                        $input.click();
                        $input.focus();
                        $input.selectionStart = 0;
                        $input.selectionEnd = $input.value.length;
                    }
                }, 1);
            },
            handleUpdateBatches() {
                if (!this.curSelectedChargeItem) return;
                const curVM = this.$refs.chargeTableTr.find((vm) => vm.$el.id === `_${this.curSelectedChargeItem.keyId}`);
                curVM?.handleSelectBatches(this.curSelectedChargeItem);
            },
            handleSetGift() {
                if (!this.curSelectedChargeItem) return;
                if (this.cooperationOrderItems.find((it) => it.goodsId === this.curSelectedChargeItem.productId)) {
                    this.$Toast({
                        message: '合作诊所处方药品不支持设置赠品',
                        type: 'error',
                    });
                    return;
                }

                // 手动标记赠品，处理数据结构
                let curItem = null;
                this.postData.chargeForms.forEach((form) => {
                    let delIndex = -1;
                    form.chargeFormItems.forEach((item, index) => {
                        if (item.keyId === this.curSelectedChargeItem.keyId) {
                            item.isGift = ChargeFormItemGiftType.MARKED_GIFT;
                            item.unitCount = item.unitCount * (item.doseCount || 1);
                            item.doseCount = 1;
                            curItem = item;
                            delIndex = index;
                        }
                    });
                    if (delIndex > -1) {
                        form.chargeFormItems.splice(delIndex, 1);
                    }
                });

                if (curItem) {
                    let markedGiftProductForm = this.postData.chargeForms.find((form) => form.sourceFormType === SourceFormTypeEnum.MARKED_GIFT_PRODUCT);
                    if (!markedGiftProductForm) {
                        markedGiftProductForm = {
                            keyId: createGUID(),
                            sourceFormType: SourceFormTypeEnum.MARKED_GIFT_PRODUCT,
                            chargeFormItems: [],
                        };
                        this.postData.chargeForms.push(markedGiftProductForm);
                    }
                    markedGiftProductForm.chargeFormItems.push(curItem);
                }

                this.calcFee();
                this.fetchOutpatientVerify();
            },
            /**
             * @desc 药店提单、提医保电子处方流程
             * <AUTHOR> Yang
             * @date 2024-08-12 18:28:14
            */
            async handleChangePostData(data) {
                this.clearDraft();
                this.postData = CreateCashierPostData();

                this.chargeStatus = data.status > ChargeStatusEnum.UN_CHARGE ? data.status : ChargeStatusEnum.RETAIL;

                if (data.patient?.id === ANONYMOUS_ID) {
                    // 兼容
                    data.patient.id = null;
                }

                this.chargeSheetId = data.id;

                // 非配方饮片
                let newNonFormulatedChargeForm = null;
                // 配方饮片
                let newChineseChargeForm = null;

                let nonFormulatedTips = [];
                let chineseTips = [];

                const chargeForms = data.chargeForms.filter((form) => {
                    form.keyId = form.keyId || createGUID();
                    // 中药form里面如果有 非配方饮片 需要提出来新起form或者放到非配方饮片里
                    if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                        const nonFormulatedChargeFormItems = form.chargeFormItems.filter((item) => {
                            return item.productInfo?.typeId === GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES;
                        });
                        if (nonFormulatedChargeFormItems.length) {
                            nonFormulatedTips = nonFormulatedChargeFormItems.map((item) => item.name);
                            // 修正doseCount
                            nonFormulatedChargeFormItems.forEach((item) => {
                                item.unitCount = Big(item.unitCount).times(item.doseCount).toNumber();
                                item.doseCount = 1;
                            });

                            form.chargeFormItems = form.chargeFormItems.filter((item) => {
                                return item.productInfo?.typeId !== GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES;
                            });

                            const nonFormulatedChargeForm = data.chargeForms.find((f) => {
                                return f.sourceFormType === SourceFormTypeEnum.NON_FORMULATED_DECOCTION_PIECES;
                            });

                            if (nonFormulatedChargeForm) {
                                nonFormulatedChargeForm.chargeFormItems = nonFormulatedChargeForm.chargeFormItems.concat(this.clearChargeItemsId(nonFormulatedChargeFormItems));
                            } else {
                                newNonFormulatedChargeForm = {
                                    sourceFormType: SourceFormTypeEnum.NON_FORMULATED_DECOCTION_PIECES,
                                    keyId: createGUID(),
                                    chargeFormItems: this.clearChargeItemsId(nonFormulatedChargeFormItems),
                                };
                            }

                        }
                    } else if (form.sourceFormType === SourceFormTypeEnum.NON_FORMULATED_DECOCTION_PIECES) {
                        // 非配方饮片form里面如果有 配方饮片 需要提出来新起form或者放到配方饮片里
                        const chineseChargeFormItems = form.chargeFormItems.filter((item) => {
                            return item.productInfo?.typeId !== GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES;
                        });
                        if (chineseChargeFormItems.length) {
                            chineseTips = chineseChargeFormItems.map((item) => item.name);

                            form.chargeFormItems = form.chargeFormItems.filter((item) => {
                                return item.productInfo?.typeId === GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES;
                            });
                            const chineseChargeForm = data.chargeForms.find((f) => {
                                return f.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                            });
                            if (chineseChargeForm) {
                                chineseChargeFormItems.forEach((item) => {
                                    item.doseCount = chineseChargeForm.doseCount;
                                    if (item.unitCount % item.doseCount === 0) {
                                        item.unitCount = item.unitCount / item.doseCount;
                                    } else {
                                        item.unitCount = null;
                                    }
                                });
                                chineseChargeForm.chargeFormItems = chineseChargeForm.chargeFormItems.concat(this.clearChargeItemsId(chineseChargeFormItems));
                            } else {
                                newChineseChargeForm = {
                                    sourceFormType: SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                                    keyId: createGUID(),
                                    pharmacyType: chineseChargeFormItems[0].pharmacyType,
                                    pharmacyNo: chineseChargeFormItems[0].pharmacyNo,
                                    pharmacyName: chineseChargeFormItems[0].pharmacyName,
                                    doseCount: 1,
                                    usageInfo: {
                                        usage: '',
                                        dailyDosage: '',
                                        freq: '',
                                        usageLevel: '',
                                        usageDays: '',
                                        requirement: '',
                                        specialRequirement: '',
                                        payType: null,
                                    },
                                    chargeFormItems: this.clearChargeItemsId(chineseChargeFormItems),
                                };
                            }
                        }
                    }
                    return form.chargeFormItems.length;
                });

                if (newNonFormulatedChargeForm?.chargeFormItems.length) {
                    chargeForms.push(newNonFormulatedChargeForm);
                }
                if (newChineseChargeForm?.chargeFormItems.length) {
                    chargeForms.push(newChineseChargeForm);
                }
                if (data.type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                    if (nonFormulatedTips.length) {
                        const content = nonFormulatedTips.length > 3 ?
                            `${nonFormulatedTips.slice(0,2).join('、')}等${nonFormulatedTips.length}种商品` :
                            nonFormulatedTips.join('、');
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${content}的类型已从【配方饮片】修改为【非配方饮片】`,
                        });
                    }
                    if (chineseTips.length) {
                        const content = chineseTips.length > 3 ?
                            `${chineseTips.slice(0,2).join('、')}等${chineseTips.length}种商品` :
                            chineseTips.join('、');
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${content}的类型已从【非配方饮片】修改为【配方饮片】`,
                        });
                    }
                }
                if (data.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT) {
                    data.prescriptionPatient = data.patient;
                }
                if (data.useMemberFlag) {
                    const { data: memberInfo } = await CrmAPI.fetchPatientOverview(data.memberId);
                    data.prescriptionPatient = data.patient;
                    data.patient = memberInfo;
                }

                chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        item.checked = true;
                        item.keyId = item.keyId || createGUID();
                        /**
                         * @desc 合作订单 有sourceFormItemId不允许修改数量
                         * <AUTHOR> Yang
                         * @date 2024-08-14 19:56:15
                         */
                        if (data.type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                            item._disabledChangeUnitCount = !!item.sourceFormItemId;
                        }
                    });
                });
                if (data.chargeSheetSummary) {
                    this.chargeSheetSummary = data.chargeSheetSummary;
                }
                Object.assign(this.postData, data, {
                    chargeForms,
                    deliveryInfo: data.deliveryInfo || {},
                    isRegisteredIdentity: Boolean(data.registerInfoId),
                    isRegisteredPrescription: Boolean(data.registerInfoId),
                });

                // 设置锁单信息
                this.getLockInfo({
                    remote: false,
                    lockList: data.patientOrderLocks,
                });
                // 设置支付锁定信息
                if (data.lockPayTransactionInfo) {
                    this.postData.lockPayTransactionInfo = data.lockPayTransactionInfo;
                }

                this.handleChangePostDataSort();
                this.initRoleHandler();
                // chargeSheetId chargeStatus 赋值后需要重新初始化
                this.initChargeService();
                this.setSelectedChargeItem(this.expandChargeFormItems[0]);
                this.createDraft();
                this.calcFee();
                this.fetchOutpatientVerify();

                this._timer = setTimeout(() => {
                    this.$refs.postDataForm?.validate();
                }, 150);
            },

            clearChargeItemsId(chargeFormItems) {
                chargeFormItems?.forEach((item) => {
                    delete item.id;
                    item.composeChildren?.forEach((child) => {
                        delete child.id;
                    });
                    item.chargeFormItemBatchInfos?.forEach((batch) => {
                        delete batch.id;
                    });
                });
                return chargeFormItems;
            },

            /**
             * @desc 获取数据初始化
             * <AUTHOR> Yang
             * @date 2024-08-12 18:27:50
            */
            async initDataHandler(initData) {
                const {
                    id,
                    sourceId,
                } = initData;
                this.cooperationDetail = null;
                await Promise.all([
                    this.fetchChargeSheet(id, initData),
                    this.fetchCooperationDetailById(sourceId),
                ]);
            },

            /**
             * @desc 对于挂单 - 提单
             * 零售提单后需要删除收费单
             * 电子医保处方、合作诊所处方提单后需要取消挂单
             * <AUTHOR> Yang
             * @date 2024-08-13 19:42:52
            */
            async handleHangup(data) {
                const {
                    id,
                    type,
                } = data;

                this.cooperationDetail = null;
                if (type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                    await this.fetchCooperationDetailById(data.sourceId);
                    //合作诊所处方需要检查处方是否变动
                    const { isExtractDataChanged } = this.cooperationDetail || {};
                    if (isExtractDataChanged) {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            confirmText: '确定',
                            showCancel: false,
                            content: '医生对处方内容进行了修改，将重新提取最新处方内容。',
                            onConfirm: async () => {
                                this.handleExtract(this.cooperationDetail, false);
                            },
                        });
                        return;
                    }
                }
                this.handleChangePostData(data);
                if (
                    [
                        ChargeSheetTypeEnum.PRESCRIPTION_OUT,
                        ChargeSheetTypeEnum.COOPERATION_ORDER,
                    ].indexOf(type) > -1
                ) {
                    ChargeAPI.cancelDraft(id);
                }
            },
            /**
             * @desc 合作处方提单
             * <AUTHOR> Yang
             * @date 2024-08-12 18:02:05
            */
            async handleExtract(data, needWarn = true) {
                const {
                    patient,
                    chargeForms,
                } = this.postData || {};
                const hasData = patient.name || chargeForms.length;
                if (needWarn && hasData) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '原销售单未保存，提单后将丢失，确认提单吗？',
                        onConfirm: () => {
                            this.extractOrderHandler(data);
                        },
                        onClose: () => {
                            this._showTips = false;
                        },
                    });
                    return;
                }
                this.extractOrderHandler(data);
            },
            async extractOrderHandler(order) {
                try {
                    this._loading = this.$Loading({
                        text: '提单中...',
                    });
                    let res;
                    if (order.status === CooperationOrderStatus.REFUNDED || order.status === CooperationOrderStatus.CLOSED) {
                        res = await ChargeAPI.reExtractCooperationOrder(order.id);
                    } else {
                        res = await ChargeAPI.extractCooperationOrder(order.id);
                    }
                    const {
                        data,
                    } = res;
                    await this.initDataHandler({
                        id: data.id,
                        sourceId: order.id,
                    });
                } catch (e) {
                    console.error(e);
                    this.$Toast({
                        message: '提单失败',
                        type: 'error',
                    });
                } finally {
                    this._loading.close();
                    this._loading = null;
                }
            },
            clearChargeSheet() {
                this.chargeSheetId = '';
                this.chargeStatus = ChargeStatusEnum.RETAIL;
                this.editItemKeyId = null;
                this.cooperationDetail = null;
                this.clearDraft();
                this.initRetail();
            },

            /**
             * 处理 postData chargeForms 排序
             * 将中药处方放于列表末尾
             * 并给中药饮片的 item 设置 doseCount
             */
            handleChangePostDataSort() {
                try {
                    // 防止空值异常
                    if (!this.postData || !this.postData.chargeForms || !Array.isArray(this.postData.chargeForms) || this.postData.chargeForms.length === 0) {
                        return;
                    }

                    // 查找中药处方的位置
                    const index = this.postData.chargeForms.findIndex((form) =>
                        form && form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                    );

                    // 如果中药处方已经在第一个位置，则不需要排序
                    if (index === 0) {
                        return;
                    }

                    // 分离中药处方和其他处方
                    const chineseForms = this.postData.chargeForms.filter((form) =>
                        form && form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                    );

                    // 非中药处方项
                    const otherForms = this.postData.chargeForms.filter((form) =>
                        form && form.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                    );

                    // // 确保中药处方中的每个项目都设置了 doseCount
                    // chineseForms.forEach((form) => {
                    //     if (form && Array.isArray(form.chargeFormItems)) {
                    //         form.chargeFormItems.forEach((item) => {
                    //             if (item && item.productInfo && item.productInfo.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES) {
                    //                 // 确保每个中药饮片项目都有剂量设置
                    //                 if (!item.doseCount) {
                    //                     item.doseCount = this.userInputFormDoseCount || 1;
                    //                 }
                    //             }
                    //         });
                    //     }
                    // });

                    // 重新组合处方列表，中药处方放在最后
                    this.postData.chargeForms = [...chineseForms, ...otherForms];
                } catch (error) {
                    console.error('处理中药处方排序异常：', error);
                    // 发生异常时不影响后续流程
                }
            },

            /**
             * @desc 更新剂量计数值
             */
            updateUserInputFormDoseCount() {
                try {
                    // 查找中药处方
                    const form = this.postData.chargeForms.find(($form) =>
                        $form && $form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                    );

                    // 如果找到中药处方，更新剂量值
                    if (form) {
                        this.userInputFormDoseCount = form.expectedDoseCount || form.doseCount || 1;
                        this.lastUserInputFormDoseCount = this.userInputFormDoseCount;
                    }
                } catch (error) {
                    console.error('更新剂量计数值异常：', error);
                }
            },

            async calcFee() {
                if (!this._chargeService) return;
                /**
                 * @desc 锁单情况下不算费
                 * <AUTHOR> Yang
                 */
                if (this.postData.lockStatus > 1) return;
                try {
                    this.calcLoading = true;
                    await this._chargeService.calcFee(null, () => {}, {
                        needRefreshStock: true,
                        needAddGiftForm: true,
                    });

                    // 计费后更新剂量计数值
                    this.updateUserInputFormDoseCount();

                    this.calcLoading = false;
                } catch (error) {
                    this.calcLoading = false;
                    console.log('calcFee error', error);
                }
            },

            async fetchOutpatientVerify() {
                const length = (this.postData.chargeForms && this.postData.chargeForms.length) || 0;
                if (length) {
                    const verifyList = [CdssAPI.fetchOutpatientVerify(this.simplifyVerifyData())];
                    if (this.restrictSwitch) {
                        verifyList.push(ShebaoRestrictAPI.verify(this.simplifyShebaoRestrictData()));
                    }
                    const [
                        outpatientVerify,
                        shebaoVerify,
                    ] = await Promise.all(verifyList);
                    this.verifyOutpatient = outpatientVerify;
                    if (shebaoVerify) {
                        this.verifyOutpatient = {
                            ...this.verifyOutpatient,
                            ...shebaoVerify,
                        };
                    }
                }
            },
            simplifyVerifyData() {
                const chargeForms = clone(this.postData.chargeForms || []).map((form) => {
                    const formItem = {
                        ...form,
                        ...(form.usageInfo || {}),
                        prescriptionFormItems: form.chargeFormItems.map((item) => {
                            item.productInfo = item.productInfo || {};
                            const itemInfo = {
                                keyId: item.keyId || createGUID(),
                                id: item.productInfo.id || item.productId,
                                goodsId: item.productInfo.goodsId || item.productId,
                                medicineCadn: item.productInfo.medicineCadn,
                                name: item.name,
                                manufacturer: item.manufacturer,
                                ingredient: IngredientObj[item.productInfo.dangerIngredient] || '',
                                packageCount: item.unit === item.productInfo.packageUnit ? item.unitCount : null,
                                pieceCount: null,
                                goodsItem: item.productInfo,
                                ast: '',
                                usage: '',
                                ivgtt: '',
                                IvgttUnit: '',
                                days: '',
                                freq: '',
                                dosage: '',
                                dosageUnit: '',
                            };
                            if (!itemInfo.packageCount) {
                                itemInfo.pieceCount = item.unitCount;
                            }
                            return itemInfo;
                        }),
                    };
                    delete formItem.usageInfo;
                    delete formItem.chargeFormItems;
                    return formItem;
                });
                return {
                    isControlledSubstances: false, // 北京网诊
                    patient: this.postData.patient,
                    region: this.currentRegion,
                    prescriptionWesternForms: chargeForms.filter((form) => form.sourceFormType !== 6),
                    prescriptionInfusionForms: [],
                    prescriptionChineseForms: chargeForms.filter((form) => form.sourceFormType === 6),
                };
            },
            simplifyShebaoRestrictData() {
                const chargeForms = clone(this.postData.chargeForms || []).map((form) => {
                    const formItem = {
                        ...form,
                        ...(form.usageInfo || {}),
                        prescriptionFormItems: form.chargeFormItems.map((item) => {
                            const itemInfo = {
                                chargeStatus: item.chargeStatus,
                                days: item.days,
                                dosage: item.dosage,
                                dosageUnit: item.dosageUnit,
                                freq: item.freq,
                                keyId: item.keyId || createGUID(),
                                name: item.medicineCadn || item.name,
                                shebaoCode: item.productInfo?.shebaoNationalCode,
                                goodsShebaoPayMode: item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                                shebaoPayMode: ShebaoPayModeByTypeEnum[item.payType] || item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                                usage: item.usage,
                                type: item.productType,
                                subType: item.productSubType,
                                payType: item.payType,
                                unitCount: item.unitCount,
                                unit: item.unit,
                                doseCount: item.doseCount,
                                usageInfo: item.usageInfo || {
                                    payType: item.payType,
                                },
                                verifySignatures: (item.usageInfo?.verifySignatures || []).map((one) => one.type),
                            };
                            if (!itemInfo.packageCount) {
                                itemInfo.pieceCount = item.unitCount;
                            }
                            if (itemInfo.usageInfo.payType) {
                                itemInfo.shebaoPayMode = ShebaoPayModeByTypeEnum[itemInfo.usageInfo.payType];
                            }
                            return itemInfo;
                        }),
                    };
                    delete formItem.usageInfo;
                    delete formItem.chargeFormItems;
                    return formItem;
                });

                return {
                    departmentId: '',
                    patient: this.postData.patient,
                    prescriptionWesternForms: chargeForms.filter((form) => form.sourceFormType !== 6),
                    prescriptionInfusionForms: [],
                    prescriptionChineseForms: chargeForms.filter((form) => form.sourceFormType === 6),
                    productForms: [],
                };
            },
            async confirm(flag = false) {
                if (flag) {
                    await MarketingAPI.referrer.updateRemindFlog('patient-points-not-enable');
                    await this.fetchRemindFlag();
                }
                this.preSubmitValidate();
            },

            handleClickCharge() {
                if (this.chargeStatus > ChargeStatusEnum.UN_CHARGE) {
                    this.$alert({
                        type: 'warn',
                        title: '零售单已结账',
                        content: '请在历史零售单查看，当前内容将自动清空',
                        onClose: () => {
                            this.handleClickClear(false);
                        },
                    });
                    return;
                }
                this.needScrollToFirstError = true;
                this.$nextTick(() => {
                    this.$refs.postDataForm?.validate(async (valid) => {
                        if (valid) {
                            // 未开启积分 为设置不需要弹提示 有顾客 顾客是会员
                            if (this.disabledMultiPoint && !this.remindFlag && this.hasPatient && this.postData.patient?.isMember) {
                                this.showPointsNeedOpenNoticeDialog = true;
                            } else {
                                this.preSubmitValidate();
                            }
                        }
                    });
                });
            },
            async hangUpOrder() {
                this.hangUpOrderConfirm();
            },

            hangUpOrderConfirm() {
                if (this._showTips) return;
                this._showTips = true;
                this.$confirm({
                    type: 'warn',
                    title: '确定挂单？',
                    content: '收费单挂单后会进入挂单列表，以便随时调出。',
                    onConfirm: () => {
                        this.hangUpOrderSubmit();
                    },
                    onClose: () => {
                        this._showTips = false;
                    },
                });
            },

            async hangUpOrderSubmit() {
                try {
                    if (this.buttonLoading) return;
                    this.buttonLoading = true;

                    this.postData.chargeForms.forEach((form, PIndex) => {
                        form.sort = PIndex;
                        form.chargeFormItems.forEach((item, index) => {
                            item.sort = index;
                        });
                    });
                    const postData = clone(this.postData);
                    if (postData.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT) {
                        if (postData.patient.id !== postData.prescriptionPatient?.id) {
                            postData.useMemberFlag = 10;
                            postData.memberId = postData.patient.id;
                            postData.patient = postData.prescriptionPatient;
                        }
                        delete postData.prescriptionPatient;
                    }
                    await ChargeAPI.hangUpOrder({
                        id: this.chargeSheetId,
                        ...postData,
                    });
                    this.$Toast({
                        message: '挂单成功',
                        type: 'success',
                    });
                    this.clearChargeSheet();
                } catch (err) {
                    console.error(err);
                } finally {
                    this.buttonLoading = false;
                }
            },
            async handleOpenTraceCodeDialog() {
                if (this.needTraceCodeFormItems.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '本单项目均无需采集追溯码',
                    });
                    return;
                }
                await this.asyncCollectionTraceCodeDialog();
            },
            async asyncTraceCodeConfirm(res) {
                // eslint-disable-next-line no-async-promise-executor
                return new Promise(async (resolve) => {
                    this.$confirm({
                        type: 'warn',
                        title: '风险提醒',
                        content: () => {
                            return <abc-flex vertical>
                                {
                                    res.errorList.map((it) => {
                                        const {
                                            count,
                                            warnTips,
                                        } = it;
                                        return <abc-flex gap={4}>
                                            <abc-text theme="warning-light" size="normal"
                                                      bold>{count}</abc-text>
                                            <abc-text theme="black" size="normal">个商品{warnTips}</abc-text>
                                        </abc-flex>;
                                    })
                                }
                            </abc-flex>;
                        },
                        confirmText: '去修改',
                        cancelText: '仍要提交',
                        disabledKeyboard: true,
                        showClose: false,
                        onConfirm: async () => {
                            await this.asyncCollectionTraceCodeDialog({
                                needInitValidate: false,
                            });
                            resolve();
                        },
                        onCancel: async () => {
                            resolve();
                        },
                    });
                });
            },
            async asyncCollectionTraceCodeDialog(options = {}) {
                this.stopBarcodeDetect();
                // eslint-disable-next-line no-async-promise-executor
                return new Promise(async (resolve, reject) => {
                    this.buttonLoading = true;
                    this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                        formItems: this.needTraceCodeFormItems,
                        requiredTraceCode: !!this.traceCodeCollectionCheck,
                        confirmText: options.confirmText || '确定',
                        needInitValidate: options.needInitValidate,
                        confirmInnerSet: false,
                        sceneType: SceneTypeEnum.CHARGE,
                        patientOrderId: this.postData?.patientOrderId ?? '',
                        isSelectedSocialPay: !!options.isSelectedSocialPay,
                        onConfirm: (flatFormItems,action) => {
                            TraceCode.setChargeFormsTraceCodeList(flatFormItems, this.postData.chargeForms);
                            if (action === 'tempSave') {
                                reject();
                            } else {
                                resolve();
                            }
                            console.log('追溯码保存，触发更新批次操作');
                            this.updateChargeFormsBatchInfos(this.postData.chargeForms);
                        },
                        onClose: () => {
                            reject();
                            this.startBarcodeDetect();
                        },
                    });
                    await this._collectionTraceCodeDialog.generateDialogAsync();
                    this.buttonLoading = false;
                });
            },
            preSubmitValidate() {
                this.$refs.postDataForm?.validate(async (valid) => {
                    if (valid) {
                        const {
                            noGoodsInfoItems = [],
                            disabledItems = [],
                            shortageItems = [],
                            unPricedItems = [],
                        } = getWarnChargeItemGroup(this.postData.chargeForms);
                        if (noGoodsInfoItems.length > 0 || disabledItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品未创建或者已被停用：',
                                content: [`<span>${noGoodsInfoItems.concat(disabledItems).map((item) => {
                                    return item.name || item.medicineCadn;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }
                        if (shortageItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品库存不足：',
                                content: [`<span>${shortageItems.map((item) => {
                                    let str = '';
                                    if (item.parentName) {
                                        str += `【${item.parentName}】`;
                                    }
                                    str += item.name || item.medicineCadn;
                                    return str;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }
                        if (unPricedItems.length > 0) {
                            const itemName = unPricedItems[0].name || unPricedItems[0].medicineCadn;
                            const content = unPricedItems.length > 0 ?
                                `${itemName}等${unPricedItems.length}种商品未定价` :
                                itemName;
                            this.$alert({
                                type: 'warn',
                                title: '商品未定价',
                                content,
                            });
                            return false;
                        }

                        const {
                            containOTC,
                            containMHJ,
                            containMHJOverCount,
                        } = getPharmacyWarnChargeItemGroup(this.postData.chargeForms);

                        if (containMHJOverCount.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '合规提醒',
                                content: [
                                    '以下含麻黄碱类复方制剂，一次性销售不得超过 2 个最小包装',
                                    `<div style="margin-top: 8px;color: var(--abc-color-T2)">${containMHJOverCount.map((item) => {
                                        return item.name || item.medicineCadn;
                                    }).join('、')}</div>`,
                                ],
                            });
                            return false;
                        }

                        if (!this._noTipOTC && containOTC.length > 0 && !this.postData.isRegisteredPrescription) {
                            if (!this._showTips) {
                                this.$confirm({
                                    type: 'warn',
                                    title: '处方药销售登记提醒',
                                    content: '销售商品中包含处方药，是否登记处方',
                                    showClose: false,
                                    confirmText: '立即登记',
                                    cancelText: '收费后补处方',
                                    onConfirm: () => {
                                        this._noTipOTC = false;
                                        this._noTipMa = false;
                                        this.showPrescriptionRegisDialog = true;
                                    },
                                    onCancel: () => {
                                        this._noTipOTC = true;
                                        this._showTips = false;
                                        this.preSubmitValidate();
                                    },
                                    onClose: () => {
                                        this._showTips = false;
                                    },
                                });
                                this._showTips = true;
                            }
                            return false;
                        }
                        if (!this._noTipMa && containMHJ.length > 0 && !this.postData.isRegisteredIdentity) {
                            if (!this._showTips) {
                                this.$confirm({
                                    type: 'warn',
                                    title: '提示',
                                    content: '销售商品中存在含麻黄碱药，是否进行实名登记？',
                                    showClose: false,
                                    confirmText: '继续收费',
                                    cancelText: '实名登记',
                                    onConfirm: () => {
                                        this._noTipMa = true;
                                        this._showTips = false;
                                        this.preSubmitValidate();
                                    },
                                    onCancel: () => {
                                        this._noTipOTC = false;
                                        this._noTipMa = false;
                                        this.$refs['quick-operation'].onClickRegisterIdentity();
                                    },
                                    onClose: () => {
                                        this._showTips = false;
                                    },
                                });
                                this._showTips = true;
                            }
                            return false;
                        }
                        this._noTipOTC = false;
                        this._noTipMa = false;
                        this.initiateCharge();
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            onCloseChargeDialog() {
                this.startCharging = false;
                this.startBarcodeDetect();
            },
            onBeforeClose(callback) {
                this._chargeDialogInstance = null;
                this.startBarcodeDetect();
                if (this.chargeStatus === ChargeStatusEnum.PART_CHARGED) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '本单未结清，确认离开吗？离开后可在零售单内补结',
                        closeAfterConfirm: true,
                        onConfirm: () => {
                            this.clearChargeSheet();
                            callback(true);
                        },
                    });
                } else {
                    callback(true);
                }
            },
            async validateTraceCodeFn(isSelectedSocialPay = false) {
                try {
                    if (this.traceCodeCollectionCheck && this.needTraceCodeFormItems?.length) {
                        const res = await TraceCode.validate({
                            scene: TraceCodeScenesEnum.PHARMACY,
                            sceneType: SceneTypeEnum.PHARMACY_CHARGE,
                            dataList: TraceCode.getFlatItems(this.needTraceCodeFormItems),
                            needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckPharmacy(),
                            patientOrderId: this.postData?.patientOrderId ?? '',
                        });
                        if (!res.flag) {
                            // 追溯码超上限需要提醒
                            const findCodeExceedLimit = res.errorList?.find((error) => error.warnType === TraceableCodeListErrorType.CODE_EXCEED_LIMIT);
                            if (findCodeExceedLimit) {
                                await this.asyncTraceCodeConfirm(res);
                            } else {
                                await this.asyncCollectionTraceCodeDialog({
                                    needInitValidate: false,
                                    isSelectedSocialPay,
                                });
                            }
                        }
                    }
                    return true;
                } catch (e) {
                    console.error(e);
                    return false;
                }
            },
            async initiateCharge() {
                const {
                    shortageItems = [],
                } = getWarnChargeItemGroup(this.postData.chargeForms);

                // 查询诊断，如果有处方登记，查询处方的诊断
                if (this.postData.registerInfoId) {
                    const { prescriptionView = {} } = await ChargeAPI.getRegisterInfo(this.postData.registerInfoId);
                    this.postData.extendDiagnosisInfos = prescriptionView.diagnosis || [{
                        toothNos: null, value: [],
                    }];
                }

                this._chargeDialogInstance = new AbcChargeDialog({
                    pcRouterVm: this.$router,
                    pcStoreVm: this.$store,
                    hiddenPayModeList: [
                        PayModeEnum.ARREARS,
                    ],
                    adjustmentFormatLength: 2,
                    needLockInventory: false,
                    disableDispensingBtn: true,
                    disableAdjustmentBtn: true,
                    chargeSheetId: this.chargeSheetId,
                    chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                    postData: this.postData, // 提交的收费单数据
                    chargeStatus: this.chargeStatus || ChargeStatusEnum.RETAIL,
                    scene: 'pharmacy-housekeeper',
                    isNeedCheckDispensingBatch: true,
                    onPartChargeSuccess: this.partChargeSuccess,
                    onChargeSuccess: this.chargeSuccess,
                    onChargeError: this.chargeError,
                    onBeforeClose: this.onBeforeClose,
                    cancelPayCallback: this.cancelPayCallback,
                    onClose: this.onCloseChargeDialog,
                    validateTraceCodeFn: this.validateTraceCodeFn,
                });

                // 需要提示的商品：没有勾选的，库存不足的（包含套餐中的）
                if (shortageItems.length > 0) {
                    let arr = [];
                    if (this.needCheckStock) {
                        arr = arr.concat(shortageItems);
                    }
                    // 列表为空，重置状态
                    if (arr.length <= 0) {
                        this.startCharging = true;
                        this.stopBarcodeDetect();
                        this._chargeDialogInstance.generateDialog({
                            parent: this,
                        });
                        return false;
                    }
                    new ChargeNoticeDialog({
                        data: arr,
                        confirm: () => {
                            this.startCharging = true;
                            this.stopBarcodeDetect();
                            this._chargeDialogInstance.generateDialog({
                                parent: this,
                            });
                        },
                    }).generateDialogAsync({
                        parent: this,
                    });
                } else {
                    this.startCharging = true;
                    this.stopBarcodeDetect();
                    this._chargeDialogInstance.generateDialog({
                        parent: this,
                    });
                }
            },

            cancelPayCallback() {
                this.$message({
                    type: 'warn',
                    title: '收费失败',
                    size: 'small',
                    content: ['支付已取消，前往销售单可继续收费'],
                    showFooter: true,
                    showConfirm: true,
                    onConfirm: () => {
                        const { chargeSheetId } = this;
                        this.clearChargeSheet();
                        this.closeChargeDialog();
                        this.$router.push({
                            name: PharmacyChargeRouterNameKeys.salesRecord,
                            query: {
                                chargeSheetId,
                            },
                        });
                    },
                    confirmText: '继续收费',
                    onCancel: () => {
                        this.closeChargeDialog();
                        this.clearChargeSheet();
                    },
                    cancelText: '关闭，稍后支付',
                });
            },

            closeChargeDialog() {
                if (this._chargeDialogInstance) {
                    this._chargeDialogInstance.destroyDialog();
                    this._chargeDialogInstance = null;
                }
            },

            async partChargeSuccess(data) {
                this.chargeSheetId = data.id;
                this.chargeStatus = data.status;
                this.postData.lockStatus = data.lockStatus;
                this.initChargeService();
                localStorage.set('last_selected_pharmacist', this.postData.pharmacistId);
                localStorage.setObj('last_selected_sellerId', this._key, {
                    sellerDepartmentId: this.postData.sellerDepartmentId,
                    sellerId: this.postData.sellerId,
                });
            },

            /**
             * desc [完成收费]
             */
            async chargeSuccess(data) {
                if (data && data.status !== undefined) {
                    this.chargeStatus = data.status;
                }
                this._chargeDialogInstance = null;
                this.partChargeSuccess(data);
                const { cache } = Printer;
                const printCache = cache.get();

                const chargeSheetId = data.id || this.chargeSheetId;
                this.shebaoSettlePrintSheetId = data.shebaoSettlePrintSheetId; // 社保结算单打印 id

                // 此次是否同时打印
                const {
                    directSmallNeedPrint,
                } = printCache;
                if (directSmallNeedPrint) {
                    this.print(chargeSheetId, printCache);
                    cache.set({
                        directSmallNeedPrint: false,
                    });
                }
                if (this.restrictSwitch) {
                    this.handleUpdateShebaoRestrictResult();
                }

                this.clearChargeSheet();
            },

            chargeError(err) {
                const {
                    code,
                } = err;
                if (code === 17208) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        showCancel: false,
                        content: '该订单已完成收费。确定后清空',
                        onConfirm: this.clearChargeSheet,
                    });
                } else if (code === 17353) {
                    this.clearChargeSheet();
                }

                // 如果单据已经部分收费的情况下，收费失败需要清空草稿
                if (this.chargeStatus === ChargeStatusEnum.PART_CHARGED) {
                    this.clearChargeSheet();
                }
            },

            async print(chargeSheetId, printCache) {
                const selecteds = [...printCache.cashier];

                // 重新拉取收费单，获取支付信息
                const res = await ChargeAPI.fetch(chargeSheetId);
                this.shebaoSettlePrintSheetId = res?.data?.shebaoSettlePrintSheetId ?? ''; // 社保结算单打印 id

                if (selecteds.includes(this._printOptions.CASHIER.label) || selecteds.includes(this._printOptions.PHARMACY_CASHIER.label) || selecteds.includes(this._printOptions.PHARMACY_CASHIER_V2.label)) {
                    // 仅医保支付，时才同时打印收费小票
                    if (printCache.cashierMeanwhilePrintPayMode === CashierMeanwhilePrintPayModeEnum.shebao) {
                        // 判断有医保支付
                        if (res?.data?.chargeTransactions && res.data.chargeTransactions.some((x) => x.payMode === PayModeEnum.SOCIAL_CARD)) {
                            await this.printCashierSheet(chargeSheetId);
                        }
                    } else {
                        await this.printCashierSheet(chargeSheetId);
                    }
                }

                if (selecteds.includes('医保结算单') && this.shebaoSettlePrintSheetId) {
                    await this.printSocialSettlementSheet();
                }
            },
            async printSocialSettlementSheet(uuid) {
                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_打印医保结算单',
                        },
                    });
                }

                const query = {
                    chargeSheetId: this.shebaoSettlePrintSheetId,
                    isPharmacySettle: true,
                };

                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_打印医保结算单接口参数',
                            query: clone(query),
                        },
                    });
                }

                const printResponse = await this.$abcSocialSecurity.settlementSheetPrint(query);

                console.log('printResponse:', printResponse);
                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_打印医保结算单接口返回值',
                            response: printResponse,
                        },
                    });
                }

                if (printResponse.status === false) {
                    return this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: printResponse.message || '调用结算单打印出错',
                    });
                }
                const {
                    html,
                    pdfBase64,
                    printConfigKey,
                } = printResponse.data || {};

                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_医保结算单HTML',
                            html,
                        },
                    });
                }

                if (html) {
                    await AbcPrinter.abcPrint({
                        templateKey: window.AbcPackages.AbcTemplates.medicalFeeSocial,
                        printConfigKey: ABCPrintConfigKeyMap.social,
                        data: {},
                        extra: {
                            // 移除医保传递的外部div
                            // 避免分页的问题
                            getHTML: () => html.replace('<div class="print-stat-wrapper">', '').replace(/<\/div>$/, ''),
                        },
                    }, uuid);
                } else if (pdfBase64) {
                    await pdfLodopPrint({
                        pdfBase64,
                        printConfigKey,
                    });
                } else {
                    //
                }
            },

            // 打印收费小票
            async printCashierSheet(chargeSheetId) {
                const select = this._printOptions.PHARMACY_CASHIER.label;
                const printData = await this.fetchPrintData(chargeSheetId, select);
                if (this.isNewPharmacyCashierVersion) {
                    await CashierPrintApi.printEscPosCashier(printData);
                } else {
                    await CashierPrintApi.printPharmacyCashier(printData);
                }
            },
            async fetchPrintData(chargeSheetId, printType) {
                let printData = null;
                const isPrintQrcode = true;
                const printLoading = this.$Loading({
                    text: '准备打印...',
                    customClass: 'print-loading-wrapper',
                });
                try {
                    // 收费小票
                    if (printType === this._printOptions.PHARMACY_CASHIER.label) {
                        const { data } = await ChargeAPI.fetchChargePrint(chargeSheetId);
                        // 构造电子发票链接二维码
                        const {
                            type,
                            digitalInvoice,
                            invoiceCategory,
                            invoiceSupplierId,
                        } = data?.invoiceView || {};
                        const invoiceImageUrl = digitalInvoice?.invoiceImageUrl; // 发票url
                        const clinicId = this.currentClinic?.clinicId || data.organ.id;

                        // 构造电子发票链接二维码
                        if (invoiceImageUrl && type !== InvoiceViewType.RED && invoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC && invoiceSupplierId === InvoiceSupplierId.FUJIAN_BOSI) {
                            data.invoiceQrcode = await QRCode.toDataURL(invoiceImageUrl, { margin: 0 });
                        } else {
                            const path = invoiceImageUrl && type !== InvoiceViewType.RED ? 'invoice-preview' : 'view-invoice';
                            const fullUrl = `${getOrigin()}/mp/${path}?clinicId=${clinicId}&businessScene=${InvoiceBusinessScene.CHARGE}&businessId=${data.id}`;

                            const { data: shortUrlData } = await ShortUrlAPI.createShortUrl({
                                fullUrl,
                            });
                            const QrcodeStr = shortUrlData.shortUrl;
                            data.invoiceQrcode = await QRCode.toDataURL(QrcodeStr, { margin: 0 });
                        }
                        printData = data;
                    }

                    if (printData && isPrintQrcode) {
                        // 二维码
                        const qrCode = await this.fetchQrCode();
                        const { printData: data } = printData;
                        printData.qrCode = qrCode;
                        if (data) {
                            data.qrCode = qrCode;
                        }
                    }
                    return printData;
                } catch (e) {
                    console.error(e);
                } finally {
                    printLoading.close();
                }
            },
            async fetchQrCode() {
                let qrcode = '';
                if (this.isOpenMp) {
                    try {
                        qrcode = await TpsAPI.genQrCode(this._patientOrderId);
                        return qrcode;
                    } catch (e) {
                        qrcode = '';
                    }
                }
                return qrcode;
            },
            async fetchChargeSheet(chargeSheetId, initData) {
                try {
                    const { data } = await ChargeAPI.fetch(chargeSheetId);
                    if (chargeSheetId !== data.id) return;
                    this.handleChangePostData(data);
                } catch (e) {
                    //  如果该单据被删除了，这儿需要重置
                    delete initData.id;
                    initData.status = ChargeStatusEnum.RETAIL;
                    this.handleChangePostData(initData);
                }
            },

            /**
             * @desc 初始化复制收费单数据
             * <AUTHOR> Yang
             * @date 2025-04-16
             * @param {string} chargeSheetId 收费单ID
             */
            async initCopyChargeSheet(chargeSheetId, chargeStatus) {
                try {
                    // 调用API获取复制的收费单数据
                    this.pageLoading = true;
                    let data = null;
                    if (chargeStatus === ChargeStatusEnum.CLOSED) {
                        const { data: openData } = await ChargeAPI.openChargeSheet(chargeSheetId);
                        data = openData;
                        delete data.id;
                    } else if (chargeStatus === ChargeStatusEnum.UN_CHARGE || chargeStatus === ChargeStatusEnum.PART_CHARGED) {
                        const { data: detailData } = await ChargeAPI.fetch(chargeSheetId);
                        data = detailData;
                    } else {
                        const { data: copyData } = await ChargeAPI.copyChargeSheet(chargeSheetId);
                        data = copyData;
                        delete data.id;
                    }
                    // 初始化数据
                    data.patient = data.patient || {
                        id: null,
                        name: '',
                        mobile: '',
                        sex: '男',
                        age: {
                            year: null,
                            month: null,
                            day: null,
                        },
                    };
                    this.handleChangePostData(data);
                } catch (e) {
                    console.error('获取复制收费单数据失败', e);
                    // 失败时初始化为普通零售
                    this.initRetail();
                } finally {
                    this.pageLoading = false;
                    // 移除URL中的chargeSheetId参数
                    this.$router.replace({
                        query: {},
                    });
                }
            },
            async fetchCooperationDetailById(coOrderId) {
                if (!coOrderId) return;
                const { data } = await ChargeAPI.fetchCooperationDetailById(coOrderId);
                if (coOrderId !== data.id) return;
                this.cooperationDetail = data;
            },
            /**
             * @desc 配方饮片计算克重
             */
            doseTotal(chargeFormItems) {
                let count = 0;
                const kinds = [];
                if (chargeFormItems.length) {
                    chargeFormItems.forEach((item) => {
                        if (item.unit === 'g') {
                            count += (item.unitCount * 10000) || 0;
                        }
                        if (kinds.indexOf(item.productId) === -1) {
                            kinds.push(item.productId);
                        }
                    });
                }
                return {
                    kind: kinds.length,
                    count: count / 10000,
                };
            },
            async handleUpdateShebaoRestrictResult() {
                const {
                    medicateVerifyLists,
                    behaviorVerifyLists,
                } = this.verifyOutpatient;
                const arrDanger = [], arrWarn = [];
                medicateVerifyLists.concat(behaviorVerifyLists).forEach((item) => {
                    if (item.verifyDetails) {
                        item.verifyDetails.forEach((it) => {
                            if (it.level === 'DANGER') {
                                arrDanger.push({
                                    ...it,
                                    dealType: item.dealType,
                                });
                            } else if (it.level === 'WARN') {
                                arrWarn.push({
                                    ...it,
                                    dealType: item.dealType,
                                });
                            }
                        });
                    }
                });
                const dangerWarnArr = arrDanger.concat(arrWarn);
                const restrictCount = dangerWarnArr.length;
                const restrictSettingCount = dangerWarnArr.filter((it) => compareShebaoPayMode(it.shebaoPayMode, it.goodsShebaoPayMode)).length;
                if (!restrictCount) return;

                const data = {
                    businessId: this.chargeSheetId,
                    restrictRuleResultViews: [{
                        businessId: this.chargeSheetId,
                        businessType: 3,
                        restrictCount,
                        restrictSettingCount,
                        restrictInfo: JSON.stringify({
                            keyId: this.chargeSheetId,
                            dangerWarnArr,
                        }),
                    }],
                };
                await ShebaoRestrictAPI.updateVerifyResult(data);
            },
            // 初始化入库后回调
            async initStockInCallback() {
                await this.calcFee();
                this.needScrollToFirstError = false;
                this.$nextTick(() => {
                    this.$refs.postDataForm?.validate();
                });
            },

            openLimitSetDialog(traceCodeUseInfo, code) {
                return new Promise((resolve) => {
                    new TraceCodeLimitDialog({
                        title: '请确认追溯码可用上限',
                        isShowTips: false,
                        showClose: false,
                        isShowLeftCountSet: true,
                        traceCodeInfo: code,
                        goods: traceCodeUseInfo.productInfo,
                        onConfirm: () => {
                            resolve();
                        },
                        onClose: () => {
                            //取消本次采集 移除当前码
                            this.postData.chargeForms.forEach((form) => {
                                form.chargeFormItems.forEach((formItem) => {
                                    if (formItem.keyId === traceCodeUseInfo.keyId) {
                                        formItem.traceableCodeList = formItem.traceableCodeList.filter((c) => c.no !== code.no);
                                    }
                                });
                            });
                            resolve();
                        },
                    }).generateDialogAsync({ parent: this });
                });
            },
            // 添加一个新的队列处理方法
            async processDialogQueue() {
                if (this.traceCodeCallbackQueue.length === 0) {
                    return;
                }

                // 取出队列中的第一个回调并执行
                const queueItem = this.traceCodeCallbackQueue.shift();
                if (queueItem && typeof queueItem.callback === 'function') {
                    await queueItem.callback();
                }

                // 处理队列中的下一个回调
                this.processDialogQueue();
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common";
    @import "~styles/mixin.scss";

    .charge-retail-wrapper {
        flex: 1;
        height: 0;
        padding: 0;

        .charge-retail-content {
            padding: 0;
            background-color: #ffffff;
            border-radius: 0 0 0 var(--abc-container-border-radius);

            .chinese-medicine-pieces {
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .chinese-medicine-pieces:hover {
                background-color: var(--abc-color-B7);
            }

            .chinese-medicine-pieces.is-selected {
                background-color: var(--abc-color-B7);
            }
        }

        .charge-retail-sidebar {
            padding: 0;
            border-left: 1px solid $P7;
            border-radius: 0 8px 8px 0;
        }

        .retail-table {
            .abc-table-body .table-cell {
                height: 100%;
                padding-top: 16px;
                padding-bottom: 16px;
            }

            .retail-verify {
                width: 100%;
            }

            .group-charge-form {
                border-bottom: 1px solid var(--abc-color-P10);

                .biz-pharmacy-charge-table-tr:last-child {
                    border-bottom: none !important;
                }
            }

            /* 当有中药饮片存在时，为最后一个中药处方分组设置特定的底部边框样式 */
            .group-charge-form.has-chinese-medicine {
                border-bottom: 1px solid var(--abc-color-P7);
            }

            .group-charge-form:not(:first-of-type) {
                .biz-pharmacy-charge-table-tr:first-child {
                    border-top: none;
                }
            }

            /* .group-charge-form:last-of-type {
                border-bottom: none;
            } */
        }

        .retail-content {
            flex: 1;
            height: 0;
            padding: 20px 10px 0 20px;
            overflow-y: scroll;
            border-radius: 8px 0 0 8px;

            @include scrollBar;

            .co-cashier-todo {
                position: absolute;
                top: -9px;
                right: -9px;
            }
        }

        .retail-settle {
            height: 156px;
            margin: 0 20px 20px;

            /* shadow-2 */

            .customer-info-wrapper {
                display: flex;
                flex: 1;
                flex-direction: column;
                align-items: flex-start;
                min-width: 350px;
                max-width: 440px;
                padding: var(--abc-paddingTB-none, 0) var(--abc-paddingLR-none, 0);
                background: var(--abc-color-cp-grey1);
                border: 1px solid var(--abc-color-P7);
                border-radius: var(--abc-border-radius-small);

                .search-wrapper {
                    width: 100%;
                    padding: 16px;
                }

                .customer-info {
                    width: 100%;
                    height: 100%;
                }

                .customer-autocomplete-wrapper {
                    width: 100%;

                    .customer-autocomplete-popover {
                        width: 100%;
                    }

                    .customer-section {
                        width: 100%;
                    }
                }
            }

            .settle-info-wrapper {
                flex: 2;
                background: var(--abc-color-cp-grey1);
                border: 1px solid var(--abc-color-P7);
                border-radius: var(--abc-border-radius-small);

                .discount-wrapper {
                    flex: 1;
                    width: 0;
                    padding: 12px 8px;

                    .discount-list {
                        flex: 1;
                        height: 0;
                        overflow-y: auto;

                        @include scrollBar;
                    }

                    .remark-info {
                        display: inline-block;
                        max-width: 100%;

                        @include ellipsis;
                    }
                }

                .total-info {
                    flex: 1;
                    min-width: 314px;
                    max-width: 400px;
                    height: 100%;
                    padding: 16px;
                    border-left: 1px dashed var(--abc-color-P7);

                    .price-info-wrapper {
                        flex: 1;
                        flex-wrap: wrap;
                    }

                    .price-info {
                        min-width: 136px;
                        min-height: 30px;
                    }

                    .price-item {
                        display: flex;
                        align-items: flex-start;
                        height: 24px;

                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }

                .label {
                    color: $T2;
                }

                .money {
                    display: inline-flex;
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 20px; /* 150% */
                }

                .total-amount {
                    font-size: 28px;
                    line-height: 22px;
                    color: var(--abc-color-R6);
                }

                .btn-wrapper {
                    margin-left: auto;

                    .huge-abc-button {
                        position: relative;
                        width: 128px;
                        height: 48px;
                        font-size: 20px;
                        font-weight: bold;
                        line-height: 28px; /* 133.333% */
                    }

                    .quick-key {
                        position: absolute;
                        right: 4px;
                        bottom: 3px;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: 16px; /* 123.077% */
                        color: $theme6;
                        zoom: 0.84;
                        transform-origin: bottom right;

                        &.gray {
                            color: $T3;
                        }
                    }

                    .is-disabled {
                        .quick-key {
                            color: $T3;
                        }
                    }
                }
            }
        }
    }

    @media screen and (max-width: 1600px) {
        .charge-retail-wrapper {
            .retail-table {
                .abc-table-body .table-cell {
                    padding-top: 8px;
                    padding-bottom: 8px;
                }

                .batches-info-wrapper.large-batches-info .batches,
                .goods-filed-wrapper.large-goods-filed .goods-info .name {
                    font-size: 14px;
                }
            }

            .retail-settle .settle-info .total-info .remark-info {
                max-width: 200px;
            }

            .charge-retail-sidebar {
                flex: 0 0 248px !important;
                width: 248px !important;
                min-width: 248px !important;
                max-width: 248px !important;
            }
        }
    }

    @media screen and (max-width: 1440px) {
        .charge-retail-wrapper {
            .charge-retail-sidebar {
                flex: 0 0 200px !important;
                width: 200px !important;
                min-width: 200px !important;
                max-width: 200px !important;
            }
        }
    }
</style>
