<template>
    <div class="cashier-work-summary-wrapper">
        <div class="summary-handler">
            <abc-space>
                <div v-if="titleName" class="title">
                    {{ titleName }}
                </div>
                <abc-date-picker-bar
                    v-model="currentDateLabel"
                    :options="datePickerBarOptions"
                    value-format="YYYY-MM-DD"
                    :picker-options="pickerOptions"
                    @change="changeDate"
                >
                </abc-date-picker-bar>
                <abc-checkbox-button
                    v-if="showViewCheckbox"
                    v-model="viewRange"
                    type="number"
                    @change="viewRangeChange"
                >
                    只看我经手的
                </abc-checkbox-button>
            </abc-space>
        </div>
        <div v-abc-loading="loading" class="summary-content">
            <abc-flex
                :style="{
                    margin: '16px 0 8px 0',width: wide ? '1248px' : '100%'
                }"
                justify="space-between"
                :gap="12"
            >
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C1"
                    :title="summaryData.sheetCount"
                    content="收费单数"
                    icon="s-order-1-fill"
                >
                </abc-statistic>
                <abc-statistic
                    v-if="isPharmacy"
                    style="flex: 1;"
                    variant="colorful"
                    theme="C2"
                    :title="summaryData.totalAmount"
                    content="零售收入"
                    icon="s-currency-fill"
                ></abc-statistic>
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C3"
                    :title="summaryData.amount"
                    :content="isPharmacy ? '零售收费' : '收费'"
                    icon="s-transfer-in-fill"
                >
                </abc-statistic>
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C4"
                    :title="summaryData.returnAmount"
                    :content="isPharmacy ? '零售退费' : '退费'"
                    icon="s-transfer-out-fill"
                >
                </abc-statistic>
                <abc-statistic
                    v-if="hasOwed && showOwedAmount"
                    style="flex: 1;"
                    variant="colorful"
                    theme="C5"
                    :title="summaryData.repaymentAmount"
                    content="还款"
                    icon="s-repayment-fill"
                >
                </abc-statistic>
                <abc-statistic
                    v-if="showOweAmount"
                    style="flex: 1;"
                    variant="colorful"
                    theme="C6"
                    :title="summaryData.oweAmount"
                    content="欠费"
                    icon="s-card-arrears-fill"
                >
                </abc-statistic>
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C7"
                    :title="summaryData.rechargeAmount"
                    content="充值收费"
                    icon="s-card-fill"
                >
                </abc-statistic>
            </abc-flex>

            <abc-flex class="tab-title" align="flex-start" gap="large">
                <abc-tabs-v2
                    v-model="selectedTab"
                    :option="tabsOption"
                    size="middle"
                    :border="false"
                    type="outline"
                    @change="changeTab"
                ></abc-tabs-v2>

                <template v-if="selectedTab === 0">
                    <abc-flex justify="flex-end" style="flex: 1;">
                        <abc-space>
                            <abc-button
                                v-if="isSupportPrintReconciliationInfo"
                                variant="ghost"
                                icon="print"
                                @click="handlePrint"
                            >
                                打印
                            </abc-button>
                        </abc-space>
                    </abc-flex>
                </template>
                <template v-if="selectedTab === 1">
                    <abc-space size="small">
                        <abc-select
                            v-model="fetchParams.sourceType"
                            :disabled="noViewPermission"
                            :width="120"
                            @change="handleChangeFilter"
                        >
                            <abc-option value="" label="全部来源">
                                全部来源
                            </abc-option>
                            <abc-option
                                v-for="action in sourceTypeOptions"
                                :key="action.id"
                                :value="action.id"
                                :label="action.name"
                            ></abc-option>
                        </abc-select>

                        <abc-select
                            v-model="fetchParams.action"
                            :disabled="noViewPermission"
                            :width="120"
                            @change="handleChangeFilter"
                        >
                            <abc-option value="" label="全部类型">
                                全部类型
                            </abc-option>
                            <abc-option
                                v-for="action in typeOptions"
                                :key="action.id"
                                :value="action.id"
                                :label="action.name"
                            ></abc-option>
                        </abc-select>

                        <pay-mode-selector
                            v-model="selectedPayModes"
                            :disabled="noViewPermission"
                            :options="payModeOptions"
                            :width="120"
                            @select="handleSelectPayModes"
                        >
                        </pay-mode-selector>
                    </abc-space>
                </template>
                <template v-if="selectedTab === 2">
                    <pro-stat-toolbar-v2
                        ref="chargeProStatToolbarRef"
                        style="flex: 1; min-width: 0;"
                        :cache-params="cacheSaleParams"
                        :date-filter="saleParams.expireDate"
                        :filter-options="renderFilterList"
                        :enable-export="false"
                        :dimension-options="[]"
                        @change-date="handleExpireDateChange"
                        @change-doctor="handleChangeDoctor"
                        @change-action="handleActionChange"
                        @change-seller="handleCashierChange"
                        @change-source-type="handleSourceTypeChange"
                        @change-pay-mode="handlePayModeChange"
                        @change-fee-type="handleFeeTypeChange"
                        @change-patient-search="handleSalePatientChange"
                        @change-sell-no="handleSellNoChange"
                        @change-tag="handleTagChange"
                        @filter="handleFilter"
                    >
                    </pro-stat-toolbar-v2>
                </template>
                <template v-if="selectedTab === 3">
                    <abc-flex justify="space-between" style="flex: 1;">
                        <abc-space>
                            <!-- 费用类型 -->
                            <abc-select
                                v-model="feeTypeIdList"
                                placeholder="费用类型"
                                :width="160"
                                clearable
                                multiple
                                multi-label-mode="text"
                                show-empty
                                @change="handleAdviceFeeTypeChange"
                            >
                                <abc-option
                                    v-for="(item, index) in adviceFeeTypeOptions"
                                    :key="index"
                                    :value="item.id"
                                    :label="item.name"
                                ></abc-option>
                            </abc-select>
                            <!-- 开票状态 -->
                            <abc-select
                                v-model="fetchParams.invoiceStatus"
                                :width="160"
                                clearable
                                placeholder="开票状态"
                                @change="handleOperationTypeChange"
                            >
                                <abc-option
                                    v-for="operate in operationOptions"
                                    :key="operate.value"
                                    :label="operate.label"
                                    :value="operate.value"
                                ></abc-option>
                            </abc-select>
                            <patient-selector
                                v-model="fetchParams.patientId"
                                style="margin-left: 0;"
                                patient-placeholder="姓名/手机"
                                :width="160"
                                @change="handlePatientChange"
                            ></patient-selector>
                        </abc-space>
                        <abc-space>
                            <abc-popover
                                v-if="isExportLoading"
                                ref="exportPopover"
                                trigger="hover"
                                placement="bottom"
                                :disabled="!isExportingLongTime"
                                class="export"
                                theme="yellow"
                            >
                                <template #reference>
                                    <abc-check-access>
                                        <abc-button
                                            variant="ghost"
                                            @click="handleExport"
                                        >
                                            <div class="export-btn-content">
                                                <abc-loading small no-cover></abc-loading>
                                                <span class="export-word">{{ exportWording }}</span>
                                            </div>
                                        </abc-button>
                                    </abc-check-access>
                                </template>

                                <div v-if="isExportingLongTime">
                                    <p>可能等待时间较长，你可以稍后回来下载</p>
                                </div>
                            </abc-popover>
                            <abc-check-access v-else>
                                <abc-button
                                    variant="ghost"
                                    @click="handleExport"
                                >
                                    <abc-icon v-if="canExportDownload" icon="arrow_bottom"></abc-icon>
                                    <abc-icon v-else icon="n-upload-line"></abc-icon>
                                    {{ exportWording }}
                                </abc-button>
                            </abc-check-access>
                        </abc-space>
                    </abc-flex>
                </template>
            </abc-flex>

            <abc-layout preset="dialog-table">
                <abc-layout-content ref="layoutContent" :key="selectedTab">
                    <template v-if="selectedTab === 0">
                        <abc-descriptions
                            :column="4"
                            :label-width="120"
                            grid
                            size="large"
                            :custom-title-style="{
                                'justify-content': 'center'
                            }"
                            style="margin-bottom: 16px;"
                        >
                            <template #title>
                                费用分类
                            </template>
                            <abc-content-empty v-if="noViewPermission">
                                已在管理处设置不允许查看账目
                            </abc-content-empty>
                            <abc-descriptions-item v-for="item in feeList" :key="item.id" :label="item.name">
                                {{ item.value | formatMoney }}
                            </abc-descriptions-item>
                        </abc-descriptions>
                        <abc-descriptions
                            :column="4"
                            :label-width="120"
                            grid
                            size="large"
                            :custom-title-style="{
                                'justify-content': 'center',
                            }"
                        >
                            <template #title>
                                收费方式
                            </template>
                            <abc-content-empty v-if="noViewPermission">
                                已在管理处设置不允许查看账目
                            </abc-content-empty>
                            <abc-descriptions-item v-for="item in payModeList" :key="item.field" :label="item.name">
                                {{ item.value | formatMoney }}
                            </abc-descriptions-item>
                        </abc-descriptions>
                        <div style="height: 24px;"></div>
                    </template>

                    <template
                        v-if="selectedTab === 1"
                    >
                        <abc-table
                            key="income-table"
                            class="income-table"
                            :render-config="tableHeaderRender"
                            :data-list="incomeTableData"
                            empty-size="small"
                            :empty-show-icon="!!emptyIncomeTips.imgUrl"
                            :empty-content="emptyIncomeTips.label"
                            :loading="incomeDataLoading"
                            :pagination="incomeTablePagination"
                            @pageChange="changePageIndex"
                        >
                            <ul v-if="incomeTableData.length" slot="paginationTipsContent">
                                <li>共 <span>{{ incomeTableTotalCount }}</span> 条</li>
                                <li v-if="displaySummaryData.actualFee">
                                    ，实收 <span>{{ displaySummaryData.actualFee }}</span>
                                </li>
                            </ul>
                        </abc-table>
                    </template>

                    <template v-if="selectedTab === 2">
                        <abc-table
                            ref="saleTable"
                            key="sale-table"
                            class="sale-table"
                            :render-config="saleTableHeaderRender"
                            :data-list="saleTableData"
                            :loading="saleLoading"
                            :empty-content="'暂无数据'"
                            empty-size="small"
                            :pagination="saleTablePagination"
                            @pageChange="handleChangeSalePage"
                        >
                            <template #footer>
                                <abc-flex flex="1" justify="flex-end">
                                    <abc-space :size="4">
                                        <abc-text theme="gray">
                                            总收入
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ totalAmount | toMoney }}
                                        </abc-text>
                                        <abc-text theme="gray">
                                            收费
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ payedAmount | toMoney }}
                                        </abc-text>
                                        <abc-text theme="gray">
                                            退费
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ refundAmount | toMoney }}
                                        </abc-text>
                                    </abc-space>
                                </abc-flex>
                            </template>
                        </abc-table>
                    </template>

                    <template v-if="selectedTab === 3">
                        <abc-table
                            ref="recordTable"
                            key="record-table"
                            class="record-table"
                            :render-config="recordTableHeaderRender"
                            :data-list="tableData"
                            :loading="invoiceLoading"
                            :empty-content="'暂无数据'"
                            empty-size="small"
                            :pagination="recordTablePagination"
                            @pageChange="changePageIndex"
                        >
                            <template #footer>
                                <abc-flex
                                    flex="1"
                                    align="center"
                                    justify="flex-end"
                                >
                                    <div v-html="totalInfo"></div>
                                </abc-flex>
                            </template>
                        </abc-table>
                    </template>
                </abc-layout-content>
            </abc-layout>
        </div>
    </div>
</template>


<script>
    import {
        mapGetters, mapState,
    } from 'vuex';
    import StatisticsAPI from 'views/statistics/core/api/operation';

    import { formatMoney } from 'src/filters/index';
    import ModulePermission from 'views/permission/module-permission';
    import ICON_EMPTY from 'src/assets/images/icon/<EMAIL>';
    import { AbcDatePickerBar } from '@abc/ui-pc';
    const { DatePickerBarOptions } = AbcDatePickerBar;
    import { Popover as AbcPopover } from '@abc/ui-pc';
    import {
        prevDate, formatDate,
    } from '@abc/utils-date';
    import localStorage from 'utils/localStorage-handler.js';
    import RevenueAPI from 'views/statistics/core/api/revenue.js';
    import ChargeAPI from 'api/charge.js';
    import themeStyle from '@/styles/theme.module.scss';
    import { GoodsTypeEnum } from '@abc/constants';
    import PayModeSelector from 'views/statistics/common/pay-mode-selector/pay-mode-selector';
    import InvoiceAPI from 'views/statistics/core/api/invoice.js';
    import PatientSelector from 'views/statistics/patient-selector';
    import { ExportStatus } from 'views/statistics/core/entities/export';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import ChargeStatAPI from 'views/statistics/core/api/charge.js';
    import PayModeMixins from 'views/statistics/mixins/pay-modes-mixin';
    import FeeTypeMixins from 'views/statistics/mixins/fee-type-mixin';
    import {
        getSummaryRenderKeys, resolveHeader as resolveHeaderRender,
    } from 'utils/table';
    import OverflowFlexTagsWrapper from 'views/registration/components/overflow-flex-tags-wrapper.vue';
    import AbcPrinter from '@/printer';
    import {
        ABCPrintConfigKeyMap, PrintMode,
    } from '@/printer/constants';
    import ProStatToolbarV2 from 'views/statistics/common/pro-stat-toolbar/pro-stat-toolbar-v2.vue';
    import useProStatToolbar from 'views/statistics/hooks/use-pro-stat-toolbar';
    import { clone } from '@abc/utils';

    export default {
        name: 'OutpatientWorkSummaryDialog',
        components: {
            ProStatToolbarV2,
            PayModeSelector,
            PatientSelector,
        },
        mixins: [ModulePermission, PayModeMixins, FeeTypeMixins],
        props: {
            titleName: {
                type: String,
                default: '收费看板',
            },
            value: Boolean,
            hiddenFilterType: {
                type: String,
                default: '',
            },
            /**
             * 是否是宽版布局（药店）
             */
            wide: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                handleRemoveFilter,
                handleAddFilter,
                getCustomFilterOptions,
                setFilterConfigThis,
                setTableScreenKey,
                setRemoteFilterList,
                maxDisplayFilterCount,
                localFilterConfig,
                remoteFilterList,
                renderFilterList,
            } = useProStatToolbar();

            return {
                handleRemoveFilter,
                handleAddFilter,
                getCustomFilterOptions,
                setFilterConfigThis,
                setTableScreenKey,
                setRemoteFilterList,
                maxDisplayFilterCount,
                localFilterConfig,
                remoteFilterList,
                renderFilterList,
            };
        },
        data() {
            return {
                viewRange: 0,
                currentDateLabel: DatePickerBarOptions.DAY.label,
                datePickerBarOptions: [
                    {
                        label: DatePickerBarOptions.DAY.label,
                        name: DatePickerBarOptions.DAY.name,
                        getValue() {
                            return [new Date(), new Date()];
                        },
                    },
                    {
                        label: DatePickerBarOptions.YESTERDAY.label,
                        name: DatePickerBarOptions.YESTERDAY.name,
                        getValue() {
                            return [
                                prevDate(new Date()),
                                prevDate(new Date()),
                            ];
                        },
                    },
                    DatePickerBarOptions.WEEK,
                    DatePickerBarOptions.MONTH,
                ],
                pickerStartDate: '',
                pickerOptions: {
                    onPick: ({ minDate }) => {
                        if (minDate) {
                            this.pickerStartDate = minDate.getTime();
                        }
                    },
                    disabledDate: (date) => {
                        const day30 = 30 * 24 * 3600 * 1000;
                        if (this.pickerStartDate !== '') {
                            let maxTime = this.pickerStartDate + day30;
                            const minTime = this.pickerStartDate - day30;
                            if (maxTime > new Date()) {
                                maxTime = new Date();
                            }
                            return date.getTime() > maxTime ||
                                date.getTime() < minTime ||
                                date.getTime() > Date.now();
                        }
                        return date.getTime() > Date.now();
                    },
                },

                selectedTab: 0,
                operationOptions: [
                    {
                        label: '待开票',
                        value: 0,
                    },
                    {
                        label: '已开票',
                        value: 1,
                    },
                    {
                        label: '开票金额异常',
                        value: 2,
                    },
                ],
                pageSize: window.screen.width > 1366 ? 9 : 7,
                fetchParams: {
                    offset: 0,
                    beginDate: '',
                    endDate: '',
                    employeeId: '',
                    action: '',
                    sourceType: '',
                    payModes: '',
                    invoiceStatus: undefined,
                    keyword: '',
                    feeTypeIds: [],
                },
                tabContentStyle: {
                    minHeight: window.screen.width > 1366 ? '486px' : '400px',
                },
                loading: false,
                checkAccountLoading: false,
                incomeDataLoading: false,
                invoiceLoading: false,

                summaryData: {
                    sheetCount: 0, //总单数
                    amount: 0, //收费
                    oweAmount: 0, //欠费
                    rechargeAmount: 0, //充值收费
                    repaymentAmount: 0, //还款收费
                    returnAmount: 0, //退费
                },

                typeOptions: [],
                sourceTypeOptions: [],

                // 收费明细
                incomeTableHeader: [],
                incomeTableData: [],
                incomeTableSummary: null,
                incomeTableTotalCount: 0,

                // 对账信息
                payModeList: [], // 收费方式
                feeList: [], // 费用分类

                chargeFormsCacheMap: new Map(), // 收费明细缓存
                curChargeForms: [],
                chargeLoading: false,
                selectedPayModes: [],
                payModeOptions: [],
                feeTypeIdList: [],
                adviceFeeTypeOptions: [],
                tableData: [],
                tableHeader: [],
                totalCount: 0,
                totalInfo: '',
                exportStatus: ExportStatus.NONE,
                timer: null,
                saleLoading: false,
                saleParams: {
                    doctorId: '',
                    actions: [],
                    sourceType: '',
                    sellerId: '',
                    patientId: '',
                    productId: '',
                    pageIndex: 0,
                    expireDate: {
                        begin: '',
                        end: '',
                        dateRange: [],
                    },
                    payModesFilter: [],
                    employeeTypeEnum: 'SELLER',
                    goodsTag: [],
                    keyword: '',
                },
                cacheSaleParams: {},
                employeeList: [],
                actionOptions: [],
                cashierList: [],
                saleTableHeader: [],
                saleTableData: [],
                payModes: [],
                feeTypeList: [],
                saleTotalCount: 0,
                payedAmount: 0,
                refundAmount: 0,
                totalAmount: 0,
            };
        },
        computed: {
            ...mapGetters(['userInfo', 'currentClinic', 'chargerPermissionInDashboard', 'chargeConfig', 'isSingleStore','enablePatientMobileInStatistics','isPharmacy']),
            ...mapState('socialPc', [
                'basicInfo', // 配置基本信息
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isSupportPrintReconciliationInfo() {
                return this.viewDistributeConfig.Statistics.isSupportPrintReconciliationInfo;
            },
            tabsOption() {
                const options = [
                    {
                        label: '对账信息',
                        value: 0,
                    },
                    {
                        label: '收退费记录',
                        value: 1,
                    },
                ];
                if (this.viewDistributeConfig.Cashier.showSalesDetail) {
                    options.push({
                        label: '收费明细',
                        value: 2,
                    });
                }
                if (!this.noViewPermission) {
                    options.push({
                        label: '开票信息',
                        value: 3,
                    });
                }
                return options;
            },

            showOwedAmount() {
                return this.viewDistributeConfig.Cashier.showOwedAmount;
            },

            showOweAmount() {
                return this.viewDistributeConfig.Cashier.showOweAmount;
            },
            exportWording() {
                let wording = '导出';
                switch (this.exportStatus) {
                    case ExportStatus.NONE:
                    case ExportStatus.DOWNLOADED:
                        wording = '导出';
                        break;

                    case ExportStatus.GENERATING:
                    case ExportStatus.GENERATING_LONG_TIME:
                        wording = '导出中';
                        break;
                    case ExportStatus.GENERATED:
                        wording = '点击下载';
                        break;
                    case ExportStatus.FAILED:
                        wording = '导出失败';
                        break;
                    default:
                        break;
                }
                return wording;
            },
            isExportingLongTime() {
                return this.exportStatus === ExportStatus.GENERATING_LONG_TIME;
            },
            isExportLoading() {
                return (
                    this.exportStatus === ExportStatus.GENERATING ||
                    this.exportStatus === ExportStatus.GENERATING_LONG_TIME
                );
            },
            canExportDownload() {
                return this.exportStatus === ExportStatus.GENERATED;
            },
            invoiceTableHeight() {
                if (window.screen.width > 1366) {
                    // 9条数据（400） + header(36)
                    return 436;
                }
                // 7条数据（320） + header(36)
                return 356;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            tableHeight() {
                if (window.screen.width > 1366) {
                    // 10条数据（400） + header(36)
                    return 436;
                }
                // 8条数据（320） + header(26)
                return 356;
            },
            summaryStyle() {
                return {
                    borderBottom: (this.tableHeight === 436 && this.incomeTableData.length < 9 || this.tableHeight === 356 && this.incomeTableData.length < 7) ? '1px solid #e6eaee' : 'none',
                    fontWeight: 'bold',
                };
            },
            clinicId() {
                return this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },

            noViewPermission() {
                return this.chargerPermissionInDashboard === 0;
            },

            emptyIncomeTips() {
                if (this.noViewPermission) {
                    return {
                        label: '已在管理处设置不允许查看账目',
                    };
                }
                return {
                    imgUrl: ICON_EMPTY,
                    label: '暂无记录',
                };
            },

            pageParams() {
                const {
                    offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / this.pageSize);
                return {
                    pageIndex,
                    pageSize: this.pageSize,
                };
            },
            incomeTablePagination() {
                return {
                    pageIndex: this.pageParams.pageIndex,
                    pageSize: this.pageParams.pageSize,
                    count: this.incomeTableTotalCount,
                };
            },
            saleTablePagination() {
                return {
                    pageIndex: this.saleParams.pageIndex,
                    pageSize: this.pageSize,
                    count: this.saleTotalCount,
                    showTotalPage: true,
                };
            },
            recordTablePagination() {
                return {
                    pageIndex: this.pageParams.pageIndex,
                    pageSize: this.pageParams.pageSize,
                    count: this.totalCount,
                };
            },

            // 是否包含欠费
            hasOwed() {
                return this.chargeConfig.oweSheetSwitch === 1;
            },

            showViewCheckbox() {
                return this.chargerPermissionInDashboard === 2;
            },

            queryUserId() {
                // 查看所有账目
                if (this.showViewCheckbox && this.viewRange === 0) {
                    return '';
                }
                return this.userInfo?.id;
            },

            tableHeaderRender() {
                return {
                    hasInnerBorder: true,
                    list: resolveHeaderRender(this.incomeTableHeader, {
                        chargeDetail: (h, row) => {
                            const clickHandler = async () => {
                                this.curChargeForms = this.chargeFormsCacheMap.get(row.chargeSheetId);
                                if (!this.curChargeForms) {
                                    this.curChargeForms = await this.getChargeSheetDetail(row.chargeSheetId);
                                    this.chargeFormsCacheMap.set(row.chargeSheetId, this.curChargeForms);
                                }
                            };
                            return (<AbcPopover
                                    trigger="click"
                                    z-index="10000"
                                    popperClass="charge-sheet-info-popper"
                                    placement="right-end"
                                    theme="yellow"
                                >
                                    <abc-table-cell slot="reference" style="justify-content: center">
                                        <abc-icon icon="info" size="12" color={themeStyle.P1} style="cursor: pointer"
                                                  onClick={clickHandler}></abc-icon>
                                    </abc-table-cell>

                                    <div class="charge-sheet-header">
                                        <div class="th name">
                                            收费项目
                                        </div>
                                        <div class="th unit-count">
                                            数量
                                        </div>
                                        <div class="th unit">
                                            单位
                                        </div>
                                        <div class="th total-price">
                                            金额
                                        </div>
                                    </div>
                                    <div class="charge-sheet-content">
                                        {this.chargeLoading ? <abc-loading></abc-loading> : this.curChargeForms?.map(
                                            (item) => (
                                                <div class="charge-sheet-item">
                                                    <div class="td name ellipsis">
                                                        {item && item.name}
                                                    </div>
                                                    <div class="td unit-count">
                                                        {item.unitCount}
                                                    </div>
                                                    <div class="td unit">
                                                        {item.unit}
                                                    </div>
                                                    <div class="td total-price">
                                                        {formatMoney(item.totalPrice || 0)}
                                                    </div>
                                                </div>
                                            ),
                                        )}
                                    </div>
                                </AbcPopover>
                            );
                        },
                    }, true),
                };
            },
            displaySummaryData() {
                return this.incomeTableData.length ? this.incomeTableSummary : null;
            },
            summaryRenderKeys() {
                return getSummaryRenderKeys(this.incomeTableHeader);
            },

            saleTableHeaderRender() {
                return {
                    hasInnerBorder: true,
                    list: resolveHeaderRender(this.saleTableHeader, {}, true),
                };
            },
            recordTableHeaderRender() {
                return {
                    hasInnerBorder: true,
                    list: resolveHeaderRender(this.tableHeader, {}, true),
                };
            },
            tableKey() {
                return this.viewDistributeConfig.Statistics.retailStat.cashierWorkSummaryTableKey;
            },
        },
        async created() {
            if (this.viewDistributeConfig.Cashier.showSalesDetail) {
                this.cacheSaleParams = clone(this.saleParams);
                this.setFilterConfigThis(this);
                this.setTableScreenKey(this.tableKey);
                this.customRemoteFilterList();
            }

            // 优先获取viewRange 再获取数据
            this.viewRange = localStorage.getObj('cashier_kanban_view_range', this.currentClinic.userId, true) || 0;
            await this.$store.dispatch('initDataPermission');
            this.changeDate([formatDate(new Date()), formatDate(new Date())]);
            this.getAdviceTypeOptions();
            this.fetchEmployeeOptions();
            this.exportService = new ExportService();
            // 检查导出任务
            this.checkExportTask();
        },
        beforeDestroy() {
            this.exportService && this.exportService.destroy();
            clearTimeout(this.timer);
        },
        methods: {
            customRemoteFilterList() {
                const remoteFilterList = [
                    {
                        key: 'expireDate',
                        sort: 1,
                        filterName: '效期',
                    },
                    {
                        key: 'doctorId',
                        sort: 2,
                        filterName: '销售人',
                    },
                    {
                        key: 'actions',
                        sort: 3,
                        filterName: '类型',
                    },
                    {
                        key: 'sourceType',
                        sort: 4,
                        filterName: '开单来源',
                    },
                    {
                        key: 'sellerId',
                        sort: 5,
                        filterName: '收费员',
                    },
                    {
                        key: 'payModesFilter',
                        sort: 6,
                        filterName: '支付方式',
                    },
                    {
                        key: 'feeTypeFilter$',
                        sort: 7,
                        filterName: '费用分类',
                    },
                    {
                        key: 'patientId',
                        sort: 8,
                        filterName: '搜索会员',
                    },
                    {
                        key: 'sellNo',
                        sort: 9,
                        filterName: '销售单号',
                    },
                    {
                        key: 'goodsTag',
                        sort: 10,
                        filterName: '商品标签',
                    },
                ];
                this.setRemoteFilterList(remoteFilterList);
            },
            handleAdviceFeeTypeChange(list = []) {
                this.fetchParams.feeTypeIds = list ;
                this.fetchInvoiceTransaction();
            },
            handleChangeSalePage(index) {
                this.saleParams.pageIndex = index - 1;
                this.getSaleDetailData(false);
                this.getChargeDetailTotal();
            },
            handleSalePatientChange(val) {
                this.saleParams.patientId = val;
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            handleSellNoChange(val) {
                this.saleParams.sellNo = val;
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            handlePatientChange() {
                this.fetchInvoiceTransaction();
            },
            handleExpireDateChange(val) {
                this.saleParams.expireDate = val;
                this.getChargeDetailTotal();
                this.getSaleDetailData();
            },
            handleSourceTypeChange(val) {
                this.saleParams.sourceType = val;
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            handlePayModeChange(val) {
                this.saleParams.payModesFilter = val;
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            handleFeeTypeChange(val) {
                this.params.feeTypeFilter$ = val;
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            handleCashierChange() {
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            handleActionChange(list) {
                this.saleParams.actions = list;
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            handleTagChange(val) {
                this.saleParams.goodsTag = val;
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            handleFilter(params) {
                params.actions = params.actions || [];
                if (params.feeTypeFilter$) this.params.feeTypeFilter$ = params.feeTypeFilter$;
                this.saleParams = Object.assign(this.saleParams, params);
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            async fetchEmployeeOptions() {
                const {
                    beginDate, endDate,
                } = this.fetchParams;
                try {
                    const res = await RevenueAPI.getEmployeeSelection({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                        includeWriter: 0,
                        employeeTypeEnum: 'SELLER',
                    });
                    this.employeeList = res?.data;
                } catch (e) {
                    console.log(e);
                    this.employeeList = [];
                }
            },
            async getRevenueActionSelection() {
                const {
                    beginDate, endDate,
                } = this.fetchParams;

                try {
                    const { data } = await RevenueAPI.getRevenueTypeSelection({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                    });
                    this.actionOptions = data?.actions || [];
                } catch (error) {
                    console.error(error);
                    this.actionOptions = [];
                }
            },
            async fetchCashierOptions() {
                const {
                    beginDate, endDate,
                } = this.fetchParams;
                try {
                    const res = await RevenueAPI.getCashierSelection({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                    });
                    this.cashierList = res?.data;
                } catch (e) {
                    console.log(e);
                    this.cashierList = [];
                }
            },
            transTemplateToTotalInfo(data = [], template = '') {
                if (!template) return '';
                let index = 0;
                const result = template.replace(
                    RegExp('%s', 'g'),
                    () =>
                        `<span style="color: #000;padding: 0 4px">${
                            index === 0 ?
                                data[index++] :
                                parseFloat(data[index++] || 0).toFixed(2)
                        }</span>`,
                );
                return result;
            },
            setTableData(isClear = false, tableData = {}, resetPageParams) {
                if (isClear) {
                    this.tableHeader = [];
                    this.tableData = [];
                    this.totalCount = 0;
                } else {
                    const {
                        header = [], data = [], total = {},
                    } = tableData || {};
                    this.tableHeader = header || [];
                    this.tableData = data || [];
                    if (resetPageParams) {
                        this.totalCount = total?.count || 0;
                    }
                }
            },

            async getAdviceTypeOptions() {
                try {
                    const {
                        beginDate, endDate,
                    } = this.fetchParams;
                    const res = await InvoiceAPI.feeSelectOptions({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                    });

                    this.adviceFeeTypeOptions = res?.data || [];
                } catch (err) {
                    console.log(err);
                }
            },
            handleOperationTypeChange() {
                this.fetchInvoiceTransaction();
            },
            handleChangeFilter() {
                this.fetchParams.offset = 0;
                this.fetchCashierTransaction();
            },

            getRevenueSelection() {
                this.getRevenueTypeSelection();
                this.getRevenueSourceTypeSelection();
                this.getPayModesSourceTypeSelection();
            },

            async getRevenueTypeSelection() {
                if (this.noViewPermission) return;
                const {
                    beginDate, endDate,
                } = this.fetchParams;
                try {
                    const { data } = await RevenueAPI.getRevenueTypeSelection({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                        cashierId: this.queryUserId,
                    });
                    this.typeOptions = data.actions;
                } catch (error) {
                    console.error(error);
                }
            },

            async getRevenueSourceTypeSelection() {
                if (this.noViewPermission) return;
                const {
                    beginDate, endDate,
                } = this.fetchParams;
                try {
                    const { data } = await RevenueAPI.getRevenueSourceTypeSelection({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                        cashierId: this.queryUserId,
                    });
                    this.sourceTypeOptions = data;
                } catch (error) {
                    console.error(error);
                }
            },

            viewRangeChange(val) {
                this.fetchParams.offset = 0;
                this.fetchParams.action = '';
                this.fetchParams.sourceType = '';
                this.fetchCashierOverview();
                if (this.selectedTab === 3) {
                    this.fetchInvoiceTransaction();
                } else if (this.selectedTab === 2) {
                    this.fetchEmployeeOptions();
                    this.getRevenueActionSelection();
                    this.fetchCashierOptions();
                    this.getSaleDetailData();
                    this.getChargeDetailTotal();
                } else {
                    this.getRevenueSelection();
                    this.fetchData();
                    localStorage.setObj('cashier_kanban_view_range', this.currentClinic.userId, val);
                }
            },
            changeTab(index, item) {
                this.selectedTab = item.value;
                this.fetchCashierOverview();
                this.$nextTick(() => {
                    this.handleMounted(this.$refs.layoutContent.calcHandler());
                });
            },
            changeDate(picker) {
                if (picker && picker.length === 2) {
                    this.fetchParams.beginDate = picker[0];
                    this.fetchParams.endDate = picker[1];
                } else {
                    this.fetchParams.beginDate = formatDate(new Date());
                    this.fetchParams.endDate = formatDate(new Date());
                    this.currentDateLabel = DatePickerBarOptions.DAY.label;
                }
                this.currentDate = 'range';
                this.fetchParams.offset = 0;
                this.fetchParams.action = '';
                this.fetchParams.sourceType = '';
                this.fetchCashierOverview();
                if (this.selectedTab === 3) {
                    this.getAdviceTypeOptions();
                    this.fetchInvoiceTransaction();
                } else if (this.selectedTab === 2) {
                    this.fetchFeeTypeOptions();
                    this.getPayModesSourceTypeSelection();
                    this.fetchEmployeeOptions();
                    this.getRevenueActionSelection();
                    this.fetchCashierOptions();
                    this.getSaleDetailData();
                    this.getChargeDetailTotal();
                } else {
                    this.getRevenueSelection();
                    this.fetchData();
                }
            },

            changePageIndex(page) {
                this.fetchParams.offset = (page - 1) * this.pageSize;
                this.fetchCashierTransaction();
                if (this.selectedTab === 3) {
                    this.fetchInvoiceTransaction(false);
                }
            },
            hideExportTips() {
                if (this.$refs.exportPopover) {
                    this.$refs.exportPopover.doClose();
                }
            },
            async handleExport(exportStatus) {
                const {
                    beginDate,
                    endDate,
                    feeTypeIds,
                    invoiceStatus,
                    patientId,
                } = this.fetchParams;
                // 已经生成，点击进入下载
                if (exportStatus === ExportStatus.GENERATED) {
                    this.downloadExport();
                    return;
                }
                const execExport = async () => {
                    clearTimeout(this.timer);
                    this.exportStatus = ExportStatus.GENERATING;
                    try {
                        await this.exportService.startExport('charge-invoice', {
                            beginDate,
                            endDate,
                            feeTypeIds,
                            patientId,
                            invoiceStatus,
                            clinicId: this.clinicId,
                            cashierId: this.queryUserId,
                        });
                        this.timer = setTimeout(() => {
                            this.checkExportTask();
                        }, 50);
                    } catch (e) {
                        console.error(e);
                    }
                };
                await execExport();
            },
            async downloadExport() {
                this.exportService.downloadExport('charge-invoice');
                this.exportStatus = ExportStatus.DOWNLOADED;
            },
            checkExportTask() {
                this.exportService.checkExportStatus(
                    'charge-invoice',
                    () => {
                        this.exportStatus = ExportStatus.GENERATING;
                    },
                    () => {
                        this.exportStatus = ExportStatus.GENERATING_LONG_TIME;
                        this.showExportTips();
                    },
                    () => {
                        this.exportStatus = ExportStatus.GENERATED;
                        // 10s 内，直接下载
                        this.downloadExport();
                    },
                    () => {
                        this.exportStatus = ExportStatus.DOWNLOADED;
                    },
                    () => {
                        this.exportStatus = ExportStatus.FAILED;
                        console.log('失败啦');
                    },
                );
            },

            handleMounted(data) {
                console.log('handleMounted', data);
                this.pageSize = data.paginationLimit || 8;

                if (this.selectedTab === 1) {
                    this.pageSize -= 1;
                    this.fetchCashierTransaction();
                } else if (this.selectedTab === 2) {
                    this.pageSize -= 1;
                    this.fetchFeeTypeOptions();
                    this.fetchEmployeeOptions();
                    this.getRevenueActionSelection();
                    this.fetchCashierOptions();
                    this.getSaleDetailData();
                    this.getChargeDetailTotal();
                } else if (this.selectedTab === 3) {
                    this.pageSize -= 1;
                    this.getAdviceTypeOptions();
                    this.fetchInvoiceTransaction();
                } else {
                    this.fetchCashierCheckAccount();
                }
            },
            async fetchData() {
                try {
                    this.loading = true;
                    await Promise.all([
                        this.fetchCashierCheckAccount(),
                        this.fetchCashierTransaction(),
                    ]);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            showExportTips() {
                if (this.$refs.exportPopover) {
                    this.$refs.exportPopover.doShow();
                }
            },
            async fetchInvoiceTransaction(resetPageParams = true) {
                const {
                    beginDate,
                    endDate,
                    feeTypeIds,
                    invoiceStatus,
                    patientId,
                    offset,
                } = this.fetchParams;
                if (resetPageParams) {
                    this.fetchParams.offset = 0;
                }
                try {
                    this.invoiceLoading = true;
                    const { data } = await InvoiceAPI.getInvoiceTransaction({
                        beginDate,
                        endDate,
                        feeTypeIds,
                        patientId,
                        invoiceStatus,
                        clinicId: this.clinicId,
                        offset,
                        limit: this.pageSize,
                        cashierId: this.queryUserId,
                    });
                    if (data) {
                        this.setTableData(false, data, resetPageParams);
                        this.totalInfo = this.transTemplateToTotalInfo(
                            data?.total?.data,
                            data?.total?.template,
                        ) || '';
                    }
                } catch (err) {
                    this.setTableData(true);
                    console.log(err);
                } finally {
                    this.invoiceLoading = false;
                }
            },

            handleChangeDoctor(val) {
                this.saleParams.doctorId = val;
                this.getSaleDetailData();
                this.getChargeDetailTotal();
            },
            async fetchFeeTypeOptions() {
                const {
                    beginDate, endDate,
                } = this.fetchParams;
                try {
                    const res = await RevenueAPI.getFeeTypeOptions({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                        employeeTypeEnum: 'SELLER',
                    });
                    this.feeTypeList = res;
                } catch (e) {
                    console.log(e);
                    this.feeTypeList = [];
                }
            },
            async getSaleDetailData(resetPageParams = true) {
                const {
                    beginDate,
                    endDate,
                } = this.fetchParams;
                if (resetPageParams) {
                    this.saleParams.offset = 0;
                }

                const {
                    actions,
                    sourceType,
                    employeeTypeEnum,
                    revisitStatus,
                    productId,
                    sellerId,
                    doctorId,
                    pageIndex,
                    expireDate,
                    patientId,
                    payModesFilter,
                    goodsTag,
                    sellNo,
                } = this.saleParams;
                const beginExpiryDate = expireDate.begin;
                const endExpiryDate = expireDate.end;
                const action = actions?.length ? actions.join(',') : '';
                const tagIds = goodsTag?.map((item) => item.tagId) || [];
                const filterQuery = [];
                payModesFilter.forEach((item) => {
                    // 有主分类不传
                    if (item.length > 1) {
                        item.shift();
                    }
                    filterQuery.push(item);
                });
                const payModes = filterQuery.flat().map((x) => x.value).join();

                const employees = doctorId ? [{
                    id: doctorId.split('-idWithName-')[0],
                    name: doctorId.split('-idWithName-')[1],
                }] : [];
                const offset = pageIndex * this.pageSize;
                try {
                    this.saleLoading = true;
                    const {
                        list = [],
                        header = [],
                    } = await ChargeStatAPI.getChargeDetail({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                        sellerId: this.viewRange ? this.currentClinic.userId : sellerId,
                        employees,
                        payModes,
                        patientId,
                        offset,
                        size: this.pageSize,
                        action,
                        sourceType,
                        employeeTypeEnum,
                        revisitStatus,
                        productId,
                        beginExpiryDate,
                        endExpiryDate,
                        tagIds,
                        dimension: 1,
                        feeType1: this.feeType1FilterForRequest$,
                        feeType2: this.feeType2FilterForRequest$,
                        enablePatientMobile: this.enablePatientMobileInStatistics,
                        sellNo,
                    });
                    this.saleTableHeader = header.map((item) => {
                        if (item.key === 'goodsTagName') {
                            item.width = 160;
                            item.render = (h, row) => {
                                const goodsTagList = (row.goodsTagName || []).map((one, index) => ({
                                    tagId: index,
                                    tagName: one,
                                    viewMode: 0,
                                }));
                                return (
                                    goodsTagList.length ?
                                        <abc-table-cell>
                                            <OverflowFlexTagsWrapper tags={goodsTagList} variant="outline" size="tiny"/>
                                        </abc-table-cell> :
                                        <abc-table-cell> - </abc-table-cell>
                                );
                            };
                        }
                        return item;
                    });
                    this.saleTableData = list;
                } catch (err) {
                    console.log(err);
                } finally {
                    this.saleLoading = false;
                }
            },
            async getChargeDetailTotal() {
                const {
                    beginDate,
                    endDate,
                } = this.fetchParams;

                const {
                    actions,
                    sourceType,
                    employeeTypeEnum,
                    revisitStatus,
                    productId,
                    keyword,
                    sellerId,
                    doctorId,
                    expireDate,
                    patientId,
                    payModesFilter,
                    sellNo,
                    goodsTag,
                } = this.saleParams;

                const beginExpiryDate = expireDate.begin;
                const endExpiryDate = expireDate.end;
                const action = actions?.length ? actions.join(',') : '';
                const filterQuery = [];
                payModesFilter.forEach((item) => {
                    // 有主分类不传
                    if (item.length > 1) {
                        item.shift();
                    }
                    filterQuery.push(item);
                });
                const payModes = filterQuery.flat().map((x) => x.value).join();

                const employees = doctorId ? [{
                    id: doctorId.split('-idWithName-')[0],
                    name: doctorId.split('-idWithName-')[1],
                }] : [];
                try {
                    const res = await ChargeStatAPI.getChargeDetailTotal({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                        sellerId: this.viewRange ? this.currentClinic.userId : sellerId,
                        employees,
                        payModes,
                        patientId,
                        action,
                        sourceType,
                        employeeTypeEnum,
                        revisitStatus,
                        productId,
                        keyword,
                        beginExpiryDate,
                        endExpiryDate,
                        dimension: 1, // 按批次纬度
                        feeType1: this.feeType1FilterForRequest$,
                        feeType2: this.feeType2FilterForRequest$,
                        sellNo,
                        tagIds: goodsTag?.map((item) => item.tagId) || [],
                    });
                    this.saleTotalCount = res.count || 0;
                    this.totalAmount = res.amount || 0;
                    this.payedAmount = res.payedAmount || 0;
                    this.owePayedAmount = res.owePayedAmount || 0;
                    this.oweRefundAmount = res.oweRefundAmount || 0;
                    this.repayAmount = res.repayAmount || 0;
                    this.refundAmount = res.refundAmount || 0;
                } catch (err) {
                    console.log(err);
                }
            },

            async fetchCashierOverview() {
                if (this.noViewPermission) return;
                const {
                    beginDate,
                    endDate,
                } = this.fetchParams;
                const { data } = await StatisticsAPI.overview.fetchCashierOverview({
                    clinicId: this.clinicId,
                    beginDate,
                    endDate,
                    employeeId: this.queryUserId,
                    employeeType: 2,
                });
                Object.assign(this.summaryData, data);
            },


            /**
             * @desc 获取对账信息
             * <AUTHOR>
             * @date 2022-03-22 15:57:21
             */
            async fetchCashierCheckAccount() {
                if (this.noViewPermission) return;
                this.checkAccountLoading = true;
                const {
                    beginDate,
                    endDate,
                } = this.fetchParams;
                const {
                    payModes, fees,
                } = await StatisticsAPI.overview.fetchCashierCheckAccount({
                    clinicId: this.clinicId,
                    beginDate,
                    endDate,
                    employeeId: this.queryUserId,
                    employeeType: 2,
                });

                this.payModeList = payModes;
                let feeHandle = fees;
                if (this.hiddenFilterType) {
                    feeHandle = fees.filter((i) => {
                        return i.name !== '中药颗粒';
                    });
                }
                this.feeList = feeHandle;
                this.checkAccountLoading = false;
            },
            async handlePrint() {
                const {
                    beginDate,
                    endDate,
                } = this.fetchParams;
                const clinicName = this.currentClinic.shortName || '';

                await AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates.statReportTableTemplate,// statOperateCashierReport,// statReportTableTemplate,
                    printConfigKey: ABCPrintConfigKeyMap.pharmacyReconciliationInfo,
                    data: {},
                    extra: {
                        getHTML: () => {
                            // 费用分类表格
                            const feeTableHtml = `
    <div style="margin-bottom: 20px;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;table-layout: fixed;border-top: none;">
            <tbody>
            <tr style="height:0;">
  <td style="width:13%;padding:0;height:0;border:none"></td>
  <td style="width:12%;padding:0;height:0;border:none"></td>
  <td style="width:13%;padding:0;height:0;border:none"></td>
  <td style="width:12%;padding:0;height:0;border:none"></td>
  <td style="width:13%;padding:0;height:0;border:none"></td>
  <td style="width:12%;padding:0;height:0;border:none"></td>
  <td style="width:13%;padding:0;height:0;border:none"></td>
  <td style="width:12%;padding:0;height:0;border:none"></td>
</tr>
                <tr>
                    <td colspan="8" style="font-size: 14px; text-align: center; padding: 8px;font-weight: 500; border: 1px solid #000; color: #000;">费用分类</td>
                </tr>
                ${this.splitArray(this.feeList, 4).map((row) => `
                    <tr>
                        ${row.map((item) => `
                            <td style="padding: 8px; border: 1px solid #000; width: 13%;font-size: 14px; color: #000; text-align: left;">
                                ${item.name || ''}
                            </td>
                            <td style="padding: 8px; border: 1px solid #000; width: 12%;font-size: 14px; color: #000; text-align: right;">
                                ${formatMoney(item.value)}
                            </td>
                        `).join('')}
                        ${row.length < 4 ? Array(4 - row.length).fill().map(() => `
                            <td style="padding: 8px; border: 1px solid #000; width: 13%;"></td><td style="padding: 8px; border: 1px solid #000; width: 12%;"></td>
                        `).join('') : ''}
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
    `;

                            // 收费方式表格
                            const payModeTableHtml = `
                <div style="margin-bottom: 20px;">
                    <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;table-layout: fixed;border-top: none;">
                        <tbody>
                          <tr style="height:0;">
                          <td style="width:13%;padding:0;height:0;border:none"></td>
                          <td style="width:12%;padding:0;height:0;border:none"></td>
                          <td style="width:13%;padding:0;height:0;border:none"></td>
                          <td style="width:12%;padding:0;height:0;border:none"></td>
                          <td style="width:13%;padding:0;height:0;border:none"></td>
                          <td style="width:12%;padding:0;height:0;border:none"></td>
                          <td style="width:13%;padding:0;height:0;border:none"></td>
                          <td style="width:12%;padding:0;height:0;border:none"></td>
                        </tr>
                <tr>
                    <td colspan="8" style="font-size: 14px; text-align: center; padding: 8px;font-weight: 500; border: 1px solid #000; color: #000;">收费方式</td>
                </tr>
                ${this.splitArray(this.payModeList, 4).map((row) => `
                    <tr>
                        ${row.map((item) => `
                            <td style="padding: 8px; border: 1px solid #000; width: 13%;font-size: 14px; color: #000; text-align: left;">
                                ${item.name || ''}
                            </td>
                            <td style="padding: 8px; border: 1px solid #000; width: 12%;font-size: 14px; color: #000; text-align: right;">
                                ${formatMoney(item.value)}
                            </td>
                        `).join('')}
                        ${row.length < 4 ? Array(4 - row.length).fill().map(() => `
                            <td style="padding: 8px; border: 1px solid #000; width: 13%;"></td><td style="padding: 8px; border: 1px solid #000; width: 12%;"></td>
                        `).join('') : ''}
                    </tr>
                `).join('')}
            </tbody>
        </table>
    </div>
    `;

                            const summaryDate = beginDate === endDate ? beginDate : `${beginDate} ~ ${endDate}`;

                            return `
                            <div style="padding: 40px 24px; background: #fff; width: 100%; box-sizing: border-box;">
                                <div style="font-size: 22px; font-weight: bold; text-align: center; margin-bottom: 20px;">${clinicName}对账报表</div>
                                <div style="font-size: 16px; font-weight: 500; text-align: left; margin-bottom: 20px;">统计时间：${summaryDate || ''}</div>
                                ${feeTableHtml}
                                ${payModeTableHtml}
                            </div>
                            `;
                        },
                    },
                    mode: PrintMode.Electron,
                });
            },
            splitArray(baseArray, n = 4) {
                if (!baseArray) return [];
                const len = baseArray.length;
                const lineNum = len % n === 0 ? len / n : Math.floor((len / n) + 1);
                const res = [];
                for (let i = 0; i < lineNum; i++) {
                    const temp = baseArray.slice(i * n, i * n + n);
                    res.push(temp);
                }
                return res;
            },

            /**
             * @desc 拉取收费明细
             * <AUTHOR>
             * @date 2022-03-22 15:48:56
             */
            async fetchCashierTransaction() {
                const {
                    beginDate,
                    endDate,
                    offset,
                    action,
                    sourceType,
                    payModes,
                } = this.fetchParams;
                this.incomeDataLoading = true;
                const { data } = await StatisticsAPI.overview.fetchCashierTransaction({
                    clinicId: this.clinicId,
                    cashierId: this.queryUserId,
                    beginDate,
                    endDate,
                    offset,
                    size: this.pageSize,
                    action,
                    sourceType,
                    payModes,
                });
                this.incomeDataLoading = false;
                const {
                    header: tableHeader = [],
                    data: tableData = [],
                    summary,
                    total: incomeTotal = {},
                } = data;
                if (incomeTotal && incomeTotal.offset !== offset) return;
                this.incomeTableHeader = tableHeader.map((item) => {
                    item.thStyle = {
                        padding: '0 4px',
                    };
                    item.tdStyle = {
                        padding: '0 4px',
                        borderLeft: 'none',
                        borderRight: 'none',
                    };
                    item.titleAlign = item.align;
                    if (item.prop === 'patientName') {
                        item.titleAlign = 'left';
                        item.thStyle = {
                            padding: '0 4px 0 12px',
                        };
                        item.tdStyle = {
                            padding: '0 4px 0 12px',
                        };
                    } else if (item.prop === 'sourceType') {
                        item.width = 60;
                    } else if (item.prop === 'action') {
                        item.width = 50;
                    } else if (item.prop === 'product') {
                        item.titleAlign = 'center';
                        item.align = 'center';
                        item.width = 50;
                    } else if (item.prop === 'payMode') {
                        item.width = 76;
                    } else if (['discountFee', 'deductPrice', 'adjustmentFee'].indexOf(item.prop) > -1) {
                        //抵扣 优惠 议价
                        item.width = 68;
                    } else if (item.prop === 'employeeName') {
                        // 开单人
                        item.width = 60;
                    } else if (item.prop === 'chargeName') {
                        // 收费员
                        item.width = 60;
                    } else if (item.prop === 'created') {
                        // 收费时间
                        item.width = 156;
                        item.titleAlign = 'center';
                    } else {
                        item.width = 70;
                        item.titleAlign = item.align;
                    }
                    return item;
                });
                // 受管理-数据权限-工作台-收费员查看账目 开关影响
                if (this.noViewPermission) {
                    this.incomeTableData = [];
                    this.incomeTableSummary = null;
                    this.incomeTableTotalCount = 0;
                    return;
                }
                this.incomeTableData = tableData || [];
                this.incomeTableSummary = summary;
                this.incomeTableTotalCount = incomeTotal ? incomeTotal.count : 0;
            },

            handleSummaries(data, col) {
                if (!this.incomeTableSummary) return '';
                if (col.prop === 'patientName') {
                    return this.incomeTableSummary[col.prop];
                }
                // 不展示这几列总计
                if (['deductPrice','totalFee','discountFee', 'adjustmentFee','receivableFee'].indexOf(col.prop) > -1) {
                    return '';
                }
                return formatMoney(this.incomeTableSummary[col.prop]);
            },

            async getChargeSheetDetail(chargeSheetId) {
                try {
                    this.chargeLoading = true;
                    const { data } = await ChargeAPI.fetch(chargeSheetId);

                    const res = [];
                    data.chargeForms?.forEach((item) => {
                        item?.chargeFormItems?.forEach ((it) => {
                            const {
                                totalPrice = '', productType = '', doseCount = '', productSubType = '', name = '',
                            } = it || {};
                            let {
                                unit = '', unitCount = '',
                            } = it || {};
                            // if ( productType === GoodsTypeEnum.REGISTRATION) {
                            //     name = !!name ? '挂号费 - ' + name : ''
                            // }
                            if ([GoodsTypeEnum.REGISTRATION, GoodsTypeEnum.DECOCTION, GoodsTypeEnum.EXPRESS_DELIVERY].includes(productType)) {
                                unit = unit || '次';
                            }
                            if (GoodsTypeEnum.MEDICINE === productType && productSubType === 2) {
                                unitCount = doseCount * unitCount;
                            }

                            res.push({
                                name,
                                unit,
                                totalPrice,
                                unitCount,
                            });
                        });
                    });
                    return res;
                } catch (err) {
                    console.error(err);
                } finally {
                    this.chargeLoading = false;
                }
            },

            /**
             * @desc 支付方式数据
             * <AUTHOR>
             * @date 2022/10/27 15:00:58
             * @param {String}  clinicId
             * @param {String}  beginDate
             * @param {String}  endDate
             * @return
             */
            async getPayModesSourceTypeSelection() {
                if (this.noViewPermission) return;
                try {
                    const {
                        beginDate, endDate,
                    } = this.fetchParams;
                    const { data } = await StatisticsAPI.transaction.getPayModes({
                        beginDate,
                        endDate,
                        clinicId: this.clinicId,
                        cashierId: this.queryUserId,
                    });
                    this.payModeOptions = data?.payModesList;
                } catch (e) {
                    console.error(e);
                }

            },
            handleSelectPayModes(val) {
                const filterQuery = [];
                val.forEach((item) => {
                    // 有主分类不传
                    if (item.length > 1) {
                        item.shift();
                    }
                    filterQuery.push(item);
                });
                this.fetchParams.payModes = filterQuery.flat().map((x) => x.value).join();
                this.handleChangeFilter();
            },
        },
    };
</script>

<style lang="scss">
    @import '~styles/theme.scss';
    @import '~styles/mixin.scss';

    .cashier-work-summary-wrapper {
        height: 100%;

        .summary-handler {
            display: flex;
            align-items: center;

            .title {
                margin-right: 16px;
                font-size: 16px;
                font-weight: bold;
            }

            .quick-select-date-bar {
                display: flex;
                align-items: center;
            }
        }

        .outpatient-summary {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 24px 24px 24px;
            margin-top: 8px;
        }

        .summary-content {
            position: relative;
            display: flex;
            flex-direction: column;
            height: calc(100% - 32px);

            .tab-title {
                display: flex;
                align-items: flex-start;
                margin-top: 16px;
                margin-bottom: 16px;

                .abc-select-wrapper {
                    font-size: 13px;

                    .abc-input__inner {
                        font-size: 13px;
                    }
                }

                .patient-selector {
                    input::placeholder {
                        font-size: 13px;
                    }
                }

                .export-btn-content {
                    position: relative;
                    width: 60px;

                    .loading-spinner {
                        position: absolute;
                        top: 0;
                        left: -9px;
                        width: 30px;
                        text-align: center;
                    }

                    .export-word {
                        margin-left: 18px;
                        color: $T2;
                    }
                }
            }

            .abc-fixed-table.income-table {
                position: relative;
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);

                .detail-table-container::after {
                    border-bottom: none;
                }

                .table-title {
                    height: 33px;
                    padding-right: 10px;
                    line-height: 33px;
                    border-top: none;
                }

                .table-tr {
                    height: 40px;
                    padding-right: 10px;
                    line-height: 40px;
                    border-right: none;
                    border-left: none;

                    &:last-child {
                        border-bottom: none;
                    }
                }

                .label-describe {
                    color: $T2;
                }

                .table-td {
                    padding: 0 2px;
                }

                .table-empty {
                    top: 50%;

                    .label {
                        margin-top: 0;
                        font-size: 14px;
                        color: $T3;
                    }

                    .icon {
                        display: none;
                    }
                }

                .abc-table__header-wrapper {
                    border-bottom: 1px solid $P1;

                    table th {
                        color: $T2;
                        border: none;
                    }
                }

                .abc-table__body-wrapper,
                .abc-table__footer-wrapper {
                    table tr {
                        border-right: none;
                        border-left: none;
                    }

                    tbody td {
                        border-right: none !important;
                        border-left: none;
                    }
                }

                .abc-table__footer-wrapper {
                    background-color: #fafbfc;

                    tbody tr {
                        border: none;
                        border-top: 1px solid $P1;
                    }
                }

                table td div.cell,
                table th div.cell {
                    padding: 0;
                    text-overflow: unset;
                }
            }

            .abc-fixed-table {
                &.sale-table,
                &.record-table {
                    .detail-table-container {
                        .abc-table_empty-scroll-wrapper {
                            min-height: auto !important;
                        }
                    }
                }
            }

            .reconciliation-table {
                margin-bottom: 16px;
                overflow: hidden;
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);

                .table-header {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 32px;
                    color: $T2;
                    background: $P5;
                    border-bottom: 1px solid $P1;
                }

                .table-body {
                    min-height: 40px;

                    .table-tr {
                        display: flex;

                        &:not(:last-child) {
                            border-bottom: 1px solid $P6;
                        }
                    }

                    .table-cell {
                        display: flex;
                        align-items: center;
                        width: 25%;
                        height: 40px;

                        label {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 106px;
                            height: 100%;
                            border-right: 1px solid $P6;
                        }

                        .content {
                            display: flex;
                            flex: 1;
                            align-items: center;
                            height: 100%;
                            padding: 0 12px;
                            border-right: 1px solid $P6;
                        }

                        &.no-right-border {
                            .content {
                                border-right: 0;
                            }
                        }
                    }

                    .no-permission {
                        align-items: center;
                        justify-content: center;
                        height: 80px;
                        color: $T3;
                    }
                }
            }
        }

        .close-wrapper {
            position: absolute;
            top: 8px;
            right: 8px;
        }
    }

    .charge-sheet-info-popper {
        min-width: 346px;
        max-width: 500px;
        min-height: 160px;
        max-height: 180px;
        padding: 12px;
        overflow-y: auto;
        overflow-y: overlay;

        @include scrollBar;

        .charge-sheet-header {
            display: flex;
            align-items: center;
            padding-bottom: 8px;
            color: $S1;
            border-bottom: 1px dashed $P6;
        }

        .charge-sheet-item {
            display: flex;
            align-items: center;
            padding-top: 3px;
            padding-bottom: 3px;
            color: $T2;
        }

        .charge-sheet-header .th,
        .charge-sheet-item .td {
            width: 40px;
            text-align: center;

            &.name {
                flex: 1;
                text-align: left;
            }

            &.total-price {
                width: 80px;
                text-align: right;
            }
        }
    }
</style>
