<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="退费"
        class="refund-way-dialog"
        content-styles="padding: 0 0 24px"
        append-to-body
        :shadow="!!awaitType"
        :auto-focus="false"
        data-cy="refund-way-dialog"
    >
        <div class="dialog-content clearfix">
            <h3 class="charge-dialog-header">
                <abc-money :value="surplusRefund" value-tag="b" :is-show-space="true"></abc-money>
            </h3>

            <template v-if="!disabledRefundWayTab">
                <abc-tabs-v2
                    v-if="chargeSheetId"
                    v-model="refundWayTab"
                    :option="refundWayOptions"
                    :border="false"
                    size="middle"
                    center
                    @change="changeTab"
                ></abc-tabs-v2>
                <abc-text
                    v-else
                    tag="p"
                    style="text-align: center;"
                >
                    撤销结算后，可在结算明细中重新发起结算
                </abc-text>
            </template>

            <div v-if="refundWayTab === 0" class="charge-paid-modes">
                <abc-tooltip
                    v-for="item of paymentSummaryInfos"
                    :key="item.chargePayTransactionId"
                    theme="black"
                    trigger="hover"
                    placement="top"
                    :open-delay="400"
                    :disabled="!payModeValidator(item, 'back').message"
                    :content="payModeValidator(item, 'back').message"
                >
                    <div
                        class="paid-mode-item"
                        :class="{
                            'is-disabled': payModeIsDisabled(item),
                            'is-selected': payModeIsSelected(item),
                        }"
                        @click="selectOriginPayMode(item)"
                    >
                        <img v-if="payModeIcon(item.payMode)" :src="payModeIcon(item.payMode)" />
                        <div>{{ item.payModeDisplayName || item.payModeName }}</div>
                        <div v-if="item.payMode === PayModeEnum.ARREARS" class="content">
                            <span>欠费 <abc-money :value="item.paidAmount" :is-show-space="true"></abc-money></span>
                            <span>
                                已销账 <abc-money :value="Math.abs(item.refundedAmount)" :is-show-space="true"></abc-money>
                            </span>
                        </div>
                        <div v-else class="content">
                            <span>已支付 <abc-money :value="item.paidAmount" :is-show-space="true"></abc-money></span>
                            <span v-if="item.refundedAmount">
                                已退 <abc-money :value="Math.abs(item.refundedAmount)" :is-show-space="true"></abc-money>
                            </span>
                        </div>
                        <template v-if="item.isCanRefund">
                            <abc-checkbox
                                v-if="item.payMode === PayModeEnum.OUTPATIENT_CENTER_PAY"
                                :disabled="payModeIsDisabled(item)"
                                :value="+payModeIsSelected(item)"
                            ></abc-checkbox>
                            <abc-radio
                                v-else
                                :disabled="payModeIsDisabled(item)"
                                :value="+payModeIsSelected(item)"
                                :label="1"
                            ></abc-radio>
                        </template>
                        <div v-else class="refunded">
                        </div>
                    </div>
                </abc-tooltip>
            </div>

            <abc-pay-mode-list
                v-else
                v-model="refundPostData"
                :patient-card-pay-mode-list="patientCardPayment"
                :hidden-pay-mode-list="hiddenPayModeList"
                :hidden-pay-mode-type-list="hiddenPayModeTypeList"
                :pay-mode-validator="payModeValidator"
                :show-balance="false"
                @toOpenABCPay="toOpenABCPay"
                @change="selectPayMode"
            >
            </abc-pay-mode-list>

            <div v-if="!refundWarnTips && payModeTips" class="tips-wrapper">
                <abc-tips-card-v2 theme="primary">
                    {{ payModeTips }}
                </abc-tips-card-v2>
            </div>

            <div v-if="refundWarnTips" class="tips-wrapper">
                <abc-tips-card-v2 theme="warning">
                    {{ refundWarnTips }}
                </abc-tips-card-v2>
            </div>

            <div v-if="refundPostData.payMode === PayModeEnum['MEMBER_CARD']" class="total-box">
                <div>
                    会员卡<span><abc-money :value="refundPostData.amount"></abc-money></span>
                </div>
                <div>
                    <span>本金 <abc-money :show-symbol="false" :value="principalAmount"></abc-money></span>
                    <span>赠金 <abc-money :show-symbol="false" :value="presentAmount"></abc-money></span>
                </div>
            </div>

            <div v-if="refundPostData.payMode === PayModeEnum.PATIENT_CARD" class="total-box">
                <div>
                    卡项<span><abc-money :value="refundPostData.amount"></abc-money></span>
                </div>
            </div>
        </div>

        <div v-if="bottomExpand" slot="bottom-extend" class="charge-dialog-bottom-extend">
            <abc-input
                v-model="chargeComment"
                :width="360"
                max-length="100"
                placeholder="备注：退费原因"
                data-cy="refund-way-dialog-remark-input"
            ></abc-input>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button
                class="add-remark"
                variant="text"
                size="small"
                data-cy="refund-way-dialog-add-remark-button"
                @click="bottomExpand = !bottomExpand"
            >
                添加备注<i
                    :class="['iconfont', bottomExpand ? 'cis-icon-dropdown_triangle_up' : 'cis-icon-dropdown_triangle']"
                    style=" margin-right: 0; font-size: 12px;"
                ></i>
            </abc-button>
            <div style="flex: 1;"></div>
            <div v-if="isSelectedSocialPay">
                <abc-button
                    v-abc-check-electron="isDaubCardChargeMode"
                    style="margin-right: 8px;"
                    :loading="buttonLoading"
                    @click="handleClickConfirm"
                >
                    {{ isScanCodeChargeMode ? '退费' : '直接退费' }}
                </abc-button>
            </div>
            <abc-button
                v-else
                :disabled="!refundPostData.payMode"
                :loading="buttonLoading"
                data-cy="refund-way-dialog-confirm"
                @click="handleClickConfirm"
            >
                退费 <abc-money :value="refundPostData.amount" :is-show-space="true"></abc-money>
            </abc-button>
            <abc-button variant="ghost" data-cy="refund-way-dialog-cancel" @click="showDialog = false">
                取消
            </abc-button>
            <abc-icon
                v-if="isSelectedSocialPay && $abcSocialSecurity.isEnableScanCodeMode"
                :size="12"
                icon="qiehuan"
                color="#AAB4BF"
                @click="onClickSwitchChargeMode"
            ></abc-icon>
        </div>
        <div slot="shadow">
            <abc-modal
                v-if="awaitType"
                v-model="showMessage"
                preset="message"
                :type="messageType"
                :show-close="false"
                :show-cancel="false"
                :show-confirm="false"
                :show-footer="messageType === 'warn'"
                :success-animation="messageType === 'success'"
                show-icon
                :title="title"
                :content="[content]"
                dialog-content-styles="width: 240px;min-with: 240px"
            >
                <template v-if="awaitType === 'alert'" slot="footerPrepend">
                    <abc-button style="min-width: 216px;" @click="handleCloseMessage">
                        确认
                    </abc-button>
                </template>
                <template v-if="awaitType === 'confirm'" slot="footerPrepend">
                    <template v-if="refundedId && refundPostData.payMode === PayModeEnum.SOCIAL_CARD">
                        <abc-button style="width: 102px;" @click="confirmRefund">
                            重新刷卡
                        </abc-button>
                        <abc-button variant="ghost" style="width: 102px;" @click="handleCloseMessage">
                            取消
                        </abc-button>
                    </template>
                    <template v-else>
                        <abc-button style="width: 102px;" @click="callBackFunc(true)">
                            继续退款
                        </abc-button>
                        <abc-button variant="ghost" style="width: 102px;" @click="callBackFunc(false)">
                            取消
                        </abc-button>
                    </template>
                </template>
            </abc-modal>
        </div>
        <abc-modal
            v-model="showSocialRefundWarnModal"
            type="warn"
            :show-confirm="false"
            :show-cancel="false"
        >
            <h4 style=" margin-bottom: 8px; font-size: 16px; font-weight: bold;">
                <abc-icon icon="Attention" color="orange" size="16px"></abc-icon>
                退费确认
            </h4>
            <p>退费项目及金额涉及医保收费，使用非医保退费方式将面临套现风险，确定继续退费？</p>
            <template #footerPrepend>
                <abc-button :disabled="confirmDisabled" @click="confirmRefund">
                    {{ confirmText }}
                </abc-button>
                <abc-button variant="ghost" @click="showSocialRefundWarnModal = false">
                    取消
                </abc-button>
            </template>
        </abc-modal>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import Big from 'big.js';
    import ChargeAPI from 'api/charge';
    import {
        PayModeList,
        PayModeEnum,
        RefundTypeEnum,
        OwedChargeStatusEnum,
        DisabledrefundEnum,
        PaySubModeEnum,
        PayModeTypeEnum,
    } from '@/service/charge/constants.js';
    import { moneyNum } from '@/filters/index';
    import AbcPayModeList from '@/service/charge/components/pay-mode-list.vue';
    import { red } from 'utils/math';
    import { sum } from '@/utils/calculation';

    import Response from 'utils/Response';
    import { socialChargeModeConst } from 'src/social-security/common/constants';
    import { navigateToAggregatePaymentContentSetting } from '@/core/navigate-helper';
    import { mapGetters } from 'vuex';
    import Logger from 'utils/logger';
    import { getSafeNumber } from 'utils/index';

    // 操作提示时间
    const ALERT_TIME = 2000;

    export default {
        name: 'RefundWayDialog',
        components: {
            AbcPayModeList,
        },
        props: {
            chargeSheetId: String,
            hospitalSheetId: String,
            value: Boolean,
            refundFee: Number,
            netIncomeFee: Number, // 欠退总金额
            receivableFee: Number, // 总共支付金额
            paymentSummaryInfos: Array,
            patientName: String,
            chargeTransactions: {
                type: Array,
                default: () => [],
            },
            refundType: {
                type: Number,
                default: RefundTypeEnum.NORMAL,
                validator: (value) => [RefundTypeEnum.NORMAL, RefundTypeEnum.REFUND_PAID].indexOf(value) !== -1,
            },
            refundData: Object,
            chargeConfig: {
                type: Object,
            },

            disabledRefundWayTab: {
                type: Boolean,
                default: false,
            },

            isHospitalSheet: {
                type: Boolean,
                default: false,
            },

            // 欠费状态
            owedStatus: {
                type: Number,
                default: OwedChargeStatusEnum.NO_OWED,
            },
            // 大开关，能否退医保
            canRefundShebao: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                PayModeEnum, // 支付方式常量
                showDialog: this.value,
                refundWayTab: 0,

                bottomExpand: false,
                chargeComment: '', // 备注：退费原因

                buttonLoading: false,

                awaitType: null, // 等待类型
                title: '', // 标题
                content: '', // 描述
                showMessage: false, // 退费二次信息弹窗
                messageType: '',

                surplusRefund: 0, // 剩余退费金额

                principalAmount: 0, // 本金
                presentAmount: 0, // 赠送
                isFirstRefund: true, // 是否第一次退费

                refundedIds: [], // 已经刷卡退费了的流水号id
                refundedId: null, // 三方退费暂存id

                timeoutRefundStatus: null, // 定时查看退款状态
                callBackFunc: null,
                paySubMode: null,

                canRefundModes: [],

                refundPostData: {
                    keyId: '',
                    payMode: 0,
                    amount: 0,
                    paySubMode: undefined,
                    thirdPartyPayCardId: undefined,
                    transactionIds: undefined,
                },

                hiddenPayModeList: [PayModeEnum.ABC_PAY],
                hiddenPayModeTypeList: [PayModeTypeEnum.THIRD_COMMON],
                isSocialCardPaid0: false,

                socialChargeMode: socialChargeModeConst.DAUB_CARD, // 医保收费模式，默认刷卡结算
                showSocialRefundWarnModal: false,
                confirmText: '确定 5',
                confirmDisabled: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isPharmacy',
                'clinicBasic',
            ]),
            isPrecheckRefundAllSocialItem() {
                const { shebao } = this.clinicBasic.charge || {};
                const { allRefund } = shebao || {};
                return allRefund;
            },
            refundWayOptions() {
                const { payMode } = this.refundPostData;
                return [
                    {
                        label: '按支付原路退回',
                        value: 0,
                    },
                    {
                        label: '全部退费方式',
                        value: 1,
                        disabled: this.isPrecheckRefundAllSocialItem && payMode === PayModeEnum.SOCIAL_CARD,
                        disabledTips: '请先完成医保退费，再退非医保结算费用',
                    },
                ];
            },
            availablePayModeIds() {
                return this.chargeConfig.chargePayModeConfigs.map((item) => item.payMode);
            },

            payModeTips() {
                const {
                    payMode,
                    payModeName,
                } = this.refundPostData;
                if (!payMode) return '';
                if (payMode === PayModeEnum.PATIENT_CARD) {
                    return '卡项支付金额需优先退回，以规避套现';
                }
                if (payMode === PayModeEnum.ARREARS) {
                    return '欠费金额需先进行销账，销账后无需退回患者费用';
                }
                if (this.refundedId && payMode === PayModeEnum.SOCIAL_CARD) {
                    return '医保卡退费必须原单金额原路退回';
                }
                if (this.refundedId && payMode === PayModeEnum.OUTPATIENT_CENTER_PAY) {
                    return '通过沈阳智慧医保APP支付的按医保限制只能全部退费';
                }
                if (payMode === PayModeEnum.ABC_PAY) {
                    return '';
                }

                const target = PayModeList.find((it) => it.mode === payMode);
                if (target && target.refundDesc) {
                    return target.refundDesc;
                }
                return `请确认通过${payModeName || '该支付'}通道完成退款`;
            },
            /**
             * @desc 医保退款警告提示
             * @desc 1、退费选项中包含医保支付项目，但没有选择医保退款
             * @desc 2、退费选项中没有全部勾选医保支付的项目，但选择医保退款
             * <AUTHOR>
             * @date 2022/08/19 15:12:03
             */
            refundWarnTips() {
                const { healthCardPayType } = this.refundData || {};
                if (!healthCardPayType) return;
                const { payMode } = this.refundPostData;
                if (payMode === PayModeEnum.SOCIAL_CARD) {
                    if (healthCardPayType === 'part') {
                        return '选择的退费项目与医保收费时项目不一致，使用医保退费或将面临监管风险';
                    }
                } else {
                    return '退费项目中含医保项目，使用非医保退费方式存在套现风险';
                }
                return '';
            },

            patientCardPayment() {
                const {
                    paymentSummaryInfos = [],
                } = this;
                return paymentSummaryInfos.filter((it) => it.payMode === PayModeEnum.PATIENT_CARD).map((it) => {
                    return {
                        ...it,
                        name: it.payModeDisplayName || it.payModeName,
                    };
                });
            },

            // 是否铁路医保支付
            isRailwaySocialPay() {
                return this.refundPostData.payMode === PayModeEnum.RAILWAY_SOCIAL_PAY;
            },
            // 是否选中医保支付，排除医保记账
            isSelectedSocialPay() {
                if (!this.refundPostData) {
                    return false;
                }
                if (this.$abcSocialSecurity.isOpenSocial === false) {
                    return false;
                }
                return (this.refundPostData.payMode === PayModeEnum.SOCIAL_CARD ||
                    this.refundPostData.payMode === PayModeEnum.AIR_PAY ||
                    this.refundPostData.payMode === PayModeEnum.SHEBAO_YIMA_PAY ||
                    this.refundPostData.payMode === PayModeEnum.MULAID_PAY ||
                    this.refundPostData.payMode === PayModeEnum.PERSONAL_POS_PAY
                );
            },
            // 是否直接退费
            isDaubCardChargeMode() {
                return this.socialChargeMode === socialChargeModeConst.DAUB_CARD;
            },
            // 是否扫码退费
            isScanCodeChargeMode() {
                return this.socialChargeMode === socialChargeModeConst.SCAN_CODE;
            },
        },
        watch: {
            // 双向数据绑定
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
            // 当收费方式改变时，如果当前选中的收费方式已被移除，需要处理
            'chargeConfig.chargePayModeConfigs': function(val) {
                if (this.refundPostData.payMode) {
                    const exit = val.find(({ payMode }) => payMode === this.refundPostData.payMode);
                    if (!exit) {
                        this.refundPostData.payMode = null;
                    }
                }
            },
            /**
             * @desc 退费区分当前是否欠费，欠费时为「欠费待还」退款，可以退欠费销账，不欠费时是其它方式支付的，不能退到欠费。
             * <AUTHOR>
             * @date 2022/05/25 16:13:45
             */
            owedStatus: {
                handler(val) {
                    if (val === OwedChargeStatusEnum.NO_OWED) {
                        this.hiddenPayModeList.push(PayModeEnum.ARREARS);
                    }
                },
                immediate: true,
            },

            showSocialRefundWarnModal(v) {
                if (!v) {
                    this._timer && clearInterval(this._timer);
                    this.confirmText = '确定 5';
                }
            },
        },
        created() {
            if (this.$abcSocialSecurity.isEnableScanCodeMode) {
                this.socialChargeMode = this.$abcSocialSecurity.cacheChargeMode;
            }

            this.surplusRefund = this.refundFee; // 剩余退费金额
            this.initCanRefundModes();
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            payModeValidator(payItem, type) {
                const {
                    payMode: currentPayMode,
                } = this.refundPostData;
                const {
                    payMode, isCanRefund, disableRefundCode,
                } = payItem;

                // 开启开关后，选中了医保退费，其它支付方式都不能选
                if (
                    this.isPrecheckRefundAllSocialItem &&
                    currentPayMode === PayModeEnum.SOCIAL_CARD &&
                    payMode !== PayModeEnum.SOCIAL_CARD
                ) {
                    return {
                        error: true,
                        message: '请先完成医保退费，再退非医保结算费用',
                    };
                }

                if (isCanRefund === 0 && disableRefundCode === DisabledrefundEnum.REFUND_AFTER_SHEBAO) {
                    return {
                        error: true,
                        message: '请先退费医保统筹金额',
                    };
                }

                if (this.canRefundModes.includes(payMode)) {
                    return {
                        error: false,
                        message: '',
                    };
                }
                // 原路退回校验
                if (type === 'back' && isCanRefund === 0) {
                    if (disableRefundCode === DisabledrefundEnum.PATIENT_CARD) {
                        return {
                            error: true,
                            message: '卡项已过期/卡项已注销，不可原路退回',
                        };
                    }
                    if (disableRefundCode === DisabledrefundEnum.MEMBER_CARD) {
                        return {
                            error: true,
                            message: '会员已失效，不可原路退回',
                        };
                    }
                }
                return {
                    error: true,
                    message: '',
                };
            },

            payModeIcon(payMode) {
                const target = PayModeList.find((it) => it.mode === payMode);
                return target ? target.img : '';
            },

            changeTab(tab) {
                this.refundWayTab = tab;
                this.initCanRefundModes();
            },

            initCanRefundModes() {
                this.canRefundModes = [];
                this.isSocialCardPaid0 = false;

                // 医保0元支付 需要优先退费 但不强制只能退医保0元
                const socialCardPaid0 = this.paymentSummaryInfos.find((item) => {
                    return item.payMode === PayModeEnum.SOCIAL_CARD && item.isCanRefund && item.amount === 0;
                });
                if (socialCardPaid0) {
                    const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(PayModeEnum.SOCIAL_CARD);
                    // 是医保卡支付的，需要在医保端默认选中0元支付退费
                    if (thirdPartyPayTransaction) {
                        this.isSocialCardPaid0 = true;
                        // 能调小端退费 || 扫码退费
                        this.canRefundModes.push(PayModeEnum.SOCIAL_CARD);
                        if (this.refundWayTab === 0) {
                            this.selectOriginPayMode(socialCardPaid0);
                        } else {
                            this.selectPayMode(socialCardPaid0);
                        }
                    }
                }

                // 微信医保支付中-医保部分 需要先退
                const wechatSocialPay = this.paymentSummaryInfos.find((item) => {
                    return item.isCanRefund &&
                        item.payMode === PayModeEnum.SOCIAL_CARD &&
                        item.paySubMode === PaySubModeEnum.WECHAT_SOCIAL_PAY_SOCIAL;
                });
                if (wechatSocialPay) {
                    const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(PayModeEnum.SOCIAL_CARD);
                    if (thirdPartyPayTransaction) {
                        this.canRefundModes.push(PayModeEnum.SOCIAL_CARD);
                        if (this.refundWayTab === 0) {
                            this.selectOriginPayMode(wechatSocialPay);
                        } else {
                            this.selectPayMode(wechatSocialPay);
                        }
                        return;
                    }
                }

                // 优先退欠费用
                const arrearsPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.ARREARS && item.isCanRefund);
                if (arrearsPay) {
                    this.canRefundModes.push(PayModeEnum.ARREARS);
                    if (this.refundWayTab === 0) {
                        this.selectOriginPayMode(arrearsPay);
                    } else {
                        this.selectPayMode(arrearsPay);
                    }
                    return;
                }

                // 默认需要先退完会员卡
                const memberPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.MEMBER_CARD && item.isCanRefund);
                if (memberPay && memberPay.isCanRefund && this.availablePayModeIds.includes(PayModeEnum.MEMBER_CARD)) {
                    this.principalAmount = memberPay.principalAmount;
                    this.presentAmount = memberPay.presentAmount;
                    this.canRefundModes.push(PayModeEnum.MEMBER_CARD);

                    if (this.refundWayTab === 0) {
                        this.selectOriginPayMode(memberPay);
                    } else {
                        this.selectPayMode({
                            payMode: PayModeEnum.MEMBER_CARD,
                            amount: Math.min(this.surplusRefund, memberPay.amount),
                            transactionIds: memberPay.transactionIds,
                        });
                    }
                    return;
                }

                // 默认需要先退完患者卡项
                const patientCardPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.PATIENT_CARD && item.isCanRefund);
                if (patientCardPay) {
                    this.canRefundModes.push(PayModeEnum.PATIENT_CARD);
                    if (this.refundWayTab === 0) {
                        this.selectOriginPayMode(patientCardPay);
                    } else {
                        this.selectPayMode(patientCardPay);
                    }
                    return;
                }

                const socialCardPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.SOCIAL_CARD && item.isCanRefund);
                if (socialCardPay && this.canRefundShebao) {
                    if (this.$abcSocialSecurity.isOpenSocial) {
                        // 能调小端退费 || 扫码退费
                        const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(PayModeEnum.SOCIAL_CARD);
                        if (thirdPartyPayTransaction) {
                            this.canRefundModes.push(PayModeEnum.SOCIAL_CARD);
                        }
                    } else {
                        this.canRefundModes.push(PayModeEnum.SOCIAL_CARD);
                    }
                }

                const airPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.AIR_PAY && item.isCanRefund);
                if (airPay) {
                    if (this.$abcSocialSecurity.isOpenSocial) {
                        // 能调小端退费 || 扫码退费
                        const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(PayModeEnum.AIR_PAY);
                        if (thirdPartyPayTransaction) {
                            this.canRefundModes.push(PayModeEnum.AIR_PAY);
                        }
                    } else {
                        this.canRefundModes.push(PayModeEnum.AIR_PAY);
                    }
                }

                const mulaidPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.MULAID_PAY && item.isCanRefund);
                if (mulaidPay) {
                    if (this.$abcSocialSecurity.isOpenSocial) {
                        // 能调小端退费 || 扫码退费
                        const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(PayModeEnum.MULAID_PAY);
                        if (thirdPartyPayTransaction) {
                            this.canRefundModes.push(PayModeEnum.MULAID_PAY);
                        }
                    } else {
                        this.canRefundModes.push(PayModeEnum.MULAID_PAY);
                    }
                }

                // 默认允许退 银联Pos个账 的支付方式
                const yinlianPosPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.PERSONAL_POS_PAY && item.isCanRefund);
                if (yinlianPosPay) {
                    if (this.$abcSocialSecurity.isOpenSocial) {
                        // 能调小端退费 || 扫码退费
                        const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(PayModeEnum.PERSONAL_POS_PAY);
                        if (thirdPartyPayTransaction) {
                            this.canRefundModes.push(PayModeEnum.PERSONAL_POS_PAY);
                        }
                    } else {
                        this.canRefundModes.push(PayModeEnum.PERSONAL_POS_PAY);
                    }
                }

                // 默认允许退 社保一码付 的支付方式
                const yimaPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.SHEBAO_YIMA_PAY && item.isCanRefund);
                if (yimaPay) {
                    if (this.$abcSocialSecurity.isOpenSocial) {
                        // 能调小端退费 || 扫码退费
                        const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(PayModeEnum.SHEBAO_YIMA_PAY);
                        if (thirdPartyPayTransaction) {
                            this.canRefundModes.push(PayModeEnum.SHEBAO_YIMA_PAY);
                        }
                    } else {
                        this.canRefundModes.push(PayModeEnum.SHEBAO_YIMA_PAY);
                    }
                }

                // 诊间支付
                const outpatientCenterPay = this.paymentSummaryInfos.find((item) => item.payMode === PayModeEnum.OUTPATIENT_CENTER_PAY && item.isCanRefund);
                if (outpatientCenterPay) {
                    if (this.$abcSocialSecurity.isOpenSocial) {
                        // 能调小端退费 || 扫码退费
                        const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(PayModeEnum.OUTPATIENT_CENTER_PAY);
                        if (thirdPartyPayTransaction.length) {
                            this.canRefundModes.push(PayModeEnum.OUTPATIENT_CENTER_PAY);
                        }
                    } else {
                        this.canRefundModes.push(PayModeEnum.OUTPATIENT_CENTER_PAY);
                    }
                }

                // 其他自由选择情况
                this.availablePayModeIds.forEach((payMode) => {
                    if (payMode !== PayModeEnum.MEMBER_CARD && payMode !== PayModeEnum.SOCIAL_CARD) {
                        this.canRefundModes.push(payMode);
                    }
                });

                // 默认允许退 欠费 的支付方式
                this.canRefundModes.push(PayModeEnum.ARREARS);

                // 优选选中医保
                let canRefundMode = this.paymentSummaryInfos.find((item) => item.isCanRefund && item.payMode === PayModeEnum.SOCIAL_CARD);
                if (!canRefundMode) {
                    canRefundMode = this.paymentSummaryInfos.find((item) => item.isCanRefund);
                }
                if (canRefundMode) {
                    if (this.refundWayTab === 0) {
                        this.selectOriginPayMode(canRefundMode);
                    } else {
                        this.selectPayMode({
                            payMode: canRefundMode.payMode,
                        });
                    }
                }

            },

            /**
             * @desc 选择原路退回方式
             * <AUTHOR>
             * @date 2021-10-11 17:17:55
             */
            selectOriginPayMode(payItem) {
                if (this.payModeIsDisabled(payItem)) return;

                const {
                    payMode,
                    payModeName,
                    paySubMode,
                    paySubModeName,
                    payModeType,
                    amount = 0,
                    isCanRefund,
                    thirdPartyPayCardId,
                    chargePayTransactionId,
                    transactionIds,
                    keyId,
                } = payItem;
                if (!isCanRefund) return;
                if (this.canRefundModes.includes(payMode)) {
                    this.refundedId = null;
                    this.refundPostData = {
                        keyId,
                        payMode,
                        payModeName,
                        paySubMode,
                        paySubModeName,
                        payModeType,
                        amount,
                        thirdPartyPayCardId,
                        chargePayTransactionId,
                        transactionIds,
                    };

                    const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(payMode);
                    if (payMode === PayModeEnum.OUTPATIENT_CENTER_PAY && thirdPartyPayTransaction.length) {
                        const amounts = thirdPartyPayTransaction.map((x) => Math.abs(x.amount));
                        this.refundPostData.amount = sum(...amounts);
                        this.refundedId = thirdPartyPayTransaction[0].id;
                        return;
                    }
                    if (thirdPartyPayTransaction) {
                        this.refundPostData.amount = Math.abs(thirdPartyPayTransaction.amount);
                        if (
                            payMode === PayModeEnum.SOCIAL_CARD ||
                            payMode === PayModeEnum.AIR_PAY ||
                            payMode === PayModeEnum.SHEBAO_YIMA_PAY ||
                            payMode === PayModeEnum.MULAID_PAY ||
                            payMode === PayModeEnum.RAILWAY_SOCIAL_PAY ||
                            payMode === PayModeEnum.PERSONAL_POS_PAY
                        ) {
                            this.refundedId = thirdPartyPayTransaction.id;
                        }
                    } else {
                        this.refundPostData.amount = Math.min(this.surplusRefund, amount);
                    }
                }
            },
            /**
             * @desc 选择全部退费方式
             * <AUTHOR>
             * @date 2021-10-12 08:54:01
             */
            selectPayMode(payItem) {
                if (this.payModeIsDisabled(payItem)) return;

                const {
                    payMode, amount = this.surplusRefund, thirdPartyPayCardId, transactionIds, name, payModeName,
                } = payItem;

                this.refundedId = null;
                this.refundPostData = {
                    payModeName: payModeName || name,
                    payMode,
                    thirdPartyPayCardId,
                    transactionIds,
                    amount: Math.min(this.surplusRefund, amount),
                };

                const thirdPartyPayTransaction = this.getThirdPartyPayTransaction(payMode);

                if (thirdPartyPayTransaction) {
                    if (
                        payMode === PayModeEnum.SOCIAL_CARD ||
                        payMode === PayModeEnum.AIR_PAY ||
                        payMode === PayModeEnum.SHEBAO_YIMA_PAY ||
                        payMode === PayModeEnum.MULAID_PAY ||
                        payMode === PayModeEnum.PERSONAL_POS_PAY
                    ) {
                        this.refundedId = thirdPartyPayTransaction.id;
                    }
                    this.refundPostData.amount = Math.abs(thirdPartyPayTransaction.amount);
                }
            },

            // 跳转到设置聚合支付
            toOpenABCPay() {
                this.showDialog = false;
                navigateToAggregatePaymentContentSetting(this.currentClinic);
            },

            // 获取 微信，社保卡 第三方支付交易记录
            getThirdPartyPayTransaction(payMode) {
                if ([
                    PayModeEnum.SOCIAL_CARD,
                    PayModeEnum.WECHAT_PAY,
                    PayModeEnum.AIR_PAY,
                    PayModeEnum.MULAID_PAY,
                    PayModeEnum.OUTPATIENT_CENTER_PAY,
                    PayModeEnum.RAILWAY_SOCIAL_PAY,
                    PayModeEnum.SHEBAO_YIMA_PAY,
                    PayModeEnum.PERSONAL_POS_PAY,
                ].indexOf(payMode) === -1) return null;

                if (payMode === PayModeEnum.OUTPATIENT_CENTER_PAY) {
                    // 诊间支付只能全退
                    return this.chargeTransactions.slice().filter((item) => {
                        return item.payMode === payMode &&
                            item.thirdPartyPayTransactionId &&
                            item.amount >= 0 &&
                            item.amount <= this.surplusRefund &&
                            item.isRefunded === 0;
                    });

                }
                return this.chargeTransactions.slice().sort((a, b) => {
                    const aDate = new Date(a.created);
                    const bDate = new Date(b.created);
                    return bDate.getTime() - aDate.getTime();
                }).find((item) => {
                    return item.payMode === payMode &&
                        item.thirdPartyPayTransactionId &&
                        item.amount >= 0 &&
                        item.amount <= this.surplusRefund &&
                        !this.refundedIds.includes(item.id) &&
                        item.isRefunded === 0;
                });
            },

            payModeIsSelected(item) {
                const {
                    keyId,
                    payMode,
                    payModeType,
                    thirdPartyPayCardId,
                    chargePayTransactionId,
                } = this.refundPostData;
                if (!payMode) return false;
                if (payMode === PayModeEnum.PATIENT_CARD) {
                    return thirdPartyPayCardId === item.thirdPartyPayCardId;
                }
                if (payModeType === PayModeTypeEnum.THIRD_COMMON || payMode === PayModeEnum.ABC_PAY) {
                    return chargePayTransactionId === item.chargePayTransactionId;
                }
                return item.keyId === keyId;
            },

            payModeIsDisabled(item) {
                const {
                    payMode,
                } = this.refundPostData;
                // 如果已经选中了医保退费，其它支付方式都不能选
                if (
                    this.isPrecheckRefundAllSocialItem &&
                    payMode === PayModeEnum.SOCIAL_CARD &&
                    item.payMode !== PayModeEnum.SOCIAL_CARD
                ) return true;
                // 诊间支付默认选中并disabled
                if (item.payMode === PayModeEnum.OUTPATIENT_CENTER_PAY) return true;
                if (!this.canRefundModes.includes(item.payMode)) return true;

                // 医保个账 同时包含医保统筹支付可退的情况下，不能选择
                if (item.payMode === PayModeEnum.PERSONAL_POS_PAY) {
                    return !!this.paymentSummaryInfos.find((it) => it.payMode === PayModeEnum.SOCIAL_CARD && it.isCanRefund);
                }
                return this.refundWayTab === 0 && !item.isCanRefund;
            },

            handleAlertRefund() {
                return new Promise((resolve) => {
                    this.handleLoading('confirm', '退费提醒', '点击继续退款，微信线上支付金额原路退回，子商户将自动扣款');
                    this.callBackFunc = (goOn) => {
                        resolve(goOn);
                    };
                })
                    .finally(() => {
                        this.handleLoading();
                        this.callBackFunc = null;
                    });
            },

            handleClickConfirm() {
                const {
                    payMode, paySubModeName, amount,
                } = this.refundPostData;

                // 使用非医保退费的金额大于非医保支付金额退款；如总额100，医保50，现金50，退款是选择现金>50
                this.refundSocialConfirm(payMode);
                if (this.showSocialRefundWarnModal) return;

                /**
                 * 1. 非医保退费
                 * 2. 存在医保 0 元支付
                 * 3. 非医保方式将要退完
                 */
                if (
                    payMode !== PayModeEnum.SOCIAL_CARD &&
                    amount === this.netIncomeFee &&
                    this.isSocialCardPaid0
                ) {
                    this.$alert({
                        type: 'info',
                        title: '提醒',
                        content: '完成退费后，将自动撤销已上报的医保0元处方信息',
                        onConfirm: () => {
                            this.confirmRefund();
                        },
                    });
                    return;
                }

                if (payMode !== PayModeEnum.SOCIAL_CARD) {
                    const record = this.chargeTransactions.filter((item) => {
                        return item.payMode === PayModeEnum.SOCIAL_CARD &&
                            !this.refundedIds.includes(item.id) &&
                            !!item.thirdPartyPayTransactionId &&
                            item.amount >= 0 &&
                            item.amount <= this.surplusRefund &&
                            item.isRefunded === 0;
                    });
                    if (record.length > 0) {
                        let total = record.reduce((sum, cur) => {
                            return sum + cur.amount * 100;
                        }, 0);
                        total /= 100;
                        if (amount > this.netIncomeFee - total) {
                            this.$confirm({
                                type: 'warn',
                                title: '退费确认',
                                content: [ '医保部分退费将导致剩余医保费用无法退还，确定继续退费？' ],
                                onConfirm: this.confirmRefund,
                            });
                            return;
                        }
                    }

                    /**
                     * @desc 当选择非医保退费，且退完全部费用，如果这个单子包含 0元医保支付，需要提示
                     * 【退费将导致订单关闭时提示医保0元退费】
                     * https://www.tapd.cn/22044681/prong/stories/view/1122044681001018737
                     * <AUTHOR>
                     * @date 2022-07-11 13:59:00
                     */
                    // 包含 0 元医保支付
                    if (this.isSocialCardPaid0) {
                        this.$alert({
                            type: 'warn',
                            title: '医保退费提醒',
                            content: '本次退费后收费单将关闭，需先将医保0元退费以撤回结算备案信息',
                        });
                        return;
                    }

                }

                if (payMode === PayModeEnum.ABC_PAY) {
                    this.$confirm({
                        title: `确定退款至患者${paySubModeName || ''}支付账户？`,
                        content: ['退费后大约30秒后到账'],
                        onConfirm: this.confirmRefund,
                    });
                    return;
                }

                // 收费支付方式包含医保支付；且医保支付金额尚未完成退款；且用户选择其它方式退款，退款金额>总金额-医保支付金额时，弹窗提示
                this.confirmRefund();
            },

            /**
             * desc [点击退费按钮]
             */
            async confirmRefund() {
                if (this.buttonLoading === true) {
                    return;
                }
                this.bottomExpand = false;

                // 医保configs接口拉取失败，不允许医保交易
                if (this.refundPostData.payMode === PayModeEnum.SOCIAL_CARD && this.$abcSocialSecurity.isInitFinish === false) {
                    this.showModal({
                        type: 'warn',
                        title: '退费失败',
                        content: ['医保入账失败，请点击确定后重新尝试'],
                        showFooter: true,
                        showConfirm: true,
                        onConfirm: () => location.reload(),
                    });
                    return;
                }

                // 微信第三方支付 退费
                if (this.refundPostData.payMode === PayModeEnum.WECHAT_PAY && this.refundPostData.paySubMode) {
                    const isGoOn = await this.handleAlertRefund();
                    if (isGoOn === false) return;
                }
                this.showModal({
                    type: 'loading',
                    title: `退费入账 ${this.$t('currencySymbol')} ${moneyNum(this.refundPostData.amount)}`,
                });

                this.buttonLoading = true;
                if (this.refundedId !== null) {
                    // 第三方退费 + 记账退费refundedId
                    const [err, res] = await this.refundPostRecord();

                    this.handleLoading();
                    if (err) {
                        this.buttonLoading = false;
                        return this.showModal({
                            type: 'warn',
                            title: '退费失败',
                            showClose: true,
                            content: [ err ],
                        });
                    }
                    this.$emit('auto-destroy-invoice', res.chargeTransactionId);
                    const {
                        payStatus, chargePayTransactionId,
                    } = res;
                    if (
                        payStatus === 1 &&
                        [
                            PayModeEnum.SOCIAL_CARD,
                            PayModeEnum.AIR_PAY,
                            PayModeEnum.MULAID_PAY,
                            PayModeEnum.OUTPATIENT_CENTER_PAY,
                            PayModeEnum.PERSONAL_POS_PAY,
                            PayModeEnum.RAILWAY_SOCIAL_PAY,
                            PayModeEnum.SHEBAO_YIMA_PAY,
                        ].includes(this.refundPostData.payMode)
                    ) {
                        this.continueThirdRefundLock(chargePayTransactionId);
                    }
                    this.awaitThirdHandle(res)
                        .then(async () => {
                            await this.successHandler();
                            this.buttonLoading = false;
                        })
                        .catch((error) => {
                            console.log('catch error', error);
                            if (error) {
                                this.showModal({
                                    type: 'warn',
                                    title: '退费失败',
                                    showClose: true,
                                    content: [ error ],
                                });
                            } else {
                                this.closeMessage();
                            }
                            this.buttonLoading = false;
                        });
                } else {
                    // 记账退费
                    const [err, res] = await this.refundPostRecord();
                    this.handleLoading();
                    if (err) {
                        this.buttonLoading = false;
                        return this.showModal({
                            type: 'warn',
                            title: '退费失败',
                            showClose: true,
                            content: [ err ],
                        });
                    }
                    this.$emit('auto-destroy-invoice', res.chargeTransactionId);
                    if (res && res.paymentSummaryInfos) {
                        this.$emit('update:payment-summary-infos', res.paymentSummaryInfos);
                    }
                    await this.successHandler();
                    this.buttonLoading = false;
                }
            },
            async successHandler() {
                await this.$nextTick();
                this.surplusRefund = red(this.surplusRefund, this.refundPostData.amount);
                if (this.surplusRefund === 0) {
                    this.$emit('finish');

                    this._timer = setTimeout(() => {
                        this.$Toast({
                            message: '退费完成',
                            type: 'success',
                        });
                    }, 300);
                } else {
                    this.refundPostData.payMode = null;
                    this.isFirstRefund = false;
                    this.refundPostData.amount = 0;
                    this.initCanRefundModes();

                    this.showModal({
                        type: 'success',
                        title: '退费成功',
                    }, 1000);
                    this._timer = setTimeout(() => {
                        this.handleLoading();
                    }, ALERT_TIME);
                }

            },
            /**
             * 当点击切换收费模式时
             * <AUTHOR>
             * @date 2022-07-12
             */
            onClickSwitchChargeMode() {
                this.socialChargeMode = this.isDaubCardChargeMode ? socialChargeModeConst.SCAN_CODE : socialChargeModeConst.DAUB_CARD;
                this.$abcSocialSecurity.setCacheChargeMode(this.socialChargeMode);
            },
            /**
             * desc [当需要第三方支付时]
             */
            awaitThirdHandle({
                chargePayTransactionId,
                thirdPartyPayTaskId,
                status,
            }) {
                if (thirdPartyPayTaskId) {
                    // eslint-disable-next-line no-async-promise-executor
                    return new Promise(async (resolve, reject) => {
                        const callback = async () => {
                            this.showModal({
                                type: 'loading',
                                title: '查询支付结果',
                                content: ['订单创建成功，等待社保结算退费'],
                            });
                            try {
                                let res;
                                if (this.isHospitalSheet) {
                                    res = await ChargeAPI.fetchHospitalPayStatus(chargePayTransactionId);
                                } else {
                                    res = await ChargeAPI.fetchPaystatus(chargePayTransactionId);
                                }
                                const { data } = res;
                                if (data.payStatus === 0) {
                                    // eslint-disable-next-line prefer-promise-reject-errors
                                    reject('未找到对应的支付流水信息，请刷新后再试');
                                }
                                if (data.payStatus === 1) {
                                    this.handleLoading(null);
                                    this.closeMessage();
                                    reject();
                                }
                                if (data.payStatus === 2) {
                                    this.refundedIds.push(this.refundedId);
                                    this.refundedId = null;
                                    resolve();
                                }
                                if (data.payStatus === 3) {
                                    // eslint-disable-next-line prefer-promise-reject-errors
                                    reject('退费失败');
                                }
                            } catch (error) {
                                // eslint-disable-next-line prefer-promise-reject-errors
                                reject('网络异常');
                            }
                        };
                        const handleResponse = await this.socialCardRefundHandler(thirdPartyPayTaskId, status, chargePayTransactionId);
                        if (handleResponse.status === true) {
                            callback();
                        } else {
                            reject();
                        }
                    }).finally(() => this.closeMessage());
                }
                return Promise.resolve();
            },
            /**
             * 处理刷卡退费
             * <AUTHOR>
             * @date 2022-07-12
             * @param {String} thirdPartyPayTaskId
             * @param {String} status
             * @returns {Promise<Response>}
             */
            async socialCardRefundHandler(thirdPartyPayTaskId, status, chargePayTransactionId) {
                if (!this.isRailwaySocialPay) {
                    this.showModal({
                        type: 'loading',
                        title: '正在调起医保支付端...',
                    });
                }

                const params = {
                    isScanCodeChargeMode: this.isScanCodeChargeMode, // 是否扫码支持
                    isAirPay: this.refundPostData.payMode === PayModeEnum.AIR_PAY, // 是否空中支付
                    isRailwaySocialRefund: this.isRailwaySocialPay, // 是否铁路医保支付退费
                    taskId: thirdPartyPayTaskId,
                    receivableFee: +this.refundPostData.amount, // 实收金额
                    payMode: this.refundPostData.payMode,
                    status,
                };
                const tradeResponse = await this.$abcSocialSecurity.trade(params, (eventName) => {
                    if (eventName === 'ping-success' && !this.isRailwaySocialPay) {
                        const options = {
                            type: 'loading',
                            title: '等待刷卡结果',
                            content: ['请确保医保专网可正常使用'],
                            showFooter: true,
                            showCancel: true,
                            onCancel: () => {},
                        };
                        if (this.isScanCodeChargeMode) {
                            Object.assign(options, {
                                title: '正在生成二维码',
                                content: [],
                            });
                        }
                        this.showModal(options);
                    }
                });
                if (tradeResponse) {
                    this.clearRefundLockTimeOut();
                    this.cancelRefund(chargePayTransactionId);
                }
                if (tradeResponse.status === false) {
                    if (
                        !tradeResponse.message || // 无报错原因
                        tradeResponse.isUserCancel === true // 用户主动取消
                    ) {
                        this.closeMessage();
                    } else {
                        await new Promise((resolve) => {
                            this.showModal({
                                type: 'warn',
                                title: '退费失败',
                                content: [ tradeResponse.message ],
                                showFooter: true,
                                showConfirm: true,
                                onConfirm: () => resolve(),
                            });
                        });
                    }
                    return tradeResponse;
                }
                return Response.success();
            },
            /**
             * desc [点击取消刷卡]
             */
            clickCancelRefund() {
                if (this.timeoutRefundStatus) {
                    clearTimeout(this.timeoutRefundStatus);
                    this.timeoutRefundStatus = null;
                }
                this.handleLoading();
            },
            /**
             * desc [退费记账处理]
             */
            async refundPostRecord() {
                let res = null;
                if (this.refundType === RefundTypeEnum.REFUND_PAID) {
                    // 部分退费时
                    res = await this.refundParTHandler();
                } else {
                    res = await this.refundChecHandler();
                }
                return res;
            },

            getRefundData() {
                if (this.hospitalSheetId) {
                    return {
                        payItem: {
                            ...this.refundPostData,
                        },
                        chargeComment: this.chargeComment,
                    };
                }
                // 诊间支付特殊处理
                const { payMode } = this.refundPostData;
                if (payMode === PayModeEnum.OUTPATIENT_CENTER_PAY) {
                    const combinedPayItems = this.paymentSummaryInfos
                        .filter((x) => x.payMode === PayModeEnum.OUTPATIENT_CENTER_PAY && x.isCanRefund);
                    return {
                        combinedPayItems,
                        chargeComment: this.chargeComment,
                    };
                }

                return {
                    combinedPayItems: [
                        {
                            ...this.refundPostData,
                        },
                    ],
                    chargeComment: this.chargeComment,
                };

            },

            /**
             * desc [部分退费时]
             */
            async refundParTHandler() {
                try {
                    const params = this.getRefundData();
                    const { data } = await ChargeAPI.paidBack(this.chargeSheetId, params, 400);
                    return [null, data];
                } catch (err) {
                    return [this.handleError(err)];
                }
            },
            /**
             * desc [选择项目退费]
             * 第一次方式退费：正常情况
             * 第二次方式退费：当做退欠退金额
             */
            async refundChecHandler() {
                try {
                    const params = this.getRefundData();
                    if (this.isFirstRefund) {
                        // 第一次退费-正常操作
                        params.needRefundFee = this.refundData.needRefundFee;
                        params.chargeForms = this.refundData.chargeForms;
                        params.adjustmentFee = this.refundData.adjustmentFee;
                    } else {
                        // 第二+次退费-当做退欠退金额
                        params.needRefundFee = 0;
                        params.chargeForms = [];
                        params.adjustmentFee = 0;
                    }
                    let data = null;
                    if (this.hospitalSheetId) {
                        const res = await ChargeAPI.hospitalRefund(this.hospitalSheetId, params, 400);
                        data = res.data;
                    } else {
                        if (this.refundData?.accessToken) {
                            params.refundCheckReq = {
                                accessToken: this.refundData.accessToken,
                            };
                        }
                        const res = await ChargeAPI.refund(this.chargeSheetId, params, 400, true);
                        data = res.data;
                    }

                    return [null, data];
                } catch (err) {
                    if (err.code === 11001) {
                        // 审核通过，授权码过期
                        const refundType = this.isPharmacy ? '退货' : '退费';
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${refundType}操作超时，请重新发起${refundType}`,
                            onClose: () => {
                                this.$emit('re-open-audit');
                            },
                        });
                        return;
                    }
                    return [this.handleError(err)];
                }
            },
            handleError(err) {
                console.log('handleError err', err);
                if (err.error) err = err.error;
                let msgError = err && err.message;
                if (err.code === 26011) {
                    // 未开启退费授权
                    msgError = '请在微信支付后台开启退款授权';
                }
                return msgError;
            },
            /**
             * desc [加载提示]
             */
            handleLoading(awaitType = null, title = '', content = '') {
                this.awaitType = awaitType;
                this.title = title;
                this.content = content;
                this.showMessage = !!this.awaitType;

                switch (this.awaitType) {
                    case 'confirm' :
                        this.messageType = 'warn';
                        break;
                    case 'alert' :
                        this.messageType = 'warn';
                        break;
                    case 'await' :
                        this.messageType = 'loading';
                        break;
                    case 'loading' :
                        this.messageType = 'loading';
                        break;
                    case 'success' :
                        this.messageType = 'success';
                        break;
                    default :
                        this.messageType = '';
                        this.showMessage = false;
                        break;
                }
            },
            handleCloseMessage() {
                this.awaitType = null;
                this.showMessage = false;
                this.title = '';
                this.content = '';
            },
            showModal(options, closeDelay = 0) {
                this.closeMessage();
                const defaultOption = {
                    customClass: 'refund-message-dialog',
                    referenceEl: this.$el.querySelector('.refund-way-dialog .abc-dialog'),
                    dialogType: 'tiny',
                    type: '',
                    title: '',
                    content: [],
                    showFooter: false,
                    showClose: false,
                    showCancel: false,
                    showConfirm: false,
                    onConfirm: () => {},
                    noDialogAnimation: true,
                    confirmText: '确定',
                    onCancel: () => {},
                    cancelText: '取消',
                };
                this._messageInstance = this.$message(Object.assign(defaultOption, options));
                if (closeDelay) {
                    const _timer = setTimeout(() => {
                        this._messageInstance.close();
                        clearTimeout(_timer);
                    }, closeDelay);
                }
            },
            closeMessage() {
                if (this._messageInstance) {
                    this._messageInstance.close();
                    this._messageInstance = null;
                }
            },
            /**
             * @desc 使用非医保退费金额大于非医保支付金额退款确认提示
             * <AUTHOR>
             * @date 2022/08/31 19:07:03
             */
            refundSocialConfirm(payMode) {
                // 是否包含医保
                const isContainSocial = this.paymentSummaryInfos.some((x) => x.payMode === PayModeEnum.SOCIAL_CARD);
                // 其它方式退费金额大于非医保支付金额需要提示
                if (isContainSocial && payMode !== PayModeEnum.SOCIAL_CARD) {
                    const socialPayItem = this.paymentSummaryInfos.find((x) => x.payMode === PayModeEnum.SOCIAL_CARD);
                    // 社保支付总和
                    const { paidAmount } = socialPayItem;
                    // 退款总和
                    const { amount } = this.refundPostData;
                    if (this.netIncomeFee > 0) {
                        // 除去社保的支付总和
                        const mountSocialDiff = new Big(getSafeNumber(this.receivableFee)).minus(getSafeNumber(paidAmount)).toNumber();
                        // 已退金额 = 支付总金额 - 欠退总金额
                        const refundedMount = new Big(getSafeNumber(this.receivableFee)).minus(getSafeNumber(this.netIncomeFee)).toNumber();
                        // 待退款总金额 = 当前退款金额 + 已退金额
                        const waitRefundMount = new Big(getSafeNumber(amount)).plus(refundedMount).toNumber();
                        // 退款总和 > 除去社保的支付总和
                        if (waitRefundMount > mountSocialDiff) {
                            this.showSocialRefundWarnModal = true;
                            this.confirmDisabled = true;
                            let count = 5;
                            this._timer = setInterval(() => {
                                count--;
                                if (count === 0) {
                                    this.confirmText = '确定';
                                    this.confirmDisabled = false;
                                    clearInterval(this._timer);
                                    return;
                                }
                                this.confirmText = `确定 ${count}`;
                            }, 1000);
                        }
                    }
                }
            },

            /**
             * @desc 医保退费，陈师傅25秒会入账失败，单子解锁，但是医保退费流程很长
             * @desc 调起医保小端后5秒续期，让陈师傅不要失败
             */
            continueThirdRefundLock(chargePayTransactionId, timeout = 1000 * 5) {
                this.clearRefundLockTimeOut();
                this._lockRefundTimeoutId = setTimeout(async() => {
                    try {
                        await ChargeAPI.lockRenewByTransaction(chargePayTransactionId);
                        this.continueThirdRefundLock(chargePayTransactionId, timeout);
                    } catch (err) {
                        if (err.message !== '没有网络连接') {
                            this.clearRefundLockTimeOut();
                        }
                        Logger.error({
                            scene: 'continueThirdRefundLock',
                            err,
                        });
                    }
                }, timeout);
            },
            clearRefundLockTimeOut() {
                this._lockRefundTimeoutId && clearTimeout(this._lockRefundTimeoutId);
            },
            async cancelRefund(chargePayTransactionId) {
                try {
                    await ChargeAPI.cancelPayByTransaction(chargePayTransactionId);
                } catch (err) {
                    Logger.error({
                        scene: 'cancelRefund',
                        err,
                    });
                }
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    @mixin justify($width) {
        width: $width;
        text-align: justify;
        text-align-last: justify;
        text-justify: distribute-all-lines;
    }

    .refund-way-dialog {
        .abc-dialog {
            width: 400px;
        }

        .abc-modal-dialog.abc-message-modal-dialog .abc-dialog {
            width: 240px;
        }

        .dialog-content {
            .charge-dialog-header {
                width: 100%;
                height: 55px;
                padding: 15px;
                text-align: center;

                span {
                    color: $Y2;
                }

                b {
                    font-size: 24px;
                    color: $Y2;
                }
            }

            .charge-paid-modes {
                padding: 0 24px;
                margin-top: 14px;

                .abc-radio-group + .abc-radio-group {
                    margin-top: 8px;
                }

                .paid-mode-item {
                    display: flex;
                    align-items: center;
                    height: 60px;
                    padding: 0 0 0 16px;
                    border: 1px solid #d3dbe1;
                    border-radius: var(--abc-border-radius-small);

                    img {
                        width: 16px;
                        height: 16px;
                        margin-right: 7px;
                    }

                    .content {
                        display: flex;
                        flex: 1;
                        align-items: center;
                        padding: 0 0 0 8px;
                        font-size: 12px;
                        color: $T2;

                        span:first-child {
                            margin-left: auto;
                        }

                        span + span {
                            margin-left: 8px;
                        }
                    }

                    .abc-radio,
                    .abc-checkbox-wrapper,
                    .refunded {
                        justify-content: center;
                        width: 48px;
                        text-align: center;
                    }

                    .abc-radio-label {
                        display: none;
                    }

                    & + .paid-mode-item {
                        margin-top: 8px;
                    }

                    &.is-selected {
                        background-color: $B4;
                        border: 1px solid $theme2;
                    }

                    &:not(.is-disabled):hover {
                        cursor: pointer;
                        border: 1px solid $theme2;
                    }

                    &.is-disabled {
                        color: $T2;
                        border: 1px solid #d3dbe1;
                    }
                }
            }

            .tips-wrapper {
                padding: 0 24px;
                font-size: 12px;
            }

            .charge-paid-modes + .tips-wrapper {
                margin-top: 16px;
            }
        }

        .pay-mode {
            padding-top: 5px;
            padding-bottom: 15px;
            font-size: 0;
            border-bottom: 1px solid $P6;

            > div {
                position: relative;
                display: inline-flex;
                align-items: center;
                justify-content: space-between;
                float: left;
                width: 178px;
                height: 48px;
                padding: 0 8px;
                margin-top: 10px;
                font-size: 14px;
                color: $T2;
                cursor: pointer;
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);

                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 7px;
                }

                .zw {
                    flex: 1;
                    height: 100%;
                }

                .name {
                    color: $T1;

                    @include justify(58px);
                }

                .circel {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 16px;
                    height: 16px;
                    border: 1px solid $P1;
                    border-radius: 16px;

                    > span {
                        width: 8px;
                        height: 8px;
                        background-color: $theme2;
                        border-radius: 8px;
                    }
                }

                .cis-icon-selected {
                    position: absolute;
                    top: -2px;
                    right: -1px;
                    margin: 0;
                    font-size: 20px;
                    color: $B2;
                }

                &:not(.is-hover):not(.disabled):hover {
                    border-color: $B1;
                }
            }

            > div.more-six {
                width: 120px;
                height: 40px;
            }

            > div.more-six:nth-child(3n-1) {
                margin: 10px 10px 0;
            }

            > div.selected {
                color: $B2;
                background-color: $B4;
                border: 1px solid $B2;
            }

            > div.disabled {
                opacity: 0.6;
            }

            > div.customized {
                img {
                    display: none;
                }

                .name {
                    @include justify(86px);

                    text-align: center;
                    text-align-last: initial;
                }
            }
        }

        .alert-box {
            padding: 15px;
            margin: 15px 0;
            font-size: 14px;
            color: #626d77;
            text-align: center;
            background-color: #f0f7ff;

            .iconfont {
                margin-right: 5px;
            }
        }

        .total-box {
            margin-top: 12px;
            text-align: center;

            > div:first-child {
                font-size: 14px;
                line-height: 2;
                color: #626d77;

                span {
                    margin-left: 8px;
                    font-size: 18px;
                    color: $S1;
                }
            }

            > div:last-child {
                font-size: 12px;
                color: #8d9aa8;

                span {
                    margin: 0 5px;
                }
            }
        }

        .abc-dialog-footer {
            padding: 15px 20px;
        }

        .dialog-footer {
            justify-content: space-between;

            .abc-icon {
                position: absolute;
                top: 10px;
                right: -14px;
                cursor: pointer;
            }
        }

        .charge-dialog-bottom-extend {
            position: absolute;
            bottom: -60px;
            left: 0;
            width: 100%;
            padding: 15px 20px;
            background-color: $S2;
            border-top: 1px solid $P6;
            border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);
        }
    }
</style>
