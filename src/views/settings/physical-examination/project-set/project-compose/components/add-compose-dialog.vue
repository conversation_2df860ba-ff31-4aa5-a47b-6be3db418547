<template>
    <abc-dialog
        v-if="isShowDialog"
        v-model="isShowDialog"
        append-to-body
        :title="title"
        content-styles="padding:0;height: 620px"
        custom-class="physical-examination-add-compose-dialog"
        :auto-focus="false"
    >
        <abc-form
            ref="projectForm"
            v-abc-loading="loading"
            label-position="left"
            :label-width="68"
            class="add-compose-from-wrapper"
            item-block
            item-no-margin
        >
            <div class="add-compose-from-left">
                <h3 class="add-compose-form-title">
                    基础信息
                </h3>

                <abc-flex vertical :gap="16" class="add-compose-form-item-wrapper">
                    <abc-form-item
                        label="套餐名称"
                        required
                        hidden-red-dot
                    >
                        <abc-input v-model="goods.name" :disabled="goodsDisabled" adaptive-width></abc-input>
                    </abc-form-item>

                    <abc-form-item label="体检类型">
                        <abc-select
                            v-model="goods.subType"
                            adaptive-width
                            disabled
                        >
                            <abc-option
                                v-for="(o,i) in composeTypeOptions"
                                :key="i"
                                v-bind="o"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>

                    <template v-if="isCommonHealth">
                        <abc-form-item label="适用人群">
                            <abc-select
                                v-model="goods.applyPopulation"
                                adaptive-width
                                :disabled="!isPhysicalExaminationTemplateClinic"
                            >
                                <abc-option
                                    v-for="(o,i) in applyPopulationOptions"
                                    :key="i"
                                    v-bind="o"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </template>

                    <abc-form-item
                        ref="genderItem"
                        label="性别限制"
                        :validate-event="validateGender"
                    >
                        <abc-select
                            v-model="goods.gender"
                            adaptive-width
                        >
                            <abc-option
                                v-for="(o,i) in genderOptions"
                                :key="i"
                                v-bind="o"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>

                    <abc-form-item
                        v-if="mode === 'edit'"
                        label="项目编号"
                        hidden-red-dot
                    >
                        <abc-input v-model="goods.shortId" :disabled="goodsDisabled || !!goodsId" adaptive-width></abc-input>
                    </abc-form-item>
                </abc-flex>
            </div>

            <div class="add-compose-from-right">
                <!-- 编辑公卫关联项目 -->
                <template v-if="showCommonHealthyEditTable">
                    <common-health-project
                        :children.sync="goods.children"
                        :public-health-compose-item-list.sync="goods.bizExtensions.publicHealthComposeItemList"
                        :package-price.sync="goods.packagePrice"
                        :disabled="goodsDisabled || !isAdmin"
                    ></common-health-project>
                </template>

                <template v-else>
                    <div class="add-normal-compose-right-wrapper">
                        <h3 class="add-compose-form-title">
                            套餐项目及定价
                        </h3>

                        <single-item-table
                            :data-source.sync="goods.children"
                            :gender="goods.gender || 0"
                            :del-disabled="(isChainSubStore || goodsDisabled)"
                            @delete-item="handleDeleteItem"
                            @compose-package-price-change="handlePriceChangeItem"
                        ></single-item-table>

                        <div class="add-compose-from-footer" :style="{ 'justify-content': !(isChainSubStore || goodsDisabled) ? 'space-between' : 'end' }">
                            <abc-button
                                v-if="!(isChainSubStore || goodsDisabled)"
                                type="blank"
                                class="addSubBtn"
                                @click="toAddPhysicalExamination"
                            >
                                添加项目
                            </abc-button>
                            <div v-if="goods.children.length" class="total-price-info">
                                <span>套餐销售价</span>
                                <abc-form-item required style="margin: 0 0 0 8px;">
                                    <abc-input
                                        v-model="composePrice"
                                        v-abc-focus-selected
                                        type="money"
                                        :width="92"
                                        :input-custom-style="{
                                            color: $store.state.theme.style.Y2,
                                            textAlign: 'right',
                                            width: '111px'
                                        }"
                                        :readonly="calcLoading"
                                        :config="{
                                            formatLength: 2, supportZero: true, max: 9999999
                                        }"
                                        @input="calcComposePriceDebounce(Clone(goods.children),3)"
                                    >
                                        <abc-loading-spinner
                                            v-if="calcLoading && showLoading"
                                            slot="prepend"
                                            small
                                            no-cover
                                        ></abc-loading-spinner>
                                        <abc-currency-symbol-icon

                                            slot="prepend"
                                            :color="$store.state.theme.style.Y2"
                                        ></abc-currency-symbol-icon>
                                    </abc-input>
                                </abc-form-item>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </abc-form>

        <abc-flex slot="footer" align="center" justify="space-between">
            <div v-if="mode === 'edit' && !isCommonHealth">
                <abc-popover
                    v-if="!goodsDisabled && isAdmin"
                    class="disable-goods-select"
                    trigger="click"
                    placement="bottom-start"
                    theme="white"
                    :visible-arrow="false"
                    :popper-style="{ padding: 0 }"
                >
                    <abc-button slot="reference" type="danger">
                        停用
                    </abc-button>

                    <ul class="disable-goods-options">
                        <li @click="stop">
                            停用但保留项目资料
                        </li>

                        <li @click="del">
                            停用并删除项目资料
                        </li>
                    </ul>
                </abc-popover>

                <abc-button
                    v-if="!goodsDisabled && !isAdmin"
                    type="danger"
                    @click="stop"
                >
                    停用
                </abc-button>

                <abc-button
                    v-if="startVisible"
                    type="blank"
                    @click="start"
                >
                    启用
                </abc-button>
            </div>

            <div>
                <abc-button
                    type="primary"
                    :loading="btnLoading"
                    @click="submit"
                >
                    确定
                </abc-button>

                <abc-button type="blank" @click="isShowDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-flex>

        <examination-select-dialog
            v-if="isShowAddPhysicalExaminationDialog"
            v-model="isShowAddPhysicalExaminationDialog"
            :compose-type="composeType"
            @onSelectGoods="setSelectGoods"
        ></examination-select-dialog>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import SingleItemTable
        from './single-item-table.vue';
    import CommonHealthProject from
    '@/views/settings/physical-examination/project-set/project-compose/components/common-health-project/index.vue';
    import {
        COMPOSE_TYPE,
        GENDER,
        applyPopulationOptions,
        composeTypeOptions,
    } from '@/views/settings/physical-examination/project-set/constant';
    import GoodsApi from 'api/goods';
    import ExaminationSelectDialog from 'src/views/layout/examination-select-dialog/index.vue';
    import { convertData } from '@/utils/adapter/common-adapter';
    import GoodsAPI from 'api/goods';
    import Clone from 'utils/clone';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import { debounce } from 'utils/lodash';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    import {
        GoodsTypeEnum,
    } from '@abc/constants/src';
    import SettingAPI from 'api/settings';

    export default {
        name: 'AddComposeDialog',
        components: {
            AbcCurrencySymbolIcon,
            SingleItemTable,
            CommonHealthProject,
            ExaminationSelectDialog,
            AbcLoadingSpinner,
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            title: {
                type: String,
                default: '',
            },
            mode: {
                type: String,
                required: true,
            },
            composeType: {
                type: Number,
                default: COMPOSE_TYPE.normal,
            },
            goodsId: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                loading: false,
                goods: {
                    type: GoodsTypeEnum.PHYSICAL_EXAMINATION_COMPOSE,
                    name: '',
                    subType: this.composeType,
                    gender: 0,
                    shortId: '',
                    children: [],
                    applyPopulation: '',
                    bizExtensions: {
                        publicHealthComposeItemList: [],
                    },
                },
                isShowAddPhysicalExaminationDialog: false,
                composePrice: '',// 套餐销售价-用户可以修改
                calcLoading: false,
                showLoading: false,
                isInvalid: false,
                btnLoading: false,
            };
        },
        computed: {
            ...mapGetters([
                'isPhysicalExaminationTemplateClinic',
                'isAdmin',
                'isChainSubStore',
            ]),

            isShowDialog: {
                get() {
                    return this.value;
                },

                set(val) {
                    this.$emit('input', val);
                },
            },

            isCommonHealth() {
                return this.composeType === COMPOSE_TYPE.commonHealthy;
            },

            showCommonHealthyEditTable() {
                return this.isCommonHealth && !this.isPhysicalExaminationTemplateClinic;
            },

            goodsDisabled() {
                return !!this.goods.disable;
            },

            genderOptions() {
                return GENDER;
            },

            applyPopulationOptions() {
                return applyPopulationOptions;
            },

            composeTypeOptions() {
                return composeTypeOptions;
            },
            startVisible() {
                if (this.isChainSubStore) {
                    return this.goodsDisabled && !this.goods?.chainV2DisableStatus;
                }

                return this.goodsDisabled;
            },
        },
        created() {
            if (this.mode === 'edit') {
                this.fetchDetail();
            }
            this.calcComposePriceDebounce = debounce((data,modifyIdentification) => this.calcComposePrice(data,modifyIdentification),800, true);
        },
        methods: {
            Clone,
            adapter(data) {
                const fields = [
                    'name',
                    'subType',
                    'gender',
                    'shortId',
                    {
                        sourceKey: 'children',
                        defaultValue: [],
                    },
                    {
                        sourceKey: 'bizExtensions',
                        default: {},
                        fields: [
                            {
                                sourceKey: 'publicHealthComposeItemList',
                                default: [],
                                fields: [
                                    {
                                        sourceKey: 'relatedGoodsId',
                                        defaultValue: '',
                                    },
                                ],
                            },
                        ],
                    },
                ];

                return convertData(data, fields);
            },

            async fetchDetail() {
                if (!this.goodsId) {
                    return;
                }
                this.loading = true;

                try {
                    const { data } = await GoodsApi.fetchGoods(this.goodsId);
                    this.goods = this.adapter(data);
                    this.composePrice = this.goods.packagePrice;
                } catch (error) {
                    console.error('拉取套餐详情错误：', error);
                }

                this.loading = false;
            },

            toAddPhysicalExamination() {
                this.isShowAddPhysicalExaminationDialog = true;
            },

            submit() {
                this.$refs.projectForm.validate((val) => {
                    if (val) {

                        const validGenders = this.goods.children?.filter((item) => item.gender) || [];
                        const uniqueGenders = new Set(validGenders.map((item) => item.gender));
                        if (uniqueGenders.size > 1) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: '套餐项目性别冲突',
                            });
                            return;
                        }
                        if (this.mode === 'edit') {
                            this.updateGoods({
                                ...this.goods,packageUnit: '次',
                            });
                        } else {
                            this.createGoods({
                                ...this.goods,packageUnit: '次',
                            });
                        }
                    }
                });
            },

            async createGoods(data) {
                try {
                    this.btnLoading = true;
                    await GoodsApi.createdNewGoods(data);
                    this.$Toast({
                        message: '新建成功',
                        type: 'success',
                    });
                    this.$emit('refresh-list');
                    this.isShowDialog = false;
                } catch (e) {
                    this.handleErrorTip(e);
                } finally {
                    this.btnLoading = false;
                }
            },

            async updateGoods(data) {
                try {
                    this.btnLoading = true;
                    await GoodsApi.updateGoods(data.id, data);
                    this.$Toast({
                        message: '修改成功',
                        type: 'success',
                    });
                    this.btnLoading = false;
                    this.$emit('refresh-list');
                    this.isShowDialog = false;
                } catch (e) {
                    this.handleErrorTip(e);
                } finally {
                    this.btnLoading = false;
                }
            },

            handleErrorTip(e) {
                if (e.code === 12006) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '套餐名重复',
                    });
                }
                if (e.code === 12204) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '项目编码重复',
                    });
                }
            },

            //停用
            async stop() {
                try {
                    await GoodsAPI.switchGoodsDisabled(this.goods.id, { disable: 1 });
                    this.$Toast({
                        message: '停用成功',
                        type: 'success',
                    });

                    this.$emit('refresh-list');
                    this.goods.disable = 1;
                    this.cacheGoods.disable = 1;
                } catch (e) {
                    if (e.code === 12403) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '套餐项目不能主动停用',
                        });
                    }
                }
            },

            //启用
            async start() {
                try {
                    await GoodsAPI.switchGoodsDisabled(this.goods.id, { disable: 0 });
                    this.$Toast({
                        message: '启用成功',
                        type: 'success',
                    });

                    this.$emit('refresh-list');
                    this.goods.disable = 0;
                    this.cacheGoods.disable = 0;
                } catch (e) {
                    console.log(e);
                }
            },

            //删除
            async del() {
                try {
                    await SettingAPI.examination.deleteExaminationGoods(this.goods.id);
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });

                    this.$emit('refresh-list');
                    this.isShowDialog = false;
                } catch (e) {
                    console.log(e);
                }
            },

            setSelectGoods(goodsList) {
                const goodsChildren = Clone(this.goods.children);
                const goodsIdSet = new Set(goodsChildren.map((item) => item.goodsId)); // 使用 Set 存储 b 数组中的 goodsId
                for (let i = 0; i < goodsList.length; i++) {
                    if (!goodsIdSet.has(goodsList[i].goodsId)) {
                        goodsChildren.push(goodsList[i]);
                    }
                }
                this.calcComposePrice(goodsChildren,4);
            },
            //根据套餐总价计算套餐价格，modifyIdentification修改单项或套餐总价标识 1=修改单项单价 2=修改单项总价 3=修改套餐总价 4=修改数量
            async calcComposePrice(goodsChildren,modifyIdentification) {
                const params = {
                    composePrice: this.composePrice,
                    itemList: goodsChildren.map((item,index) => {
                        return {
                            goodsId: item.id,
                            goodsName: item.medicineCadn || item.name,
                            composeSort: index,
                            composeUseDismounting: 0,
                            composePackageCount: 1,
                            composePackagePrice: item.composePackagePrice ? Number(item.composePackagePrice) : Number(item.packagePrice),
                            composePrice: item.composePrice ? Number(item.composePrice) : Number(item.packagePrice),
                            composeFractionPrice: item.composeFractionPrice,
                        };
                    }).filter((item) => item.composePieceCount || item.composePackageCount),
                    modifyIdentification, //修改数量
                    srcComposePrice: '',
                };
                // 无列表不请求
                if (!params.itemList?.length) return;

                this.calcLoading = true;
                if (modifyIdentification !== 3) {
                    this.showLoading = true;
                }
                try {
                    const { data } = await GoodsApi.calcComposePrice(params);
                    this.composePrice = data.composePrice || (data.composePrice === 0 ? data.composePrice : data.srcComposePrice);
                    for (let i = 0; i < goodsChildren.length; i++) {
                        for (let j = 0; j < data.itemList.length; j++) {
                            if (data.itemList[j].goodsId === goodsChildren[i].id) {
                                goodsChildren[i].goodsId = data.itemList[j].goodsId;
                                goodsChildren[i].goodsName = data.itemList[j].goodsName;
                                goodsChildren[i].composeSort = data.itemList[j].composeSort;
                                goodsChildren[i].composePackageCount = data.itemList[j].composePackageCount;
                                goodsChildren[i].composePackagePrice = data.itemList[j].composePackagePrice;
                                goodsChildren[i].composePrice = data.itemList[j].composePrice;
                                goodsChildren[i].composeUseDismounting = data.itemList[j].composeUseDismounting;
                                goodsChildren[i].composePieceCount = '';
                                goodsChildren[i].composePiecePrice = 0;
                                goodsChildren[i].composeFractionPrice = data.itemList[j].composeFractionPrice;
                            }
                        }
                    }

                    this.goods.children = goodsChildren;
                    this.isInvalid = goodsChildren.some((item) => {
                        return item.gender !== this.goods.gender && Number(item.gender) !== 0 && Number(this.goods.gender) !== 0 &&
                            item.gender !== undefined && this.goods.gender !== undefined ;
                    });
                    this.$refs.genderItem.validate();
                    this.showLoading = false;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.calcLoading = false;
                }
            },

            handleDeleteItem(idx) {
                this.goods.children.splice(idx,1);
                this.calcComposePriceDebounce(Clone(this.goods.children),4);
            },
            /**
             * @des 修改单项价格
             */
            handlePriceChangeItem(goodsChildren) {
                this.calcComposePriceDebounce(Clone(goodsChildren),1);
            },

            validateGender(val, callback) {
                this.goods.gender = GENDER.find((item) => item.label === val)?.value;
                this.isInvalid = this.goods.children.some((item) => {
                    return item.gender !== this.goods.gender && Number(item.gender) !== 0 && Number(this.goods.gender) !== 0 &&
                        item.gender !== undefined && this.goods.gender !== undefined ;
                });
                if (this.isInvalid) {
                    callback({
                        validate: false,
                        message: '与项目性别限制冲突',
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.physical-examination-add-compose-dialog {
    .add-compose-from-wrapper {
        display: flex;
        height: 100%;

        .add-compose-from-left {
            box-sizing: border-box;
            width: 400px;
            padding: 24px;
            border-right: 1px solid $P6;

            .add-compose-from-wrapper {
                .add-compose-form-item-wrapper {
                    display: flex;
                    flex-direction: column;
                    gap: 16px 24px;
                    margin-bottom: 40px;

                    .abc-form-item {
                        width: 233px;

                        .abc-input-wrapper {
                            width: 100%;
                        }
                    }
                }
            }
        }

        .add-compose-from-right {
            display: flex;
            flex-direction: column;

            .add-normal-compose-right-wrapper {
                padding: 24px;

                .add-compose-from-footer {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 16px;

                    .total-price-info {
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }

        .add-compose-form-title {
            margin-bottom: 16px;
            font-weight: bold;
            color: $T1;
        }
    }

    .add-compose-dialog-footer {
        display: flex;
        justify-content: flex-end;
    }
}
</style>
