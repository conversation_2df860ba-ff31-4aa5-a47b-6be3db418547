import { defaultCountryCode } from '@/utils/country-codes.js';
import { getDefaultMedicalRecord } from 'src/views-distribute/utils.js';
import { RegistrationCategory } from '@/views-hospital/registered-fee/constant';
import { ShebaoPayMode } from 'views/inventory/goods/archives/components/social-code-autocomplete/constant';
export * from '@/common/constants/outpatient.js';


export const OutpatientSheetTypeEnum = Object.freeze({
    REGISTRATION: 1,
});

export const createMedicalRecord = () => {
    return {
        isTemplate: 0, // 是否是模板
        doctorAdvice: '',
        attachments: [],
        type: 0,
        ...getDefaultMedicalRecord(),

        // 儿保独用
        birthHistory: '', // 出生史
    };
};

export const createPostData = (type = 0) => {
    return {
        payType: 0, // 10-整单不刷医保
        outpatientSource: 0,
        type, // 儿保门诊传1
        patient: {
            id: null,
            name: '',
            mobile: '',
            countryCode: defaultCountryCode,
            sex: '男',
            age: {
                year: null,
                month: null,
                day: null,
            },
            wxOpenId: null,
            isMember: null,
            wxBindStatus: 0,
            wxStatus: 0,
            shebaoCardInfo: null,
            appFlag: 0,
            arrearsFlag: 0,
            tags: [],
            idCard: null,
            idCardType: null,
            address: {
                addressProvinceId: null,
                addressProvinceName: null,
                addressCityId: null,
                addressCityName: null,
                addressDistrictId: null,
                addressDistrictName: null,
            },
        },
        psychotropicNarcoticEmployee: {
            name: '',
            sex: '',
            age: {
                year: null,
                month: null,
                day: null,
            },
            idCard: '',
        },
        departmentId: '',
        doctorId: '',
        registrationFee: '',
        consultantId: '',
        medicalRecord: createMedicalRecord(),
        questionSheets: [],
        productForms: [],
        prescriptionChineseForms: [],
        prescriptionWesternForms: [],
        prescriptionInfusionForms: [],
        prescriptionExternalForms: [],
        prescriptionGlassesForms: [],
        shebaoCardInfo: null,
        revisitStatus: null,
        diagnosedDateStr: undefined,
        registrationCategory: RegistrationCategory.ORDINARY,
        referralPatientOrderId: null,
        referralFlag: null,
    };
};

export const createHealthReportRecord = () => {
    return {
        conclusion: '',
        advice: '',
    };
};

export const NEED_PRINT_EXECUTION = [
    '静滴',
    '雾化',
    '雾化吸入',
    '皮试',
    '超声透药',
    '静脉注射',
    '肌内注射',
    '皮内注射',
    '皮下注射',
    '外用',
    '滴入',
    '喷入',
    '直肠给药',
    '含漱',
    '涂抹',
    '塞肛用',
    '阴道用',
    '塞肛',
    '注射',
    '肌注',
    '腔内注射',
    '静脉输注',
    '皮试用',
    '直肠塞入',
    '穴位注射',
    '阴道给药',
    '静脉点滴',
    '局部麻醉',
    '静脉输液',
    '皮内注射',
    '滴鼻',
    '超雾',
    '超声透药',
    '外敷',
    '阴道给药',
    '局部注射',
    '吸入',
    '涂患处',
    '静脉滴注',
    '肌肉注射',
];

export const outpatientCompareKey = [
    'patient',
    'departmentId',
    'doctorId',
    'registrationFee',
    'revisitStatus',
    'medicalRecord',
    'productForms',
    'prescriptionChineseForms',
    'prescriptionWesternForms',
    'prescriptionInfusionForms',
    'prescriptionExternalForms',
    'prescriptionGlassesForms',
    'fee',
    'expectedTotalPrice',
    'shebaoChargeType',
    'diagnosedDate',
];

// 中药 form 需要对比是否变化的key
export const PRCFormCompareKey = [
    'specification',
    'doseCount',
    'usage',
    'dailyDosage',
    'freq',
    'usageLevel',
    'requirement',
    'expectedTotalPrice',
    // 空中药房
    'pharmacyType',
    'usageScopeId',
    'medicineStateScopeId',
    'vendorId',
    // 加工信息
    'isDecoction',
    'contactMobile',
    'usageType',
    'usageSubType',
    'processBagUnitCount',
    'psychotropicNarcoticType',
    'psychotropicNarcoticEmployee',
];

export const PRCItemCompareKey = [
    'unit',
    'goodsId',
    'name',
    'unitCount',
    'specialRequirement',
    'verifySignatures',
    'freq',
    'usageLevel',
    'dailyDosage',
    'days',
    'totalPrice',
    'expectedTotalPrice',
    'expectedTotalPriceRatio',
    'totalPriceRatio',
    'remark',
    'usage',
    'dosage',
    'dosageUnit',
    'payType',
    'billingType',
    'externalUnitCount',
    'acupoints',
    'usageType',
    'usageSubType',
    'toothNos',
];


export const PsychotropicNarcoticTypeEnum = Object.freeze({
    NONE: 0,
    JING_1: 1,
    JING_2: 2,
    MA_ZUI: 3,
    DU: 4,
    GENERAL: 5, // 普通
    CHILD: 6, // 儿科
    CHRONIC: 7, // 慢病
    OLD: 8, // 老年病
    EMERGENCY: 9, // 急诊
    LONG_TERM: 10, // 长期
});
export const PsychotropicNarcoticTypeEnumStr = Object.freeze({
    [PsychotropicNarcoticTypeEnum.NONE]: '',
    [PsychotropicNarcoticTypeEnum.JING_1]: '精一',
    [PsychotropicNarcoticTypeEnum.JING_2]: '精二',
    [PsychotropicNarcoticTypeEnum.MA_ZUI]: '麻',
    [PsychotropicNarcoticTypeEnum.DU]: '毒',
    [PsychotropicNarcoticTypeEnum.GENERAL]: '普',
    [PsychotropicNarcoticTypeEnum.CHILD]: '儿',
    [PsychotropicNarcoticTypeEnum.CHRONIC]: '慢',
    [PsychotropicNarcoticTypeEnum.OLD]: '老',
    [PsychotropicNarcoticTypeEnum.EMERGENCY]: '急',
    [PsychotropicNarcoticTypeEnum.LONG_TERM]: '长期',
});


export const DepartmentTypeStatus = {
    OUT_PATIENT_TYPE: 1, // 临床科室
    CHILD_HEALTH_TYPE: 2, // 儿保科室
};

export const ChildHealthCareKeyLabelObj = {
    inoculation: '接种',
    diet: '饮食',
    sleep: '睡眠',
    mouth: '口腔护理',
    growthPromotion: '发育促进',
    diseasePrevention: '疾病预防',
    accidentalDamage: '预防意外伤害',
    eye: '眼和视力保健',
    otherAdvice: '其他',
};

export const MEDICAL_IMAGE_TYPE = [
    {
        'label': 'CT', 'value': 'CT',
    },
    {
        'label': 'DR', 'value': 'DX',
    },
    {
        'label': 'MR', 'value': 'MR',
    },
    {
        'label': 'CR', 'value': '103',
    },
    {
        'label': 'XR', 'value': '104',
    },
    {
        'label': '心电图', 'value': '105',
    },
    {
        'label': '骨密度', 'value': '106',
    },
    {
        'label': '视光', 'value': '107',
    },
    {
        'label': '彩超', 'value': '108',
    },
    {
        'label': '内窥镜', 'value': '110',
    },
    {
        'label': 'B超', 'value': '111',
    },
    {
        'label': '脑电图', 'value': '112',
    },
    {
        'label': '其他', 'value': '113',
    },

];

export const MEDICAL_IMAGE_OPTIONS = [
    {
        'label': '全部', 'value': '',
    },
    {
        'label': 'CT', 'value': 'CT',
    },
    {
        'label': 'DR', 'value': 'DX',
    },
    {
        'label': 'MR', 'value': 'MR',
    },
    {
        'label': '彩超', 'value': '108',
    },
    {
        'label': '其他', 'value': '113',
    },
];

// 其他的老数据
export const OLD_OTHER_IMAGE_TYPE = '0';
export const OTHER_IMAGE_TYPE = '113';


export const outpatientFormTabEnum = Object.freeze({
    OUTPATIENT: 0,
    CHRONIC: 1,
    CONSULTATION: 2,
    FOLLOW_UP: 3,
    PROCESSING: 4,
});

export const OutpatientStatusEnumNew = Object.freeze({
    WAITING: 0, // 待诊 (含草稿)
    DIAGNOSED: 1, // 已诊
    DIAGNOSING: 2, // 接诊中
    TO_BE_TRIAGED: 10, // 待分诊
});

export const PrescriptionTypeEnum = Object.freeze({
    WESTERN: 1,
    INFUSION: 2,
    CHINESE: 3,
    EXTERNAL: 4,
    GLASSES: 5,
});

export const PrescriptionDefaultTypeEnum = Object.freeze({
    NO_DEFAULT: 0, // 无默认
    WESTERN: 1, // 西药
    CHINESE_PIECES: 2, // 中药饮片
    INFUSION: 3, // 输注
    GLASSES: 4, // 配镜
    CHINESE_GRANULE: 5, // 中药颗粒
});

export const OutpatientFromTypeEnum = Object.freeze({
    QUICK: 0, // 快速接诊
    REGISTER: 1, // 预约
});

// item 上 payType 字段，透传给社保，与shebaoPayMode有区别，shebaoPayMode是goods属性，payType是当前item支付属性
export const ShebaoPayTypeEnum = Object.freeze({
    // 优先统筹支付
    OVERALL: 0,
    // 优先个账支付
    SELF: 10,
    // 处方内自费（跟其它几个不一样，仅前端使用，不能用作后台流转）
    FORM_SELF: 20,
    // 不使用医保支付
    NO_USE: 30,
});

export const ShebaoPayTypeByModeEnum = Object.freeze({
    [ShebaoPayMode.OVERALL]: ShebaoPayTypeEnum.OVERALL,
    [ShebaoPayMode.SELF]: ShebaoPayTypeEnum.SELF,
    [ShebaoPayMode.NO_USE]: ShebaoPayTypeEnum.NO_USE,
});

export const ShebaoPayModeByTypeEnum = Object.freeze({
    [ShebaoPayTypeEnum.OVERALL]: ShebaoPayMode.OVERALL,
    [ShebaoPayTypeEnum.SELF]: ShebaoPayMode.SELF,
    [ShebaoPayTypeEnum.NO_USE]: ShebaoPayMode.NO_USE,
});

export const AuditResultEnum = Object.freeze({
    PASS: 1,
    FAIL: 2,
});

export const DIAGNOSIS_MODEL_OPTIONS = Object.freeze([
    {
        label: '中医大模型',
        value: 1 << 0,
    },
    {
        label: '西医大模型',
        value: 1 << 1,
    },
]);

export const DIALECTICAL_SCHOOL_OPTIONS = Object.freeze([
    {
        label: '经典学派',
        value: 1 << 0,
        subLabel: '脏腑八纲辨证',
    },
    {
        label: '伤寒派',
        value: 1 << 1,
        subLabel: '伤寒六经辨证',
    },
    {
        label: '扶阳派',
        value: 1 << 2,
        subLabel: '重视阳主阴从',
    },
    {
        label: '补土派',
        value: 1 << 3,
        subLabel: '重视调理脾胃',
    },
    // {
    //     label: '温病派',
    //     value: 1 << 4,
    //     subLabel: '卫气营血三焦',
    // },
    // {
    //     label: '滋阴派',
    //     value: 1 << 5,
    //     subLabel: '重视滋阴降火',
    // },
    // {
    //     label: '寒凉派',
    //     value: 1 << 6,
    //     subLabel: '重视清热泻火',
    // },
    // {
    //     label: '攻邪派',
    //     value: 1 << 7,
    //     subLabel: '重视汗吐下法',
    // },
]);

export const TREATMENT_METHOD_OPTIONS = Object.freeze([
    {
        label: '中药',
        value: 1 << 0,
        isWestern: false,
    },
    {
        label: '西成药',
        value: 1 << 1,
        isWestern: true,
    },
    {
        label: '中成药',
        value: 1 << 2,
        isWestern: true,
    },
    {
        label: '针灸',
        value: 1 << 3,
        isWestern: false,
    },
    {
        label: '贴敷',
        value: 1 << 4,
        isWestern: false,
    },
    {
        label: '艾灸',
        value: 1 << 5,
        isWestern: false,
    },
    {
        label: '拔罐',
        value: 1 << 6,
        isWestern: false,
    },
    {
        label: '刮痧',
        value: 1 << 7,
        isWestern: false,
    },
    {
        label: '推拿',
        value: 1 << 8,
        isWestern: false,
    },
]);

export const CHINESE_MEDICINE_NUM_OPTIONS = Object.freeze([
    // {
    //     label: '合理所需',
    //     value: 1 << 0,
    // },
    // {
    //     label: '5 味以内',
    //     value: 1 << 1,
    // },
    {
        label: '10 味以内',
        value: 1 << 2,
    },
    {
        label: '10 - 15 味',
        value: 1 << 3,
    },
    {
        label: '15 - 20 味',
        value: 1 << 4,
    },
    {
        label: '20 - 25 味',
        value: 1 << 5,
    },
    {
        label: '25 - 30 味',
        value: 1 << 6,
    },
]);

export const MERGE_DIAGNOSIS_MODEL_OPTIONS = Object.freeze([
    {
        label: '西医 • 循证医学',
        value: '2',
        subLabel: '',
    },
    {
        label: '中医 • 经典学派',
        value: '1_1',
        subLabel: '脏腑八纲辨证',
    },
    {
        label: '中医 • 伤寒派',
        value: '1_2',
        subLabel: '伤寒六经辨证',
    },
    {
        label: '中医 • 扶阳派',
        value: '1_4',
        subLabel: '重视阳主阴从',
    },
    {
        label: '中医 • 补土派',
        value: '1_8',
        subLabel: '重视调理脾胃',
    },
]);
