<template>
    <abc-list
        ref="listRef"
        :create-key="createKey"
        :data-list="traceableCodeList"
        show-divider
        :scroll-config="{
            paddingSize: 'tiny',
        }"
        :divider-config="{
            variant: 'dashed',
            theme: 'light',
        }"
        style="padding-top: 0;"
        :custom-padding="[6, 10]"
        :height="height"
        :hover-item-func="hoverItemFunc"
        :custom-item-class="customItemClass"
        :enable-virtual-list="enableVirtualList"
        :virtual-list-config="virtualListConfig"
    >
        <template
            #default="{
                item, index
            }"
        >
            <abc-popover
                width="260px"
                placement="bottom-end"
                trigger="hover"
                theme="yellow"
                :arrow-offset="280"
                :offset="10"
                :disabled="item.disabledPopover"
                style="width: 100%;"
            >
                <abc-flex
                    slot="reference"
                    justify="space-between"
                    align="center"
                    flex="1"
                    :data-id="item.no"
                    :class="[
                        'trace-code-list-item',
                        'ellipsis',
                        readonly ? '' : 'item-wrapper',
                    ]"
                    style="height: 28px;"
                >
                    <abc-flex v-if="isCompatibleHistoryData(item)" :gap="8" class="ellipsis">
                        <abc-text class="ellipsis" :theme="item.theme" :title="formatNo(item)">
                            {{ formatNo(item) }}
                        </abc-text>
                        <abc-text v-if="item.count > 1" theme="gray">
                            x{{ item.count }}
                        </abc-text>
                    </abc-flex>

                    <abc-flex
                        v-else
                        justify="space-between"
                        align="center"
                        style="flex: 1;"
                        :gap="4"
                    >
                        <abc-text
                            class="ellipsis"
                            :theme="item.theme"
                            :title="formatNo(item)"
                            style="max-width: 196px;"
                        >
                            {{ formatNo(item) }}
                        </abc-text>
                        <abc-form v-if="!readonly && !isChineseMedicineGoods && !isNoTraceCodeGoods" item-no-margin>
                            <abc-form-item
                                trigger="custom-active"
                                :error-style="{ 'margin-left': '-100px' }"
                            >
                                <abc-space is-compact>
                                    <abc-tooltip
                                        placement="top"
                                        :disabled="!isUsageOverLimit(item)"
                                    >
                                        <abc-input
                                            v-abc-focus-selected
                                            :value="item.count"
                                            type="number"
                                            size="small"
                                            :disabled="readonly"
                                            :width="50"
                                            :config="{
                                                supportZero: false,
                                                max: isReturn ? item.codeMaxCount : 99999,
                                            }"
                                            :input-custom-style="isUsageOverLimit(item) ? {
                                                border: '1px solid var(--abc-color-Y2, #F93)',
                                                background: 'var(--abc-color-Y4, #FFF4EA)',
                                                textAlign: 'center',
                                                position: 'relative',
                                                'z-index': 3,
                                            } : { textAlign: 'center' }"
                                            @change="(val)=>handleCountChange(val,item)"
                                        >
                                        </abc-input>
                                        <template #content>
                                            <abc-flex :gap="4" vertical align="end">
                                                <abc-tips
                                                    icon
                                                    theme="warning"
                                                >
                                                    追溯码可用上限{{ displayFormatPieceUnit(item,goods) }}，本次入库：{{ item.count }}{{ item.unit }}
                                                </abc-tips>
                                                <abc-button
                                                    class="traceable-code-cell-item-usage-over-limit"
                                                    variant="text"
                                                    size="small"
                                                    @click="handleToDetail(item)"
                                                >
                                                    数据有误，去修正
                                                </abc-button>
                                            </abc-flex>
                                        </template>
                                    </abc-tooltip>
                                    <abc-select
                                        v-model="item.unit"
                                        :width="46"
                                        :inner-width="46"
                                        :disabled="isDisabledUnitSelect"
                                        size="small"
                                        custom-class="traceable-code-cell-item-unit-group"
                                        @change="(val)=>handleUnitChange(val,item)"
                                    >
                                        <abc-option
                                            v-if="goods?.pieceUnit"
                                            :value=" goods.pieceUnit"
                                            :label=" goods.pieceUnit"
                                        ></abc-option>
                                        <abc-option
                                            v-if="goods?.packageUnit"
                                            :value=" goods.packageUnit"
                                            :label=" goods.packageUnit"
                                        ></abc-option>
                                    </abc-select>
                                </abc-space>
                            </abc-form-item>
                        </abc-form>
                        <abc-text v-else>
                            x {{ displayCount(item) }}{{ displayUnit(item) }}
                        </abc-text>
                    </abc-flex>


                    <abc-space :size="4">
                        <abc-text theme="warning-light">
                            {{ item.warningText }}
                        </abc-text>
                        <abc-flex
                            align="center"
                            justify="center"
                            style="width: 20px; height: 20px;"
                        >
                            <abc-icon
                                v-if="item.warningText"
                                class="attention-icon"
                                :size="16"
                                icon="n-alert-fill"
                                color="var(--abc-color-Y2)"
                            ></abc-icon>
                            <abc-delete-icon
                                v-if="canDelete(item)"
                                class="delete-icon"
                                data-cy="abc-delete-icon"
                                theme="dark"
                                @delete="handleDeleteItem(item, index)"
                            ></abc-delete-icon>
                        </abc-flex>
                    </abc-space>
                </abc-flex>

                <abc-flex vertical>
                    <span>{{ formatNo(item) }}</span>
                    <abc-divider
                        margin="small"
                        theme="dark"
                        variant="dashed"
                        size="normal"
                    ></abc-divider>
                    <span>采集追溯码与该商品绑定的产品标识码不同，请确认是否采集错误</span>
                </abc-flex>
            </abc-popover>
        </template>
    </abc-list>
</template>

<script>
    import TraceCode, { TraceableCodeTypeEnum } from '@/service/trace-code/service';
    import TraceCodeLimitDialog from '@/service/trace-code/dialog-limit-trace-code';
    import {
        isNotNull, isNull,
    } from '@/utils';

    export default {
        name: 'TraceableCodeList',
        props: {
            traceableCodeList: {
                type: Array,
                default: () => [],
            },
            goods: {
                type: Object,
                required: true,
            },
            height: {
                type: Number,
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            isNoTraceCodeGoods: Boolean,
            isChineseMedicineGoods: {
                type: Boolean,
                default: false,
            },
            isReturn: {
                type: Boolean,
                default: false,
            },
            goodsCount: {
                type: Object,
                default: () => ({
                    label: '采购数量',
                    countLabel: '采购数量',
                    unitCount: 0,
                    maxCount: 0,
                    unit: '',
                    isTrans: false,// 后端是否换算过
                }),
            },
        },
        data() {
            return {
                highLightCode: null,
                virtualListConfig: {
                    rowHeight: 37,// 36height+1border
                    bufferSize: 50,
                    bufferLoad: false,
                },
            };
        },
        computed: {
            enableVirtualList() {
                return this.traceableCodeList.length > 150;
            },
            isDisabledUnitSelect() {
                return this.readonly || this.goods?.pieceUnit === this.goods?.packageUnit;
            },
        },
        beforeDestroy() {
            this._timer = null;
            this.highLightCode = null;
        },
        methods: {
            handleDeleteItem(item, index) {
                this.$emit('deleteItem', item, index);
            },
            formatNo(item) {
                if (item.traceableCodeNoInfo?.type === TraceableCodeTypeEnum.NO_CODE) {
                    return `${item.drugIdentificationCode}`;
                }

                const {
                    start,end,
                } = TraceCode.formatTraceableCode(item);
                return `${start} ${end}`;
            },
            canDelete(item) {
                if (this.readonly) return false;
                if (this.isNoTraceCodeGoods) {
                    return item.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE;
                }
                return true;
            },
            createKey(e) {
                return e.keyId || e.id || e.no;
            },
            hoverItemFunc(item) {
                if (this.readonly) return false;
                return item.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE;
            },
            customItemClass(item) {
                return item.no === this.highLightCode ? 'highlight' : '';
            },
            triggerHighLight(code, duration = 1500) {
                this.$nextTick(() => {
                    this.highLightCode = code;
                    this.scrollTarget();
                    this._timer = setTimeout(() => {
                        this.highLightCode = null;
                    }, duration);
                });
            },
            scrollTarget() {
                if (!this.highLightCode) return;

                if (this.enableVirtualList) {
                    const item = this.traceableCodeList.find((e) => e.no === this.highLightCode);
                    this.$refs.listRef.scrollToElement(this.createKey(item));
                }

                this.$nextTick(() => {
                    this.$el.querySelector(`.trace-code-list-item[data-id="${this.highLightCode}"]`)?.scrollIntoView({
                        behavior: 'smooth',
                    });
                });
            },
            isCompatibleHistoryData(item) {
                return isNull(item.hisPackageCount) && isNull(item.hisPieceCount);
            },
            isUsageOverLimit(item) {
                if (this.readonly) return false;
                const hisMaxPieceCount = (item.hisMaxPackageCount ?? Number.MAX_SAFE_INTEGER) * (this.goods?.pieceNum ?? 1);
                const leftCount = hisMaxPieceCount - TraceCode.getTraceCollectCodeCountBySmall(this.goods,item);
                return leftCount < 0;
            },
            displayFormatPieceUnit(item,productInfo) {
                const leftCount = (item.hisMaxPackageCount ?? Number.MAX_SAFE_INTEGER) * (this.goods?.pieceNum ?? 1);
                return TraceCode.displayFormatPieceUnit(leftCount ?? 0, productInfo);
            },
            handleToDetail(item) {
                new TraceCodeLimitDialog({
                    traceCodeInfo: item,
                    goods: this.goods,
                    onConfirm: () => {
                        this.$emit('updateHisMaxPackageCount', item);
                    },
                }).generateDialogAsync({ parent: this });
            },
            handleCountChange(val,item) {
                const newVal = isNotNull(val) && !Number.isNaN(+val) ? +val : item.count;
                const { packageUnit } = this.goods;
                if (packageUnit === item.unit) {
                    this.$set(item,'hisPackageCount',newVal);
                    delete item.hisPieceCount;
                } else {
                    this.$set(item,'hisPieceCount',newVal);
                    delete item.hisPackageCount;
                }
                this.$set(item,'count',newVal);
                this.$emit('changeCount', item);
            },
            handleUnitChange(val,item) {
                const unit = isNotNull(val) ? val : item.unit;
                this.$set(item, 'unit', unit);
                if (unit === this.goods.packageUnit) {
                    this.$set(item, 'hisPackageCount', item.count);
                    delete item.hisPieceCount;
                } else {
                    this.$set(item, 'hisPieceCount', item.count);
                    delete item.hisPackageCount;
                }
                this.$emit('changeUnit', item);
            },
            displayCount(item) {
                if (isNotNull(item.hisPackageCount)) return item.hisPackageCount;
                if (isNotNull(item.hisPieceCount)) return item.hisPieceCount;
                if (isNotNull(item.count)) return item.count;
                return 1;
            },
            displayUnit(item) {
                if (isNotNull(item.unit)) return item.unit;
                if (isNotNull(item.hisPackageCount)) return this.goods.packageUnit;
                if (isNotNull(item.hisPieceCount)) return this.goods.pieceUnit;
                return this.goodsCount.unit ?? '';
            },
        },
    };
</script>

<style lang="scss" scoped>
.item-wrapper {
    .attention-icon {
        display: block;
    }

    .delete-icon {
        display: none;
    }

    &:hover {
        .attention-icon {
            display: none;
        }

        .delete-icon {
            display: flex;
        }
    }
}
</style>
