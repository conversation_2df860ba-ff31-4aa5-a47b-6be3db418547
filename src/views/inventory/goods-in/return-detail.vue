<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle.title"
        responsive
        size="hugely"
        append-to-body
        :disabled-keyboard="disabledKeyboard"
        @open="pushDialogName"
        @close="popDialogName"
    >
        <div slot="title-append" style="margin-left: 8px;">
            <abc-space>
                <abc-text theme="gray">
                    {{ dialogTitle.orderNo }}
                </abc-text>

                <abc-tag-v2
                    v-if="dialogTitle.hasTag"
                    :variant="dialogTitle.variant"
                    :theme="dialogTitle.tagTheme"
                    size="small"
                >
                    {{ dialogTitle.tagName }}
                </abc-tag-v2>
            </abc-space>
        </div>

        <abc-form
            v-abc-loading.coverOpaque="!order"
            item-no-margin
            is-excel
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-descriptions
                        v-if="order"
                        :column="3"
                        :label-width="90"
                        background
                        grid
                        size="large"
                        stretch-last-item
                    >
                        <abc-descriptions-item
                            :span="1"
                            content-class-name="ellipsis"
                            label="出库人"
                        >
                            <span>{{ order.createdUser?.name }}</span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="1"
                            content-class-name="ellipsis"
                            label="出库门店"
                        >
                            <span>{{ clinicName(order.toOrgan) }}</span>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            :span="1"
                            content-class-name="ellipsis"
                            label="出库库房"
                        >
                            <span>{{ order.pharmacy?.name }}</span>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            v-if="order.stockInOrder"
                            :span="2"
                            content-class-name="ellipsis"
                            label="供应商"
                        >
                            <overflow-tooltip
                                v-if="order.stockInOrder.stockInOrderNo"
                                :content="`${order.stockInOrder.supplier} ${order.stockInOrder.stockInOrderNo}`"
                            >
                                <span>{{ order.stockInOrder.supplier }}</span>
                                <abc-text
                                    theme="gray"
                                    size="mini"
                                >
                                    (入库单号：{{ order.stockInOrder.stockInOrderNo }})
                                </abc-text>
                            </overflow-tooltip>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="1"
                            content-class-name="ellipsis"
                            label="备注"
                        >
                            <span :title="commentText">{{ commentText }}</span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        v-if="order"
                        type="excel"
                        :render-config="refundTableConfig"
                        :data-list="order.list"
                        empty-size="small"
                        :show-hover-tr-bg="false"
                        cell-size="large"
                    >
                        <template
                            #shortId="{
                                trData: row
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <overflow-tooltip :content="row.goods.shortId">
                                </overflow-tooltip>
                            </abc-table-cell>
                        </template>

                        <!--药品名称-->
                        <template #cadn="{ trData: row }">
                            <display-name-cell
                                :goods="row.goods"
                                :hover-config="{
                                    openDelay: 500,
                                    showPrice: true,
                                    showShebaoCode: true,
                                    pharmacyNo: row.pharmacy ? row.pharmacy.no : order.pharmacy.no
                                }"
                            ></display-name-cell>
                        </template>


                        <!--批次-->
                        <template #batchId="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title.ellipsis="row.batchId || ''"></span>
                            </abc-table-cell>
                        </template>
                        <!--批号 不可修改-->
                        <template #batchNo="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title.ellipsis="row.batchNo || ''"></span>
                            </abc-table-cell>
                        </template>

                        <!--效期-->
                        <template #expiryDate="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title.ellipsis="row.expiryDate || ''"></span>
                            </abc-table-cell>
                        </template>

                        <!-- 出库数量 -->
                        <template #count="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="formatGoodsStock(row.packageCount, row.pieceCount, row.goods)">
                                    {{ formatGoodsStock(row.packageCount, row.pieceCount, row.goods) }}
                                </span>
                            </abc-table-cell>
                        </template>

                        <!--进价-->
                        <template #packageCostPrice="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span
                                    v-if="row.inItem"
                                    class="ellipsis"
                                    :title="`${moneyDigit(row.inItem.useUnitCostPrice, 5) }/${row.inItem.useUnit}`"
                                >{{ moneyDigit(row.inItem.useUnitCostPrice, 5) }}/{{ row.inItem.useUnit }}</span>
                                <span
                                    v-else
                                    class="ellipsis"
                                    :title="`${
                                        moneyDigit(row.stock?.packageCostPrice || row.packageCostPrice, 5)}/${row.goods.packageUnit || row.goods.pieceUnit}`"
                                >{{
                                    moneyDigit(row.stock?.packageCostPrice || row.packageCostPrice, 5)
                                }}/{{ row.goods.packageUnit || row.goods.pieceUnit }}</span>
                            </abc-table-cell>
                        </template>

                        <!--金额-->
                        <template #totalPrice="{ trData: row }">
                            <abc-table-cell>
                                <inventory-order-fixed-hover-popover
                                    :order-type="CorrectOrderTypeEnum.GoodsReturn"
                                    is-revise-order-hover
                                    :order-item="row"
                                    :order-id="row.id"
                                >
                                    <abc-text
                                        v-abc-title.ellipsis="paddingMoney(row.useTotalCostPrice, true)"
                                        :theme="getAmountTheme(row)"
                                    >
                                    </abc-text>
                                </inventory-order-fixed-hover-popover>
                            </abc-table-cell>
                        </template>

                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item,
                            }"
                        >
                            <traceable-code-cell
                                v-model="item.traceableCodeList"
                                :goods="item.goods"
                                :goods-count="getUnitCount(item, item.goods, '出库数量')"
                                readonly
                            ></traceable-code-cell>
                        </template>

                        <template #footer>
                            <abc-flex
                                align="center"
                                flex="1"
                                justify="flex-end"
                                style="padding: 0 12px;"
                            >
                                <abc-space v-if="order?.list?.length" :size="4">
                                    <abc-text theme="gray">
                                        品种
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.kindCount }}
                                    </abc-text>

                                    <abc-text theme="gray">
                                        ，数量
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ moneyDigit(order.count, 4, false) }}
                                    </abc-text>

                                    <abc-text theme="gray">
                                        ，金额
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.amount | formatMoney }}
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <div slot="footer" class="dialog-footer" style="min-height: 32px;">
            <template v-if="order">
                <logs-v3-popover v-if="logs?.length" :logs="logs" style="margin-right: auto;"></logs-v3-popover>

                <template v-if="order.status === GOODS_IN_STATUS.CONFIRM">
                    <abc-button
                        type="primary"
                        :loading="btnLoading"
                        @click="confirmOrderPrev('pass')"
                    >
                        确认出库
                    </abc-button>
                    <abc-button type="danger" @click="confirmOrderPrev('fail')">
                        驳回出库
                    </abc-button>
                </template>
                <abc-space v-else>
                    <abc-button v-if="canReOrder" @click="reOrderHandler">
                        修改并重新发起
                    </abc-button>
                    <abc-button
                        v-if="canRevoke"
                        :loading="btnLoading"
                        variant="ghost"
                        theme="danger"
                        @click="revokeHandler"
                    >
                        撤回
                    </abc-button>
                    <abc-check-access>
                        <abc-button variant="ghost" @click="exportExcel">
                            导出
                        </abc-button>
                    </abc-check-access>

                    <abc-check-access>
                        <print-dropdown
                            :loading="printBtnLoading"
                            @print="print"
                            @select-print-setting="openPrintConfigSettingDialog"
                        ></print-dropdown>
                    </abc-check-access>
                </abc-space>
            </template>

            <abc-button variant="ghost" style="margin-left: 8px;" @click="showDialog = false">
                关闭
            </abc-button>
        </div>

        <abc-dialog
            v-if="showConfirmDialog"
            v-model="showConfirmDialog"
            title="确认"
            custom-class="goods-confirm"
            content-styles="width: 360px;"
        >
            <div class="dialog-content clearfix">
                <template v-if="confirmStatus === 1">
                    <p class="confirm-result">
                        确认结果：<span class="confirm-success">确认出库</span>
                    </p>
                    <p class="confirm-result">
                        确认出库后将会实时更新库存信息
                    </p>
                </template>

                <template v-else>
                    <p class="confirm-result">
                        确认结果：<span class="confirm-refuse">驳回</span>
                    </p>

                    <abc-form
                        ref="checkForm"
                        label-position="left"
                        :label-width="100"
                    >
                        <abc-form-item required style="margin-bottom: 0;">
                            <abc-textarea
                                v-model="rejectComment"
                                :width="312"
                                :height="50"
                                :maxlength="100"
                                placeholder="请输入驳回原因"
                            >
                            </abc-textarea>
                        </abc-form-item>
                    </abc-form>
                    <p class="confirm-result" style="margin-top: 16px;">
                        总部可在驳回单据内修改并重新发起
                    </p>
                </template>
            </div>
            <template slot="footer">
                <div class="dialog-footer">
                    <abc-button style="margin-left: auto;" :loading="btnLoading" @click="confirmHandle">
                        确定
                    </abc-button>
                    <abc-button type="blank" @click="showConfirmDialog = false">
                        取消
                    </abc-button>
                </div>
            </template>
        </abc-dialog>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';

    import GoodsAPI from 'api/goods/index';
    import StockInAPI from 'api/goods/stock-in';
    // import StockOutAPI from 'api/goods/stock-out';
    import {
        moneyDigit, paddingMoney,
    } from '@/utils';
    import { formatGoodsStock } from 'views/inventory/goods-out/common.js';

    import {
        clinicName,
        goodsFullName, goodsHoverTitle, goodsSpec, goodsTotalCostPrice, isChineseMedicine,
    } from 'src/filters/goods';
    import GoodsCommon from '../common';

    import Clone from 'utils/clone.js';

    import totalInfo from '../mixins/total-info.js';
    import DeleteGoodsHandler from '../mixins/delete-goods-handler';
    import AbcPrinter from '@/printer';
    import { ABCPrintConfigKeyMap } from '@/printer/constants.js';
    import {
        GOODS_IN_STATUS, CorrectOrderTypeEnum,
    } from '../constant.js';

    import dialogAutoWidth from 'views/inventory/mixins/dialog-auto-width.js';
    import GoodsTableV3Mixins from 'views/inventory/mixins/goods-table-v3.js';

    import PrintDropdown from 'views/print/print-dropdown';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import OverflowTooltip from 'components/overflow-tooltip.vue';
    import { TagHelper } from 'utils/tag-helper';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import useReviseOrder from 'views/inventory/hooks/useReviseOrder';
    import TraceCode from '@/service/trace-code/service';

    const { orderMainNameText } = getViewDistributeConfig().Inventory;

    export default {
        name: 'OrderReturnDetail',

        components: {
            PrintDropdown,
            LogsV3Popover,
            DisplayNameCell,
            OverflowTooltip,
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
            InventoryOrderFixedHoverPopover: () => import('views/inventory/components/inventory-order-fixed-hover-popover.vue'),
        },

        mixins: [totalInfo, DeleteGoodsHandler, GoodsCommon, dialogAutoWidth, GoodsTableV3Mixins],

        props: {
            visible: Boolean,
            orderId: {
                type: [String, Number],
                default: '',
            },
            goodsId: {
                type: [String, Number],
                default: '',
            },
            onlyApplication: Boolean,
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName,popDialogName,
            } = useDialogStackManager(`退货单详情-${Math.random().toString(36).slice(2)}`);

            const {
                hasRevise,
            } = useReviseOrder();

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                hasRevise,
            };
        },

        data() {
            return {
                CorrectOrderTypeEnum,
                GOODS_IN_STATUS,
                loading: false,
                searchKey: '',
                order: null,
                cloneOrder: {},
                comment: '',
                isFirstPrint: true,
                showConfirmDialog: false,
                confirmStatus: 1,
                rejectComment: '',
                btnLoading: false,
                popoverLoading: false,
                printBtnLoading: false,
                showDialog: this.visible,
            };
        },

        computed: {
            ...mapGetters(['currentClinic', 'multiPharmacyCanUse', 'traceCodeConfig']),
            dialogTitle() {
                const obj = {
                    title: '退货出库单',
                    orderNo: this.order?.orderNo || '',
                    hasTag: false,
                    variant: '',
                    tagName: '',
                    tagTheme: '',
                };

                if (this.order) {
                    if (
                        this.order.status === GOODS_IN_STATUS.CONFIRM &&
                        this.order.toOrgan &&
                        (this.order.toOrgan.name || this.order.toOrgan.shortName)
                    ) {
                        obj.hasTag = true;
                        obj.tagName = '待确认';
                        obj.tagTheme = TagHelper.TODO_TAG.theme;
                        obj.variant = TagHelper.TODO_TAG.variant;
                    }
                    if (this.order.status === GOODS_IN_STATUS.REVIEW) {
                        obj.hasTag = true;
                        obj.tagName = '待总部审核';
                        obj.tagTheme = TagHelper.ING_TAG.theme;
                        obj.variant = TagHelper.ING_TAG.variant;
                    }
                    if (this.order.status === GOODS_IN_STATUS.WITH_DRAW) {
                        obj.hasTag = true;
                        obj.tagName = '已撤回';
                        obj.tagTheme = TagHelper.CANCEL_TAG.theme;
                        obj.variant = TagHelper.CANCEL_TAG.variant;
                    }
                    if (this.order.status === GOODS_IN_STATUS.REFUSE) {
                        obj.hasTag = true;
                        obj.tagName = '已驳回';
                        obj.tagTheme = TagHelper.REFUSE_TAG.theme;
                        obj.variant = TagHelper.REFUSE_TAG.variant;
                    }
                }
                return obj;

            },
            commentText() {
                let commentText = '-';

                if (this.logs.length) {
                    commentText = this.logs.slice(-1)[0].comment || '-';
                }

                return commentText;
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            // 是否为单据发起方，需要后端记录applyClinicId
            isApplyClinic() {
                return this.clinicId === this.order?.applyClinicId;
            },
            // 已撤回/已拒绝状态，发起方可以修改并重新发起
            canReOrder() {
                if (this.order?.status === GOODS_IN_STATUS.REFUSE || this.order?.status === GOODS_IN_STATUS.WITH_DRAW) {
                    return this.isApplyClinic;
                }
                return false;
            },
            // 待审核/待确定状态，发起方可以撤回单据
            canRevoke() {
                // 新判断逻辑
                // if (this.order?.status === GOODS_IN_STATUS.REVIEW || this.order?.status === GOODS_IN_STATUS.CONFIRM) {
                //     return this.isApplyClinic;
                // }

                // 发起方是总部
                if (this.order?.status === GOODS_IN_STATUS.CONFIRM) {
                    return this.isChainAdmin;
                }

                // 发起方是门店
                if (this.order?.status === GOODS_IN_STATUS.REVIEW) {
                    return !this.isChainAdmin;
                }
                return false;
            },
            logs() {
                return this.order?.logs ?? [];
            },
            refundTableConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: `${orderMainNameText}编码`,
                            key: 'shortId',
                            width: 120,
                            style: {
                                width: '120px',
                            },
                        },
                        {
                            label: `${orderMainNameText}名称`,
                            key: 'cadn',
                            flex: 1,
                            style: {
                                width: '180px',
                            },
                        },
                        {
                            label: '批次',
                            key: 'batchId',
                            width: 120,
                            style: {
                                width: '120px',
                            },
                        },
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            width: 120,
                            style: {
                                width: '120px',
                            },
                        },
                        {
                            label: '效期',
                            key: 'expiryDate',
                            width: 120,
                            style: {
                                width: '120px',
                            },
                        },
                        {
                            label: '进价',
                            key: 'packageCostPrice',
                            width: 120,
                            justifyContent: 'flex-end',
                            style: {
                                width: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '出库数量',
                            key: 'count',
                            width: 80,
                            justifyContent: 'flex-end',
                            style: {
                                width: '80px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '金额',
                            key: 'totalPrice',
                            width: 80,
                            justifyContent: 'flex-end',
                            style: {
                                width: '80px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
                this.$emit('update:visible', val);
                if (!val) {
                    this.$emit('close');
                }
            },
        },
        created() {
            this.fetchOrder();
        },

        methods: {
            clinicName,
            moneyDigit,
            goodsFullName,
            goodsSpec,
            goodsTotalCostPrice,
            isChineseMedicine,
            paddingMoney,
            formatGoodsStock,
            getAmountTheme(item) {
                return item.fixedStockOrderList?.length ? 'warning-light' : 'black';
            },
            async onPopoverShow(item) {
                // 可能为可能为空数组，说明请求过了
                if (item.detail) return;

                try {
                    this.popoverLoading = true;
                    const { data } = await GoodsAPI.fetchGoodsBatchList(item.goodsId,{
                        actionType: 10,
                        orderId: item.orderId,
                        orderItemId: item.id,
                    });

                    this.$set(item,'detail',data?.rows ?? []);
                } catch (e) {
                    console.log(e);
                } finally {

                    this.popoverLoading = false;
                }


            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'goods-out' }).generateDialogAsync({ parent: this });
            },

            async exportExcel() {
                return StockInAPI.exportById(this.orderId);
            },
            confirmOrderPrev(type) {
                if (type === 'pass') {
                    this.confirmStatus = 1;
                }
                if (type === 'fail') {
                    this.confirmStatus = 0;
                }
                this.showConfirmDialog = true;
            },
            async confirmHandle() {
                if (this.confirmStatus === 1) {
                    await this.confirmOrder();
                }
                if (this.confirmStatus === 0) {
                    this.$refs.checkForm.validate(async (val) => {
                        if (val) {
                            await this.rejectOrder();
                        }
                    });
                }
            },

            /**
             * @desc 确认出库单
             * <AUTHOR>
             * @date 2018/11/21 20:18:17
             */
            async confirmOrder() {
                try {
                    this.btnLoading = true;
                    await StockInAPI.confirmOrder(this.orderId, {
                        comment: '',
                        pharmacyNo: this.order.pharmacy.no,
                        lastModifiedDate: this.order.lastModifiedDate,
                    });
                    this.btnLoading = false;
                    this.showDialog = false;
                    this.$emit('refresh');
                } catch (e) {
                    this.btnLoading = false;
                    if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail);
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail);
                    } else if (e.code === 962) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前出库单已被确认，已经刷新到新版本，请再次确认',
                            onClose: () => {
                                this.fetchOrder();
                            },
                        });
                    } else if (e.code === 12010) {
                        const shortageMedicineTips = [];
                        const goodsStock = (e && e.detail) || [];
                        const { length } = goodsStock;

                        goodsStock.forEach((item, index) => {
                            let str = '';
                            str = `${item.goods.medicineCadn || item.goods.name || ''}`;
                            if (length - 1 === index) {
                                str += ' 库存不足';
                            }
                            shortageMedicineTips.push(str);
                        });
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: shortageMedicineTips,
                            onClose: () => {
                                this.fetchOrder();
                            },
                        });
                    } else if (e.code === 470) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${e.message}`,
                        });
                    }
                }
            },

            /**
             * @desc 拒绝出库单
             * <AUTHOR>
             * @date 2019/09/17 09:59:47
             */
            async rejectOrder() {
                try {
                    this.btnLoading = true;
                    await StockInAPI.rejectOrder(this.orderId, {
                        comment: this.rejectComment,
                        lastModifiedDate: this.order.lastModifiedDate,
                    });
                    this.btnLoading = false;
                    this.showDialog = false;
                    this.$emit('refresh');
                } catch (e) {
                    this.btnLoading = false;
                }
            },

            async fetchOrder() {
                if (!this.orderId) return false;
                this.loading = true;
                this.order = await StockInAPI.getById(this.orderId, 1, 1);
                if (!this.order.list) {
                    this.order.list = [];
                }
                if (this.goodsId) {
                    this.sortOrderList();
                }
                this.isFirstPrint = true;
                this.loading = false;
            },
            print() {
                let printData = Clone(this.order);
                // 是否产生修正
                const hasRevise = this.hasRevise(printData?.importFlag);

                const printAction = async () => {
                    try {
                        printData.comment = '';
                        if (printData.logs && printData.logs.length) {
                            printData.comment = printData.logs[printData.logs.length - 1]?.comment || '';
                        }
                        printData.multiPharmacyCanUse = !!this.multiPharmacyCanUse;

                        if (hasRevise) {
                            this.printBtnLoading = true;
                            // 获取合并后数据
                            const fullOrder = await StockInAPI.getOrderById(this.orderId, {
                                limit: 9999,
                                offset: 0,
                                withStockId: 1,
                                withReturnLeft: 1,
                                needMergedOrder: 1,
                            });

                            printData = {
                                ...fullOrder,
                                comment: printData.comment || '',
                                multiPharmacyCanUse: printData.multiPharmacyCanUse,
                            };
                        }

                        AbcPrinter.abcPrint({
                            templateKey: window.AbcPackages.AbcTemplates.goodsOut,
                            printConfigKey: ABCPrintConfigKeyMap.CK,
                            data: printData,
                        });

                    } catch (e) {
                        console.error('打印失败:', e);
                        this.$Toast({
                            type: 'error',
                            message: '打印失败，请重试',
                        });
                    } finally {
                        this.printBtnLoading = false;
                    }
                };

                // 保持原有的确认逻辑
                if (hasRevise) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '该退货出库单存在入库数量/进价修改过的药品，将基于正确退货出库信息打印',
                        onConfirm: () => {
                            printAction();
                        },
                    });
                } else {
                    printAction();
                }
            },
            sortOrderList() {
                const newOrderList = Clone(this.order.list);
                newOrderList.sort((a, b) => {
                    return (b.goodsId === this.goodsId) - (a.goodsId === this.goodsId);
                });
                this.order.list = Clone(newOrderList);
            },
            formatGoodsNameSpec(goods) {
                if (!goods) return '';
                return `${goodsHoverTitle(goods)} ${goodsSpec(goods)}`;
            },
            reOrderHandler() {
                this.showDialog = false;
                this.$emit('resubmit', {
                    orderId: this.order?.id,
                    outType: this.order?.type,
                });
            },
            revokeHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确认撤回后单据将失效，可在此单据内修改并重新发起',
                    onConfirm: async () => {
                        await this.revoke();
                    },
                });
            },
            async revoke() {
                try {
                    this.btnLoading = true;
                    await StockInAPI.revokeOrder(this.orderId);
                    this.btnLoading = false;
                    this.fetchOrder();
                    this.$emit('refresh',false, '', false);

                } catch (e) {
                    this.btnLoading = false;
                }
            },
            getUnitCount(item, goods, label) {
                const count = TraceCode.getCollectCount(item, goods);
                let unitCount = '', unit = '', packageCount = '', pieceCount = '';
                // 拆零
                if (Math.abs(count) < goods.pieceNum) {
                    unit = goods.pieceUnit;
                    unitCount = Math.abs(count);
                    pieceCount = unitCount;
                } else {
                    unit = goods.packageUnit;
                    unitCount = Math.ceil(Math.abs(count / goods.pieceNum));
                    packageCount = Math.floor(Math.abs(count / goods.pieceNum));
                    pieceCount = Math.abs(count % goods.pieceNum);
                }

                return {
                    useExternalCount: this.isStrictCountWithTraceCodeCollect,
                    packageCount,
                    pieceCount,
                    unitCount,
                    unit,
                    label,
                    countLabel: label,
                    maxCount: item._maxTraceCodeCount,
                    isTrans: item._isTransformable,
                };
            },
        },
    };
</script>
