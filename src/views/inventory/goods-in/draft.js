import { mapGetters } from 'vuex';
import Draft from '../mixins/draft.js';
import Clone from 'utils/clone';
import GoodsInAPI from 'api/goods/stock-in';
import { createGUID } from '@/utils';
import TraceCode from '@/service/trace-code/service';

export default {
    mixins: [Draft],
    data() {
        return {
            saveDraftBtnLoading: false,
            deleteDraftBtnLoading: false,
        };
    },
    computed: {
        ...mapGetters(['draftGoodsIn']),
    },
    async created() {
        if (this.draftId) {
            await this.fetchDraft('goods-in', this.draftId, (draft) => {
                const _draft = {
                    ...draft,
                    purchaseType: draft.extendData?.purchaseType,
                    platformType: draft.extendData?.platformType,
                    list: draft.list?.map((e) => {
                        // 如果是商城单子，确保要有 mallGoodsInfo
                        if (this.mallOrderId) {
                            e.mallGoodsInfo = e.mallGoodsInfo || {};
                        }
                        if (e.extendData) {
                            e.shebaoCode = e.extendData.shebaoCode;
                            e.erpGoodsId = e.extendData.erpGoodsId;
                            e.emergencyFlag = e.extendData.emergencyFlag;
                            e.erpOrderItemId = e.extendData.erpOrderItemId;
                        }
                        e.searchGoodsKey = e?.goods ? e?.goods.medicineCadn || e?.goods.name : e?.rowMatchInfo?.medicineCadn || e?.rowMatchInfo?.name;
                        e.keyId = createGUID();
                        return e;
                    }) ?? [],
                    toOrganId: draft.toOrganId || this.currentClinic.clinicId,
                    toOrganName: draft.toOrgan?.name,
                };
                // 保存过供应商
                if (_draft.supplierId) {
                    _draft.supplier = {
                        id: _draft.supplierId,
                        name: _draft.supplier,
                    };
                }
                return _draft;
            });

            if (this.goodsId && this.order.list.length) {
                this.sortOrderList();
            }

            await this.initCollectCodeCountList(this.order.list);

            this.order.list.forEach((item) => {
                // 补充无码标识
                this.initNoTraceCodeList(item);
            });

            const updateAttributeCallbackFn = (item, key, val) => {
                this.$set(item, key, val);
            };
            (this.order.list ?? []).forEach((item) => {
                TraceCode.initTraceableCodeListInInventory(item.traceableCodeList, item.goods, updateAttributeCallbackFn);
            });

            this._cacheOrderDraft = Clone(this.order);
        }
    },
    methods: {
        // 获取入库云草稿-商城使用
        async fetchGoodsInDraftDetail() {
            const { data } = await GoodsInAPI.getGoodsInDraftDetail(this.draftId);
            const draftOrder = {
                ...this.order,
                ...data,
                list: data.list?.map((e) => {
                // 如果是商城单子，确保要有 mallGoodsInfo
                    if (this.mallOrderId) {
                        e.mallGoodsInfo = e.mallGoodsInfo || {};
                    }
                    return e;
                }) ?? [],
                createdDate: data.created || '',
                lastModifiedDate: data.lastModified || '',
                toOrganId: data.toOrganId || this.currentClinic.clinicId,
                toOrganName: data.toOrgan?.name,
                isCloud: true,// 后续用来区分本地草稿和云草稿
            };

            // 保存过供应商
            if (data.supplierId) {
                draftOrder.supplier = {
                    id: data.supplierId,
                    name: data.supplier,
                };
            }

            this.order = draftOrder;
        },
        /**
         * @desc 草稿数据创建
         * <AUTHOR>
         * @date 2024/9/24 上午11:15
         * @param {Object} obj 额外参数
         */
        createDraftData(obj = {}) {
            const draft = {
                lastModified: new Date(),
                created: new Date(),
                createdUser: {
                    id: this.userInfo.id,
                    name: this.userInfo.name,
                },
                pharmacy: this.order.pharmacy,
                pharmacyNo: this.order.pharmacy?.no,
                pharmacyType: this.order.pharmacy?.type,
                type: this.order.type, // 入库单类型
                supplier: this.order.supplier?.name,
                supplierId: this.order?.supplierId || this.order.supplier?.id,
                inspectBy: this.order.inspectBy,
                comment: this.order.comment,
                outOrderNo: this.order.outOrderNo,
                amount: this.amount,
                count: this.totalCount,
                sum: this.totalCount,
                kindCount: this.kindCount,
                mallOrderId: (this.orderInfo && this.orderInfo.mallOrderId) || this.mallOrderId || '',
                // 总部可以给其他门店入库，所以先取 order上的 toOrganId没有才去当前门店拿。
                toOrganId: this.order.toOrganId || this.currentClinic.clinicId,
                order: Clone(this.order),
                extendData: {
                    purchaseType: this.order.purchaseType,
                    platformType: this.order.platformType,
                },
                orderClientUniqKey: this._orderClientUniqKey,
                ...obj,
            };

            return draft;
        },
        autoSaveDraftAsync() {
            clearTimeout(this._timer);

            this._timer = setTimeout(() => {
                this.autoSaveDraftHandler();
            }, 1000);
        },
        autoSaveDraftHandler() {
            if (!this._draftId) {
                this._draftId = this._beforeDraft?.draftId || `${Date.now()}`;
            }
            console.log('保存草稿防止数据丢失', this._draftId);

            if (this.isShebaoCollect) {
                this.order.list.forEach((item) => {
                    item.extendData = {
                        shebaoCode: item.shebaoCode,
                        erpGoodsId: item.erpGoodsId,
                        emergencyFlag: item.emergencyFlag,
                        erpOrderItemId: item.erpOrderItemId,
                    };
                });
            }

            this.setDraft('goods-in', this.createDraftData({
                id: this._beforeDraft?.id,
                draftId: this._draftId,
            }));
        },
        /**
         * @desc 触发 设置草稿
         * <AUTHOR>
         * @date 2018/11/27 15:52:25
         */
        setDraftHandler(onFulfilled, onRejected = console.error, isForceSave = false) {
            if (this.orderId && !this.isReInStock) return false;

            const list = this.order.list || [];

            if (this.isShebaoCollect) {
                list.forEach((item) => {
                    item.extendData = {
                        shebaoCode: item.shebaoCode,
                        erpGoodsId: item.erpGoodsId,
                        emergencyFlag: item.emergencyFlag,
                        erpOrderItemId: item.erpOrderItemId,
                    };
                });

            }
            const data = this.createDraftData({
                list,
            });
            if (isForceSave) {
                // 强制提交
                data.forceSubmit = 1;
                // 保证调新建
                data.id = null;
            }

            this.saveDraftMixin('goods-in', data).then(onFulfilled, onRejected);
        },

        // 删除草稿
        deleteDraft(callback, onRejected = console.error) {
            if (!this.draftId) return onRejected();
            this.deleteDraftMixin('goods-in', this.draftId).then(callback, onRejected);
        },
        /**
         * @desc 保存草稿
         * <AUTHOR>
         * @date 2019/12/12
         */
        async saveDraft(isForceSave = false) {
            this.saveDraftBtnLoading = true;
            // 清除异步草稿任务
            clearTimeout(this._timer);
            this.setDraftHandler(
                () => {
                    this.saveDraftBtnLoading = true;
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                    if (typeof this.close === 'function') {
                        this.close();
                    }
                    this.$emit('close');
                    this.showDialog = false;
                },
                (e) => {
                    console.error(e);
                    if (e.code === 12812) {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                            confirmText: '确认',
                            onConfirm: () => this.saveDraft(true),
                        });
                    } else {
                        this.$Toast({
                            type: 'error',
                            message: '保存失败',
                        });
                    }
                    this.saveDraftBtnLoading = false;
                },
                isForceSave,
            );
        },
        /**
         * @desc 删除草稿
         * <AUTHOR>
         * @date 2019/12/12
         */
        deleteDraftHandler() {
            this.$confirm({
                type: 'warn',
                title: '删除确认',
                content: '删除后不能恢复，确定删除该草稿？',
                onConfirm: () => {
                    this.deleteDraftBtnLoading = true;
                    this.deleteDraft(() => {
                        this.deleteDraftBtnLoading = false;
                        this.$Toast({
                            type: 'success',
                            message: '删除成功',
                        });
                        if (typeof this.close === 'function') {
                            this.close();
                        }
                        this.$emit('close');
                        this.showDialog = false;
                    },(e) => {
                        if (e.code === 12813) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: e.message,
                                onClose: () => {
                                    this.$emit('refresh', true, 'add');
                                    this.showDialog = false;
                                },
                            });
                        } else {
                            this.$Toast({
                                type: 'error',
                                message: '删除失败',
                            });
                        }
                        this.deleteDraftBtnLoading = false;
                    });
                },
            });
        },
    },
};
