<template>
    <abc-dialog
        v-if="showDialog"
        ref="dialogRef"
        v-model="showDialog"
        :title="dialogTitle.title"
        class="goods-in-wrapper"
        responsive
        :auto-focus="false"
        disabled-keyboard
        append-to-body
    >
        <div slot="title-append" style="margin-left: 8px;">
            <abc-space>
                <abc-text theme="gray">
                    {{ dialogTitle.orderNo }}
                </abc-text>

                <abc-tag-v2
                    v-if="dialogTitle.hasTag"
                    :variant="dialogTitle.variant"
                    :theme="dialogTitle.tagTheme"
                    size="small"
                >
                    {{ dialogTitle.tagName }}
                </abc-tag-v2>
            </abc-space>
        </div>
        <!--没有入库单，需要选择入库单-->
        <template v-if="!order">
            <abc-form is-excel item-no-margin style="height: 100%;">
                <abc-layout preset="dialog-table">
                    <abc-layout-header>
                        <abc-descriptions
                            :column="isChainAdmin ? 4 : 3"
                            :label-width="88"
                            background
                            grid
                            size="large"
                        >
                            <abc-descriptions-item
                                :span="1"
                                content-padding="0px"
                                content-class-name="ellipsis"
                                label="创建日期"
                            >
                                <abc-form-item>
                                    <abc-date-picker
                                        v-model="dateRange"
                                        type="daterange"
                                        :picker-options="pickerOptions"
                                        :clearable="false"
                                        size="tiny"
                                        value-format="YYYY-MM-DD"
                                        @change="changeDate"
                                    ></abc-date-picker>
                                </abc-form-item>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="isChainAdmin"
                                :span="1"
                                content-padding="0px"
                                content-class-name="ellipsis"
                                label="选择门店"
                            >
                                <abc-form-item>
                                    <clinic-select
                                        v-model="fetchParams.clinicId"
                                        @change="changeClinic"
                                    ></clinic-select>
                                </abc-form-item>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                :span="1"
                                content-padding="0px"
                                content-class-name="ellipsis"
                                label="供应商"
                            >
                                <abc-form-item>
                                    <abc-select
                                        v-model="fetchParams.supplierId"
                                        inner-width="280px"
                                        custom-class="supplierWrapper"
                                        with-search
                                        :input-style="{ paddingRight: '26px' }"
                                        :fetch-suggestions="fetchSuggestions"
                                        @change="changeSupplier"
                                    >
                                        <abc-option :value="''" label="全部供应商"></abc-option>
                                        <abc-option
                                            v-for="it in currentSupplierList"
                                            :key="`${it.id }`"
                                            :value="it.id"
                                            :label="it.name"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                :span="1"
                                content-padding="0px"
                                content-class-name="ellipsis"
                                :label="`${orderMainNameText}名称`"
                            >
                                <abc-form-item>
                                    <goods-auto-complete-cover-title
                                        class="abc-autocomplete-search"
                                        :placeholder="`扫码/${orderMainNameText}名称/首字母`"
                                        :with-stock="false"
                                        :pharmacy-no="pharmacyNo"
                                        :search.sync="searchKey"
                                        enable-local-search
                                        clearable
                                        @selectGoods="selectGoods"
                                        @clear="clearSearch"
                                    >
                                    </goods-auto-complete-cover-title>
                                </abc-form-item>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-layout-header>

                    <abc-layout-content>
                        <abc-table
                            :render-config="renderConfig"
                            :data-list="inOrders"
                            empty-size="small"
                            :loading="loading"
                            empty-content="没有相关入库单"
                        >
                            <template #order-info="{ trData: item }">
                                <abc-flex align="center" justify="space-between" class="goods-out-order-item">
                                    <abc-flex vertical style="width: 1026px;">
                                        <abc-space :size="16">
                                            <abc-text size="large" theme="black" bold>
                                                {{ item.orderNo }}
                                            </abc-text>
                                            <abc-text size="large" theme="gray">
                                                {{ item.supplier }}
                                            </abc-text>
                                            <abc-text v-if="multiPharmacyCanUse" size="large" theme="gray">
                                                {{ item.pharmacy && item.pharmacy.name }}
                                            </abc-text>

                                            <abc-popover
                                                trigger="hover"
                                                class="inventory-popper-wrapper"
                                                placement="bottom-start"
                                                theme="yellow"
                                            >
                                                <abc-text
                                                    slot="reference"
                                                    class="view-detail"
                                                    size="large"
                                                    theme="primary"
                                                >
                                                    查看详情
                                                </abc-text>
                                                <div class="order-table-wrapper">
                                                    <div class="table-header">
                                                        <div class="name">
                                                            {{ orderMainNameText }}
                                                        </div>
                                                        <div class="batch-no">
                                                            生产批号
                                                        </div>
                                                        <div class="date">
                                                            效期
                                                        </div>
                                                        <div class="package-price">
                                                            进价
                                                        </div>
                                                        <div class="count">
                                                            入库数量
                                                        </div>
                                                        <div class="return-count">
                                                            可退数量
                                                        </div>
                                                    </div>
                                                    <div class="table-body">
                                                        <div
                                                            v-for="(inOrder, oIndex) in (item.list || [])"
                                                            :key="oIndex"
                                                            class="table-tr"
                                                        >
                                                            <div class="name ellipsis">
                                                                {{ inOrder.goods | goodsFullName }}
                                                            </div>
                                                            <div class="batch-no ellipsis">
                                                                {{ inOrder.batchNo }}
                                                            </div>
                                                            <div class="date ellipsis">
                                                                {{ inOrder.expiryDate }}
                                                            </div>
                                                            <div class="package-price">
                                                                {{ paddingMoney(inOrder.useUnitCostPrice) }}/{{ inOrder.useUnit }}
                                                            </div>
                                                            <div class="count">
                                                                {{ inOrder.useCount }}{{ inOrder.useUnit }}
                                                            </div>
                                                            <div class="return-count">
                                                                {{ formatReturnLeft(inOrder) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </abc-popover>
                                        </abc-space>
                                        <abc-space :size="16" class="ellipsis" style="max-width: 100%;">
                                            <template v-for="e in item.list">
                                                <abc-text :key="e.goodsId" size="normal" theme="gray">
                                                    {{ `${e.goods.medicineCadn || e.goods.name || ''} ${formatGoodsCount(e)}` }}
                                                </abc-text>
                                            </template>
                                        </abc-space>
                                    </abc-flex>

                                    <abc-button class="btn" :disabled="disabledBtn(item.list || [])" @click="item.sourceType === 'goodsInit' ? handleSelect(item) : selectInOrder(item.id)">
                                        选择
                                    </abc-button>
                                </abc-flex>
                            </template>

                            <template #footer>
                                <div style="padding: 12px;">
                                    <abc-pagination
                                        :show-total-page="true"
                                        :pagination-params="pageParams"
                                        :count="orderCount"
                                        @current-change="changePageIndex"
                                    ></abc-pagination>
                                </div>
                            </template>
                        </abc-table>
                    </abc-layout-content>
                </abc-layout>
            </abc-form>
        </template>

        <template v-else>
            <template v-if="showCreateOrder">
                <abc-form
                    ref="createForm"
                    v-abc-loading="loading"
                    item-no-margin
                    is-excel
                    style="height: 100%;"
                >
                    <abc-layout preset="dialog-table">
                        <abc-layout-header>
                            <abc-descriptions
                                :column="3"
                                :label-width="90"
                                background
                                grid
                                size="large"
                                stretch-last-item
                            >
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="出库人"
                                >
                                    <span>{{ userInfo?.name }}</span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="出库门店"
                                >
                                    <span>{{ clinicName(order.toOrgan) }}</span>
                                </abc-descriptions-item>

                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="供应商"
                                >
                                    <overflow-tooltip
                                        :content="`
                                        ${order.supplier}
                                        ${orderId ? order.stockInOrder?.orderNo : order.orderNo}
                                        `"
                                    >
                                        <span>{{ order.supplier }}</span>
                                        <abc-text
                                            theme="gray"
                                            size="mini"
                                        >
                                            (入库单号：{{ orderId ? order.stockInOrder?.orderNo : order.orderNo }})
                                        </abc-text>
                                    </overflow-tooltip>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="备注"
                                >
                                    <span :title="comment || '-'">{{ comment || '-' }}</span>
                                </abc-descriptions-item>
                            </abc-descriptions>
                        </abc-layout-header>
                        <abc-layout-content>
                            <abc-table
                                key="outTable"
                                type="excel"
                                :render-config="outTableConfig"
                                :data-list="list"
                                empty-size="small"
                                :show-checked="false"
                                :show-hover-tr-bg="false"
                                cell-size="large"
                            >
                                <template
                                    #shortId="{
                                        trData: row
                                    }"
                                >
                                    <abc-table-cell class="ellipsis">
                                        <overflow-tooltip :content="row.goods.shortId">
                                        </overflow-tooltip>
                                    </abc-table-cell>
                                </template>
                                <!--药品名称-->
                                <template #cadn="{ trData: row }">
                                    <display-name-cell
                                        :goods="row.goods"
                                        :hover-config="{
                                            openDelay: 500,
                                            showPrice: true,
                                            showShebaoCode: true,
                                            pharmacyNo: order.pharmacy ? order.pharmacy.no : pharmacyNo
                                        }"
                                    ></display-name-cell>
                                </template>

                                <!--批号-->
                                <template #batchNo="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span class="ellipsis" :title="row.batchNo || (row.inItem && row.inItem.batchNo)">
                                            {{ row.batchNo || (row.inItem && row.inItem.batchNo) }}
                                        </span>
                                    </abc-table-cell>
                                </template>

                                <!--效期-->
                                <template #expiryDate="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span class="ellipsis" :title="row.expiryDate || (row.inItem && row.inItem.expiryDate)">
                                            {{ row.expiryDate || (row.inItem && row.inItem.expiryDate) }}
                                        </span>
                                    </abc-table-cell>
                                </template>

                                <!--出库数量-->
                                <template #count="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span class="ellipsis" :title="row | complexCount">{{ row | complexCount }}</span>
                                    </abc-table-cell>
                                </template>

                                <!--进价-->
                                <template #costPrice="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span
                                            v-if="row.inItem"
                                            v-abc-title.ellipsis="`${paddingMoney(row.inItem.useUnitCostPrice)}/${row.inItem.useUnit}`"
                                        >
                                        </span>
                                        <template v-else>
                                            <span
                                                v-if="row.useUnitCostPrice"
                                                v-abc-title.ellipsis="`${paddingMoney(row.useUnitCostPrice)}/${row.useUnit}`"
                                            ></span>
                                            <span
                                                v-else
                                                v-abc-title.ellipsis="`${paddingMoney(row.packageCostPrice)}/${row.goods.packageUnit || row.goods.pieceUnit}`"
                                            ></span>
                                        </template>
                                    </abc-table-cell>
                                </template>

                                <!--金额-->
                                <template #amount="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span v-abc-title.ellipsis="formatMoney(goodsTotalCostPrice(row))"></span>
                                    </abc-table-cell>
                                </template>

                                <!--追溯码-->
                                <template
                                    #traceableCode="{
                                        trData: item,
                                    }"
                                >
                                    <traceable-code-cell
                                        v-model="item.partReturnTraceCodeList"
                                        :goods="item.goods"
                                        :goods-count="getUnitCount(item, item.goods, '出库数量')"
                                        :need-validate="validateCell"
                                        :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                                        :readonly="true"
                                        :is-return="true"
                                    ></traceable-code-cell>
                                </template>

                                <template #footer>
                                    <abc-flex
                                        align="center"
                                        flex="1"
                                        justify="flex-end"
                                        style="padding: 0 12px;"
                                    >
                                        <abc-space v-if="list.length" :size="4">
                                            <abc-text theme="gray">
                                                品种
                                            </abc-text>
                                            <abc-text theme="black">
                                                {{ outOrder?.orderNo ? outOrder.kindCount : kindCount }}
                                            </abc-text>

                                            <abc-text theme="gray">
                                                ，数量
                                            </abc-text>
                                            <abc-text theme="black">
                                                {{ outOrder?.orderNo ? moneyDigit(outOrder.count, 4, false) : moneyDigit(totalCount, 4, false) }}
                                            </abc-text>

                                            <abc-text theme="gray">
                                                ，金额
                                            </abc-text>
                                            <abc-text v-if="outOrder?.orderNo" theme="black">
                                                {{ outOrder.amount | formatMoney }}
                                            </abc-text>
                                            <abc-text v-else theme="black">
                                                {{ amount | formatMoney }}
                                            </abc-text>
                                        </abc-space>
                                    </abc-flex>
                                </template>
                            </abc-table>
                        </abc-layout-content>
                    </abc-layout>
                </abc-form>

                <div slot="footer" class="dialog-footer">
                    <logs-v3-popover v-if="isUpdate && logs?.length" :logs="logs" style="margin-right: auto;"></logs-v3-popover>

                    <abc-button v-if="canRevoke" type="danger" @click="revokeHandler">
                        撤回
                    </abc-button>
                    <template v-if="isChainAdmin && orderStatus === GOODS_IN_STATUS.REVIEW">
                        <abc-button @click="reviewOrderHandle('pass')">
                            通过
                        </abc-button>
                        <abc-button type="danger" @click="reviewOrderHandle('fail')">
                            驳回
                        </abc-button>
                    </template>
                    <template v-if="isAdd">
                        <abc-button type="blank" @click="cancelOutOrder">
                            上一步
                        </abc-button>
                        <abc-button
                            type="primary"
                            :loading="buttonLoading"
                            :disabled="!list.length"
                            @click="createOutOrder"
                        >
                            {{ submitBtnText }}
                        </abc-button>
                        <abc-button type="blank" @click="showDialog = false">
                            取消
                        </abc-button>
                    </template>
                    <abc-button v-else type="blank" @click="showDialog = false">
                        关闭
                    </abc-button>
                </div>
            </template>

            <!--没有退货药品，需要先选药品-->
            <template v-else>
                <abc-form
                    ref="selectForm"
                    v-abc-loading="loading"
                    item-no-margin
                    is-excel
                    style="height: 100%;"
                >
                    <abc-layout preset="dialog-table">
                        <abc-layout-header>
                            <abc-descriptions
                                :column="3"
                                :label-width="90"
                                background
                                grid
                                size="large"
                                stretch-last-item
                            >
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="入库单号"
                                >
                                    <span>{{ order.orderNo }}</span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="创建日期"
                                >
                                    <span>{{ parseTime(order.createdDate,'y-m-d h:i:s',true) }}</span>
                                </abc-descriptions-item>

                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="供应商"
                                >
                                    <span>{{ order.supplier }}</span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="入库人"
                                >
                                    <span>{{ order.createdUser?.name ?? '' }}</span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="入库门店"
                                >
                                    <span>{{ clinicName(order.toOrgan) }}</span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    content-padding="0px"
                                    content-class-name="ellipsis"
                                    label="备注"
                                >
                                    <abc-form-item>
                                        <abc-input
                                            v-model="comment"
                                            :max-length="200"
                                        ></abc-input>
                                    </abc-form-item>
                                </abc-descriptions-item>
                            </abc-descriptions>
                        </abc-layout-header>
                        <abc-layout-content>
                            <abc-table
                                key="selectTable"
                                type="excel"
                                :render-config="selectTableConfig"
                                :data-list="order.list"
                                empty-size="small"
                                :scroll-load-config="scrollLoadConfig"
                                :show-hover-tr-bg="true"
                                cell-size="large"
                                :tr-clickable="true"
                                :disabled-item-func="disabledItemFunc"
                                :show-all-checkbox="order.list.length > 1"
                                :custom-all-checked-func="customAllCheckedFunc"
                                @changeChecked="checkOne"
                                @changeAllChecked="checkAll"
                            >
                                <template #topHeader>
                                    <goods-auto-complete-cover-title
                                        ref="return-goods-auto-complete-search"
                                        class="return-goods-auto-complete-search back-focus-to-autocomplete"
                                        placeholder="扫码/商品名称/首字母"
                                        :search.sync="returnGoodsSearchKey"
                                        :focus-show="true"
                                        size="medium"
                                        :width="480"
                                        enable-local-search
                                        :custom-medicine-search="searchInReturnGoods"
                                        @selectGoods="selectReturnGoods"
                                        @input="handleReturnGoodsInput"
                                    >
                                        <abc-icon slot="prepend" icon="n-search-line-medium" color="var(--abc-color-T3)"></abc-icon>
                                        <div slot="append" class="search-icon" @click="clearReturnGoodsSearch">
                                            <abc-icon v-if="returnGoodsSearchKey" icon="cross_small"></abc-icon>
                                        </div>
                                    </goods-auto-complete-cover-title>
                                </template>

                                <template
                                    #shortId="{
                                        trData: row
                                    }"
                                >
                                    <abc-table-cell>
                                        <overflow-tooltip :content="row.goods.shortId">
                                        </overflow-tooltip>
                                    </abc-table-cell>
                                </template>
                                <!--药品名称-->
                                <template #cadn="{ trData: row }">
                                    <display-name-cell
                                        :goods="row.goods"
                                        :hover-config="{
                                            openDelay: 500,
                                            showPrice: true,
                                            showShebaoCode: true,
                                            pharmacyNo: order.pharmacy ? order.pharmacy.no : pharmacyNo
                                        }"
                                    ></display-name-cell>
                                </template>

                                <!--批号-->
                                <template #batchNo="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span class="ellipsis" :title="row.batchNo">{{ row.batchNo }}</span>
                                    </abc-table-cell>
                                </template>

                                <!--效期-->
                                <template #expiryDate="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span class="ellipsis" :title="row.expiryDate">{{ row.expiryDate }}</span>
                                    </abc-table-cell>
                                </template>

                                <!--入库数量-->
                                <template #count="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span class="ellipsis" :title="row | complexCount">{{ row | complexCount }}</span>
                                    </abc-table-cell>
                                </template>
                                <!--可退数量-->
                                <template #returnCount="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <abc-popover
                                            placement="bottom-end"
                                            trigger="hover"
                                            theme="yellow"
                                            :popper-style="{
                                                padding: 0
                                            }"
                                            :disabled="!row.batchReturnInfo"
                                        >
                                            <span slot="reference" class="ellipsis" :title="formatReturnLeft(row)">
                                                {{ formatReturnLeft(row) }}
                                            </span>

                                            <abc-flex
                                                v-if="row.batchReturnInfo"
                                                vertical
                                                :gap="8"
                                                style="padding: 16px;"
                                            >
                                                <abc-flex :gap="16" align="center" justify="space-between">
                                                    <abc-text theme="gray">
                                                        可退数量
                                                    </abc-text>
                                                    <abc-flex align="center" :gap="4">
                                                        <abc-text bold>
                                                            {{ row.batchReturnInfo.canReturnCount }}
                                                        </abc-text>
                                                        <abc-text v-if="row.batchReturnInfo.returnYetCount" theme="gray" size="small">
                                                            (已退{{ row.batchReturnInfo.returnYetCount }})
                                                        </abc-text>
                                                    </abc-flex>
                                                </abc-flex>

                                                <abc-flex v-if="row.batchReturnInfo.batchCount" justify="space-between">
                                                    <abc-text theme="gray">
                                                        当前库存
                                                    </abc-text>
                                                    <abc-text>
                                                        {{ row.batchReturnInfo.batchCount }}
                                                    </abc-text>
                                                </abc-flex>

                                                <abc-flex v-if="row.batchReturnInfo.lockingCount" justify="space-between">
                                                    <abc-text theme="gray">
                                                        锁定数量
                                                    </abc-text>
                                                    <abc-text>
                                                        {{ row.batchReturnInfo.lockingCount }}
                                                    </abc-text>
                                                </abc-flex>
                                            </abc-flex>

                                            <abc-flex
                                                vertical
                                                style="max-width: 200px; padding: 10px 8px; border-top: 1px solid var(--abc-color-P8);"
                                            >
                                                <abc-text theme="gray" size="mini">
                                                    可退数量=当前库存-锁定数量，但退货数量总数不超过入库数量
                                                </abc-text>
                                            </abc-flex>
                                        </abc-popover>
                                    </abc-table-cell>
                                </template>
                                <!--退货数量-->
                                <template #realReturnCount="{ trData: row }">
                                    <abc-form-item
                                        v-if="row.checked"
                                        :required="!row.returnPackageCount && !row.returnPieceCount"
                                        :validate-event="validateStock(row)"
                                        style="display: flex; margin: 0;"
                                    >
                                        <abc-flex style="height: 100%;" @click.stop>
                                            <template v-if="!isChineseMedicine(row.goods)">
                                                <abc-input
                                                    v-model.number="row.returnPackageCount"
                                                    v-abc-focus-selected
                                                    :width="74"
                                                    class="count-input"
                                                    type="number"
                                                    :config="getConfig(row.goods)"
                                                    @enter="enterEvent"
                                                    @change="handleChangeUnitCount(row, row.goods)"
                                                >
                                                    <div slot="appendInner">
                                                        {{ row.goods.packageUnit }}
                                                    </div>
                                                </abc-input>
                                            </template>
                                            <template
                                                v-if="!unitEqual(row.goods) || isChineseMedicine(row.goods)"
                                            >
                                                <abc-input
                                                    v-model.number="row.returnPieceCount"
                                                    v-abc-focus-selected
                                                    :width="74"
                                                    type="number"
                                                    class="count-input"
                                                    :config="getConfig(row.goods)"
                                                    @enter="enterEvent"
                                                    @change="handleChangeUnitCount(row, row.goods)"
                                                >
                                                    <div slot="appendInner">
                                                        {{ row.goods.pieceUnit }}
                                                    </div>
                                                </abc-input>
                                            </template>
                                        </abc-flex>
                                    </abc-form-item>
                                </template>

                                <!--进价-->
                                <template #costPrice="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span
                                            v-if="row.inItem"
                                            v-abc-title.ellipsis="`${paddingMoney(row.inItem.useUnitCostPrice)}/${row.inItem.useUnit}`"
                                        >
                                        </span>
                                        <span
                                            v-else
                                            v-abc-title.ellipsis="`${paddingMoney(row.useUnitCostPrice)}/${row.useUnit}`"
                                        >
                                        </span>
                                    </abc-table-cell>
                                </template>

                                <!--金额-->
                                <template #amount="{ trData: row }">
                                    <abc-table-cell class="ellipsis">
                                        <span v-abc-title.ellipsis="paddingMoney(row.useTotalCostPrice)"></span>
                                    </abc-table-cell>
                                </template>

                                <!--追溯码-->
                                <template
                                    #traceableCode="{
                                        trData: item,
                                    }"
                                >
                                    <abc-flex v-if="item.checked" style="height: 100%;" @click.stop>
                                        <traceable-code-cell
                                            v-model="item.partReturnTraceCodeList"
                                            :item="item"
                                            :goods="item.goods"
                                            :goods-count="getUnitCount({
                                                packageCount: item.returnPackageCount,
                                                pieceCount: item.returnPieceCount
                                            }, item.goods, '退货数量')"
                                            :need-validate="validateCell"
                                            :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                                            :readonly="!isPartReturn(item)"
                                            :is-return="true"
                                            @input="syncTraceableCodeToSelectedList(item, $event)"
                                        ></traceable-code-cell>
                                    </abc-flex>
                                </template>

                                <template #footer>
                                    <abc-space :size="4" style="margin-right: 12px; margin-left: auto;">
                                        <abc-text theme="gray">
                                            品种
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ returnGoodsFooterKindCount }}
                                        </abc-text>

                                        <abc-text theme="gray">
                                            ，数量
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ returnGoodsFooterTotalCount }}
                                        </abc-text>

                                        <abc-text theme="gray">
                                            ，金额
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ returnGoodsFooterAmount | formatMoney(false) }}
                                        </abc-text>
                                    </abc-space>
                                </template>
                            </abc-table>
                        </abc-layout-content>
                    </abc-layout>
                </abc-form>

                <div slot="footer" class="dialog-footer">
                    <abc-button v-if="!quickReturnOrderId" type="blank" @click="backGetInOrder">
                        上一步
                    </abc-button>
                    <abc-button
                        type="primary"
                        :disabled="disabledNextInitOrder"
                        @click="initOutOrder"
                    >
                        下一步
                    </abc-button>
                    <abc-button v-if="isAdd" type="blank" @click="showDialog = false">
                        取消
                    </abc-button>
                </div>
            </template>
        </template>

        <abc-dialog
            v-if="showReviewDialog"
            v-model="showReviewDialog"
            title="审核"
            custom-class="goods-confirm"
            content-styles="width: 360px;"
        >
            <div class="dialog-content clearfix">
                <p v-if="reviewData.pass" class="confirm-result">
                    审核结果：<span class="confirm-success">通过</span>
                </p>
                <p v-else class="confirm-result">
                    审核结果：<span class="confirm-refuse">驳回</span>
                </p>

                <p v-if="reviewData.pass" class="confirm-result">
                    出库单审核通过后，将会改变{{ order.toOrgan && order.toOrgan.name }}库存
                </p>

                <template v-else>
                    <abc-form
                        ref="checkForm"
                        label-position="left"
                        :label-width="100"
                    >
                        <abc-form-item required style="margin-bottom: 0;" :validate-event="validateComment">
                            <abc-edit-div
                                v-model="reviewData.comment"
                                :maxlength="100"
                                responsive
                                placeholder="请输入驳回原因"
                                style="height: 50px;"
                            ></abc-edit-div>
                        </abc-form-item>
                    </abc-form>
                    <p class="confirm-result" style="margin-top: 16px;">
                        门店可在驳回单据内修改并重新发起
                    </p>
                </template>
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button
                    style="margin-left: auto;"
                    :loading="reviewBtnLoading"
                    @click="reviewHandle"
                >
                    确定
                </abc-button>
                <abc-button type="blank" @click="showReviewDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <return-dialog
            v-if="showGoodsInitReturnDialog"
            v-model="showGoodsInitReturnDialog"
            :type="type"
            @refresh="handleRefresh"
            @close="handleCancel"
        ></return-dialog>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import Big from 'big.js';
    import StockOutAPI from 'api/goods/stock-out.js';
    import StockInAPI from 'api/goods/stock-in.js';

    import {
        getSafeNumber,
        moneyDigit,
        paddingMoney, parseTime,
    } from '@/utils';
    import { formatDate } from '@abc/utils-date';
    import clone from 'utils/clone';

    import ClinicSelect from 'views/layout/clinic-select/clinic-select';

    import pickerOptions from 'views/common/pickerOptions';
    import dialogAutoWidth from 'views/inventory/mixins/dialog-auto-width.js';
    import GoodsTableV3Mixins from 'views/inventory/mixins/goods-table-v3.js';

    import {
        _complexCount, clinicName, isChineseMedicine,
    } from 'src/filters/goods';
    import DeleteGoodsHandler from '../mixins/delete-goods-handler';
    import {
        formatGoodsNameSpec, validateComment,
    } from './common';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    const ReturnDialog = () => import('@/views-pharmacy/inventory/frames/purchase/return-goods/orderDialog.vue');

    const GoodsAutoCompleteCoverTitle = () => import('views/inventory/common/goods-auto-complete-cover-title.vue');
    import {
        CHECK_IN_SUPPLIER_ID, GOODS_IN_ORDER_TYPE, GOODS_IN_STATUS,
    } from 'views/inventory/constant.js';
    import EnterEvent from 'views/common/enter-event';
    import { isEmptyCount } from '../goods-check/common';
    import { unitEqual } from 'views/inventory/goods-utils';
    import { GoodsTypeEnum } from '@abc/constants';
    import { formatMoney } from '@/filters';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import OverflowTooltip from '@/components/overflow-tooltip.vue';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    import { TagHelper } from 'utils/tag-helper';
    import TraceCode, { TraceCodeScenesEnum } from '@/service/trace-code/service';
    import useReviseOrder from 'views/inventory/hooks/useReviseOrder';

    const { orderMainNameText } = getViewDistributeConfig().Inventory;

    function uniqueCount(list) {
        let count = 0;
        const Obj = Object.create(null);

        list.forEach((item) => {
            if (!Obj[item.goods.id]) {
                count++;
                Obj[item.goods.id] = 1;
            }
        });
        return count;
    }

    export default {
        name: 'GoodsReturn',

        components: {
            LogsV3Popover,
            DisplayNameCell,
            GoodsAutoCompleteCoverTitle,
            ClinicSelect,
            OverflowTooltip,
            ReturnDialog,
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
        },

        mixins: [EnterEvent, pickerOptions, DeleteGoodsHandler, dialogAutoWidth, GoodsTableV3Mixins],

        props: {
            visible: Boolean,
            isResubmit: Boolean,
            onlyApplication: Boolean,
            // 待审核、待确定并且是总部/或者是重新发起会传
            orderId: [String, Number],
            quickReturnOrderId: [String, Number],
            pharmacyNo: Number,
            pharmacyName: String,
        },
        setup() {
            const {
                currentSupplierList,
                fetchSuggestions,
            } = useSearchSupplier({
                pharmacyType: 0,
            });

            const {
                isReviseCount,
                hasRevise,
                getReviseOrderList,
            } = useReviseOrder();

            return {
                currentSupplierList,
                fetchSuggestions,

                isReviseCount,
                hasRevise,
                getReviseOrderList,
            };
        },
        data() {
            const endDate = new Date();
            const endDay = endDate.getDate(); // 当前日
            const endMonth = endDate.getMonth(); // 当前月
            const endYear = endDate.getFullYear(); // 当前年

            const startDate = new Date(endYear, endMonth, endDay - 29);

            return {
                CHECK_IN_SUPPLIER_ID,
                GOODS_IN_STATUS,
                orderMainNameText,
                loading: true,
                buttonLoading: false,
                searchKey: '',
                dateRange: [formatDate(startDate), formatDate(endDate)],
                fetchParams: {
                    clinicId: '',
                    offset: 0,
                    limit: 10,
                    begDate: formatDate(startDate),
                    endDate: formatDate(endDate),
                    goodsId: '',
                    listCount: 10,
                    supplierId: '',
                    withReturnLeft: 1,
                    pharmacyNo: this.pharmacyNo,
                },
                // confirmTablePageParams: {
                //     offset: 0,
                //     limit: 100,
                //     totalCount: 0,
                // },

                inOrders: [],
                orderCount: 0,
                order: null,

                type: GOODS_IN_ORDER_TYPE.RETURN_OUT,// 创建type10的入库单
                fromOrganId: '', // 出库到的科室（领用科室）
                comment: '',
                list: [],
                showCreateOrder: false,
                orderInId: '',
                applyClinicId: '',
                orderStatus: '',
                currentSuppliers: [],
                logs: [],
                outOrder: null,
                showReviewDialog: false,
                reviewData: {
                    list: [],
                    pass: false,
                    lastModifiedDate: '',
                    comment: '',
                },
                reviewBtnLoading: false,
                validateCell: false,
                showGoodsInitReturnDialog: false,


                returnGoodsSearchKey: '', // 选择退货商品支持商品搜索
                returnGoodsFetchParams: {
                    offset: 0,
                    limit: 500,
                },
                returnGoodsPageParams: {
                    showTotalPage: true,
                    pageIndex: 0,
                    pageSize: 500,
                    count: 0,
                },
                returnGoodsFooterParams: {
                    kindCount: 0,
                    totalCount: 0,
                    amount: 0,
                },
                returnGoodsSelectedList: [], // 保存已选中的退货商品列表
                isSearchMode: false, // 是否处于搜索模式
            };
        },
        computed: {
            ...mapGetters([
                'subClinics',
                'currentClinic',
                'userInfo',
                'clinicConfig',
                'isChainAdmin',
                'goodsConfig',
                'multiPharmacyCanUse',
                'traceCodeConfig',
                'isStrictCountWithTraceCodeCollect',
            ]),
            showDialog: {
                get() {
                    return this.visible;
                },
                set(v) {
                    this.$emit('close');
                    this.$emit('update:visible', v);
                },
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            currentSubClinicsArray() {
                return this.subClinics;
            },
            isUpdate() {
                return this.orderId && !this.isResubmit;
            },
            isAdd() {
                return !this.isUpdate;
            },
            dialogTitle() {
                const obj = {
                    title: '退货出库单',
                    orderNo: this.outOrder?.orderNo || '',
                    hasTag: false,
                    variant: '',
                    tagName: '',
                    tagTheme: '',
                };

                let text = '退货出库单';
                if (this.onlyApplication) {
                    text = '退货申请单（采购召回专用）';
                }

                if (!this.order) {
                    obj.title = '选择入库单';
                }
                if (!this.list.length) {
                    obj.title = `选择需要退货的${orderMainNameText}`;
                }
                if (this.outOrder && this.outOrder.orderNo) {
                    if (this.orderStatus === GOODS_IN_STATUS.REVIEW) {
                        obj.title = text;
                        obj.hasTag = true;
                        obj.tagName = this.isChainAdmin ? '待审核' : '待总部审核';
                        obj.tagTheme = this.isChainAdmin ? TagHelper.TODO_TAG.theme : TagHelper.ING_TAG.theme;
                        obj.variant = this.isChainAdmin ? TagHelper.TODO_TAG.variant : TagHelper.ING_TAG.variant;
                    }
                    if (this.orderStatus === GOODS_IN_STATUS.CONFIRM) {
                        obj.title = text;
                        obj.hasTag = true;
                        obj.tagName = `待${this.outOrder.toOrgan.shortName || this.outOrder.toOrgan.name}确认`;
                        obj.tagTheme = TagHelper.ING_TAG.theme;
                        obj.variant = TagHelper.ING_TAG.variant;
                    }
                    return obj;
                }

                if (this.showCreateOrder) {
                    obj.title = this.onlyApplication ? '确认退货申请的药品' : '确认退货出库的药品';
                } else {
                    obj.title = text;
                }

                return obj;
            },
            toOrganName() {
                if (!this.currentClinic) return '';
                if (this.isChainAdmin) {
                    return '总部';
                }
                return this.currentClinic.clinicName;

            },
            kindCount() {
                if (!this.list.length) return '';
                return uniqueCount(this.list);
            },
            totalCount() {
                let count = Big(0);
                this.list.forEach((item) => {
                    const pieceCount = Big(getSafeNumber(item.pieceCount)).div(item.pieceNum || 1).toFixed(4);
                    count = Big(count).plus(getSafeNumber(item.packageCount)).plus(pieceCount);
                });
                return count.toNumber();
            },
            amount() {
                let total = Big(0);
                this.list.forEach((item) => {
                    total = total.plus(getSafeNumber(this.goodsTotalCostPrice(item)));
                });
                return total.toNumber();
            },
            disabledNextInitOrder() {
                return this.returnGoodsSelectedList.length === 0;
            },
            stockOutChainReview() {
                return this.goodsConfig?.chainReview?.stockInChainReview;
            },
            disabledCheckAll() {
                // !以前的逻辑是，所有药品都可退，全选按钮才启用
                // ?现在的逻辑是，所有药品都不可退，全选按钮才禁用
                return this.order?.list?.every((item) => !item.returnLeft);
            },
            pageParams() {
                const {
                    limit: pageSize,
                    offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
            // 退货选择商品-滚动加载配置
            scrollLoadConfig() {
                // 搜索模式下不启用滚动加载
                if (this.isSearchMode) {
                    return null;
                }
                return {
                    fetchData: this.getInboundOrderList,
                    total: this.returnGoodsPageParams.count,
                };
            },
            returnGoodsFooterKindCount() {
                if (!this.returnGoodsSelectedList.length) return 0;
                return uniqueCount(this.returnGoodsSelectedList);
            },
            returnGoodsFooterTotalCount() {
                let count = Big(0);
                this.returnGoodsSelectedList.forEach((item) => {
                    const pieceCount = Big(item.returnPieceCount || 0).div(item.pieceNum || 1).toFixed(4);
                    count = count.plus(Big(item.returnPackageCount || 0)).plus(Big(pieceCount));
                });
                // 如果是整数则展示整数，小数则保留两位
                const result = count.toString();
                if (result.indexOf('.') === -1 || result.endsWith('.00')) {
                    return parseInt(result, 10);
                }
                return count.toFixed(2);
            },
            returnGoodsFooterAmount() {
                let total = 0;
                this.returnGoodsSelectedList.forEach((item) => {
                    const tempItem = { ...item };
                    tempItem.packageCount = tempItem.returnPackageCount || '';
                    tempItem.pieceCount = tempItem.returnPieceCount || '';
                    total += +this.goodsTotalCostPrice(tempItem) || 0;
                });
                return total;
            },
            // confirmScrollLoadConfig() {
            //     // 待审核、待确定并且是总部才滚动加载
            //     if (!this.orderId) {
            //         return null;
            //     }
            //     return {
            //         fetchData: this.fetchData,
            //         total: this.confirmTablePageParams.totalCount,
            //     };
            // },
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },
            // 是否为单据发起方，需要后端记录applyClinicId
            isApplyClinic() {
                return this.clinicId === this.applyClinicId;
            },
            // 发起方可以撤回单据
            canRevoke() {
                // 新判断逻辑
                // if (this.orderStatus === GOODS_IN_STATUS.REVIEW || this.orderStatus === GOODS_IN_STATUS.CONFIRM) {
                //     return this.isApplyClinic;
                // }
                // 发起方是总部
                if (this.orderStatus === GOODS_IN_STATUS.CONFIRM) {
                    return this.isChainAdmin;
                }
                // 发起方是门店
                if (this.orderStatus === GOODS_IN_STATUS.REVIEW) {
                    return !this.isChainAdmin;
                }

                return false;
            },
            submitBtnText() {
                let text = '确定出库';
                if (this.onlyApplication) {
                    text = '确定';
                }
                if (this.isChainAdmin) {
                    // 出库门店用的是入库单的入库门店
                    return this.clinicId !== this.order?.toOrganId ? '发送门店确认' : text;
                }
                // 门店出库如果开启审核需要总部审核
                return this.stockOutChainReview ? '提交出库审核' : text;
            },
            columns() {
                return [
                    { prop: 'dateRange' },
                    { prop: 'supplier' },
                    { prop: 'clinic' },
                    { prop: 'goodsName' },
                ].filter((item) => {
                    if (item.prop === 'clinic') {
                        return this.isChainAdmin;
                    }
                    return true;
                });
            },
            selectTableColumns() {
                const {
                    orderNo, createdDate, createdUser, toOrgan, supplier,
                } = this.order || {};
                return [
                    {
                        prop: 'orderNo', label: '入库单号：', value: orderNo,
                    },
                    {
                        prop: 'createdDate', label: '创建日期：', value: parseTime(createdDate,'y-m-d h:i:s',true),
                    },
                    {
                        prop: 'supplier',
                        label: '供应商：',
                        value: supplier,
                        style: {
                            maxWidth: '210px',
                        },
                    },
                    {
                        prop: 'user',
                        label: '入库人：',
                        value: createdUser?.name ?? '',
                        style: {
                            maxWidth: '120px',
                        },
                    },
                    {
                        prop: 'clinicName',
                        label: '入库门店：',
                        value: clinicName(toOrgan),
                        style: {
                            maxWidth: '210px',
                        },
                    },
                    {
                        prop: 'comment',
                        label: '备注：',
                        value: '',
                        style: {
                            maxWidth: '210px',
                        },
                    },
                ];
            },
            createTableColumns() {
                const {
                    toOrgan, supplier,
                } = this.order || {};
                return [
                    {
                        prop: 'user',
                        label: '出库人：',
                        value: this.userInfo?.name,
                        style: {
                            maxWidth: '120px',
                        },
                    },
                    {
                        prop: 'clinicName',
                        label: '出库门店：',
                        value: clinicName(toOrgan),
                        style: {
                            maxWidth: '210px',
                        },
                    },
                    {
                        prop: 'supplier',
                        label: '供应商：',
                        value: supplier,
                    },
                    {
                        prop: 'comment',
                        label: '备注：',
                        value: this.comment || '-',
                        style: {
                            maxWidth: '210px',
                        },
                    },
                ];
            },
            renderConfig() {
                return {
                    list: [
                        {
                            label: '入库单列表',
                            key: 'order-info',
                            style: {
                                'flex': '1',
                            },
                        },
                    ],
                };
            },
            selectTableConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: ' ',
                            isCheckbox: true,
                            pinned: true,
                            style: {
                                flex: 'none',
                                width: '40px',
                                maxWidth: '',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            label: `${orderMainNameText}编码`,
                            key: 'shortId',
                            width: 120,
                            style: {
                                width: '120px',
                            },
                            tdClass: 'is-disabled',
                        },
                        {
                            label: `${orderMainNameText}名称`,
                            key: 'cadn',
                            flex: 1,
                            style: {
                                width: '180px',
                            },
                            tdClass: 'is-disabled',
                        },
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            width: 110,
                            style: {
                                width: '110px',
                            },
                            tdClass: 'is-disabled',

                        },
                        {
                            label: '效期',
                            key: 'expiryDate',
                            width: 100,
                            style: {
                                width: '100px',
                            },
                            tdClass: 'is-disabled',

                        },
                        {
                            label: '入库数量',
                            key: 'count',
                            width: 100,
                            style: {
                                width: '100px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',

                        },
                        {
                            label: '进价',
                            key: 'costPrice',
                            width: 100,
                            style: {
                                width: '100px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',

                        },
                        {
                            label: '金额',
                            key: 'amount',
                            width: 100,
                            style: {
                                width: '100px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',

                        },
                        {
                            label: '可退数量',
                            key: 'returnCount',
                            width: 100,
                            style: {
                                width: '100px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',

                        },
                        {
                            label: '退货数量',
                            key: 'realReturnCount',
                            width: 148,
                            style: {
                                width: '148px',
                                maxWidth: '148px',
                            },
                        },
                        {
                            label: '追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
            outTableConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: `${orderMainNameText}编码`,
                            key: 'shortId',
                            width: 120,
                            tdClass: 'is-disabled',
                            style: {
                                width: '120px',
                            },
                        },
                        {
                            label: `${orderMainNameText}名称`,
                            key: 'cadn',
                            flex: 1,
                            tdClass: 'is-disabled',
                            style: {
                                width: '180px',
                            },
                        },
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            width: 120,
                            tdClass: 'is-disabled',
                            style: {
                                width: '120px',
                            },
                        },
                        {
                            label: '效期',
                            key: 'expiryDate',
                            width: 120,
                            tdClass: 'is-disabled',
                            style: {
                                width: '120px',
                            },
                        },
                        {
                            label: '进价',
                            key: 'costPrice',
                            width: 120,
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',
                            style: {
                                width: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '出库数量',
                            key: 'count',
                            width: 120,
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',
                            style: {
                                width: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '金额',
                            key: 'amount',
                            width: 120,
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',
                            style: {
                                width: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
        },
        watch: {
            order() {
                this.updateDialogHeight();
            },
        },
        async created() {
            if (this.onlyApplication) {
                this.type = GOODS_IN_ORDER_TYPE.RETURN_APPLICATION_OUT;
            }

            if (this.quickReturnOrderId) {
                this.selectInOrder(this.quickReturnOrderId);
            } else {
                if (this.orderId) {
                    this.fetchData();
                }
                if (!this.orderId) {
                    this.fetchInOrder();
                }
            }
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            clinicName,
            moneyDigit,
            formatMoney,
            isChineseMedicine,
            paddingMoney,
            parseTime,
            validateComment,
            unitEqual,
            isEmptyCount,
            formatGoodsNameSpec,
            updateDialogHeight() {
                console.log('updateDialogHeight');
                this.$nextTick(() => {
                    this.$refs.dialogRef?.updateDialogHeight();
                });
            },
            getConfig(goods) {
                return {
                    formatLength: (this.isChineseMedicine(goods) || goods.type === GoodsTypeEnum.GOODS) ? 2 : 0,
                    max: 10000000,
                    supportZero: false,
                };
            },
            formatReturnLeft(item) {
                if (this.isChineseMedicine(item.goods)) {
                    return `${item.returnLeft}${item.goods.pieceUnit}`;
                }
                if (item.returnLeftPieceCount) {
                    return `${
                        item.returnLeftPackageCount || 0
                    }${
                        item.goods.packageUnit || ''
                    }${
                        item.returnLeftPieceCount
                    }${
                        item.goods.pieceUnit || ''
                    }`;
                }

                return `${item.returnLeft || 0}${item.useUnit || ''}`;
            },
            disabledBtn(list = []) {
                return list.every((item) => !item.returnLeft);
            },
            validateStock({
                returnPackageCount,
                returnPieceCount,
                pieceNum,
                returnLeftPackageCount,
                returnLeftPieceCount,
            }) {
                const flag = +returnPieceCount + returnPackageCount * pieceNum > +returnLeftPieceCount + returnLeftPackageCount * pieceNum;
                if (flag) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '库存不足',
                        });
                    };
                }
                return (_, callback) => {
                    callback({ validate: true });
                };

            },
            reviewHandle() {
                if (!this.reviewData.pass) {
                    this.$refs.checkForm.validate((val) => {
                        if (val) {
                            this.reviewOrder();
                        }
                    });
                } else {
                    this.reviewOrder();
                }
            },
            goodsTotalCostPrice(item) {
                try {
                    // 大单位成本价格
                    const packageCostPrice = item.inItem ? item.inItem.useUnitCostPrice : item.packageCostPrice;

                    // 小单位转换为大单位
                    const packageCount = Big(item.packageCount || 0).plus(Big(item.pieceCount || 0).div(item.pieceNum || 1));
                    // 计算总价
                    const price = (Big(packageCostPrice).times(packageCount)).round(2);

                    return price || '';
                } catch (e) {
                    console.error(e);
                    return '';
                }

            },
            /**
             * @desc
             * <AUTHOR>
             * @date 2018/11/19 17:36:41
             * @params picker array
             */
            changeDate(picker) {
                if (picker.length === 2) {
                    this.fetchParams.begDate = picker[0];
                    this.fetchParams.endDate = picker[1];
                } else {
                    this.fetchParams.begDate = '';
                    this.fetchParams.endDate = '';
                }
                this.fetchParams.offset = 0;
                this.fetchInOrder();
            },
            changeSupplier() {
                this.fetchParams.offset = 0;
                this.fetchInOrder();
            },

            /**
             * @desc 选择medicine
             * <AUTHOR>
             * @date 2018/11/19 17:51:52
             */
            async selectGoods(goods) {
                this.fetchParams.goodsId = goods.id;
                this.searchKey = goods.medicineCadn + (goods.name ? `（${goods.name}）` : '');
                this.fetchParams.offset = 0;
                this.fetchInOrder();
            },
            changeClinic() {
                this.fetchParams.offset = 0;
                this.fetchInOrder();
            },

            /**
             * @desc 清除药品搜索条件
             * <AUTHOR>
             * @date 2018/11/19 17:56:43
             */
            clearSearch() {
                this.searchKey = '';
                this.fetchParams.goodsId = '';
                this.fetchInOrder();
            },

            /**
             * @desc 转化 药品列表
             * <AUTHOR>
             * @date 2018/11/19 19:29:49
             */
            transList(list) {
                return list
                    .map((item) => {
                        return `${item.goods.medicineCadn || item.goods.name || ''} ${_complexCount(item) || ''}`;
                    })
                    .join('、');
            },
            formatGoodsCount(item) {
                return _complexCount(item) || '';
            },
            // 获取退货出库单详情-待审核、待确定并且是总部会传orderId拉取数据
            async fetchData() {
                try {
                    const order = await StockInAPI.getById(this.orderId);// orderId是退货出库单id
                    if (this.isResubmit && order.stockInOrder && order.stockInOrder.stockInOrderId) {
                        await this.selectInOrder(order.stockInOrder.stockInOrderId, order);
                    } else {
                        this.order = order;
                    }
                    this.list = clone(this.order?.list ?? []);
                    // 如果是重新出库需要回到第二步。
                    this.showCreateOrder = !this.isResubmit;
                    this.orderStatus = order.status;
                    this.applyClinicId = order.applyClinicId;
                    this.logs = order.logs;
                    this.outOrder = order;
                    if (order.logs?.length) {
                        this.comment = order.logs?.[order.logs.length - 1]?.comment ?? '';
                    } else {
                        this.comment = '';
                    }
                    this.loading = false;

                    this.reviewData.lastModifiedDate = order.lastModifiedDate;
                } catch (e) {
                    console.error(e);
                }

            },
            /**
             * @desc 根据 时间/门店/药品 获取入库单
             * <AUTHOR>
             * @date 2018/11/19 17:21:16
             */
            async fetchInOrder() {
                this.loading = true;

                if (!this.isChainAdmin) {
                    this.fetchParams.clinicId = this.currentClinic.clinicId;
                } else {
                    this.fetchParams.pharmacyNo = '';
                }

                try {
                    const { data } = await StockOutAPI.fetchInOrder(this.fetchParams);
                    // const { data: initGoodsData } = await StockOutAPI.fetchInOrderWithImport(this.fetchParams);

                    const {
                        count,
                        rows,
                    } = data;
                    // 汇总成一条数据
                    // if (initGoodsData?.rows?.length) {
                    //     rows.unshift({
                    //         sourceType: 'goodsInit',
                    //         orderNo: initGoodsData.orderNo,
                    //         list: initGoodsData.rows.reduce((res, item) => {
                    //             return res.concat(item.list);
                    //         }, []),
                    //     });
                    //     this.orderCount = count + 1;
                    // } else {
                    //     this.orderCount = count;
                    // }
                    this.orderCount = count;
                    this.inOrders = rows;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            /**
             * @desc 分页改变页码
             * <AUTHOR>
             * @date 2018/11/20 20:05:32
             */
            changePageIndex(index) {
                this.fetchParams.offset = (index - 1) * this.fetchParams.limit;
                this.fetchInOrder();
            },
            handleSelect(item) {
                console.log(item);
                // 打开初始化入库单退货
                this.showGoodsInitReturnDialog = true;
            },
            /**
             * @desc 选择 入库单
             * <AUTHOR>
             * @date 2018/11/19 22:39:03
             * @params id order.id
             * @params order 重新入库获取详情会传入,为了得到上次提交填写的数量
             */
            async selectInOrder(id, order) {
                this.loading = true;
                this.returnGoodsFetchParams.offset = 0;
                const newOrder = await StockInAPI.getOrderById(id, {
                    withStockId: 1,
                    withReturnLeft: 1,
                    offset: this.returnGoodsFetchParams.offset,
                    limit: this.returnGoodsFetchParams.limit,
                    withGoodsId: this.fetchParams.goodsId,
                    withGoodsIdOrderFirst: 1,
                });
                this.orderInId = id;
                const map = new Map();
                const originalListItemMap = new Map();

                if (order?.list?.length) {
                    order.list.forEach((item) => {
                        map.set(item.goodsId, item);
                    });
                }
                newOrder.list?.forEach((item) => {
                    originalListItemMap.set(item.id, item);
                });

                // 修正数量或者进价后，使用修改后的数据退货
                if (this.hasRevise(newOrder.importFlag)) {

                    // 1、拿到所有有修正的数据。
                    const reviseOrderList = this.getReviseOrderList({ list: newOrder.list || [] });
                    console.log('所有处理过的修正单数据', reviseOrderList);

                    // 2、遍历所有有修正的数据，找到当前药品的修正数据，将修正数据赋值给当前药品
                    reviseOrderList.forEach((item) => {
                        const {
                            originalItemId, inId,
                        } = item;
                        const originalListItem = originalListItemMap.get(originalItemId || inId);
                        if (originalListItem) {
                            originalListItem.packageCount = item.packageCount;
                            originalListItem.pieceCount = item.pieceCount;
                            originalListItem.packageCostPrice = item.packageCostPrice;
                            originalListItem.useTotalCostPrice = item.useTotalCostPrice;
                            originalListItem.useUnitCostPrice = item.useUnitCostPrice;
                            originalListItem.useUnit = item.useUnit;
                        }
                    });
                }

                const newList = newOrder.list.map((item) => {
                    // 获取到之前提交过的 item, 重新出库才会用到
                    const _item = map.get(item.goodsId);
                    console.log('_item', _item);
                    // 默认填充可退数量
                    if (isChineseMedicine(item.goods)) {
                        item.returnLeftPieceCount = item.returnLeftPieceCount || _item?.pieceCount || 0;
                        // 中药只有pieceUnit
                        item.returnPieceCount = this.isResubmit ? _item?.pieceCount : item.returnLeftPieceCount;
                        item.returnPackageCount = '';
                    } else {
                        // 兼容处理保证可退数量存在
                        if (!item.returnLeftPieceCount && !item.returnLeftPackageCount) {
                            item.returnLeftPieceCount = _item?.pieceCount || 0;
                            item.returnLeftPackageCount = _item?.packageCount || 0;
                        }

                        item.returnPieceCount = this.isResubmit ? _item?.pieceCount : item.returnLeftPieceCount || '';
                        item.returnPackageCount = this.isResubmit ? _item?.packageCount : item.returnLeftPackageCount || '';
                    }
                    const checked = !!(this.isResubmit && _item) || (this.fetchParams.goodsId === item.goodsId && item.returnLeft);
                    item.checked = checked;
                    if (item.checked) {
                        this.checkOne(item);
                    }
                    return item;
                });

                const cloneList = newList;

                // 如果没有搜索选中商品，那么需要按照可退数量排序
                if (!this.fetchParams.goodsId) {
                    cloneList.sort((a, b) => {
                        return b.returnLeft > a.returnLeft ? 1 : -1;
                    });
                }

                this.order = {
                    ...newOrder,
                    list: (this.order?.list ?? []).concat(cloneList),
                };
                this.cacheOrderList = clone(this.order.list);
                this.returnGoodsPageParams = {
                    showTotalPage: true,
                    pageIndex: 0,
                    pageSize: this.returnGoodsFetchParams.limit,
                    count: this.order.totalCount,
                };
                this.returnGoodsFetchParams.offset += this.returnGoodsFetchParams.limit;
                this.loading = false;
            },
            async searchInReturnGoods(params) {
                // 设置为搜索模式
                this.isSearchMode = true;

                const res = await StockInAPI.getInboundOrderList(this.order.id, {
                    totalCount: this.order.totalCount,
                    offset: 0,
                    limit: 200,
                    keyword: params.key,
                });
                if (res?.data?.rows) {
                    res.data.list = res.data.rows.map((e) => {
                        // 默认填充可退数量
                        if (isChineseMedicine(e.goods)) {
                            e.returnLeftPieceCount = e.returnLeftPieceCount || 0;
                            // 中药只有pieceUnit
                            e.returnPieceCount = e.returnLeftPieceCount;
                            e.returnPackageCount = '';
                        } else {
                            // 兼容处理保证可退数量存在
                            if (!e.returnLeftPieceCount && !e.returnLeftPackageCount) {
                                e.returnLeftPieceCount = 0;
                                e.returnLeftPackageCount = 0;
                            }

                            e.returnPieceCount = e.returnLeftPieceCount || '';
                            e.returnPackageCount = e.returnLeftPackageCount || '';
                        }

                        return {
                            ...e.goods,
                            ...e,
                            name: e.goods?.displayName || e.goods?.name || '',
                            // 入库数量
                            inPackageCount: e.packageCount,
                            inPieceCount: e.pieceCount,
                            // 可退数量
                            packageCount: e.returnLeftPackageCount,
                            pieceCount: e.returnLeftPieceCount,
                        };
                    });
                }
                return res?.data;
            },
            // 滚动加载退货单列表
            async getInboundOrderList(autoChecked = false) {
                if (this.loading) {
                    return;
                }
                this.loading = true;
                try {
                    const params = {
                        keyword: '',
                        withGoodsId: this.fetchParams.goodsId,
                        withGoodsIdOrderFirst: 1,
                        totalCount: this.order.totalCount,
                        offset: this.returnGoodsFetchParams.offset,
                        limit: this.returnGoodsFetchParams.limit,
                    };
                    const res = await StockInAPI.getInboundOrderList(this.order.id, params);
                    const newList = res?.data?.rows?.map((item) => {
                        if (isChineseMedicine(item.goods)) {
                            item.returnLeftPieceCount = item.returnLeftPieceCount || item?.pieceCount || 0;
                            // 中药只有pieceUnit
                            item.returnPieceCount = this.isResubmit ? item?.pieceCount : item.returnLeftPieceCount;
                            item.returnPackageCount = '';
                        } else {
                            // 兼容处理保证可退数量存在
                            if (!item.returnLeftPieceCount && !item.returnLeftPackageCount) {
                                item.returnLeftPieceCount = item?.pieceCount || 0;
                                item.returnLeftPackageCount = item?.packageCount || 0;
                            }

                            item.returnPieceCount = this.isResubmit ? item?.pieceCount : item.returnLeftPieceCount || '';
                            item.returnPackageCount = this.isResubmit ? item?.packageCount : item.returnLeftPackageCount || '';
                        }

                        // 检查是否在已选中列表中
                        const selectedItem = this.returnGoodsSelectedList.find((selected) =>
                            selected.goodsId === item.goodsId && selected.batchId === item.batchId,
                        );

                        let checked = selectedItem ? true : (!!(this.isResubmit && item) || this.fetchParams.goodsId === item.goodsId && item.returnLeft);

                        // 如果已选中，使用已选中项的数据
                        if (selectedItem) {
                            return {
                                ...item,
                                checked,
                                returnPackageCount: selectedItem.returnPackageCount,
                                returnPieceCount: selectedItem.returnPieceCount,
                                traceableCodeList: selectedItem.traceableCodeList || [],
                            };
                        }

                        // 用于全选时批量拉取剩余数据
                        if (autoChecked && item.returnLeft) {
                            checked = true;
                        }

                        return {
                            ...item,
                            checked,
                            traceableCodeList: [],
                        };
                    });
                    const cacheOrderList = clone(newList);
                    cacheOrderList.sort((a, b) => {
                        return b.returnLeft > a.returnLeft ? 1 : -1;
                    });
                    this.order.list = this.order.list.concat(cacheOrderList);
                    this.cacheOrderList = clone(this.order.list);
                    this.returnGoodsPageParams.count = res.data.total;
                    this.returnGoodsFetchParams.offset += (res?.data?.rows?.length || 0);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            async selectReturnGoods(goods) {
                // 在搜索结果中选择商品时，保留已选中的商品状态
                const existingItem = this.returnGoodsSelectedList.find((selected) =>
                    selected.goodsId === goods.goodsId && selected.batchId === goods.batchId,
                );

                if (existingItem) {
                    goods.checked = true;
                    goods.returnPackageCount = existingItem.returnPackageCount;
                    goods.returnPieceCount = existingItem.returnPieceCount;
                    goods.traceableCodeList = existingItem.traceableCodeList || [];
                } else {
                    // 初始化退货数量
                    if (isChineseMedicine(goods.goods)) {
                        goods.returnPieceCount = goods.returnLeftPieceCount || 0;
                        goods.returnPackageCount = '';
                    } else {
                        goods.returnPieceCount = goods.returnLeftPieceCount || '';
                        goods.returnPackageCount = goods.returnLeftPackageCount || '';
                    }
                    goods.checked = goods.returnLeft ? true : false;
                }

                this.order.list = [goods];
                this.checkOne(goods);
            },
            handleReturnGoodsInput(val) {
                if (!val) {
                    this.clearReturnGoodsSearch();
                }
            },
            clearReturnGoodsSearch() {
                console.log('clearReturnGoodsSearch', this.cacheOrderList, this.returnGoodsSelectedList);
                // 退出搜索模式
                this.isSearchMode = false;

                // 恢复原始列表，并保持已选中项的状态
                const originalList = this.cacheOrderList || [];

                // 更新原始列表中的选中状态和数据
                this.order.list = originalList.map((item) => {
                    const selectedItem = this.returnGoodsSelectedList.find((selected) =>
                        selected.goodsId === item.goodsId && selected.batchId === item.batchId,
                    );

                    if (selectedItem) {
                        return {
                            ...item,
                            checked: true,
                            returnPackageCount: selectedItem.returnPackageCount,
                            returnPieceCount: selectedItem.returnPieceCount,
                            traceableCodeList: selectedItem.traceableCodeList || [],
                        };
                    }

                    return {
                        ...item,
                        checked: false,
                    };
                });

                this.returnGoodsSearchKey = '';
            },
            getUnitCount(item, goods, label) {
                const count = TraceCode.getCollectCount(item, goods);
                let unitCount = '', unit = '', packageCount = '', pieceCount = '';

                // 拆零
                if (Math.abs(count) < goods.pieceNum) {
                    unit = goods.pieceUnit;
                    unitCount = Math.abs(count);
                    pieceCount = unitCount;
                } else {
                    unit = goods.packageUnit;
                    unitCount = Math.ceil(Math.abs(count / goods.pieceNum));
                    packageCount = Math.floor(Math.abs(count / goods.pieceNum));
                    pieceCount = Math.abs(count % goods.pieceNum);
                }

                return {
                    useExternalCount: this.isStrictCountWithTraceCodeCollect,
                    packageCount,
                    pieceCount,
                    unitCount,
                    unit,
                    label,
                    countLabel: label,
                    maxCount: item._maxTraceCodeCount,
                    isTrans: item._isTransformable,
                };
            },
            async initCollectCodeCountList(list = []) {
                const resList = await TraceCode.getMaxTraceCountList({
                    scene: TraceCodeScenesEnum.INVENTORY,
                    dataList: list,
                    createKeyId: (item) => item.id,
                    getGoodsInfo: (item) => item.goods,
                    getUnitInfo: (item) => {
                        return this.getUnitCount({
                            packageCount: item.returnPackageCount,
                            pieceCount: item.returnPieceCount,
                        }, item.goods);
                    },
                });

                resList.forEach((e) => {
                    const item = list.find((i) => i.id === e.keyId);
                    if (item) {
                        this.$set(item, '_maxTraceCodeCount', e.traceableCodeNum);
                        this.$set(item, '_isTransformable', e.isTransformable);
                    }
                });
            },
            initNoTraceCodeList(item) {
                // 对无码商品初始化追溯码
                if (TraceCode.isSupplementNoCodeGoods(item.goods)) {
                    const traceableCodeList = TraceCode.mergeNoTraceCodeList({
                        ...item,
                        ...this.getUnitCount({
                            ...item,
                            packageCount: item.returnPackageCount,
                            pieceCount: item.returnPieceCount,
                        }, item.goods),
                    });
                    this.$set(item, 'traceableCodeList', traceableCodeList);
                }
            },
            // 选择退货药品数量输入变化
            async handleChangeUnitCount(item,goods) {
                if (this.isEnableTraceableCode) {
                    // 开启强校验时才实时计算
                    if (this.isStrictCountWithTraceCodeCollect && TraceCode.isSupportTraceCodeForceCheckStock()) {
                        await this.initCollectCodeCountList([item]);
                    }
                    this.initNoTraceCodeList(item);
                }
                const isPartReturn = this.isPartReturn(item);
                if (!isPartReturn) {
                    const {
                        packageUnit,pieceUnit,
                    } = goods;
                    item.partReturnTraceCodeList = item.traceableCodeList.map((code) => {
                        const isDismounting = TraceCode.isShouldApplyPieceUnit(code);
                        return {
                            ...code,
                            unit: isDismounting ? pieceUnit : packageUnit,
                        };
                    });
                } else {
                    item.partReturnTraceCodeList = [];
                }

                // 同步修改到 returnGoodsSelectedList
                const existIndex = this.returnGoodsSelectedList.findIndex((selected) =>
                    selected.goodsId === item.goodsId && selected.batchId === item.batchId,
                );

                if (existIndex !== -1) {
                    // 更新已存在项的退货数量
                    const updatedItem = clone(item);
                    this.returnGoodsSelectedList.splice(existIndex, 1, updatedItem);
                }
            },
            isPartReturn(item) {
                const {
                    returnLeft,returnPackageCount,returnPieceCount,pieceNum,useUnit,goods = {},
                } = item;
                const { packageUnit } = goods;
                const isBigUnit = packageUnit === useUnit;
                const safePieceNum = Big(getSafeNumber(pieceNum));
                const canReturnCount = isBigUnit ? Big(getSafeNumber(returnLeft)).times(safePieceNum) : Big(getSafeNumber(returnLeft));
                const returnCount = Big(getSafeNumber(returnPieceCount)).plus(Big(getSafeNumber(returnPackageCount)).times(safePieceNum));
                return !canReturnCount.eq(returnCount);
            },
            // 同步追溯码到选中列表
            syncTraceableCodeToSelectedList(item, traceableCodeList) {
                // 同步追溯码到 returnGoodsSelectedList
                const existIndex = this.returnGoodsSelectedList.findIndex((selected) =>
                    selected.goodsId === item.goodsId && selected.batchId === item.batchId,
                );

                if (existIndex !== -1) {
                    // 更新已存在项的追溯码
                    const updatedItem = clone(item);
                    updatedItem.traceableCodeList = traceableCodeList;
                    this.returnGoodsSelectedList.splice(existIndex, 1, updatedItem);
                }
            },
            /**
             * @desc 初始化 出库单
             * <AUTHOR>
             * @date 2018/11/20 14:55:53
             */
            initOutOrder() {
                this.validateCell = false;

                this._timer = setTimeout(() => {
                    this.$refs.selectForm?.validate(async (val) => {
                        if (val) {
                            const orderList = this.returnGoodsSelectedList;
                            this.list = orderList.reduce((list, item) => {
                                if (item.checked) {
                                    list.push({
                                        ...item,
                                        packageCount: item.returnPackageCount || '',
                                        pieceCount: item.returnPieceCount || '',
                                        traceableCodeList: item.traceableCodeList || [],
                                    });
                                }
                                return list;
                            }, []);
                            this.showCreateOrder = true;
                            if (this.isStrictCountWithTraceCodeCollect) {
                                this.validateCell = true;
                            }
                        }
                    });
                }, 0);
            },

            cancelOutOrder() {
                const helpMap = this.list.reduce((res, item) => {
                    res[`${item.goods.id}-${item.batchId}`] = item;
                    return res;
                }, {});

                this.list = [];
                this.showCreateOrder = false;
                this.order.list = this.order.list.map((item) => {
                    if (item.returnLeft) {
                        const _item = helpMap[`${item.goods.id}-${item.batchId}`];
                        item.checked = !!_item;
                        if (this.isEnableTraceableCode) {
                            // 还原已保存追溯码
                            item.traceableCodeList = _item?.traceableCodeList || [];
                        }
                    }
                    return item;
                });
            },

            async submitConfirm() {
                this.buttonLoading = false;

                // 开启了审核
                if (this.stockOutChainReview) {
                    if (this.isChainAdmin) {
                        // 其中一方是总部时不需要审核
                        if (this.currentClinic.clinicId === this.order.toOrganId) {
                            this.buttonLoading = true;
                            await this.createOrder();
                            this.buttonLoading = false;
                        } else {
                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                content: `确认提交后将发送${this.order.toOrgan.name}确认，确认通过立即出库`,
                                onConfirm: () => {
                                    this.createOrder();
                                },
                            });
                        }
                    } else {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: '出库单需要总部审核，将锁定相关库存，审核通过立即出库',
                            onConfirm: () => {
                                this.createOrder();
                            },
                        });
                    }
                } else {
                    // 单店和未开启审核都会走路径
                    // 单店和创建自己的出库单 不需要确认提示
                    if (this.currentClinic.clinicId === this.order.toOrganId) {
                        this.buttonLoading = true;
                        await this.createOrder();
                        this.buttonLoading = false;
                    } else {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: `确认提交后将发送${this.order.toOrgan.name}确认，确认通过立即出库`,
                            onConfirm: () => {
                                this.createOrder();
                            },
                        });
                    }
                }
            },
            /**
             * @desc 创建出库单
             * <AUTHOR>
             * @date 2018/11/20 14:43:50
             */
            createOutOrder() {
                this.validateCell = false;
                this.buttonLoading = true;

                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.createForm.validate(async (val) => {
                        if (val) {

                            if (this.isEnableTraceableCode) {
                                const {
                                    flag, errorList,
                                } = await TraceCode.validate({
                                    scene: TraceCodeScenesEnum.INVENTORY,
                                    dataList: this.list,
                                    createKeyId: (item) => item.id,
                                    getGoodsInfo: (item) => item.goods,
                                    getUnitInfo: (item) => {
                                        return this.getUnitCount(item, item.goods, '退货数量');
                                    },
                                    needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckStock(),
                                });
                                if (!flag) {
                                    this.$confirm({
                                        type: 'warn',
                                        title: '追溯码采集风险提醒',
                                        content: errorList.map((it) => {
                                            const {
                                                count,
                                                warnTips,
                                            } = it;
                                            return `有 ${count} 个商品${warnTips}`;
                                        }),
                                        confirmText: '去修改',
                                        cancelText: '仍要提交',
                                        disabledKeyboard: true,
                                        showClose: false,
                                        onConfirm: () => {
                                            this.validateCell = true;
                                            this.buttonLoading = false;

                                            // eslint-disable-next-line abc/no-timer-id
                                            setTimeout(() => {
                                                this.$refs.createForm.validate();
                                            }, 0);
                                        },
                                        onCancel: () => {
                                            this.submitConfirm();
                                        },
                                    });
                                    return;
                                }
                            }


                            this.submitConfirm();
                        } else {
                            this.buttonLoading = false;
                        }
                    });
                }, 0);
            },
            /**
             * @desc 创建退货出库
             * <AUTHOR>
             * @date 2019/10/18 10:59:25
             * @params
             * @return
             */
            async createOrder() {
                this.fromOrganId = this.order.toOrganId;
                const {
                    type,
                    fromOrganId,
                    comment,
                    list,
                } = this;
                try {
                    const params = {
                        type,
                        clinicId: fromOrganId,
                        comment,
                        list: list.map((item) => {
                            return {
                                goodsId: item.goods?.id,
                                stockId: item.stock?.id,
                                stockInId: item.id, // 详单ID
                                pieceCount: item.pieceCount || 0,
                                packageCount: item.packageCount || 0,
                                traceableCodeList: TraceCode.transCodeList(item.traceableCodeList || []),
                            };
                        }),
                        returnInOrderId: this.order.id, // 入库单ID
                    };
                    if (this.multiPharmacyCanUse) {
                        params.pharmacyNo = this.isResubmit ? this.order?.pharmacy?.no : this.pharmacyNo;
                    }

                    await StockInAPI.createOrder(params);

                    this.showDialog = false;
                    this.$emit('refresh', true, 'add');
                } catch (e) {
                    console.error(e);
                    if (e.code === 12010) {
                        const shortageMedicineTips = [];
                        const goodsStock = (e && e.detail) || [];
                        const { length } = goodsStock;
                        goodsStock.forEach((item, index) => {
                            let str = '';
                            str = `${item.goods.medicineCadn || item.goods.name || ''}`;
                            if (length - 1 === index) {
                                str += ' 库存不足';
                            }
                            shortageMedicineTips.push(str);
                        });
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: shortageMedicineTips,
                        });
                    } else if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail, () => {
                            this.list = this.list.filter((item) => item.goods.id !== e.detail.goodsId);
                        });
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail, () => {
                            this.list = this.list.filter((item) => item.goods.id !== e.detail.goodsId);
                        });
                    }
                }
            },

            /**
             * @desc 返回上一步获取入库单
             * <AUTHOR>
             * @date 2019/10/17 01:28:00
             * @params
             * @return
             */
            backGetInOrder() {
                if (this.returnGoodsSelectedList.length > 0) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: `返回上一步（选择入库单），已选择的${orderMainNameText}将清空`,
                        onConfirm: () => {
                            this.order = null;
                            // 清空已选中列表
                            this.returnGoodsSelectedList = [];
                            this.returnGoodsSearchKey = '';
                            if (!this.inOrders.length) {
                                this.fetchInOrder();
                            }
                        },
                    });
                    return;
                }
                this.order = null;
                // 清空已选中列表
                this.returnGoodsSelectedList = [];
                this.returnGoodsSearchKey = '';
                if (!this.inOrders.length) {
                    this.fetchInOrder();
                }
            },

            disabledItemFunc(item) {
                return !item.returnLeft;
            },
            checkOne(changedItem) {
                if (!changedItem) {
                    return;
                }

                if (changedItem?.checked) {
                    // 初始化退货数量（如果未设置）
                    if (isChineseMedicine(changedItem.goods)) {
                        if (!changedItem.returnPieceCount && changedItem.returnLeftPieceCount) {
                            changedItem.returnPieceCount = changedItem.returnLeftPieceCount;
                            changedItem.returnPackageCount = '';
                        }
                    } else {
                        if ((!changedItem.returnPieceCount && !changedItem.returnPackageCount) &&
                            (changedItem.returnLeftPieceCount || changedItem.returnLeftPackageCount)) {
                            changedItem.returnPieceCount = changedItem.returnLeftPieceCount || '';
                            changedItem.returnPackageCount = changedItem.returnLeftPackageCount || '';
                        }
                    }

                    // 添加到已选中列表
                    const existIndex = this.returnGoodsSelectedList.findIndex((selected) =>
                        selected.goodsId === changedItem.goodsId && selected.batchId === changedItem.batchId,
                    );

                    if (existIndex === -1) {
                        this.returnGoodsSelectedList.push(changedItem);
                    } else {
                        // 更新已存在的项
                        this.returnGoodsSelectedList.splice(existIndex, 1, changedItem);
                    }

                    this.handleChangeUnitCount(changedItem, changedItem.goods);

                    // 聚焦到该行第一个可编辑的input
                    this.$nextTick(() => {
                        const tr = document.querySelector(`.abc-table-tr[data-id="${changedItem.id}"]`);
                        if (tr) {
                            // 找到第一个abc-input-wrapper下的input元素并聚焦
                            const firstInputWrapper = tr.querySelector('.abc-input-wrapper');
                            if (firstInputWrapper) {
                                const input = firstInputWrapper.querySelector('input');
                                if (input) {
                                    input.focus();
                                }
                            }
                        }
                    });
                } else if (changedItem) {
                    // 从已选中列表中移除
                    const existIndex = this.returnGoodsSelectedList.findIndex((selected) =>
                        selected.goodsId === changedItem.goodsId && selected.batchId === changedItem.batchId,
                    );

                    if (existIndex !== -1) {
                        this.returnGoodsSelectedList.splice(existIndex, 1);
                    }
                }
            },
            customAllCheckedFunc(dataList) {
                return dataList.filter((item) => item.returnLeft).every((item) => item.checked);
            },
            async checkAll(checked) {
                console.log('checkAll', checked);
                if (checked) {
                    // 判断是否已经加载了所有数据
                    const isAllLoaded = this.order.list.length >= this.order.totalCount;

                    // 如果没有加载完所有数据，先加载全部数据
                    if (!isAllLoaded) {
                        try {
                            // 计算还需要加载的数据数量
                            const remainingCount = this.order.totalCount - this.order.list.length;
                            // 根据剩余数量计算还需要请求的次数
                            const requestsNeeded = Math.ceil(remainingCount / this.returnGoodsFetchParams.limit);

                            for (let i = 0; i < requestsNeeded; i++) {
                                await this.getInboundOrderList(true);
                                // 如果已加载完成，提前退出循环
                                if (this.order.list.length >= this.order.totalCount) {
                                    break;
                                }
                            }
                        } catch (error) {
                            console.error('加载全部数据失败:', error);
                        }
                    }

                    if (this.isEnableTraceableCode) {
                        const list = this.order.list.filter((item) => item.checked);
                        if (this.isStrictCountWithTraceCodeCollect) {
                            await this.initCollectCodeCountList(list);
                        }

                        list.forEach((item) => {
                            // 补充无码标识
                            this.initNoTraceCodeList(item);
                        });
                    }

                    this.$nextTick(() => {
                        this.handleGoodsAllSelection();
                    });
                } else {
                    // 取消全选直接清空已选中列表
                    this.returnGoodsSelectedList = [];
                }
            },

            /**
             * 处理商品全选状态变化
             * 由于全选时已经加载了所有数据，只需处理当前列表中选中的项
             * 对于已经存在于选中列表的项，保留其原有数据
             */
            handleGoodsAllSelection() {
                if (!this.order.list.length) return;

                // 创建已选中项目的映射表
                const selectedMap = new Map();
                this.returnGoodsSelectedList.forEach((item) => {
                    const key = `${item.goodsId}-${item.batchId}`;
                    selectedMap.set(key, item);
                });

                const checkedItems = this.order.list.filter((item) => item.checked && item.returnLeft).map((item) => {
                    const key = `${item.goodsId}-${item.batchId}`;

                    if (selectedMap.has(key)) {
                        const selectedItem = selectedMap.get(key);
                        const result = selectedItem;
                        result.checked = true;
                        return result;
                    }

                    return item;
                });

                this.returnGoodsSelectedList = checkedItems;
            },

            validateNumber(item) {
                let flag = true;
                const packageCount = Number(item.packageCount) || 0;
                const pieceCount = Number(item.pieceCount) || 0;

                const count = packageCount + pieceCount; // packageCount 和pieceCount 只会存在一个值

                if (item.inItem) {
                    flag = count > item.inItem.returnLeft;
                } else {
                    flag = count > item.returnLeft;
                }

                if (flag) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不可大于可退数量',
                        });
                    };
                }
                return (_, callback) => {
                    callback({ validate: true });
                };

            },

            async reviewOrder() {
                this.reviewData.list = this.list.map((item) => {
                    return {
                        id: item.id,
                        goodsId: item.goodsId,
                        stockId: item.stockId,
                        stockInId: item.stockInId,
                        pieceCount: item.pieceCount,
                        packageCount: item.packageCount,
                    };
                });
                try {
                    this.reviewBtnLoading = true;
                    await StockInAPI.review(this.orderId, this.reviewData);
                    this.showReviewDialog = false;
                    this.showDialog = false;
                    this.reviewBtnLoading = false;
                    this.$emit('refresh');
                } catch (e) {
                    this.reviewBtnLoading = false;
                    if (e.code === 962) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前出库单已被修改，请再次确认',
                            onClose: () => {
                                this.$emit('refresh');
                            },
                        });
                    } else if (e.code === 12010) {
                        const shortageMedicineTips = [];
                        const goodsStock = (e && e.detail) || [];
                        const { length } = goodsStock;
                        goodsStock.forEach((item, index) => {
                            let str = '';
                            str = `${item.goods.medicineCadn || item.goods.name || ''}`;
                            if (length - 1 === index) {
                                str += ' 库存不足';
                            }
                            shortageMedicineTips.push(str);
                        });
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: shortageMedicineTips,
                        });
                    } else if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail, () => {
                            this.list = this.list.filter((item) => item.goods.id !== e.detail.goodsId);
                        });
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail, () => {
                            this.list = this.list.filter((item) => item.goods.id !== e.detail.id);
                        });
                    }
                }
            },
            reviewOrderHandle(type) {
                if (type === 'pass') {
                    this.$refs.createForm.validate(async (val) => {
                        if (val) {
                            this.showReviewDialog = true;
                            this.reviewData.pass = 1;
                        }
                    });
                } else {
                    this.showReviewDialog = true;
                    this.reviewData.pass = 0;
                }
            },
            revokeHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确认撤回后单据将失效，可在此单据内修改并重新发起',
                    onConfirm: async () => {
                        await this.revoke();
                    },
                });
            },
            async revoke() {
                try {
                    this.btnLoading = true;
                    await StockInAPI.revokeOrder(this.orderId);
                    this.btnLoading = false;
                    this.fetchData();
                    this.$emit('refresh',false, '', false);
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            handleRefresh(data) {
                this.$emit('refresh', data);
                this.handleCancel();
            },
            handleCancel() {
                this.showDialog = false;
                this.$emit('close');
            },
        },
    };
</script>
<style lang="scss">
.goods-in-wrapper {
    .abc-form.abc-form--is-excel {
        .return-goods-auto-complete-search {
            .abc-input__inner {
                border: 1px solid var(--abc-color-P7);
                border-radius: var(--abc-border-radius-small);
            }
        }
    }

    .abc-table--excel {
        .abc-table-tr.show-hover-tr-bg:not(.is-checked):hover {
            .abc-table-td {
                background-color: $tableTrHoverBg;
            }
        }
    }
}
</style>
