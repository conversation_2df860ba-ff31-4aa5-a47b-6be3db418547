<template>
    <abc-autocomplete
        ref="autoComplete"
        v-model="searchKey"
        v-abc-focus-selected
        :placeholder="placeholder"
        :focus-placeholder="focusPlaceholder"
        :width="width"
        :size="size"
        :inner-width="calcInnerWidth"
        :delay-time="0"
        :fetch-suggestions="medicineSearch"
        :async-fetch="true"
        :auto-focus-first="autoFocusFirst"
        :focus-show="focusShow"
        :resident-sugguestions="(!!searchKey && residentSugguestions)"
        :show-empty="showEmpty"
        :clearable="clearable"
        :disabled="disabled"
        :class="{
            'abc-auto-complete_has-right-border-radius': hasRightBorderRadius
        }"
        max-length="80"
        custom-class="inventory-suggestion-wrapper"
        :close-on-click-outside="handleClickOutSide"
        @enterEvent="(e)=>selectGoods(e)"
        @blur="handleBlur"
        @focus="handleFocus"
        @enter="enter"
        @clear="handleClear"
        @input="handleInput"
    >
        <template slot="suggestion-header">
            <div class="suggestion-title">
                <div class="short-id">
                    商品编码
                </div>
                <div class="medicine-name-group">
                    药名
                </div>
                <!--<div class="type-name">-->
                <!--    类型-->
                <!--</div>-->
                <div class="spec">
                    规格
                </div>
                <div class="manufacturer">
                    <manufacturer-select
                        v-model="selectedManufacturer"
                        :manufacturer-options="manufacturerOptions"
                        size="tiny"
                        @open="handleManufacturerSelectOpen"
                        @close="handleManufacturerSelectClose"
                        @change="handleManufacturerChange"
                    ></manufacturer-select>
                </div>
                <div v-if="withStock && showStock && !isPriceAdjustment" class="display-inventory">
                    库存
                </div>
                <div v-if="isPriceAdjustment" class="display-inventory">
                    二级分类
                </div>
                <div v-if="showLastSupplier" class="display-supplier">
                    {{ supplierName }}
                </div>
                <div v-if="showRemark" class="display-remark">
                    备注
                </div>
                <div v-if="showPurchasing" class="display-purchasing">
                </div>
                <div v-if="showIdentificationCode" class="display-identification-code">
                    产品标识码
                </div>
                <div :style="{ width: '60px' }">
                </div>
            </div>
        </template>

        <div v-if="$slots['fixed-footer']" slot="suggestion-fixed-footer">
            <slot name="fixed-footer"></slot>
        </div>

        <template
            slot="suggestions"
            slot-scope="{
                suggestion, index, currentIndex
            }"
        >
            <dt
                class="suggestions-item"
                :class="{
                    selected: index === currentIndex, 'is-disabled': suggestion.v2DisableStatus
                }"
                @click="selectGoods(suggestion)"
            >
                <div class="short-id" :title="suggestion.shortId">
                    {{ suggestion.shortId }}
                </div>
                <div
                    style=" display: flex; gap: 4px; align-items: center; justify-content: start; width: 234px; min-width: 234px; max-width: 234px; padding-right: 6px;"
                >
                    <div
                        :title="suggestion | goodsFullName"
                        class="ellipsis"
                    >
                        {{ suggestion | goodsFullName }}
                    </div>
                    <abc-tooltip
                        v-if="showTooltip(suggestion)"
                        placement="top"
                        content="不在供应商经营范围"
                        :z-index="9999"
                    >
                        <abc-icon
                            style="overflow: unset;"
                            :size="16"
                            icon="s-alert-small-fill"
                            :color="index === currentIndex ? 'var(--abc-color-T4)' : 'var(--abc-color-Y3)'"
                        ></abc-icon>
                    </abc-tooltip>
                </div>
                <!--<div style="width: 100px; padding-right: 6px;" class="ellipsis" :title="suggestion | goodsTypeName">-->
                <!--    {{ suggestion | goodsTypeName }}-->
                <!--</div>-->
                <div style="width: 140px; padding-right: 6px;" class="ellipsis" :title="suggestion | goodsDisplaySpec">
                    {{ suggestion | goodsDisplaySpec }}
                </div>
                <div class="ellipsis manufacturer" :title="suggestion.manufacturer">
                    <span style="padding: 4px;">{{ suggestion.manufacturer }}</span>
                </div>
                <div
                    v-if="withStock && showStock && !isPriceAdjustment"
                    style="width: 94px; padding-right: 6px; text-align: right;"
                    :class="{
                        warning: !!suggestion.shortageWarnFlag
                    }"
                >
                    {{ showAssignGoodsCount(suggestion) }}
                </div>
                <div v-if="isPriceAdjustment" style="width: 94px; padding-right: 6px; text-align: right;">
                    {{ getMedicalSecondary(suggestion) }}
                </div>
                <div
                    v-if="showLastSupplier"
                    class="display-supplier"
                    :title="suggestion.lastStockInOrderSupplier || ''"
                >
                    {{ suggestion.lastStockInOrderSupplier || '' }}
                </div>
                <div v-if="showRemark" :title="suggestion.remark" class="display-remark">
                    {{ suggestion.remark || '' }}
                </div>
                <div
                    v-if="showIdentificationCode"
                    class="display-identification-code"
                    :title="getIdentificationCode(suggestion, index === currentIndex).title"
                >
                    <abc-text
                        :theme="getIdentificationCode(suggestion, index === currentIndex).theme"
                        size="normal"
                    >
                        {{ getIdentificationCode(suggestion, index === currentIndex).text }}
                    </abc-text>
                </div>
                <div v-if="showPurchasing" class="display-purchasing">
                    <template v-if="suggestion.purchasingPackageCount || suggestion.purchasingPieceCount">
                        <abc-icon icon="n-truck-fill" :color="index === currentIndex ? 'var(--abc-color-S2)' : 'var(--abc-color-Y2)'"></abc-icon>
                        <span
                            v-abc-title.ellipsis="`采购中 ${
                                complexCount({
                                    packageCount: suggestion.purchasingPackageCount,
                                    pieceCount: suggestion.purchasingPieceCount,
                                    goods: suggestion
                                })
                            }`"
                        ></span>
                    </template>

                    <template v-if="suggestion.claimingPackageCount || suggestion.claimingPieceCount">
                        <abc-icon icon="n-truck-fill" :color="index === currentIndex ? 'var(--abc-color-S2)' : 'var(--abc-color-Y2)'"></abc-icon>
                        <span
                            v-abc-title.ellipsis="`要货中 ${
                                complexCount({
                                    packageCount: suggestion.claimingPackageCount,
                                    pieceCount: suggestion.claimingPieceCount,
                                    goods: suggestion
                                })
                            }`"
                        ></span>
                    </template>
                </div>
                <div :style="{ width: '60px' }">
                    <template v-if="suggestion.v2DisableStatus">
                        已停用
                    </template>
                </div>
            </dt>
        </template>
        <slot slot="prepend" name="prepend"></slot>
        <slot slot="append" name="append"></slot>
        <slot slot="appendInner" name="appendInner"></slot>
        <!--<template #empty>-->
        <!--    <abc-flex align="center" justify="center" style="height: 120px;">-->
        <!--        <abc-content-empty :show-icon="false" value="暂无数据"></abc-content-empty>-->
        <!--    </abc-flex>-->
        <!--</template>-->
    </abc-autocomplete>
</template>
<script>
    import { mapGetters } from 'vuex';
    import * as repository from 'MfFeEngine/repository';
    import GoodsAPI from 'api/goods';
    import EnterEvent from 'views/common/enter-event';
    import {
        isBarcode, isNull,
    } from '@/utils';
    import { showAssignGoodsCount } from 'views/inventory/goods-utils';
    import { GoodsTypeEnum } from '@abc/constants';
    import {
        complexCount,
    } from '@/filters';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';
    import TraceCode from '@/service/trace-code/service';
    import Logger from 'utils/logger';
    import useAutoCompleteManufacturerSelect from 'views/inventory/common/manufacturer-select/index.js';
    import ManufacturerSelect from '@/views/inventory/common/manufacturer-select/index.vue';


    export default {
        name: 'GoodsAutoCompleteCoverTitle',
        components: {
            ManufacturerSelect,
        },
        mixins: [EnterEvent],
        props: {
            search: String,
            placeholder: String,
            focusPlaceholder: String,
            isOut: Boolean,
            formatCountKey: String,// 'out'
            withStock: {
                type: Boolean,
                default: true,
            },
            showStock: {
                type: Boolean,
                default: true,
            },
            needFilterDisable: {
                // 是否需要过滤停用的药品
                type: Boolean,
                default: false,
            },
            onlyStock: {
                type: Boolean,
                default: false,
            },
            hasRightBorderRadius: {
                type: Boolean,
                default: false,
            },
            clinicId: {
                type: String,
                default: '',
            },
            clearSearchKey: {
                type: Boolean,
                default: true,
            },
            clearable: {
                type: Boolean,
                default: false,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            enableBarcodeDetector: {
                type: Boolean,
                default: true,
            },
            filter: {
                type: Function,
                default: null,
            },
            autoFocusFirst: {
                type: Boolean,
                default: true,
            },
            width: {
                type: Number,
                default: 200,
            },
            focusShow: {
                type: Boolean,
                default: false,
            },
            type: {
                type: [String, Number],
                default: '',
            },
            // 采购模块支持西成药，允许多个subType 和 subType的参数
            isTypeArr: {
                type: Boolean,
                default: false,
            },
            typeArr: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            subType: {
                type: [String, Number],
                default: '',
            },
            subTypeArr: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            cMSpec: {
                type: [String, Number],
                default: '',
            },
            customTypeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            typeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 支持模糊搜索
            supportKeywordSearch: {
                type: Boolean,
                default: false,
            },
            inorderConfig: {
                type: Number,
            },
            pharmacyNo: {
                type: [String, Number],
            },
            isPriceAdjustment: Boolean,//是否来自批量调价
            goodsAllTypes: { // 系统所有的药品物资商品分类以及二级分类
                type: Array,
                default: () => {
                    return [];
                },
            },
            enableLocalSearch: {
                type: Boolean,
                default: false,
            },
            formatSearchResultFn: {
                type: Function,
                default: (res) => res,
            },
            size: String,
            closeOnClickOutside: Function,
            residentSugguestions: Boolean,
            showEmpty: Boolean,
            showLastSupplier: Boolean,
            showIdentificationCode: Boolean,
            supplierName: {
                type: String,
                default: '最近供应商',
            },
            showPurchasing: Boolean,
            needSearchNoStock: {
                type: Boolean,
                default: true,
            },
            searchRecommendFn: Function,
            priceType: Number,
            customMedicineSearch: Function,
            // enter auto focus next input
            nextInputAutoFocus: {
                type: Boolean,
                default: true,
            },
            // 无档案时是否提示
            isHasNoMedicine: {
                type: Boolean,
                default: false,
            },
            customErrorHandler: Function,
            // 是否需要提示
            needTooltip: {
                type: Boolean,
                default: false,
            },
            // 经营范围
            bizScopeList: {
                type: Array,
                default: () => [],
            },
            showRemark: {
                type: Boolean,
                default: true,
            },
        },
        setup() {
            const {
                disabledScanBarcode,
                startBarcodeDetect,
                stopBarcodeDetect,
            } = useBarcodeScanner();

            const {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            } = useAutoCompleteManufacturerSelect();

            return {
                disabledScanBarcode,
                startBarcodeDetect,
                stopBarcodeDetect,
                
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            };
        },
        data() {
            return {
                searchKey: '',
                keywordTraceableCodeNoInfo: null,
                hasScrollBar: false,
                isSearching: false, // 是否正在搜索
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            ...mapGetters(['subClinics', 'currentClinic', 'isChainAdmin', 'isSingleStore', 'userInfo']),
            hasEyeglasses() {
                return this.viewDistributeConfig.Inventory.hasEyeglasses;
            },
            calcInnerWidth() {
                let width = 744;
                if (this.showLastSupplier) {
                    width += 104;
                }
                if (this.showPurchasing) {
                    width += 148;
                }
                if (this.showIdentificationCode) {
                    width += 156;
                }
                if (this.showRemark) {
                    width += 104;
                }
                return width;
            },
            isSupportBarcodeDetector() {
                return this.enableBarcodeDetector && !this.disabledScanBarcode;
            },
        },
        watch: {
            searchKey(v) {
                this.$emit('update:search', v);
                this.clearManufacturerData();
            },
            search: {
                handler(v) {
                    this.searchKey = v;
                },
                immediate: true,
            },
            isSupportBarcodeDetector: {
                handler(v) {
                    if (v) {
                        this.startBarcodeDetect(this.onDetectBarcode);
                    } else {
                        this.stopBarcodeDetect();
                    }
                },
                immediate: true,
            },
        },
        methods: {
            complexCount,
            showAssignGoodsCount(goods) {
                if (this.formatCountKey) {
                    return showAssignGoodsCount(goods, this.formatCountKey);
                }
                return showAssignGoodsCount(goods);
            },
            getMedicalSecondary(item) {
                let resultInfo = '-';
                try {
                    if (this.goodsAllTypes && this.goodsAllTypes.length && item && item.typeId) {
                        const contItem = this.goodsAllTypes.find((i) => {
                            return Number(i.id) === Number(item.typeId);
                        }) || {};
                        if (contItem && contItem.customTypes) {
                            const custom = contItem.customTypes.find((i) => {
                                return Number(i.id) === Number(item.customTypeId);
                            });
                            resultInfo = (custom && custom.name) || '-';
                        }
                    }
                    return resultInfo;
                } catch (e) {
                    console.error(e);
                    return resultInfo;
                }
            },
            // 获取商品追溯码展示
            getIdentificationCode(item, isSelected) {
                // 是否支持追溯码
                const isSupportTraceCode = TraceCode.isSupportTraceCode(item.typeId);
                let text = '无（非中西成药/耗材）';
                let title = '';
                let theme = 'warning-light';

                if (isSupportTraceCode) {
                    if (TraceCode.isNoTraceCodeGoods(item)) {
                        text = '无追溯码';
                        title = '';
                        theme = 'black';
                    } else if (TraceCode.getDrugIdentificationCodeList(item)) {
                        text = TraceCode.getDrugIdentificationCodeList(item).join('、');
                        title = text;
                        theme = 'black';
                    } else {
                        text = '未关联';
                        title = '';
                        theme = 'gray-light';
                    }

                }

                return {
                    text,
                    title,
                    theme: isSelected ? '' : theme,
                };

            },
            handleClose() {
                this.$refs.autoComplete.handleClose();
            },
            manualFocus() {
                this.$refs.autoComplete?.$refs?.abcinput?.focus();
                this.handleFocus();
            },
            manualBlur() {
                this.$refs.autoComplete?.$refs?.abcinput?.blur();
            },
            async enter(e, selectItem) {
                // e.preventDefault();
                // e.stopPropagation();
                const { value } = e.currentTarget;
                if (!selectItem && (TraceCode.isTraceableCode(value) || isBarcode(value))) {
                    this.$emit('clearTraceCode');
                    this.searchKey = '';
                    // 条形码\追溯码搜索
                    this.handleBarcode(value, () => {
                        this.$emit('searchGoods', value);
                    });
                } else if (!selectItem && value.trim()) {
                    // 关键字搜索
                    this.$emit('searchGoods', value);
                } else {
                    this.nextInputAutoFocus && this.enterEvent(e);
                    if (this.supportKeywordSearch) {
                        this.$emit('searchGoods', '');
                    }
                }
                // 敲回车后药品推荐弹窗收起
                this.handleClose();
            },

            async handleBarcode(barcode, callback) {
                if (isNull(barcode)) return;
                this.isSearching = true;
                try {
                    const params = {
                        key: barcode,
                        withStock: this.withStock,
                        onlyStock: this.onlyStock,
                        clinicId: this.clinicId,
                        needFilterDisable: this.needFilterDisable,
                        inorderConfig: this.inorderConfig,
                        pharmacyNo: this.pharmacyNo,
                        customTypeId: this.customTypeIdList,
                        typeId: this.typeIdList,
                    };
                    let data;
                    if (typeof this.customMedicineSearch === 'function') {
                        data = await this.customMedicineSearch(params);
                    } else {
                        const res = await GoodsAPI.search(params);
                        data = res?.data;
                    }

                    // 追溯码拉取详情数据
                    if (data.keywordTraceableCode) {
                        this.keywordTraceableCodeNoInfo = data.keywordTraceableCode;
                    } else {
                        this.keywordTraceableCodeNoInfo = null;
                    }
                    // 追溯码或者条码才走下面逻辑
                    console.log('data123', data);
                    if (this.keywordTraceableCodeNoInfo || isBarcode(barcode)) {
                        const ret = (data && data.list) || [];
                        if (ret.length) {
                            this.selectGoods(
                                ret[0],
                                this.keywordTraceableCodeNoInfo?.traceableCodeList || [],
                                this.keywordTraceableCodeNoInfo ? 1 : 2,
                            );
                        } else {
                            callback && callback();
                            // 扫的追溯码,未关联商品应该出提示弹窗，外部自行处理
                            if (!this.isOut && this.keywordTraceableCodeNoInfo && !isBarcode(barcode)) {
                                this.$emit('traceableCodeEnter', this.keywordTraceableCodeNoInfo);
                                return;
                            }
                            this.errorHandler(barcode);
                        }
                    } else {
                        callback && callback();
                    }
                } catch (err) {
                    console.error(err);
                    Logger.reportAnalytics('goods-business', {
                        key: 'handleBarcodeError',
                        err,
                    });
                    callback && callback();
                    if (err.code === 12015) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: err.message,
                        });
                    } else {
                        // 防止重复提示
                        if (!err.alerted) {
                            this.$Toast({
                                message: err.message,
                                type: 'error',
                            });
                        }
                    }
                } finally {
                    this.keywordTraceableCodeNoInfo = null;
                    this.isSearching = false;
                }
            },

            async errorHandler(barcode) {
                $(this.$el).find('input').blur();

                // 根据查询到的信息做不同提示
                const res = await GoodsAPI.search({
                    key: barcode,
                    clinicId: this.clinicId,
                    pharmacyNo: this.pharmacyNo,
                });

                const list = res?.data?.list ?? [];

                // 已建过档
                if (list[0]) {
                    if (typeof this.customErrorHandler === 'function') {
                        this.customErrorHandler(list[0]);
                        return;
                    }

                    if (list[0].noStocks) {
                        this.$alert({
                            type: 'warn',
                            title: '商品未入库',
                            content: '此商品未入过库，请先入库再进行操作',
                            onClose: () => {
                                this.searchKey = '';
                                $(this.$el).find('input').focus();
                            },
                        });
                    }

                } else {
                    if (this.isHasNoMedicine && this.isSingleStore) {
                        this.$emit('hasNoMedicine', barcode);
                        return;
                    }

                    this.$alert({
                        type: 'warn',
                        title: '条码不存在',
                        content: '此条码无匹配商品，请联系商品管理员补充条码',
                        onClose: () => {
                            this.searchKey = '';
                            $(this.$el).find('input').focus();
                        },
                    });
                }
            },

            async medicineSearch(key, callback) {
                key = key.trim();

                // 解析key，使用空格分隔，取第一项为原始keyword，最后一项为parseManufacturer
                let parseManufacturer = '';

                if (key) {
                    const keyParts = key.split(' ').filter((part) => part.trim());
                    if (keyParts.length > 1) {
                        key = keyParts[0];
                        parseManufacturer = keyParts[keyParts.length - 1];
                    }
                }

                if (key) {
                    this._goods_repo_instance = repository.GoodsRepositoryService.getInstance();
                    let {
                        pharmacyNo,
                        onlyStock,
                        enableLocalSearch,
                    } = this;
                    let defaultType = this.isTypeArr ? this.typeArr : this.type;
                    // 本地搜索参数处理
                    if (enableLocalSearch && this._goods_repo_instance?.searchConfig?.useLocalSearchGoodsAPI) {
                        // 总部启用本地搜索的情况下，不带药房号进行搜索（总部没入库的会搜不出)
                        if (this.isChainAdmin) {
                            pharmacyNo = '';
                            if (this.needSearchNoStock) {
                                onlyStock = '';
                            }
                            // 总部指定门店搜索时，不走本地搜索
                            if (this.clinicId && this.clinicId !== this.currentClinic.clinicId) {
                                enableLocalSearch = false;
                            }
                        }
                        // 保证本地搜索只搜药品物资商品眼镜
                        if (!defaultType || !defaultType.length) {
                            defaultType = [GoodsTypeEnum.MEDICINE, GoodsTypeEnum.GOODS, GoodsTypeEnum.MATERIAL];
                            if (this.hasEyeglasses) {
                                defaultType.push(GoodsTypeEnum.EYEGLASSES);
                            }
                        }
                    }
                    const params = {
                        key,
                        withStock: this.withStock ? 1 : '',
                        onlyStock: onlyStock ? 1 : '',
                        clinicId: this.clinicId,
                        type: defaultType,
                        subType: this.isTypeArr ? this.subTypeArr : this.subType,
                        cMSpec: this.cMSpec,
                        disable: this.needFilterDisable ? 0 : '',
                        inorderConfig: this.inorderConfig,
                        intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                        pharmacyNo,
                        priceType: this.priceType,
                        customTypeId: this.customTypeIdList.length ? this.customTypeIdList.map(Number) : '',
                        typeId: this.typeIdList.length ? this.typeIdList.map(Number) : '',
                        limit: 200,// 默认只展示40条，可能要搜索在40条后，导致看不到。
                    };
                    let res;
                    if (typeof this.customMedicineSearch === 'function') {
                        res = await this.customMedicineSearch(params);
                    } else {
                        res = await this._goods_repo_instance.searchStockGoods(params, enableLocalSearch, this.hasEyeglasses ? 2 : 1);
                    }
                    // 追溯码拉取详情数据
                    if (res.keywordTraceableCode) {
                        this.keywordTraceableCodeNoInfo = res.keywordTraceableCode;
                    } else {
                        this.keywordTraceableCodeNoInfo = null;
                    }

                    let list = this.formatSearchResultFn(res?.list ?? []);

                    if (this.isPriceAdjustment) {
                        list = list.filter((item) => {
                            return item.isSell === 1;
                        });
                    }

                    // 构建表头厂家选项数据
                    this.createManufacturerOptions(list);

                    // 根据选中的厂家筛选结果
                    list = this.filterManufacturer(list);

                    this.hasScrollBar = list.length > 6;
                    list.sort((a,b) => {
                        return a.v2DisableStatus - b.v2DisableStatus;
                    });

                    if (this.$props.filter) {
                        const result = this.$props.filter(list);
                        this.callbackHandler(callback, result);
                    } else {
                        this.callbackHandler(callback, list);
                    }
                } else {
                    if (this.searchRecommendFn) {
                        const result = await this.searchRecommendFn();
                        this.callbackHandler(callback, result);
                    } else {
                        this.callbackHandler(callback, []);
                    }
                    this.keywordTraceableCodeNoInfo = null;
                }
            },
            callbackHandler(callback, result) {
                if (callback) {
                    callback(result);
                } else {
                    this.$refs.autoComplete.focus();
                    this.$refs.autoComplete.callbackHandler(result);
                }
            },
            async selectGoods(goods, traceableCodeList = [], codeType) {
                if (!goods) return false;
                this.$emit('selectGoods', goods, {
                    keywordTraceableCodeNoInfo: this.keywordTraceableCodeNoInfo,
                    traceableCodeList,
                    // 区分是追溯码1还是条码2
                    codeType,
                });
            },
            handleBlur(event) {
                this.$emit('blur', event);
                if (this.isSupportBarcodeDetector) {
                    this.startBarcodeDetect(this.onDetectBarcode);
                }
            },
            handleFocus(event) {
                this.$emit('focus', event);
                this.stopBarcodeDetect();
            },
            onDetectBarcode(e, barcode) {
                this.handleBarcode(barcode);
                // 抛出事件，让业务能够自由处理
                this.$emit('enterBarcode', barcode, e);
            },
            handleClear() {
                this.searchKey = '';
                this.clearManufacturerData();
                this.$emit('clear');
            },
            handleInput(e) {
                this.$emit('input', e);
            },
            showTooltip(item) {
                if (this.needTooltip && this.bizScopeList?.length && item?.businessScopeList?.length) {
                    const bizScopeIds = this.bizScopeList.map((scope) => scope.id);
                    const bizScopeParentIds = this.bizScopeList.map((scope) => scope.parentId);
                    return !item.businessScopeList.some((listItem) => bizScopeIds.includes(listItem.id) || bizScopeParentIds.includes(listItem.id));
                }
                return false;
            },
            handleManufacturerSelectOpen() {
                console.log('handleManufacturerSelectOpen');
            },
            handleManufacturerSelectClose() {
                console.log('handleManufacturerSelectClose');
            },
            // 处理厂家筛选变化
            handleManufacturerChange(e) {
                console.log('handleManufacturerChange',e);
                if (this.focusShow) {
                    // 配合focus-show重新触发查询
                    this.$nextTick(() => {
                        this.$refs.autoComplete.focus();
                    });
                } else {
                    this.medicineSearch(this.searchKey);
                }
            },
            handleClickOutSide(e) {
                const eventPath = e?.path || (e?.composedPath?.());
                if (eventPath?.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className.includes('goods-auto-complete-manufacturer-select');
                })) {
                    return false;
                }
                let closePanel = true;
                if (this.closeOnClickOutside) {
                    closePanel = this.closeOnClickOutside(e);
                }
                // // 厂家筛选关闭面板时重置搜索结果--产品明确要的效果
                if (closePanel) {
                    // 清除选中厂家
                    if (typeof this.clearManufacturerData === 'function') this.clearManufacturerData(true);
                    // // 更新列表数据
                    // this.medicineSearch(this.searchKey, (list) => {
                    //     this.$refs.autoComplete.focus();
                    //     this.$refs.autoComplete.callbackHandler(list);
                    // });
                    this.$emit('closePanel');
                }
                return closePanel;
            },
        },
    };
</script>
<style lang="scss">
.abc-auto-complete_has-right-border-radius {
    .abc-input__inner {
        border-top-right-radius: 6px !important;
        border-bottom-right-radius: 6px !important;
    }
}

.inventory-suggestion-wrapper {
    &-hidden-right-border-radius {
        .abc-input__inner {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }
    }

    .suggestions-item {
        padding: 0 0 0 10px;

        &.is-disabled {
            color: var(--abc-color-T2);
        }
    }

    .suggestion-title {
        display: flex;
        align-items: center;
        padding: 0 10px;
    }

    .suggestions-item > div {
        box-sizing: border-box;
        font-size: 14px;
        line-height: 18px;
    }

    .suggestion-title,
    .suggestions-item {
        .short-id {
            width: 84px;
            min-width: 84px;
            max-width: 84px;
            padding-right: 6px;
        }

        .medicine-name-group {
            width: 234px;
            min-width: 234px;
            max-width: 234px;
            padding-right: 6px;
        }

        .type-name {
            width: 100px;
            padding-right: 6px;
        }

        .spec {
            width: 140px;
            padding-right: 6px;
        }

        .display-inventory {
            width: 94px;
            padding-right: 6px;
            text-align: right;
        }

        .display-supplier {
            width: 94px;
            padding-right: 6px;
            text-align: left;
        }

        .display-remark {
            width: 94px;
            padding-right: 6px;
            text-align: left;
        }

        .display-identification-code {
            width: 156px;
            min-width: 156px;
            padding-right: 6px;
            text-align: left;
        }

        .display-purchasing {
            width: 148px;
            padding-right: 6px;
            color: var(--abc-color-Y2);
        }

        .display-price {
            width: 80px;
            padding-right: 6px;
            text-align: right;
        }

        .medical-fee-grade {
            width: 40px;
            text-align: center;
        }

        .manufacturer {
            flex: 1;
            min-width: 90px;
            padding-right: 6px;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .secondaryClassification {
            width: 120px;
            padding-left: 6px;
            margin-right: 30px;
            text-align: right;
            text-overflow: initial;
            white-space: nowrap;
        }

        .warning {
            color: var(--abc-color-Y2);
        }

        &.selected {
            .display-purchasing {
                color: var(--abc-color-S2);
            }

            .warning {
                color: var(--abc-color-S2);
            }
        }
    }
}
</style>
