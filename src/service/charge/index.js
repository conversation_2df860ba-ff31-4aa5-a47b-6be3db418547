import { ModalFunc as AbcModal } from '@abc/ui-pc';
import {
    ChargeSheetTypeEnum,
    ChargeType,
    ChargeStatusEnum,
    PayModeEnum,
    SourceFormTypeEnum,
    UseMemberFlagEnum,
} from '@/service/charge/constants.js';
import ChargeAPI from 'api/charge.js';
import Clone from 'utils/clone.js';
import {
    GoodsTypeEnum, PharmacyTypeEnum,
} from '@abc/constants';
import { loosePlainText } from 'utils/xss-filter.js';
import AbcError from 'utils/error/abc-error.js';
import { handleMemberInfo } from '@/utils/handle-member-info';
import { createGUID } from '@/utils';
import Logger from 'utils/logger';
import { ANONYMOUS_ID } from '@abc/constants';
export const OrderPayStatus = Object.freeze({
    NONE: 0,
    WAITING: 1,
    SUCCESS: 2,
    FAILED: 3,
    CANCELED: 4,
});

export const CancelOrderEnum = Object.freeze({
    CODE: 400,
    MSG: '支付已被取消',
});

export default class AbcChargeService {

    constructor(params) {
        const {
            chargeType = ChargeType.NORMAL,
            chargeSheetId, // 收费单id，可缺省，代表零售开单
            hospitalSheetId, // 住院单id 如果传了，就走住院单结算流程
            chargeStatus = ChargeStatusEnum.RETAIL, // 收费状态
            chargeSheetSummary, // 收费单价格信息
            postData, // 提交的收费单数据
            isReplay, // 重新收费
            isOnlyPay, // 仅支付
        } = params;

        this.chargeLoading = false;

        this.chargeType = chargeType;
        this.chargeSheetId = chargeSheetId;
        this.hospitalSheetId = hospitalSheetId;
        this.chargeStatus = chargeStatus;
        this.chargeSheetSummary = chargeSheetSummary;
        this.postData = postData;
        this.isReplay = isReplay;
        this.isOnlyPay = isOnlyPay;

        if (this.chargeType === ChargeType.HOSPITAL && !hospitalSheetId) {
            console.error('住院单结算，必须传hospitalSheetId');
        }
    }

    static #calcRequestId;

    /**
     * @desc 提交收费单
     * <AUTHOR>
     * @date 2021-09-16 17:22:15
     */
    async submit(chargeData, callback) {
        // 收费结果
        const data = await this.paidHandler(chargeData, callback);

        if (data) {
            const {
                id, status, isNotDispensed,
            } = data;
            this.chargeSheetId = id;
            this.chargeStatus = status;
            /**
             * @desc 收费时勾选同时发药，收费成功，发药失败的情况特殊处理
             * <AUTHOR>
             * @date 2020/05/13 14:26:13
             */
            if (isNotDispensed === 1) {
                AbcModal.alert({
                    type: 'warn',
                    title: '提示',
                    content: '药品库存发生变更，发药失败。请在药房手动执行发药',
                });
            }
        }
        return data;
    }

    /**
     * @desc 支付处理流程
     * <AUTHOR>
     * @date 2022-02-18 18:49:21
     */
    async paidHandler(chargeData, callback) {

        /**
         * @desc 支付类型是住院收费单 && 有住院单id走住院单结算流程
         * <AUTHOR>
         * @date 2022-02-18 18:54:19
         */
        if (this.chargeType === ChargeType.HOSPITAL && this.hospitalSheetId) {
            chargeData.payItem = chargeData.combinedPayItems[0];
            delete chargeData.combinedPayItems;
            const { data } = await ChargeAPI.hospitalDischarge(this.hospitalSheetId, chargeData);
            data.chargeType = this.chargeType;
            return data;
        }
        // 欠费还款
        if (this.chargeType === ChargeType.REPAYMENT) {
            const {
                combinedPayItems, chargeOweSheets, chargeComment,
            } = chargeData;
            const payItem = combinedPayItems[0];
            const {
                patientId, memberId,
            } = chargeOweSheets[0];
            let result = null;
            if (chargeOweSheets.length === 1) {
                // 单个还款可以修改金额，amount为payItem中的item
                const oweSheetItems = chargeOweSheets.map((x) => ({
                    oweSheetId: x.id,
                    receivableFee: x.needPay,
                    amount: payItem.amount,
                }));
                result = await ChargeAPI.oweSingleRepayment(
                    chargeOweSheets[0].id,
                    {
                        oweSheetItems,
                        patientId,
                        payItem,
                        chargeComment,
                        memberId,
                    },
                );
            } else if (chargeOweSheets.length > 1) {
                // 组合还款不能修改金额，amount为needPay
                const oweSheetItems = chargeOweSheets.map((x) => ({
                    oweSheetId: x.id,
                    receivableFee: x.needPay,
                    amount: x.needPay,
                }));
                result = await ChargeAPI.oweRepayment({
                    oweSheetItems,
                    patientId,
                    payItem,
                    chargeComment,
                    memberId,
                });
            }
            return {
                ...result?.data, chargeType: this.chargeType,
            };
        }

        // 代表继续收费
        if (this.chargeStatus > ChargeStatusEnum.UN_CHARGE) {

            const {
                receivableFee,
                combinedPayItems,
                chargeComment,
                dispensing,
                specifiedChargedTime,
            } = chargeData;

            const { data } = await ChargeAPI.repaid(this.chargeSheetId, {
                receivableFee,
                combinedPayItems,
                chargeComment,
                dispensing,
                specifiedChargedTime,
                deliveryInfo: this.postData ? this.postData.deliveryInfo : undefined,
            });

            return data;
        }

        const _postData = AbcChargeService.transSimplePostData(this.postData, chargeData);

        Logger.report({
            scene: 'charge_auto_dispensing',
            data: {
                info: '收费自动发药',
                scene: 'final-req',
                data: {
                    chargeDataDispensing: chargeData.dispensing,
                    postDataDispensing: _postData.dispensing,
                    chargeSheetId: this.chargeSheetId,
                },
            },
        });

        if (this.chargeSheetId) {
            if (this.isOnlyPay) {
                // 仅支付
                const { data } = await ChargeAPI.pay(this.chargeSheetId, _postData);
                return data;
            }
            // 重新收费时
            if (this.isReplay) {
                const { data } = await ChargeAPI.renewpaid(this.chargeSheetId, _postData);
                return data;
            }

            const { data } = await ChargeAPI.paid(this.chargeSheetId, _postData);
            return data;
        }

        const { combinedPayItems } = _postData;
        const { payMode } = combinedPayItems[0];

        // 零售开单，非记账支付 需要走锁单保存后支付的逻辑
        if ([
            PayModeEnum.ABC_PAY,
            PayModeEnum.MEMBER_CARD,
            PayModeEnum.PATIENT_CARD,
        ].indexOf(payMode) > -1) {
            const { data: result } = await ChargeAPI.saveLockOrder(_postData);
            this.chargeSheetId = result.id;
            callback && callback(result);
            const { data } = await ChargeAPI.pay(this.chargeSheetId, _postData);
            return data;
        }
        // 零售开单
        const { data } = await ChargeAPI.create(_postData);
        return data;
    }

    /**
     * @desc 锁单加保存
     * <AUTHOR>
     * @date 2021-12-30 19:20:26
     */
    saveLockOrder(chargeData) {
        const _postData = AbcChargeService.transSimplePostData(this.postData, chargeData);
        const { data } = ChargeAPI.saveLockOrder(_postData);
        this.chargeSheetId = data.id;
        return data;
    }

    /**
     * @desc 当开启了医保限价，选择医保支付的时候需要重新算费获取限价
     * <AUTHOR>
     * @date 2019/03/30 17:13:03
     */
    async calcFee(params, callback, options, needMerge = true) {
        if (!this.postData) return;
        try {
            AbcChargeService.#calcRequestId = createGUID();
            const postData = this.getCalcPostData({
                ...this.postData,
            }, this.isReplay);
            postData.requestId = AbcChargeService.#calcRequestId;

            if (params) {
                Object.assign(postData, params);
            }

            const { data } = await ChargeAPI.calcFee(postData, true);
            // 前后端都有requestId，且不一致，不做处理
            if (AbcChargeService.#calcRequestId && data.requestId && AbcChargeService.#calcRequestId !== data.requestId) return;
            needMerge && this.calcFeeHandler(data, options);
            callback && callback(data);
        } catch (err) {
            console.error(err);
        }
    }

    calcFeeHandler(data, options) {
        const {
            needPayFee,
            totalFee,
            sourceTotalPrice,
            receivableFee,
            roundingType,

            adjustmentFee,
            draftAdjustmentFee,
            outpatientAdjustmentFee,
            oddFee,
            afterRoundingDiscountedTotalFee,

            discountFee,
            discountTotalFee,
            registrationDiscountFee,
            examinationDiscountFee,
            treatmentDiscountFee,
            westernMedicineDiscountFee,
            materialDiscountFee,
            chineseMedicineDiscountFee,
            memberInfo = {},
            chargeForms = [],
            promotions = [],
            giftRulePromotions = [],
            couponPromotions = [],
            patientCardPromotions = [],
            patientPointDeductProductPromotions = [],
            patientPointsInfo,
            oncomingGiftRulePromotion,
            medicalBill,
            unitAdjustmentFee,
            verifyInfoViews,

            totalCostPrice, // 进价
            canAdjustment, // 是否可以议价
            canAdjustmentFee, // 最低议价
        } = data;

        Object.assign(this.chargeSheetSummary, {
            needPayFee,
            totalFee,
            sourceTotalPrice,
            sourceTotalFee: sourceTotalPrice,
            receivableFee,
            roundingType,

            adjustmentFee,
            draftAdjustmentFee,
            outpatientAdjustmentFee,
            oddFee,
            afterRoundingDiscountedTotalFee,
            unitAdjustmentFee,

            discountFee,
            discountTotalFee,
            registrationDiscountFee,
            examinationDiscountFee,
            treatmentDiscountFee,
            westernMedicineDiscountFee,
            materialDiscountFee,
            chineseMedicineDiscountFee,

            totalCostPrice,
        });

        /**
         * @desc 药店零售算费后议价会摊到单项，expectedTotalFee需要清空
         * <AUTHOR> Yang
         * @date 2025-04-22 17:33:25
        */
        this.postData.expectedTotalFee = null;
        // 后台有可能传null
        this.postData.promotions = promotions || [];
        this.postData.giftRulePromotions = giftRulePromotions || [];
        this.postData.couponPromotions = couponPromotions || [];
        this.postData.patientCardPromotions = patientCardPromotions || [];
        this.postData.patientPointDeductProductPromotions = patientPointDeductProductPromotions || [];
        this.postData.patientPointsInfo = patientPointsInfo;
        this.postData.oncomingGiftRulePromotion = oncomingGiftRulePromotion;
        this.postData.canAdjustment = canAdjustment;
        this.postData.canAdjustmentFee = canAdjustmentFee;
        const res = handleMemberInfo(memberInfo);
        this.postData.memberInfo = res.memberInfo;
        this.postData.memberId = res.memberId;
        this.postData.medicalBill = medicalBill;
        this.postData.verifyInfoViews = verifyInfoViews || [];
        this.postData.chargeForms.forEach((form) => {
            form.chargeFormItems.forEach((item) => {
                // 超出库存反选后，重新算费给item赋值
                if (item.expectedDoseCount) {
                    item.doseCount = item.expectedDoseCount;
                }
                item.expectedDoseCount = undefined;
            });
        });
        const {
            needAddGiftForm = false,
        } = options || {};
        // 是否需要将算费的后新增的赠品form添加进来
        if (needAddGiftForm) {

            this.postData.chargeForms = this.postData.chargeForms.filter((form) => {
                return [
                    SourceFormTypeEnum.GIFT,
                    SourceFormTypeEnum.SINGLE_PROMOTION_GIFT,
                    SourceFormTypeEnum.MARKED_GIFT_PRODUCT,
                ].indexOf(form.sourceFormType) === -1;
            });
            const giftForms = chargeForms.filter((form) => {
                return [
                    SourceFormTypeEnum.GIFT,
                    SourceFormTypeEnum.SINGLE_PROMOTION_GIFT,
                    SourceFormTypeEnum.MARKED_GIFT_PRODUCT,
                ].indexOf(form.sourceFormType) > -1;
            });
            if (giftForms) {
                giftForms.forEach((giftForm) => {
                    giftForm.keyId = giftForm.keyId || createGUID();
                    giftForm.chargeFormItems.forEach((giftItem) => {
                        giftItem.checked = giftItem.checked ?? true;
                        giftItem.keyId = giftItem.keyId || createGUID();
                        this.setSourceFormItemKeyId(chargeForms, giftItem);
                    });
                });
                this.postData.chargeForms = this.postData.chargeForms.concat(giftForms);
            }
        }
        chargeForms.forEach((form) => {
            form.chargeFormItems.forEach((item) => {
                if (item.productType === GoodsTypeEnum.EXAMINATION) {
                    item.unit = '次';
                }
                this.setChargeFormItem(form, item, options);
            });
        });
    }

    setSourceFormItemKeyId(chargeForms, giftItem) {
        chargeForms.forEach((form) => {
            form.chargeFormItems.forEach((item) => {
                if (giftItem.sourceFormItemId === item.id) {
                    giftItem.sourceFormItemKeyId = item.keyId;
                }
            });
        });
    }

    /**
     * @desc 将后台算出来的费用进行回写
     * <AUTHOR>
     * @date 2019/10/25 18:11:56
     */
    setChargeFormItem(newForm, newItem, options) {
        const {
            needRefreshStock = false,
        } = options || {};
        this.postData.chargeForms = this.postData.chargeForms.map((form) => {
            if (newForm.keyId === form.keyId) {
                form = Object.assign({}, form, {
                    totalPrice: newForm.totalPrice,
                    medicineTotalPrice: newForm.medicineTotalPrice,
                    totalDiscountPrice: newForm.totalDiscountPrice,
                    sourceTotalPrice: newForm.sourceTotalPrice,
                    sourceMedicineTotalPrice: newForm.sourceMedicineTotalPrice,
                    processRule: newForm.processRule,
                    canAdjustment: newForm.canAdjustment,
                    canAdjustmentFee: newForm.canAdjustmentFee,
                });
                form.doseCount = newForm.doseCount || form.doseCount;
                form.chargeFormItems = form.chargeFormItems.map((item) => {
                    item.expectedDoseCount = undefined;
                    if (
                        item.keyId === newItem.keyId &&
                        (item.useDismounting === newItem.useDismounting || item.useDismounting === undefined)
                    ) {

                        const _obj = {
                            unitPrice: newItem.unitPrice,
                            totalPriceRatio: newItem.totalPriceRatio,
                            totalPrice: newItem.totalPrice,
                            discountedTotalPrice: newItem.discountedTotalPrice,
                            sourceUnitPrice: newItem.sourceUnitPrice,
                            sourceTotalPrice: newItem.sourceTotalPrice,
                            expectedTotalPriceRatio: newItem.expectedTotalPriceRatio,
                            expectedUnitPrice: newItem.expectedUnitPrice,
                            expectedTotalPrice: newItem.expectedTotalPrice,
                            doseCount: newItem.doseCount,
                            batchInfos: newItem.batchInfos,
                            limitInfo: newItem.limitInfo,
                            singlePromotions: newItem.singlePromotions,
                            chargeFormItemBatchInfos: newItem.chargeFormItemBatchInfos,
                            singlePromotionedUnitPrice: newItem.singlePromotionedUnitPrice,
                            singlePromotionedTotalPrice: newItem.singlePromotionedTotalPrice,
                            giftGoodsPromotionId: newItem.giftGoodsPromotionId,
                            sourceFormItemId: newItem.sourceFormItemId,
                            canAdjustment: newItem.canAdjustment,
                            grossProfitRate: newItem.grossProfitRate,
                            composeChildren: newItem.composeChildren,
                        };
                        if (needRefreshStock) {
                            Object.assign(_obj, {
                                stockPackageCount: newItem.stockPackageCount,
                                stockPieceCount: newItem.stockPieceCount,
                            });
                        }

                        item = Object.assign({}, item, _obj);
                    }
                    return item;
                });
            }
            return form;
        });
    }
    /**
     * @desc 获取算费的postData
     * <AUTHOR> Yang
     * @date 2021-03-15 19:46:50
     * @params isReplay 是否是重新收费
     */
    getCalcPostData(postData, isReplay) {
        const params = {
            expectedAdjustmentFee: postData.expectedAdjustmentFee, // 用户修改金额后的 期望议价
            expectedTotalFee: postData.expectedTotalFee, // 用户修改金额后的 期望总价
            patientId: postData.patient ? postData.patient.id : undefined,
            payType: isReplay ? 2 : 0,
            chargeSheetId: this.chargeSheetId || postData.id,
            memberId: postData.memberId,
            chargeForms: postData.chargeForms.map((form) => {
                // 选择虚拟药房，需要把 sourceFormType 改为中药
                if (form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    form.sourceFormType = SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                }
                return form;
            }),
            promotions: postData.promotions,
            giftRulePromotions: postData.giftRulePromotions,
            couponPromotions: postData.couponPromotions,
            patientPointsInfo: postData.patientPointsInfo,
            patientCardPromotions: postData.patientCardPromotions,
            patientPointDeductProductPromotions: postData.patientPointDeductProductPromotions,
            deliveryInfo: {
                deliveryPayType: postData.deliveryInfo.deliveryPayType,
                deliveryCompanyId: postData.deliveryInfo.deliveryCompanyId,
                addressCityId: postData.deliveryInfo.addressCityId,
                addressCityName: postData.deliveryInfo.addressCityName,
                addressDetail: postData.deliveryInfo.addressDetail,
                addressDistrictId: postData.deliveryInfo.addressDistrictId,
                addressDistrictName: postData.deliveryInfo.addressDistrictName,
                addressProvinceId: postData.deliveryInfo.addressProvinceId,
                addressProvinceName: postData.deliveryInfo.addressProvinceName,
                deliveryCompany: {
                    id: postData.deliveryInfo.deliveryCompany && postData.deliveryInfo.deliveryCompany.id,
                },
            },
            useMemberFlag: postData?.useMemberFlag || UseMemberFlagEnum.USE_DEFAULT,
            mallVerifications: postData?.mallVerifications || [],
            batchExtractOriginalSheetIds: postData?.batchExtractOriginalSheetIds, // 批量提取透传
            retailType: postData?.retailType,
        };
        if (postData.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT) {
            if (postData.patient.id !== postData.prescriptionPatient?.id && postData.patient.id !== ANONYMOUS_ID) {
                params.useMemberFlag = 10;
                params.memberId = postData.patient.id;
                params.patient = postData.prescriptionPatient;
            }
        }
        return params;
    }

    /**
     * @desc 第三方支付查询 支付结果
     * <AUTHOR>
     * @date 2021-09-26 10:49:26
     */
    static async queryChargeStatus(result) {
        const { chargePayTransactionId } = result;
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve, reject) => {
            try {
                const {
                    chargeType, // 标记是住院单结算
                    combineOrderPayTransactionId,
                } = result;
                let res;
                if (chargeType === ChargeType.HOSPITAL || chargeType === ChargeType.REPAYMENT) {
                    res = await ChargeAPI.fetchSpecialChargeTypePayStatus(combineOrderPayTransactionId);
                } else {
                    res = await ChargeAPI.fetchPaystatus(chargePayTransactionId);
                }

                const { data } = res;

                const {
                    payStatus, message = '第三方支付操作失败',
                } = data;

                if (payStatus === OrderPayStatus.NONE) {
                    reject(new AbcError({
                        code: 404,
                        message: '未找到对应的支付流水信息，请刷新后再试',
                    }));
                } else if (payStatus === OrderPayStatus.WAITING) {
                    console.log('下单成功，第三方异步支付中，不做操作继续pending');
                } else if (payStatus === OrderPayStatus.SUCCESS) {
                    /**
                     * @desc 查询支付成功后会返回needPayFee，需要更新 result中的needPay
                     * <AUTHOR>
                     * @date 2021-11-26 16:48:29
                     */
                    result.needPay = data.needPayFee;
                    result.isAirPharmacyCanPay = data.isAirPharmacyCanPay;
                    result.airPharmacyOrderIds = data.airPharmacyOrderIds;
                    if (data.chargeTransactions) {
                        result.chargeTransactions = data.chargeTransactions;
                    }
                    resolve(data);
                } else if (payStatus === OrderPayStatus.FAILED) {
                    reject(new AbcError({
                        code: 500,
                        message,
                    }));
                } else if (payStatus === OrderPayStatus.CANCELED) {
                    reject(new AbcError({
                        code: CancelOrderEnum.CODE,
                        message: CancelOrderEnum.MSG,
                    }));
                } else {
                    reject(new AbcError({
                        code: 404,
                        message: '未知的支付状态',
                    }));
                }
            } catch (err) {
                reject(err);
            }
        });
    }

    static getChargeItemStruct(item) {
        return {
            keyId: item.keyId,
            id: item.id,
            unit: item.unit,
            name: item.name,
            unitCount: item.unitCount,
            doseCount: item.doseCount || 1,
            unitPrice: item.unitPrice,
            productId: item.productId,
            productType: item.productType,
            productSubType: item.productSubType,
            sourceUnitPrice: item.sourceUnitPrice,
            sourceTotalPrice: item.sourceTotalPrice,
            expectedTotalPriceRatio: item.expectedTotalPriceRatio,
            expectedUnitPrice: item.expectedUnitPrice,
            expectedTotalPrice: item.expectedTotalPrice,
            unitAdjustmentFeeLastModifiedBy: item.unitAdjustmentFeeLastModifiedBy,
            useDismounting: item.useDismounting,
            specialRequirement: item.specialRequirement,
            pharmacyType: item.pharmacyType,
            pharmacyNo: item.pharmacyNo,
            departmentId: item.departmentId,
            doctorId: item.doctorId,
            nurseId: item.nurseId,
            remark: item.remark,
            toothNos: item.toothNos,
            usageInfo: item.usageInfo || {},
            singlePromotions: item.singlePromotions || [],
            isExpectedBatch: item.isExpectedBatch,
            chargeFormItemBatchInfos: item.chargeFormItemBatchInfos,
            isFixedData: item.isFixedData,
            traceableCodeList: item.traceableCodeList ? item.traceableCodeList.map((code) => {
                const {
                    count, ...restCode
                } = code;
                return restCode;
            }) : [],
            shebaoDismountingFlag: item.shebaoDismountingFlag,
            traceableCodeRule: item.traceableCodeRule,
            isGift: item.isGift,
            giftGoodsPromotionId: item.giftGoodsPromotionId,
            sourceFormItemId: item.sourceFormItemId,
            sourceFormItemKeyId: item.sourceFormItemKeyId,
            originalChargeFormItemIds: item.originalChargeFormItemIds, // 用于批量提单透传
            productPrimaryId: item.productPrimaryId,
        };
    }

    static transSimplePostData(originPostData, params) {
        if (!originPostData) return params;

        const postData = Clone(originPostData);
        if (params) {
            Object.assign(postData, params);
        }

        postData.chargeForms.forEach((form) => {
            if (form.sourceFormType === SourceFormTypeEnum.REGISTRATION && form.registration) {
                postData.registration = form.registration;
            }
        });
        const chargeForms = [];
        postData.chargeForms.forEach((form, PIndex) => {
            if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_GLASSES) {
                chargeForms.push(form);
            }
            const chargeFormItems = form.chargeFormItems.filter((item) => {
                return item.checked;
            });
            if (chargeFormItems.length) {
                const tempForm = {
                    keyId: form.keyId,
                    id: form.id,
                    sort: PIndex,
                    registration: form.registration,
                    sourceFormType: form.sourceFormType,
                    pharmacyType: form.pharmacyType,
                    pharmacyNo: form.pharmacyNo,
                    pharmacyName: form.pharmacyName,
                    vendorId: form.vendorId,
                    vendorUsageScopeId: form.vendorUsageScopeId,
                    vendorName: form.vendorName,
                    usageScopeId: form.usageScopeId,
                    medicineStateScopeId: form.medicineStateScopeId,
                    medicalRecord: form.medicalRecord,
                    usageInfo: form.usageInfo,
                    deliveryInfo: form.deliveryInfo,
                    deliveryRule: form.deliveryRule,
                    processInfo: form.processInfo,
                    processRule: form.processRule,
                    specification: form.specification,
                    expectedTotalPrice: form.expectedTotalPrice,
                    expectedPriceFlag: form.expectedPriceFlag,
                    chargeFormItems: chargeFormItems.map((item, index) => {
                        const obj = AbcChargeService.getChargeItemStruct(item);
                        obj.sort = index;
                        obj.composeChildren = item.composeChildren?.map((child) => {
                            return AbcChargeService.getChargeItemStruct(child);
                        });
                        if (form.sourceFormType === SourceFormTypeEnum.EXPRESS_DELIVERY) {
                            obj.productInfo = item.productInfo;
                        }
                        return obj;
                    }),
                };
                chargeForms.push(tempForm);
            }
        });
        postData.chargeForms = chargeForms;
        // 提交前需要去掉诊断中的提示 html标签
        postData.diagnosis = loosePlainText(postData.diagnosis);
        if (postData.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT) {
            if (postData.patient.id !== postData.prescriptionPatient?.id && postData.patient.id !== ANONYMOUS_ID) {
                postData.useMemberFlag = 10;
                postData.memberId = postData.patient.id;
                postData.patient = postData.prescriptionPatient;
            }
            delete postData.prescriptionPatient;
        }
        return postData;
    }
}
