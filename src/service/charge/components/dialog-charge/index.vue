<template>
    <div v-if="visible" class="abc-charge-dialog-wrapper">
        <abc-dialog
            v-model="visible"
            :disabled-keyboard="confirmDialog"
            class="charge-dialog-wrapper"
            data-cy="abc-charge-dialog"
            content-styles="padding: 0px"
            :custom-class="`${showFeeChange ? 'no-position-dialog' : ''}`"
            :title="dialogTitle"
            :before-close="onBeforeClose"
            :shadow="showAdjustment"
            :show-close="showClose"
        >
            <div v-if="showOneCodePay" slot="top-extend">
                <abc-tips-card-v2
                    theme="primary"
                    variant="outline"
                    :custom-icon="{
                        name: 's-broadcast-fill', color: 'var(--abc-color-Y2)'
                    }"
                    align="left"
                >
                    请各定点机构尽快领取医保刷脸设备
                    <template slot="operate">
                        <abc-button
                            shape="square"
                            variant="text"
                            theme="primary"
                            size="small"
                            @click="handleOneCodePayOpen"
                        >
                            了解详情
                        </abc-button>
                    </template>
                </abc-tips-card-v2>
            </div>
            <adjustment-dialog
                v-if="showAdjustment"
                slot="left-extend"
                v-model="showAdjustment"
                :class="{
                    'charge-dialog-left-extend': true,
                    'adjustment-confirm-style': adjustmentConfirm,
                    'charge-dialog-left-extend__v2': true,
                }"
                :adjustment-format-length="adjustmentFormatLength"
                :personal-local-storage-key="personalLocalStorageKey"
                :need-pay-fee="receivableFee || 0"
                :origin-total-fee="originTotalFee"
                :total-cost-price="totalCostPrice"
                :is-show-profit="isShowProfit"
                @change="changeExpectedAdjustmentFeeHandle"
            ></adjustment-dialog>

            <div v-abc-loading="dialogContentLoading" class="dialog-content checkout-dialog clearfix">
                <div class="charge-dialog-header">
                    <!--抹零凑整操作-->
                    <div v-if="!isCharged" class="charge-odd-fee-wrapper">
                        <template v-if="systemAutoOddText">
                            {{ systemAutoOddText }}
                        </template>
                        <template v-else-if="canAdjustment">
                            <template v-if="expectedOddFee">
                                {{ expectedOddFee > 0 ? '凑整：' : '抹零：' }}{{ expectedOddFee | getMoneyText }}
                                <abc-button type="text" @click="handleCancelOddFee">
                                    取消
                                </abc-button>
                            </template>
                            <template v-else-if="receivableFeeHasOdd && !isSocialCardLimitPrice">
                                <abc-button type="blank" @click="handleFloor">
                                    抹零
                                </abc-button>
                                <abc-button type="blank" @click="handleCeil">
                                    凑整
                                </abc-button>
                            </template>
                        </template>
                    </div>

                    <div class="receivable-fee">
                        <abc-money
                            :value="receivableFee"
                            value-tag="b"
                            :symbol-icon-size="16"
                            :symbol-style="{
                                marginRight: '3px',
                                position: 'relative',
                                top: '1px',
                                fontWeight: 'bold',
                            }"
                        ></abc-money>
                    </div>

                    <!--议价操作-->
                    <div v-if="!isCharged" class="adjustment-fee-wrapper">
                        <abc-tooltip
                            v-if="canAdjustment"
                            placement="top"
                            :content="adjustBtnTipsy"
                            :disabled="!isSocialCardLimitPrice"
                        >
                            <abc-button
                                :class="{
                                    'yijia-button': true,
                                    checked: !!showAdjustment,
                                }"
                                :disabled="disabledAdjustment"
                                type="blank"
                                data-cy="adjustment-fee-button"
                                @click="showAdjustment = true"
                            >
                                整单议价
                            </abc-button>
                        </abc-tooltip>
                        <div v-if="draftAdjustmentFee" class="adjustment-fee">
                            {{ draftAdjustmentFee | getMoneyText }}
                        </div>
                    </div>
                </div>

                <pay-mode-list
                    ref="payModeList"
                    v-model="chargeData"
                    :member-total="memberTotal"
                    :patient-card-pay-mode-list="canPaidPatientCards"
                    :disabled-keyboard="disabledKeyboard"
                    :hidden-pay-mode-list="hiddenPayModeList"
                    :hidden-pay-mode-type-list="hiddenPayModeTypeList"
                    :disabled-pay-mode-list="disabledPayModeList"
                    :default-selected-pay-mode="defaultSelectedPayMode"
                    :available-pay-mode-list="availablePayModeList"
                    :pay-mode-validator="payModeValidator"
                    @change="changePayMode"
                    @toOpenABCPay="toOpenABCPay"
                ></pay-mode-list>
            </div>

            <div
                v-if="isOnlyPay"
                style=" padding: 0 24px 16px; margin-top: -9px;"
            >
                <div style="margin-bottom: 16px; border-top: 1px solid #e6eaee;"></div>
                <abc-tips-card-v2 theme="primary">
                    一次只能选择一种收费方式
                </abc-tips-card-v2>
            </div>

            <template v-else>
                <div v-if="currentSelectedPayModeId" class="charge-form">
                    <div class="item right-b">
                        <label class="ellipsis" :title="chargeData.name" v-html="chargeData.name"></label>
                        <abc-form>
                            <abc-form-item :validate-event="validateMoney" style="margin-right: 0; margin-bottom: 0;">
                                <abc-input
                                    ref="input-money"
                                    v-model="chargeData.money"
                                    v-abc-focus-selected
                                    type="number"
                                    data-cy="abc-charge-dialog-money-input"
                                    :config="{
                                        formatLength: 2, max: 9999999
                                    }"
                                    :width="254"
                                    :input-custom-style="{
                                        height: '42px'
                                    }"
                                    :disabled="moneyInputDidabled"
                                    @blur="blurMoneyInput()"
                                    @input="inputMoneyInput()"
                                    @focus="inputMoneyInput()"
                                >
                                </abc-input>
                            </abc-form-item>
                        </abc-form>

                        <div v-if="showCounter" class="money give-change-status">
                            <template v-if="giveChange < 0">
                                找零：<span class="num">{{ Math.abs(giveChange) | formatMoney }}</span>
                            </template>
                            <template v-else>
                                欠收：<span class="num">{{ Math.abs(giveChange) | formatMoney }}</span>
                                <abc-tooltip
                                    v-if="showCalcFeeLimitPriceTips"
                                    placement="top"
                                    content="部分项目超限价或未对码，医保结算后将自费支付"
                                >
                                    <abc-icon icon="info" color="#d9d8db" :size="14"></abc-icon>
                                </abc-tooltip>
                            </template>
                        </div>

                        <div v-else-if="showOweCount" class="money ellipsis give-change-status">
                            当前欠费：<span class="num">{{ oweSheetsCount | formatMoney }}</span>
                        </div>

                        <div v-else-if="showExternalTotalPrice" class="money ellipsis give-change-status">
                            外诊应收：<span class="num">{{ chargeSheetSummary?.externalTotalPrice | formatMoney }}</span>
                        </div>

                        <template v-else>
                            <div
                                v-if="isSocialCardLimitPrice"
                                class="is-limit-price"
                            >
                                已限价
                            </div>
                        </template>
                    </div>
                    <abc-space
                        class="tips"
                        :style="{
                            visibility: showOpenSocialTips ? 'unset' : 'hidden',
                        }"
                    >
                        暂未开通医保，本次收费仅记账<span class="operate" @click="toOpenSocial">去开通</span>
                    </abc-space>
                    <div class="others">
                        <div class="result">
                            <span
                                v-if="!bottomExpand"
                                class="remark"
                                data-cy="abc-charge-dialog-add-remark"
                                @click="bottomExpand = !bottomExpand"
                            >
                                添加备注
                            </span>
                            <abc-input
                                v-else
                                v-model="chargeComment"
                                size="small"
                                :width="168"
                                :input-custom-style="{ fontSize: '12px' }"
                                style="background-color: #ffffff;"
                                data-cy="abc-charge-dialog-remark"
                                placeholder="输入备注信息"
                                @focus="focusComment"
                                @blur="blurComment"
                            ></abc-input>

                            <abc-flex align="center">
                                <abc-date-picker
                                    v-if="showChangeTimePicker"
                                    :value="defaultSpecifiedChargedDate"
                                    value-format="YYYY-MM-DD"
                                    data-cy="abc-charge-dialog-date-picker"
                                    :picker-options="pickerOptions"
                                    @change="handleDateChange"
                                >
                                    <div
                                        class="default-specified-charged-time"
                                        :class="{
                                            'custom-date': !isChargeDateSimilarToday
                                        }"
                                    >
                                        <div class="label">
                                            收费时间：
                                        </div>
                                        <abc-button type="text">
                                            {{ defaultSpecifiedChargedDate }}
                                        </abc-button>
                                    </div>
                                </abc-date-picker>
                                <abc-time-picker
                                    v-if="showChangeTimePicker"
                                    :value="defaultSpecifiedChargedTime"
                                    :clearable="false"
                                    :time-step="5"
                                    :disabled-time="timePickerDisabledFn"
                                    @change="handleTimeChange"
                                >
                                    <abc-button
                                        variant="text"
                                        theme="default"
                                        size="small"
                                        style="height: 24px; padding: 2px 4px; font-size: var(--abc-font-size-mini);"
                                        :style="{ color: isChargeDateSimilarToday ? 'var(--abc-color-T2)' : 'var(--abc-color-T1)' }"
                                    >
                                        {{ defaultSpecifiedChargedTime }}
                                    </abc-button>
                                </abc-time-picker>
                            </abc-flex>
                        </div>
                    </div>
                </div>
                <div v-else class="charge-form">
                    <div class="charge-form-tip">
                        请选择支付方式
                    </div>
                </div>
            </template>

            <div slot="footer" class="dialog-footer">
                <div class="stance">
                    <template v-if="chargeType !== ChargeType.REPAYMENT">
                        <transition name="show">
                            <template v-if="!disablePrintBtn">
                                <div class="print-meanwhile-set">
                                    <abc-checkbox v-model="isChargeMeanwhilePrint" data-cy="abc-charge-dialog-print-meanwhile-checkbox"></abc-checkbox>
                                    <span class="label-text">
                                        同时打印
                                        <abc-icon
                                            v-if="!disablePrintSetting"
                                            icon="s-b-settings-line"
                                            size="14"
                                            class="meanwhile-print-icon"
                                            @click="openPrintConfigSettingDialog"
                                        ></abc-icon>
                                    </span>
                                </div>
                            </template>
                        </transition>
                        <transition name="show">
                            <label v-if="!isDaijianCenterDispensing && hasDeliveryPermission && !disableDispensingBtn" style="margin-left: 24px;">
                                <abc-tooltip v-if="disDispensing" :content="disabledTips">
                                    <abc-checkbox
                                        :checked="!disDispensing"
                                        disabled
                                        class="meanwhile-dispensing"
                                        data-cy="abc-charge-dialog-dispensing-meanwhile-checkbox"
                                    >
                                        同时发药
                                    </abc-checkbox>
                                </abc-tooltip>
                                <abc-checkbox
                                    v-else
                                    v-model="dispensing"
                                    type="number"
                                    class="meanwhile-dispensing"
                                    data-cy="abc-charge-dialog-dispensing-meanwhile-checkbox"
                                    @change="onDispensingCheckChange"
                                >同时发药</abc-checkbox>
                            </label>
                        </transition>
                    </template>
                </div>

                <div v-if="!showClose" style="margin-right: 8px;">
                    <abc-button
                        size="large"
                        variant="ghost"
                        data-cy="abc-charge-dialog-pay-no-now-button"
                        @click="visible = false"
                    >
                        暂不收费
                    </abc-button>
                </div>

                <abc-button
                    v-if="isSelectedABCPay"
                    size="large"
                    type="success"
                    style="min-width: 96px;"
                    data-cy="abc-charge-dialog-abc-pay-confirm-button"
                    :loading="btnLoading"
                    :disabled="disabledBtn || showFeeChange"
                    @click="handleClickConfirm"
                >
                    扫码支付
                </abc-button>
                <div
                    v-else-if="isRailwaySocialPay"
                    class="social-btn-wrapper"
                >
                    <div>
                        <abc-button
                            v-abc-check-electron
                            size="large"
                            style="min-width: 96px;"
                            :loading="btnLoading"
                            :disabled="disabledBtn || $abcSocialSecurity.isDisabledSocial"
                            @click="handleClickConfirm"
                        >
                            医保结算
                        </abc-button>
                    </div>
                </div>
                <abc-flex v-else-if="isSelectedSocialPay" :gap="4" align="center">
                    <abc-tooltip
                        placement="top"
                        content="就诊与结算不在同一天，不允许医保支付，请选择其他结算方式"
                        :offset="50"
                        :max-width="204"
                        :disabled="!isCheckSameDatePaySocial"
                    >
                        <div>
                            <template v-if="isShowFundSwitch">
                                <abc-button
                                    v-abc-check-electron="{ isCheck: !isCheckSameDatePaySocial && !isScanCodeChargeMode }"
                                    size="large"
                                    style="min-width: 96px;"
                                    variant="outline"
                                    :loading="btnLoading"
                                    :disabled="disabledBtn || isCheckSameDatePaySocial || (!isScanCodeChargeMode && $abcSocialSecurity.isDisabledSocial)"
                                    @click="handleClickConfirm({ isFundSettle: true })"
                                >
                                    统筹支付
                                </abc-button>
                                <abc-button
                                    v-abc-check-electron="{ isCheck: !isCheckSameDatePaySocial && !isScanCodeChargeMode }"
                                    size="large"
                                    style="min-width: 96px;"
                                    :loading="btnLoading"
                                    :disabled="disabledBtn || isCheckSameDatePaySocial || (!isScanCodeChargeMode && $abcSocialSecurity.isDisabledSocial)"
                                    @click="handleClickConfirm"
                                >
                                    个账支付
                                </abc-button>
                            </template>
                            <abc-button
                                v-else
                                v-abc-check-electron="{ isCheck: !isCheckSameDatePaySocial && !isScanCodeChargeMode }"
                                size="large"
                                style="min-width: 96px;"
                                :loading="btnLoading"
                                :disabled="disabledBtn || isCheckSameDatePaySocial || (!isScanCodeChargeMode && $abcSocialSecurity.isDisabledSocial) || showFeeChange"
                                @click="handleClickConfirm"
                            >
                                {{ isScanCodeChargeMode ? '扫码结算' : '刷卡结算' }}
                            </abc-button>
                        </div>
                    </abc-tooltip>
                    <abc-dropdown
                        v-if="$abcSocialSecurity.isEnableScanCodeMode"
                        style="position: absolute; right: -14px;"
                        trigger="click"
                        placement="right"
                        :offset="-3"
                        @change="onClickSwitchChargeMode"
                    >
                        <abc-icon
                            slot="reference"
                            :size="12"
                            icon="qiehuan"
                            color="#AAB4BF"
                        ></abc-icon>
                        <abc-dropdown-item
                            v-if="isScanCodeChargeMode"
                            style="padding-top: 6px; padding-bottom: 6px;"
                            label="刷卡结算"
                            :value="socialChargeModeConst.DAUB_CARD"
                        ></abc-dropdown-item>
                        <abc-dropdown-item
                            v-if="isDaubCardChargeMode"
                            style="padding-top: 6px; padding-bottom: 6px;"
                            label="扫码结算"
                            :value="socialChargeModeConst.SCAN_CODE"
                        ></abc-dropdown-item>
                    </abc-dropdown>
                </abc-flex>
                <abc-tooltip v-else content="请选择一种支付方式" :disabled="!!chargeData">
                    <div>
                        <abc-button
                            :disabled="disabledBtn"
                            size="large"
                            :loading="btnLoading"
                            style="min-width: 96px;"
                            data-cy="abc-charge-dialog-confirm"
                            @click="handleClickConfirm"
                        >
                            {{ btnText }}
                        </abc-button>
                    </div>
                </abc-tooltip>
            </div>

            <div
                v-if="showAdjustment"
                slot="shadow"
                style="width: 100%; height: 100%;"
                @click="clickEmptyDou"
            ></div>

            <dialog-fee-change
                v-if="showFeeChange"
                v-model="showFeeChange"
                :receivable-fee="feeChangeReceivableFee"
                :before-receivable-fee="feeChangeBeforeReceivableFee"
                :form-items="feeChangeFormItems"
                :reason="feeChangeReason"
                :pay-mode="chargeData.payMode"
                @confirm="yimafuPayHandler"
                @cancel="handleCancelYimafuPay"
            ></dialog-fee-change>
        </abc-dialog>

        <abc-dialog
            v-if="confirmDialog"
            v-model="confirmDialog"
            title="确认收费"
        >
            <div class="dialog-content clearfix">
                <p style=" font-size: 16px; color: #000000; text-align: center;">
                    {{ chargeData.name }} <span style="margin-left: 16px;">{{ chargeData.money }}</span>
                </p>
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button :disabled="disabledBtn" :loading="btnLoading" @click="handleClickConfirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="confirmDialog = false">
                    关闭
                </abc-button>
            </div>
        </abc-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    // const
    import {
        ChargeType,
        PayModeEnum,
        ChargeStatusEnum,
        PaySubModeEnum,
        SourceFormTypeEnum,
        ChargeSheetTypeEnum, PayModeTypeEnum,
    } from '@/service/charge/constants.js';
    import {
        CancelOrderEnum, OrderPayStatus,
    } from '@/service/charge/index.js';

    // api
    import MemberAPI from '@/api/member.js';
    import ChargeAPI from '@/api/charge.js';
    import DispensaryAPI from '@/api/dispensary';

    // utils
    import localStorage from '@/utils/localStorage-handler.js';
    import {
        off, on,
    } from '@/utils/dom.js';
    import {
        add, red,
    } from '@/utils/math.js';
    import {
        moneyNum, formatMoney,isChineseMedicine,
    } from '@/filters';
    import ABCPay from '@/service/charge/pay-way/abc-pay.js';
    import MemberPasswordPay from '@/service/charge/pay-way/member-password-pay.js';
    import AbcChargeService from '@/service/charge';
    import {
        getTraceCodeChargeItems, isStockGoods,
    } from 'src/views/cashier/utils';
    import { socialChargeModeConst } from 'src/social-security/common/constants';
    import { debounce } from 'utils/lodash';
    import { formatDate } from '@abc/utils-date';

    // 自定义组件
    import Printer from 'views/print';
    import AdjustmentDialog from './dialog-adjustment.vue';
    import PayModeList from '../pay-mode-list.vue';
    import getChargeDialogOptions from '@/service/charge/components/dialog-charge/init-charge-dialog-props.js';
    import { sum } from 'utils/calculation';
    import { getViewDistributeConfig } from '@/views-distribute/utils';
    import { parseTime } from 'utils/index.js';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import store from '@/store';
    import { autoDestroyInvoice } from 'views/cashier/invoice/utils';
    import {
        navigateToAggregatePaymentContentSetting,
        navigateToOpenSocial,
    } from '@/core/navigate-helper';
    import Logger from 'utils/logger';
    import CollectionTraceCodeDialog from '@/service/trace-code/dialog-collection-trace-code';
    import { CollectionTraceCodeCheck } from 'views/settings/trace-code/constants';
    import TraceCode, {
        SceneTypeEnum, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import useDisabledScanBarcode from '@/hooks/business/use-disabled-scan-barcode';
    import ThirdCommonPay from '@/service/charge/pay-way/third-common';
    import PayAPI from 'api/pay';
    import { mapGetters } from 'vuex';
    import OneCodePayOpenModal from 'views/settings/aggregate-payment/components/one-code-pay-open';
    import OneCodePayOperationModal from 'views/settings/aggregate-payment/components/one-code-pay-operation';
    import { useOneCodePayStore } from 'views/settings/aggregate-payment/store/useOneCodePayStore';
    import { storeToRefs } from 'pinia';
    import DialogFeeChange from '@/service/charge/components/dialog-charge/dialog-fee-change.vue';
    import { BusinessKeyMap } from '@/assets/configure/buried-point';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import PharmacyBatchesConfirmDialog from 'views/pharmacy/pharmacy-batches-confirm';

    export default {
        name: 'ChargeDialog',
        components: {
            AdjustmentDialog,
            PayModeList,
            DialogFeeChange,
        },
        filters: {
            getMoneyText(fee) {
                return (fee >= 0 ? '+' : '-') + formatMoney(Math.abs(fee));
            },
        },
        props: {
            showClose: {
                type: Boolean,
                default: true,
            },
            // 收费提交数据, 可缺省，只用传chargeSheetId的情况
            postData: {
                type: Object,
            },

            // 扩展收费提交数据, 可缺省
            extendPostData: {
                type: Object,
            },

            chargeSheetId: {
                type: [String, Number],
                default: '',
            },

            chargeType: {
                type: Number,
                default: ChargeType.NORMAL,
                validator: (value) => [ChargeType.NORMAL, ChargeType.HOSPITAL, ChargeType.REPAYMENT].indexOf(value) > -1,
            },

            // 住院单id
            hospitalSheetId: {
                type: [String, Number],
                default: '',
            },

            // 收费状态
            chargeStatus: {
                type: Number,
                validator: (value) => Object.values(ChargeStatusEnum).includes(value),
                required: true,
            },

            // 收费价格合计
            chargeSheetSummary: {
                type: Object,
                required: true,
            },

            // 是否重新收费
            isReplay: {
                type: Boolean,
                default: false,
            },

            // 是否仅支付
            isOnlyPay: {
                type: Boolean,
                default: false,
            },

            // 是否禁用议价按钮
            disableAdjustmentBtn: {
                type: Boolean,
                default: false,
            },
            // 是否禁用打印按钮
            disablePrintBtn: {
                type: Boolean,
                default: false,
            },
            // 是否禁用打印设置
            disablePrintSetting: {
                type: Boolean,
                default: false,
            },
            // 是否禁用同时发药按钮
            disableDispensingBtn: {
                type: Boolean,
                default: false,
            },

            // 禁用的支付方式
            disabledPayModeList: {
                type: Array,
                default: () => {
                    return [];
                },
            },

            chargeTransactions: {
                type: Array,
                default: () => {
                    return [];
                },
            },

            // 隐藏不可选的支付方式
            hiddenPayModeList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 隐藏不可选的支付方式
            hiddenPayModeTypeList: {
                type: Array,
                default: () => {
                    return [];
                },
            },

            dialogTitle: {
                type: String,
                default: '收费',
            },

            // 收费类型为还款时需要还款的单据list
            chargeOweSheets: {
                type: Array,
                default() {
                    return [];
                },
            },
            // 是否需要初次锁库
            needLockInventory: {
                type: Boolean,
                default: false,
            },

            pcRouterVm: Object,
            pcStoreVm: Object,
            onPartChargeSuccess: Function,
            onChargeSuccess: Function,
            onChargeError: Function,
            onClose: Function,
            onBeforeClose: Function,
            // 取消支付后回调
            cancelPayCallback: Function,
            //是否为代煎中心
            isDaijianCenterDispensing: {
                type: Boolean,
                default: false,
            },
            // 议价金额小数点后位数
            adjustmentFormatLength: {
                type: Number,
                default: 2,
            },
            scene: {
                type: String,
                default: 'cashier',
            },

            // 是否允许查看整单利润
            isShowProfit: {
                type: Boolean,
                default: false,
            },

            isNeedCheckDispensingBatch: {
                type: Boolean,
                default: false,
            },
            validateTraceCodeFn: {
                type: Function,
                default: null,
            },
        },
        setup() {
            const {
                setDisabledScanBarcode,
            } = useDisabledScanBarcode();

            const oneCodePayStore = useOneCodePayStore();
            const { hasOneCodePayApplied } = storeToRefs(oneCodePayStore);

            const { initOneCodePayStatus } = oneCodePayStore;
            return {
                setDisabledScanBarcode,

                initOneCodePayStatus,
                hasOneCodePayApplied,
            };
        },
        data() {
            return {
                socialChargeModeConst,
                ChargeType,
                visible: false,
                closeScene: '', // 关闭场景，业务需要知道是收费成功关闭的还是其它场景关闭的

                currentChargeSheetId: this.chargeSheetId,
                currentHospitalSheetId: this.hospitalSheetId,
                currentChargeStatus: this.chargeStatus,
                currentIsReplay: this.isReplay,

                currentClinic: {},
                traceCodeConfig: {},
                // 是否开通ABC 支付
                isOpenAbcPay: false,
                // 用户是否有发药权限
                hasDeliveryPermission: false,
                personalLocalStorageKey: '',
                // 收费设置
                chargeConfig: {
                    bargainSwitch: 0,
                    roundingType: 0,
                    chargePayModeConfigs: [],
                    oweSheetSwitch: 0,
                    usedDiscountNotAllowShebaoSwitch: 0,
                    singleBargainSwitch: 0,
                },

                dialogContentLoading: false,

                printOpt: {
                    printSelect: '',
                    finishSelect: [],
                },

                confirmDialog: false, // 显示弹框
                adjustmentConfirm: false,
                showAdjustment: false,

                bottomExpand: false,
                meanWhileBottomExpand: false, //打印展开
                chargeComment: '', // 备注
                dispensing: 0, // 同时发药

                memberTotal: 0, // 会员余额
                receivableFee: 0, // 当前应收金额
                chargeData: null, // 支付输入

                PayModeEnum, // 支付方式常量

                btnLoading: false, // 按钮loading
                cancelLoading: false,

                hoverIndex: 0,
                useLimitPrice: false, // 是否显示医保限价
                useListingPrice: false, // 是否显示挂网价限价
                sheBaoReceivableFee: 0, // 医保应收

                memberInfo: null,

                expectedOddFee: 0, // 没有 设置系统抹零策略的时候，自主抹零
                receivableFeeHasOdd: false, // 实收是否有小数

                canPaidPatientCards: [], // 可用的营销卡项列表
                inputError: {
                    error: false,
                    message: '',
                },

                isChargeMeanwhilePrint: false, // 是否需要同时打印
                curChargeOweSheets: this.chargeOweSheets,
                oweSheetsCount: 0, // 欠费总量

                socialChargeMode: socialChargeModeConst.DAUB_CARD, // 医保收费模式，默认刷卡结算
                isSocialCoordinatedPay: false, // 是否医保统筹支付

                pickerOptions: {
                    disabledDate: (date) => {
                        const {
                            type: chargeSheetType,
                        } = this.postData || {};
                        return date > new Date() ||
                            (chargeSheetType !== ChargeSheetTypeEnum.DIRECT_SALE &&
                                (date.getTime() + 24 * 60 * 60 * 1000) <= new Date(this.chargeSheetSummary.diagnosedDate));
                    },
                },
                defaultSpecifiedChargedDate: parseTime(new Date(), 'y-m-d', true),
                defaultSpecifiedChargedTime: parseTime(new Date(), 'h:i', true),
                specifiedChargedTime: undefined,
                curChargeTransactions: this.chargeTransactions || [],


                showFeeChange: false,
                lastCalcFeeResult: null,
                curCalcFeeResult: null,
                feeChangeReason: '',
                feeChangeFormItems: [],
                feeChangeReceivableFee: 0,
                feeChangeBeforeReceivableFee: 0,
                yimafuPostData: null,
                showYimafuOperationModal: false,
            };
        },
        computed: {
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustPrice',
            ]),
            ...mapGetters(['userInfo', 'isPharmacy']),
            showCalcFeeLimitPriceTips() {
                return [
                    PayModeEnum.SOCIAL_CARD,
                    PayModeEnum.OUTPATIENT_CENTER_PAY,
                ].includes(this.currentSelectedPayModeId) && this.chargeData.money <= this.sheBaoReceivableFee;
            },
            // 开启追溯码功能
            traceCodeCollectionSwitch() {
                return this.traceCodeConfig?.collectionSwitch || 0;
            },
            // 追溯码提醒
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            //是否启用采集强控制模式
            hasEnableCollCheckStrictMode() {
                return TraceCode.hasEnableCollCheckStrictMode;
            },
            //是否启用追溯码暂存功能 不校验追溯码
            isEnableTraceTempSave() {
                return this.hasEnableCollCheckStrictMode;
            },
            isDisabledActualCount() {
                if (this.hasEnableCollCheckStrictMode) {
                    // 医保支付过后 不能编辑发药组合
                    if (this.includeSocialPay) {
                        return true;
                    }
                    if (this.traceCodeCollectionCheck === CollectionTraceCodeCheck.charge) {
                        //开启发药强采且采集提醒为收费 就 完成结算后 不能编辑发药组合
                        return this.chargeStatus >= ChargeStatusEnum.CHARGED;
                    }
                }
                return false;
            },
            isCharged() {
                return this.currentChargeStatus > ChargeStatusEnum.UN_CHARGE;
            },
            disabledAdjustment() {
                return this.isCharged || this.isSocialCardLimitPrice;
            },
            adjustBtnTipsy() {
                if (this.isSocialCardLimitPrice) {
                    return '已有项目触发医保限价，无法整单议价';
                }
                return '';
            },
            // 是否显示医保限价
            isSocialCardLimitPrice() {
                return (this.useLimitPrice || this.useListingPrice) && this.currentSelectedPayModeId === PayModeEnum.SOCIAL_CARD;
            },
            printOptions() {
                console.log(getViewDistributeConfig().Print.printOptions);
                return getViewDistributeConfig().Print.printOptions;
            },
            // 检验检验单打印选项
            examPrintOptions() {
                return getViewDistributeConfig().Print.examPrintOptions;
            },
            // 是否可以点击完成收费
            disabledBtn() {
                if (!this.chargeData) {
                    // 必须选择一种支付方式
                    return true;
                }
                if (this.inputError.error) {
                    // 支付金额报错
                    return true;
                }
                return false;
            },
            // 按钮文字描述
            btnText() {
                const target = this.chargeData;
                if (target) {
                    if (target.payMode === PayModeEnum.SOCIAL_CARD && this.$abcSocialSecurity.isOpenSocial) {
                        // 支持医保卡支付 && 选择的医保卡
                        return `刷卡 ${this.$t('currencySymbol')} ${moneyNum(target.money)}`;
                    }
                    if (parseFloat(target.money) !== this.receivableFee) {
                        // 收费未完成
                        return `收费 ${this.$t('currencySymbol')} ${moneyNum(target.money)}`;
                    }
                }

                if (this.chargeType === ChargeType.REPAYMENT) {
                    return '完成还款';
                }
                return '完成收费';
            },

            /**
             * desc [所选项目中是否含有可发药的药品]
             */
            hasMedicine() {
                if (!this.postData) return false;
                const types = [
                    SourceFormTypeEnum.PRESCRIPTION_WESTERN,
                    SourceFormTypeEnum.PRESCRIPTION_INFUSION,
                    SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                    SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM,
                    SourceFormTypeEnum.MATERIAL,
                    SourceFormTypeEnum.EYEGLASSES,
                ];
                return this.postData.chargeForms.some((form) => {
                    let hasStockGoods = false;
                    if (form.sourceFormType === SourceFormTypeEnum.COMPOSE) {
                        form.chargeFormItems.forEach((item) => {
                            if (item.productInfo && item.productInfo.children) {
                                item.productInfo.children.forEach((child) => {
                                    if (isStockGoods(child.type)) {
                                        hasStockGoods = true;
                                    }
                                });
                            }
                        });
                    }
                    if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL) {
                        form.chargeFormItems.forEach((item) => {
                            if (isStockGoods(item.productType)) {
                                hasStockGoods = true;
                            }
                        });
                    }
                    return (
                        form.chargeFormItems.length &&
                        types.includes(form.sourceFormType)
                    ) || hasStockGoods;
                });
            },

            // 同时发药禁用提示
            disabledTips() {
                if (!this.hasMedicine) {
                    return '收费项目里没有需发药的药品';
                }
                if (!this.chargeData || this.giveChange > 0) {
                    return '需收费完成后才能发药';
                }
                return '';
            },
            /**
             * desc [隐藏打印清单+同时发药]
             * 在未完成收费时
             */
            showPrintDispensing() {
                return this.chargeData && (red(this.receivableFee, this.chargeData.money) <= 0);
            },
            /**
             * desc [是否可以发药打印]
             * 1、没药时，
             * 1、没选择支付方式时，
             * 1、有欠收时，
             * 1、还款流程时，
             */
            disDispensing() {
                return !this.hasMedicine || this.giveChange > 0 || this.chargeType === ChargeType.REPAYMENT;
            },
            // 选择的支付方式
            currentSelectedPayModeId() {
                return this.chargeData ?
                    this.chargeData.payMode :
                    null;
            },

            // 需要找现/欠付的钱
            giveChange() {
                if (this.chargeData) {
                    return red(this.receivableFee, this.chargeData.money);
                }
                return 0;
            },
            // 是否显示找零计算器
            showCounter() {
                if (this.giveChange === 0) {
                    // 为零时，暂不显示
                    return false;
                }
                if (this.giveChange < 0) {
                    // 显示找零时-支持现金找零
                    return this.currentSelectedPayModeId === PayModeEnum.CASH_PAY;
                }
                return true;
            },

            // 累计欠费
            showOweCount() {
                return this.currentSelectedPayModeId === PayModeEnum.ARREARS;
            },

            // 长护外诊
            showExternalTotalPrice() {
                const { externalTotalPrice } = this.chargeSheetSummary;
                return this.currentSelectedPayModeId === PayModeEnum.SOCIAL_CARD && externalTotalPrice;
            },

            bargainSwitch() {
                return !!this.chargeConfig.bargainSwitch && this.employeeCanAdjustPrice;
            },

            roundingType() {
                return this.chargeConfig.roundingType;
            },

            chargePayModeConfigs() {
                return this.chargeConfig.chargePayModeConfigs || [];
            },

            // 是否议价按钮
            canAdjustment() {
                return !!this.bargainSwitch && !this.disableAdjustmentBtn;
            },

            /**
             * @desc 系统自动抹零，用户不可以取消，修改
             * <AUTHOR> Yang
             * @date 2021-04-13 16:35:19
             */
            isSystemAutoOdd() {
                return this.roundingType > 0;
            },

            // 系统议价文字描述
            sysAdjustmentDesc() {
                switch (this.roundingType) {
                    case 1:
                        return '凑整到角';
                    case 2:
                        return '凑整到元';
                    case 3:
                        return '抹零到角';
                    case 4:
                        return '抹零到元';
                    case 5:
                        return '四舍五入到角';
                    case 6:
                        return '四舍五入到元';
                    default:
                        // 未设置或不处理
                        return '';
                }
            },

            /**
             * @desc 原价 + 折扣 + 医生议价 后的￥
             * <AUTHOR> Yang
             * @date 2020-09-04 16:08:01
             */
            originTotalFee() {
                return this.chargeSheetSummary.afterRoundingDiscountedTotalFee;
            },

            /**
             * @desc 成本价
             */

            totalCostPrice() {
                return this.chargeSheetSummary.totalCostPrice;
            },

            /**
             * @desc 收费处议价
             * <AUTHOR> Yang
             * @date 2020-09-04 16:11:19
             */
            draftAdjustmentFee() {
                return this.chargeSheetSummary.draftAdjustmentFee;
            },

            /**
             * @desc 系统抹零展示
             * <AUTHOR> Yang
             * @date 2021-04-16 10:57:51
             */
            systemAutoOddText() {
                if (this.isSystemAutoOdd && this.chargeSheetSummary.oddFee) {
                    let str = `${this.sysAdjustmentDesc}：`;
                    str += this.chargeSheetSummary.oddFee >= 0 ? '+' : '-';
                    str += formatMoney(Math.abs(this.chargeSheetSummary.oddFee));
                    return str;
                }
                return '';

            },

            isSelectedABCPay() {
                if (!this.chargeData) return false;
                return this.chargeData.payMode === PayModeEnum.ABC_PAY;
            },

            // 禁用键盘事件
            disabledKeyboard() {
                return this.btnLoading || this.confirmDialog || this.showAdjustment || this._disabledKeydown || this.showYimafuOperationModal || this.showFeeChange;
            },

            disabledMemberCardPay() {
                return this.disabledPayModeList.includes(PayModeEnum.MEMBER_CARD) ||
                    this.hiddenPayModeList.includes(PayModeEnum.MEMBER_CARD);
            },

            disabledPatientCardPay() {
                return this.disabledPayModeList.includes(PayModeEnum.PATIENT_CARD) ||
                    this.hiddenPayModeList.includes(PayModeEnum.PATIENT_CARD);
            },

            // 是否禁用修改付款金额
            moneyInputDidabled() {
                // 欠费支付不能修改、组合还款（chargeOweSheets）不能修改
                return this.chargeData.payMode === PayModeEnum.ARREARS || this.curChargeOweSheets.length > 1;
            },

            // 是否选中医保支付，排除医保记账
            // 新增空中支付与医保卡支付同流程
            // 新增代支支付与医保卡支付同流程
            isSelectedSocialPay() {
                if (!this.chargeData) {
                    return false;
                }
                if (this.$abcSocialSecurity.isOpenSocial === false) {
                    return false;
                }
                return (this.chargeData.payMode === PayModeEnum.SOCIAL_CARD ||
                    this.chargeData.payMode === PayModeEnum.AIR_PAY ||
                    this.chargeData.payMode === PayModeEnum.SHEBAO_YIMA_PAY ||
                    this.chargeData.payMode === PayModeEnum.MULAID_PAY ||
                    this.chargeData.payMode === PayModeEnum.PERSONAL_POS_PAY
                );
            },
            /**
             * 医保支付过
             * @return {boolean}
             */
            includeSocialPay() {
                // 使用医保支付过
                if (Array.isArray(this.curChargeTransactions) && this.curChargeTransactions.length) {
                    for (let i = 0; i < this.curChargeTransactions.length; i++) {
                        const chargeTransaction = this.curChargeTransactions[i];
                        if (chargeTransaction.payMode === PayModeEnum.SOCIAL_CARD && chargeTransaction.isPaidback === 0) {
                            return true;
                        }
                    }
                }
                // 不涉及医保支付
                return false;
            },
            // 是否铁路医保支付
            isRailwaySocialPay() {
                return this.chargeData?.payMode === PayModeEnum.RAILWAY_SOCIAL_PAY;
            },

            // 是否刷卡结算
            isDaubCardChargeMode() {
                return this.socialChargeMode === socialChargeModeConst.DAUB_CARD;
            },

            // 是否扫码结算
            isScanCodeChargeMode() {
                return this.socialChargeMode === socialChargeModeConst.SCAN_CODE;
            },

            // 同日支付限制
            isCheckSameDatePaySocial() {
                if (this.$abcSocialSecurity.isOpenSameDayMedicalSettleLimit) {
                    const { diagnosedDate } = this.postData || {};
                    if (!diagnosedDate) return false;
                    const diagnosedDateStr = formatDate(diagnosedDate, 'YYYY-MM-DD');
                    const currentDateStr = formatDate(new Date(), 'YYYY-MM-DD');
                    return diagnosedDateStr !== currentDateStr;
                }
                return false;
            },

            // 同时发药
            sameTimeDispensing() {
                const { paid } = this.pcStoreVm?.getters?.clinicEmployeeSetting || {};
                const { sameTimeDispensing } = paid || {};
                return sameTimeDispensing;
            },

            isDifferentMember() {
                const memberIdMap = {};
                this.chargeOweSheets.forEach((item) => {
                    const { memberInfo } = item;
                    const { memberType } = memberInfo || {};
                    const { id } = memberType || {};
                    if (id) {
                        if (memberIdMap[id]) {
                            memberIdMap[id]++;
                        } else {
                            memberIdMap[id] = 1;
                        }
                    }
                });
                return Object.keys(memberIdMap).length > 1;
            },
            showChangeTimePicker() {
                if (this.chargeType === ChargeType.REPAYMENT) {
                    return false;
                }
                if (this.chargeData.payMode === PayModeEnum.ARREARS) {
                    return false;
                }
                return true;
            },
            // 是否使用了优惠，包含会员、卡项、抵扣、折扣、积分、优惠券等优惠，议价除外
            usedDiscounts() {
                let discounts = 0;
                const {
                    patientPointsInfo, // 积分
                    couponPromotions = [], // 优惠券
                    giftRulePromotions = [], // 满减
                    promotions = [], // 折扣
                    patientCardPromotions = [], // 卡项抵扣
                } = this.postData;
                const {
                    checked,
                    checkedDeductionPrice,
                } = patientPointsInfo || {};
                // 积分
                if (checked && checkedDeductionPrice) {
                    discounts = sum(discounts, checkedDeductionPrice);
                }
                // 优惠券、满减、折扣
                [
                    ...couponPromotions,
                    ...giftRulePromotions,
                    ...promotions,
                ].forEach((item) => {
                    const {
                        checked,
                        discountPrice,
                    } = item;
                    if (checked && discountPrice) {
                        discounts = sum(discounts, Math.abs(discountPrice));
                    }
                });
                // 卡项抵扣
                patientCardPromotions.forEach((item) => {
                    const {
                        checked,
                        totalDeductPrice,
                    } = item;
                    if (checked && totalDeductPrice) {
                        discounts = sum(discounts, Math.abs(totalDeductPrice));
                    }
                });
                return discounts;
            },
            showOpenSocialTips() {
                const { payMode } = this.chargeData;
                return payMode === PayModeEnum.SOCIAL_CARD && !this.$abcSocialSecurity.isOpenSocial;
            },
            isShowFundSwitch() {
                // 处方外购走之前流程
                if (this.postData?.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT) return false;
                return this.$abcSocialSecurity.isShowFundSwitch; // 是否显示统筹开关 true 显示，false 不显示
            },

            defaultSelectedPayMode() {
                const {
                    type,
                } = this.postData || {};

                /**
                 * @desc 只有待收费的情况下，处方外购的单子只能选择医保卡支付
                 * <AUTHOR> Yang
                 * @date 2024-11-01 17:47:46
                */
                if (type === ChargeSheetTypeEnum.PRESCRIPTION_OUT && this.currentChargeStatus === ChargeStatusEnum.UN_CHARGE) {
                    return PayModeEnum.SOCIAL_CARD;
                }
                return undefined;
            },
            availablePayModeList() {
                const {
                    type,
                } = this.postData || {};
                if (type === ChargeSheetTypeEnum.PRESCRIPTION_OUT && this.currentChargeStatus === ChargeStatusEnum.UN_CHARGE) {
                    return PayModeEnum.SOCIAL_CARD;
                }
                return undefined;
            },
            curSpecifiedChargedDate: {
                get() {
                    if (!this.specifiedChargedTime) return undefined;
                    return this.specifiedChargedTime.split(' ')[0];
                },
                set(val) {
                    this.specifiedChargedTime = `${val} ${this.curSpecifiedChargedTime || this.defaultSpecifiedChargedTime}`;
                },
            },
            curSpecifiedChargedTime: {
                get() {
                    if (!this.specifiedChargedTime) return undefined;
                    return this.specifiedChargedTime.split(' ')[1];
                },
                set(val) {
                    this.specifiedChargedTime = `${this.curSpecifiedChargedDate || this.defaultSpecifiedChargedDate} ${val}`;
                },
            },
            isChargeDateSimilarToday() {
                if (!this.curSpecifiedChargedDate) return true;
                const todayDate = parseTime(new Date(), 'y-m-d', true);
                return this.curSpecifiedChargedDate === todayDate;
            },
            showOneCodePay() {
                return this.$abcSocialSecurity.isShowOneCodePay && !this.hasOneCodePayApplied && !this.isPharmacy;
            },
        },

        watch: {
            visible(val) {
                if (!val) {
                    if (this._ABCPay) {
                        this._ABCPay.destroy();
                        this._ABCPay = null;
                    }
                    this.onClose && this.onClose(this.closeScene);
                    this.destroyElement();
                }
            },
            // 当前为欠费显示欠费总计
            async showOweCount(val) {
                if (val) {
                    const { id } = this.postData.patient || {};
                    const { data } = await ChargeAPI.getPatientOweList(id);
                    this.oweSheetsCount = data.totalOweFee || 0;
                }
            },

            sameTimeDispensing: {
                async  handler(val) {
                    if (this.disDispensing) {
                        this.dispensing = 0;
                        Logger.report({
                            scene: 'charge_auto_dispensing',
                            data: {
                                info: '收费自动发药',
                                scene: 'sameTimeDispensing-disDispensing',
                                data: {
                                    hasMedicine: this.hasMedicine,
                                    giveChange: this.giveChange,
                                    chargeType: this.chargeType,
                                    chargeSheetId: this.currentChargeSheetId,
                                    sameTimeDispensing: val,
                                },
                            },
                        });
                        return;
                    }
                    const { personalLocalStorageKey } = await getChargeDialogOptions();
                    if (val === null) {
                        if (personalLocalStorageKey) {
                            this.dispensing = localStorage.getObj('cashier_delivery_medicine', personalLocalStorageKey, true) || 0;
                        } else {
                            this.dispensing = 0;
                        }
                        Logger.report({
                            scene: 'charge_auto_dispensing',
                            data: {
                                info: '收费自动发药',
                                scene: 'sameTimeDispensing-has-not-val',
                                data: {
                                    personalLocalStorageKey,
                                    localDispensing: localStorage.getObj('cashier_delivery_medicine', personalLocalStorageKey, true),
                                    chargeSheetId: this.currentChargeSheetId,
                                    sameTimeDispensing: val,
                                    config: this.pcStoreVm?.getters?.clinicEmployeeSetting,
                                },
                            },
                        });
                    } else {
                        this.dispensing = val;
                        Logger.report({
                            scene: 'charge_auto_dispensing',
                            data: {
                                info: '收费自动发药',
                                scene: 'sameTimeDispensing-has-val',
                                data: {
                                    chargeSheetId: this.currentChargeSheetId,
                                    sameTimeDispensing: val,
                                    config: this.pcStoreVm?.getters?.clinicEmployeeSetting,
                                },
                            },
                        });
                    }
                },
                immediate: true,
            },
        },
        created() {
            this._calcFee = debounce(this.calcFee, 200, true);
            if (this.$abcSocialSecurity.isEnableScanCodeMode) {
                this.socialChargeMode = this.$abcSocialSecurity.cacheChargeMode;
            }

            this.initHandler();
            this.setDisabledScanBarcode(true);
            this.initOneCodePayStatus();
        },

        mounted() {
            on(document, 'keydown', this.keydownHandle);
        },
        beforeDestroy() {
            off(document, 'keydown', this.keydownHandle);
            this.timeoutId && clearTimeout(this.timeoutId);
            clearTimeout(this._queryDispensingTimer);
            this.setDisabledScanBarcode(false);
            this._oneCodePayOpenModal = null;
            this._oneCodePayOperationModal = null;
        },

        methods: {
            isChineseMedicine,
            async initHandler() {
                this.dialogContentLoading = true;
                const {
                    hasDeliveryPermission,
                    personalLocalStorageKey,
                    chargeConfig = {},
                    currentClinic = {},
                    traceCodeConfig,
                } = await getChargeDialogOptions();
                const {
                    isOpenAbcPay,
                } = await ABCPay.getABCPayOptions();

                this.currentClinic = currentClinic;
                this.traceCodeConfig = traceCodeConfig;
                this.isOpenAbcPay = isOpenAbcPay;
                this.hasDeliveryPermission = hasDeliveryPermission;
                this.getPrintOpt(); // 获取打印选项配置后再处理收费完成后的是否打印逻辑
                Object.assign(this.chargeConfig, {
                    bargainSwitch: chargeConfig.bargainSwitch,
                    roundingType: chargeConfig.roundingType,
                    chargePayModeConfigs: chargeConfig.chargePayModeConfigs,
                    oweSheetSwitch: chargeConfig.oweSheetSwitch,
                    usedDiscountNotAllowShebaoSwitch: chargeConfig.usedDiscountNotAllowShebaoSwitch,
                    singleBargainSwitch: chargeConfig.singleBargainSwitch,
                });
                this.personalLocalStorageKey = personalLocalStorageKey;

                this._chargeService = new AbcChargeService({
                    chargeType: this.chargeType,
                    chargeSheetId: this.currentChargeSheetId,
                    hospitalSheetId: this.currentHospitalSheetId,
                    chargeStatus: this.currentChargeStatus, // 收费状态
                    chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                    postData: this.postData, // 提交的收费单数据
                    isReplay: this.currentIsReplay, // 重新收费
                    isOnlyPay: this.isOnlyPay, // 仅支付
                });


                this.initReceivableFee();

                // 还款不走此流程
                if (this.chargeType !== ChargeType.REPAYMENT) {
                    if (this.postData && !this.isCharged) {

                        if (this.needLockInventory) {
                            // needLockInventory 的情况下需要固定批次
                            this.postData.chargeForms.forEach((form) => {
                                form.chargeFormItems.forEach((item) => {
                                    item.isFixedData = 1;
                                    item.composeChildren?.forEach((child) => {
                                        child.isFixedData = 1;
                                    });
                                });
                            });
                        }
                        await this._calcFee(undefined);
                    } else {
                        await this.queryPatientCardByChargeSheetId();
                    }
                }
                await this.getMemberTotal();
                this.dialogContentLoading = false;
            },

            updateFunctionalProps({ chargeOweSheets }) {
                this.curChargeOweSheets = chargeOweSheets;
            },

            payModeValidator(payItem) {
                if (payItem.payMode === PayModeEnum.ABC_PAY && !this.isOpenAbcPay) {
                    return {
                        error: true,
                        message: '',
                    };
                }

                if (payItem.payMode === PayModeEnum.SOCIAL_CARD) {
                    // if (this.$abcSocialSecurity.isCommonMode && !this.$abcSocialSecurity.isElectron) {
                    //     // 支持的时候，不显示信息
                    //     return {
                    //         error: true,
                    //         message: '请下载客户端支付',
                    //     };
                    // }
                    // 还款
                    if (this.chargeType === ChargeType.REPAYMENT) {
                        // 多个欠费单同时还款不支持医保刷卡
                        if (this.chargeOweSheets?.length > 1) {
                            return {
                                error: true,
                                message: '多个欠费单同时还款不支持医保刷卡',
                            };
                        }
                        //仅全部欠费且未进行过退欠费可使用医保结算
                        if (this.postData.isOweSheetCanPayForShebao !== 1) {
                            return {
                                error: true,
                                message: '仅全部欠费且未进行过退欠费可使用医保结算',
                            };
                        }
                    }
                    if (
                        this.chargeConfig.usedDiscountNotAllowShebaoSwitch &&
                        this.usedDiscounts
                    ) {
                        return {
                            error: true,
                            message: '本单如需使用医保结算，请先取消已选择的会员、卡项、抵扣、折扣、优惠券等优惠',
                        };
                    }
                    if (this.curChargeTransactions?.find((it) => it.payMode === PayModeEnum.PERSONAL_POS_PAY)) {
                        return {
                            error: true,
                            message: '已刷POS个账，无法刷医保统筹',
                        };
                    }
                }

                if (payItem.payMode === PayModeEnum.PATIENT_CARD) {
                    if (!payItem.availableBalance) {
                        return {
                            error: true,
                            message: '可用余额不足',
                        };
                    }
                }

                if (payItem.payMode === PayModeEnum.MEMBER_CARD) {
                    // 还款
                    if (this.chargeType === ChargeType.REPAYMENT) {
                        if (this.isDifferentMember) {
                            return {
                                error: true,
                                message: '欠费单享受了不同会员卡折扣，请组合相同会员卡的欠费单还款',
                            };
                        }
                    }

                    // 无会员余额
                    if (!this.memberTotal) {
                        return {
                            error: true,
                            message: '会员余额不足',
                        };
                    }

                    // 仅支付且会员余额不足以支付
                    if (this.isOnlyPay && +this.memberTotal < +this.receivableFee) {
                        return {
                            error: true,
                            message: '会员余额不足',
                        };
                    }
                }

                if (payItem.payMode === PayModeEnum.ARREARS) {
                    if (this.chargeConfig.oweSheetSwitch !== 1) {
                        return {
                            error: true,
                            message: `未开启欠费功能，请前往${this.pcStoreVm?.getters?.isChainSubStore ? '总部' : ''}「管理」-「收费设置」中开启`,
                        };
                    }

                    if (this.postData && !this.postData.patient?.id) {
                        return {
                            error: true,
                            message: '不支持匿名患者欠费',
                        };
                    }
                }

                if (this.disabledPayModeList.includes(payItem.payMode)) {
                    return {
                        error: true,
                        message: '',
                    };
                }
                return {
                    error: false,
                    message: '',
                };
            },

            toOpenABCPay() {
                navigateToAggregatePaymentContentSetting(this.currentClinic);
            },

            toOpenSocial() {
                navigateToOpenSocial(this.currentClinic);
            },

            /**
             * @desc 点击遮罩，提示需要确认议价
             */
            clickEmptyDou() {
                if (this.adjustmentConfirm) return;
                this.adjustmentConfirm = true;
                this.timeoutId = setTimeout(() => {
                    this.adjustmentConfirm = false;
                }, 300);
            },
            async changeExpectedAdjustmentFeeHandle(data) {
                this.postData.expectedAdjustmentFee = data.expectedAdjustmentFee;
                // 议价改动，清空自主抹零，重新算费
                this.expectedOddFee = 0;
                await this.calcFee();
                this.refreshChargeInputItem();
            },

            refreshChargeInputItem(needCalcFee = false) {
                if (this.chargeData) {
                    // 存在支付方式
                    let money = this.receivableFee;
                    money = this.validatePayMoney(this.chargeData, money);
                    this.chargeData.money = moneyNum(money);
                    this.inputMoneyInput();
                }
                // 有卡项，编辑凑整抹零 需要掉一次算费
                if (needCalcFee && this.canPaidPatientCards && this.canPaidPatientCards.length) {
                    this.calcFee();
                }
            },

            /**
             * @desc 当开启了医保限价，选择医保支付的时候需要重新算费获取限价
             * <AUTHOR>
             * @date 2019/03/30 17:13:03
             */
            async calcFee(payMode) {
                try {
                    this.btnLoading = true;
                    await this._chargeService.calcFee({
                        payMode,
                        isNeedPatientCardBalance: 1,
                        expectedOddFee: this.expectedOddFee || undefined,
                    }, async (result) => {

                        // 这次和上次算费结果不一致，并且是医保限价导致，需要弹出价格对比
                        this.curCalcFeeResult = result;

                        this.receivableFee = result.needPayFee;
                        this.selfOddFeeHandler(result.needPayFee);
                        this.useLimitPrice = result.useLimitPrice;
                        this.useListingPrice = result.useListingPrice;
                        this.sheBaoReceivableFee = result.sheBaoReceivableFee;

                        // 触发医保限价，需要清空各种议价抹零
                        if (result.useLimitPrice || result.useListingPrice) {
                            this.expectedOddFee = undefined;
                            if (this.postData.expectedAdjustmentFee && !result.adjustmentFee) {
                                this.postData.expectedAdjustmentFee = undefined;
                                this.$Toast({
                                    message: '议价已清空。原因：议价导致项目触发医保限价',
                                    type: 'error',
                                    duration: 2000,
                                    referenceEl: this.$el.querySelector('.receivable-fee'),
                                });
                            }
                        }

                        this.canPaidPatientCards = result.canPaidPatientCards || [];
                        this.refreshChargeInputItem();


                    });
                    // diff价格
                    const isSocialPay = payMode === PayModeEnum.SOCIAL_CARD;
                    if (this.lastCalcFeeResult && this.curCalcFeeResult.needPayFee !== this.lastCalcFeeResult.needPayFee && isSocialPay) {
                        await this.compareChargeResult(this.curCalcFeeResult);
                    }
                    this.lastCalcFeeResult = this.curCalcFeeResult; // 保存上次算费结果

                    this.btnLoading = false;
                } catch (err) {
                    console.error(err);
                    this.btnLoading = false;
                }
            },
            /**
             * @desc: 请求两次收费的价格对比
             * @author: ff
             * @time: 2025/5/20
             */
            async compareChargeResult() {
                const { data } = await ChargeAPI.compareChargeResult({
                    firstCalculateChargeSheetRsp: this.lastCalcFeeResult,
                    secondCalculateChargeSheetRsp: this.curCalcFeeResult,
                });
                this.filterDiffPrice(data);
                if (this._ABCPay) {
                    this._ABCPay.destroy();
                    this._ABCPay = null;
                }
                this.showFeeChange = true;
            },
            /**
             * @desc: 过滤两次价格不一样的收费项目
             * @author: ff
             * @time: 2025/5/20
             */
            filterDiffPrice(diffData) {
                const {
                    beforeReceivableFee, receivableFee, changeReason, chargeForms,
                } = diffData;
                if (beforeReceivableFee === receivableFee) {
                    this.showFeeChange = false;
                    return;
                }
                this.feeChangeBeforeReceivableFee = beforeReceivableFee;
                this.feeChangeReceivableFee = receivableFee;
                this.feeChangeReason = changeReason;
                this.feeChangeFormItems = [];

                this.feeChangeReason = changeReason;
                // 遍历chargeForms 下面的item, 过滤chargeFlag > 0 的item
                chargeForms.forEach((form) => {
                    form.items.forEach((item) => {
                        if (item.changeFlag > 0) {
                            this.feeChangeFormItems.push({
                                name: item.name,
                                receivableFee: item.receivableFee,
                                beforeReceivableFee: item.beforeReceivableFee,
                            });
                        }
                    });
                });

            },

            /**
             * @desc 初始化 receivableFee
             * 系统议价情况
             * <AUTHOR>
             * @date 2019/10/22 14:12:22
             */
            initReceivableFee() {
                if (this.chargeType === ChargeType.REPAYMENT) {
                    const needPays = this.curChargeOweSheets.map((x) => x.needPay);
                    this.receivableFee = sum(...needPays);
                } else {
                    this.receivableFee = this.chargeSheetSummary.needPayFee;
                    this.sheBaoReceivableFee = this.chargeSheetSummary.sheBaoReceivableFee;
                }
                this.selfOddFeeHandler(this.receivableFee);
            },

            getPrintOpt() {
                // 如果禁用打印,则默认不展开打印选项
                if (this.disablePrintBtn) return;
                const { cache } = Printer;
                const { isChargeMeanwhilePrint } = cache.get();
                this.isChargeMeanwhilePrint = isChargeMeanwhilePrint;

            },
            cachePrintOpt() {
                if (this.disablePrintBtn) {
                    return;
                }
                const { cache } = Printer;
                cache.set({
                    isChargeMeanwhilePrint: this.isChargeMeanwhilePrint,
                    directSmallNeedPrint: this.isChargeMeanwhilePrint,
                });
            },
            /**
             * @desc 获取会员卡总金额
             * <AUTHOR>
             * @date 2019/03/22 16:26:08
             */
            async getMemberTotal() {
                if (this.disabledMemberCardPay) return false;
                if (!this.postData && !this.extendPostData) return false;

                let memberId;
                if (this.postData) {
                    memberId = this.postData.memberId;
                } else if (this.extendPostData) {
                    memberId = this.extendPostData.memberId;
                }
                this.memberTotal = 0;
                if (this.chargeType === ChargeType.REPAYMENT) {
                    memberId = this.chargeOweSheets[0]?.memberId;
                }
                if (!memberId) return;
                const { data } = await MemberAPI.findMembers(memberId);
                if (data) {
                    this.memberTotal = add(data?.memberInfo?.present, data?.memberInfo?.principal);
                    this.memberInfo = {
                        patientId: data?.id,
                        name: data?.name,
                        totalMoney: this.memberTotal,
                    };
                }
            },

            /**
             * desc [会员卡支付时余额限制]
             */
            validatePayMoney(pay, money) {
                // 当为会员卡时-需要判余额是否充裕
                if (pay.payMode === PayModeEnum.MEMBER_CARD) {
                    return Math.min(this.memberTotal, money);
                }

                // 当为第三方卡支付时-需要判余额是否充裕
                // 当为会员卡时-需要判余额是否充裕
                if (
                    pay.type === PayModeTypeEnum.THIRD_COMMON ||
                    pay.payMode === PayModeEnum.PATIENT_CARD
                ) {
                    return Math.min(pay.availableBalance || 0, money);
                }

                // 社保卡支付使用(不等于欠费还款)
                if (
                    (pay.payMode === PayModeEnum.SOCIAL_CARD || !!this.yimafuPostData) &&
                    this.chargeType !== ChargeType.REPAYMENT &&
                    !this.isCharged
                ) {
                    return Math.min(this.sheBaoReceivableFee, money);
                }
                return money;
            },

            /**
             * @desc 改变选中的payMode
             * <AUTHOR>
             * @date 2021-10-15 10:35:16
             */
            async changePayMode(pay) {
                this.yimafuPostData = null;
                const money = this.validatePayMoney(pay, this.receivableFee);

                this.chargeData = {
                    ...pay,
                    money: moneyNum(money),
                };

                this.inputError = {
                    error: false,
                    message: '',
                };

                if (pay.payMode === PayModeEnum.ARREARS) {
                    this.specifiedChargedTime = undefined;
                }

                if (!this.isCharged) {
                    await this._calcFee(pay.payMode);
                }

                this.$nextTick(() => {
                    $('.charge-dialog-wrapper .charge-form').find('input').eq(0).focus();
                });
            },

            getPostData() {
                const combinedPayItems = [];
                combinedPayItems.push({
                    payMode: this.chargeData.payMode, // 支付方式
                    amount: +this.chargeData.money, // 金额
                    thirdPartyPayCardId: this.chargeData.thirdPartyPayCardId || undefined,
                    thirdPartyPayCardPassword: this.chargeData.thirdPartyPayCardPassword || undefined,
                });

                // 已经不是组合收费了，因此不需要过滤金额为0的情况
                // combinedPayItems = combinedPayItems.filter((item) => item.amount !== 0)
                // 收费未完成，不发药
                const dispensing = this.isDaijianCenterDispensing ? 0 : parseFloat(this.chargeData.money) < this.receivableFee ?
                    false :
                    this.dispensing;

                Logger.report({
                    scene: 'charge_auto_dispensing',
                    data: {
                        info: '收费自动发药',
                        scene: 'getPostData',
                        data: {
                            isDaijianCenterDispensing: this.isDaijianCenterDispensing,
                            amount: this.chargeData.money,
                            receivableFee: this.receivableFee,
                            compare: parseFloat(this.chargeData.money) < this.receivableFee,
                            dispensing: this.dispensing,
                            finalDispensing: dispensing,
                            chargeSheetId: this.currentChargeSheetId,
                            sameTimeDispensing: this.sameTimeDispensing,
                        },
                    },
                });

                return {
                    chargeSheetType: this.postData?.type,
                    isPrescriptionCharge: this.postData?.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT, // 是否为处方外购收费单
                    expectedAdjustmentFee: this.postData ? this.postData.expectedAdjustmentFee : undefined, // 用户修改金额后的 期望议价
                    expectedTotalFee: this.postData ? this.postData.expectedTotalFee : undefined, // 用户修改金额后的 期望议价
                    expectedOddFee: this.expectedOddFee || null, // 用户修改抹零后的 期望抹零
                    receivableFee: this.receivableFee,
                    dispensing,
                    chargeComment: this.chargeComment,
                    combinedPayItems,
                    chargeOweSheets: this.curChargeOweSheets,
                    specifiedChargedTime: this.specifiedChargedTime,
                    shebaoCardInfo: this.postData?.shebaoCardInfo,
                };
            },

            handleClickConfirm(options) {
                const { isFundSettle } = options || {};
                this.confirm(false, isFundSettle);
            },
            async asyncCollectionTraceCodeDialog(formItems, isSelectedSocialPay = false) {
                return new Promise((resolve, reject) => {
                    this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                        formItems,
                        requiredTraceCode: this.traceCodeCollectionCheck > 0,
                        needInitValidate: false,
                        patientOrderId: this.postData?.patientOrderId,
                        sceneType: SceneTypeEnum.CHARGE,
                        isEnableTraceTempSave: this.isEnableTraceTempSave,
                        isDisabledActualCount: this.isDisabledActualCount,
                        isSelectedSocialPay,
                        onConfirm: async (_,action) => {
                            await this.saveTraceCodeHandler(formItems);
                            if (action === 'tempSave') {
                                reject();
                            } else {
                                resolve();
                            }
                        },
                        onClose: () => {
                            reject();
                        },
                    });
                    this._collectionTraceCodeDialog.generateDialogAsync();
                });
            },
            async saveTraceCodeHandler(formItems) {
                // 已收情况下，需要仅保存追溯码
                if (!this.currentChargeSheetId) return;
                if (this.currentChargeStatus <= ChargeStatusEnum.UN_CHARGE) return;
                try {
                    await ChargeAPI.saveTraceCode(this.currentChargeSheetId, {
                        list: formItems.map((item) => {
                            return {
                                id: item.id,
                                shebaoDismountingFlag: item.shebaoDismountingFlag,
                                traceableCodeRule: item.traceableCodeRule,
                                traceableCodeList: item.traceableCodeList ? item.traceableCodeList.map((code) => {
                                    const {
                                        count, ...restCode
                                    } = code;
                                    return restCode;
                                }) : [],
                                composeChildren: item.composeChildren && item.composeChildren.map((it) => {
                                    return {
                                        id: it.id,
                                        shebaoDismountingFlag: it.shebaoDismountingFlag,
                                        traceableCodeRule: it.traceableCodeRule,
                                        traceableCodeList: it.traceableCodeList ? it.traceableCodeList.map((code) => {
                                            const {
                                                count, ...restCode
                                            } = code;
                                            return restCode;
                                        }) : [],
                                    };
                                }),
                            };
                        }),
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            async validateTraceCode(isSelectedSocialPay = false, postData = {}) {
                if (this.postData?.chargeForms?.length) {
                    const shouldValidateSocialPay = window.$abcSocialSecurity.config.isSichuanChengdu ? isSelectedSocialPay : false;
                    // 开启了追溯码采集，设置在收费前提醒
                    const needTraceCodeFormItems = getTraceCodeChargeItems(this.postData.chargeForms);
                    if (
                        this.traceCodeCollectionSwitch &&
                        this.traceCodeCollectionCheck === CollectionTraceCodeCheck.charge &&
                        needTraceCodeFormItems.length &&
                        typeof this.validateTraceCodeFn === 'function'
                    ) {
                        // 只有成都地区医保支付需要校验实采是 0 的情况
                        const traceCodeValidate = await this.validateTraceCodeFn(shouldValidateSocialPay);
                        if (!traceCodeValidate) {
                            return false;
                        }
                    }
                    // 开启了追溯码采集，设置在发药前提醒，开启了同时发药
                    if (
                        this.traceCodeCollectionSwitch &&
                        this.traceCodeCollectionCheck === CollectionTraceCodeCheck.dispensing &&
                        postData.dispensing
                    ) {
                        const res = await TraceCode.validate({
                            scene: TraceCodeScenesEnum.PHARMACY,
                            sceneType: SceneTypeEnum.CHARGE,
                            dataList: TraceCode.getFlatItems(needTraceCodeFormItems),
                            needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckPharmacy(),
                            patientOrderId: this.postData?.patientOrderId,
                        });
                        if (needTraceCodeFormItems?.length && !res.flag) {
                            try {
                                await this.asyncCollectionTraceCodeDialog(needTraceCodeFormItems, shouldValidateSocialPay);
                            } catch (e) {
                                return false;
                            }
                        }
                    }
                    if (isSelectedSocialPay && (postData.dispensing || this.isNeedCheckDispensingBatch)) {
                        await this.getBatchWhenDispensing();
                    }
                }
                return true;
            },
            /** W
             * desc [提交到上层确认]
             */
            async confirm(showConfirm = false, isFundSettle) {
                if (this.btnLoading === true) return;
                this.btnLoading = true;


                // 判断是否第三方卡支付,是的话需要在下面生成 postData 之前获取第三方卡密码
                const isThirdCardPay = await ThirdCommonPay.isThirdCardPay(this.chargeData);
                if (isThirdCardPay) {
                    const password = await ThirdCommonPay.getThirdCardPassword(this.chargeData);
                    if (!password) {
                        this.btnLoading = false;
                        return;
                    }
                    this.chargeData.thirdPartyPayCardPassword = password || '';
                }

                const postData = this.getPostData();
                // 记录同时发药，后续收费完成后，如果勾选了同时发药需要去查询发药单是否成功
                this._isDispensing = postData.dispensing;
                postData.isFundSettle = isFundSettle;
                const { combinedPayItems } = postData;
                const { payMode } = combinedPayItems[0];

                // 仅支付且选择了会员卡进行支付
                if (this.extendPostData && payMode === PayModeEnum.MEMBER_CARD) {
                    Object.assign(postData, { memberId: this.extendPostData.memberId });
                }

                // 医保configs接口拉取失败，不允许医保交易
                if (payMode === PayModeEnum.SOCIAL_CARD && this.$abcSocialSecurity.isInitFinish === false) {
                    this.showMessage({
                        type: 'warn',
                        title: '收费失败',
                        content: ['医保入账失败，请点击确定后重新尝试'],
                        showFooter: true,
                        showConfirm: true,
                        onConfirm: () => location.reload(),
                    });
                    this.btnLoading = false;
                    return;
                }

                // 判断是否支持ABC支付
                const supportABCPay = await ABCPay.supportABCPay(payMode);
                // 判断是否支持会员卡密码支付
                const supportMemberPay = await MemberPasswordPay.supportMemberPasswordPay(payMode);
                if (supportMemberPay) {
                    // 校验追溯码
                    const isValidate = await this.validateTraceCode(this.includeSocialPay, postData);
                    if (!isValidate) {
                        this.btnLoading = false;
                        return;
                    }
                    await this.memberPasswordPayHandler(postData);
                } else if (this.isSelectedSocialPay) {
                    // 校验追溯码
                    const isValidate = await this.validateTraceCode(true, postData);
                    if (!isValidate) {
                        this.btnLoading = false;
                        return;
                    }
                    await this.socialCardPayHandler(postData);
                } else if (this.isRailwaySocialPay) {
                    // 校验追溯码
                    const isValidate = await this.validateTraceCode(this.includeSocialPay, postData);
                    if (!isValidate) {
                        this.btnLoading = false;
                        return;
                    }
                    await this.socialCardPayHandler(postData);
                } else if (supportABCPay) {
                    await this.ABCPayHandler(postData);
                } else if (ThirdCommonPay.isThirdCardPay(this.chargeData)) {
                    // 校验追溯码
                    const isValidate = await this.validateTraceCode(this.includeSocialPay, postData);
                    if (!isValidate) {
                        this.btnLoading = false;
                        return;
                    }
                    await this.thirdCardPayHandler(postData);
                } else {
                    // 是否展示二次收费弹窗
                    if (showConfirm) {
                        this.$refs['input-money']?.$el?.querySelector('input')?.blur();
                        this.confirmDialog = true;
                    } else {
                        // 校验追溯码
                        const isValidate = await this.validateTraceCode(this.includeSocialPay, postData);
                        if (!isValidate) {
                            this.btnLoading = false;
                            return;
                        }
                        await this.normalPayHandler(postData);
                    }
                }
                this.btnLoading = false;
            },

            /**
             * @desc 普通记账支付
             * <AUTHOR>
             * @date 2021-09-26 10:55:46
             */
            async normalPayHandler(postData) {
                const { combinedPayItems } = postData;
                const { amount } = combinedPayItems[0];
                // 记账支付
                this.showMessage({
                    type: 'loading',
                    title: `收费入账 ${this.$t('currencySymbol')} ${moneyNum(amount)}`,
                });
                try {
                    const result = await this._chargeService.submit(postData, this.onPartChargeSuccess);
                    this.chargeSuccessHandler(result);
                } catch (err) {
                    this.chargeErrorHandler(err);
                }
            },

            /**
             * @desc 第三方通用支付处理
             * <AUTHOR>
             * @date 2024-01-22 09:54:29
             */
            async thirdCardPayHandler(postData) {
                const { combinedPayItems } = postData;
                const { amount } = combinedPayItems[0];
                // 记账支付
                this.showMessage({
                    type: 'loading',
                    title: `收费入账 ${this.$t('currencySymbol')} ${moneyNum(amount)}`,
                });
                try {
                    const result = await this._chargeService.submit(postData, this.onPartChargeSuccess);
                    const { payStatus } = result;
                    if (payStatus === OrderPayStatus.SUCCESS) {
                        this.chargeSuccessHandler(result);
                    } else {
                        this.startQueryChargeStatus(result);
                    }
                } catch (err) {
                    this.chargeErrorHandler(err);
                }
            },

            /**
             * 当点击切换收费模式时
             * <AUTHOR>
             * @date 2022-07-12
             */
            onClickSwitchChargeMode(val) {
                this.socialChargeMode = val;
                this.$abcSocialSecurity.setCacheChargeMode(this.socialChargeMode);
            },


            /**
             * 处理刷卡结算
             * <AUTHOR>
             * @date 2022-05-16
             * @param {Object} postData
             * @param {Object} options
             * @returns {Promise}
             */
            async socialCardPayHandler(postData, options) {
                try {
                    const { combinedPayItems } = postData;
                    const { amount } = combinedPayItems[0];
                    console.log('amount', amount);
                    if (!this.isRailwaySocialPay) {
                        this.showMessage({
                            type: 'loading',
                            title: '正在调起医保支付端...',
                        });
                    }
                    const result = await this._chargeService.submit(postData);
                    // 服务器已经下单，走后续医保支付需要提前让零售收费单 响应更新ql更新chargeSheetId
                    this.onPartChargeSuccess && this.onPartChargeSuccess(result);

                    let chargeDataPayMode = this.chargeData.payMode;
                    if (options?.payMode) {
                        chargeDataPayMode = options.payMode;
                    }

                    const params = {
                        shebaoCardInfo: postData.shebaoCardInfo,
                        isPrescriptionCharge: postData.isPrescriptionCharge, // 是否外购处方
                        isScanCodeChargeMode: this.isScanCodeChargeMode, // 是否扫码支持
                        isAirPay: chargeDataPayMode === PayModeEnum.AIR_PAY, // 是否空中支付
                        isRailwaySocialPay: this.isRailwaySocialPay, // 是否铁路医保支付
                        receivableFee: postData.receivableFee,
                        taskId: result.thirdPartyPayTaskId,
                        ...(this.chargeData.socialParams || {}),
                        payMode: chargeDataPayMode,
                        isFundSettle: postData.isFundSettle,
                        chargeSheetType: postData.chargeSheetType,
                        dispensing: postData.dispensing, // 同时发药
                    };
                    const tradeResponse = await this.$abcSocialSecurity.trade(params, (eventName) => {
                        if (eventName === 'ping-success' && !this.isRailwaySocialPay) {
                            const options = {
                                type: 'loading',
                                title: '等待刷卡结果',
                                content: ['请确保医保专网可正常使用'],
                                showFooter: true,
                                showCancel: true,
                                onCancel: () => {},
                            };
                            if (this.isScanCodeChargeMode) {
                                Object.assign(options, {
                                    title: '正在生成二维码',
                                    content: [],
                                });
                            }
                            this.showMessage(options);
                        }
                    });
                    if (tradeResponse.status === false) {
                        if (
                            !tradeResponse.message || // 无报错原因
                            tradeResponse.isUserCancel === true // 用户主动取消
                        ) {
                            this.closeMessage();
                            this.visible = false;
                        } else if (tradeResponse.isSocialError) {
                            await this.$abcSocialSecurity.showSocialErrorMessage({
                                title: '医保支付失败',
                                response: tradeResponse,
                            });
                        } else {
                            this.showMessage({
                                type: 'warn',
                                title: '收费失败',
                                content: [ tradeResponse.message ],
                                showFooter: true,
                                showConfirm: true,
                                onConfirm: () => {
                                    this.visible = false;
                                },
                            });
                        }
                        await this.handleZeroCancel({
                            ...result,
                            chargeSheetId: result.id,
                        });
                        if (typeof this.cancelPayCallback === 'function') {
                            this.cancelPayCallback();
                        }
                        return;
                    }
                    this.showMessage({
                        type: 'loading',
                        title: '查询支付结果',
                        content: ['订单创建成功，等待社保支付成功'],
                    });
                    await this.handleZeroCancel({
                        ...result,
                        chargeSheetId: result.id,
                    });
                    await AbcChargeService.queryChargeStatus(result);
                    this.chargeSuccessHandler(result);
                } catch (error) {
                    this.chargeErrorHandler(error);
                }
            },

            /**
             * @desc ABC支付
             * <AUTHOR>
             * @date 2021-09-26 17:55:08
             */
            async ABCPayHandler(postData) {
                const { combinedPayItems } = postData;
                const { amount } = combinedPayItems[0];
                this._ABCPay = ABCPay.generateABCPay({
                    $el: this.$el.querySelector('.charge-dialog-wrapper .abc-dialog'),
                    receivableFee: amount,
                });

                const qrCode = await this._ABCPay.getQrCodeAsync();
                if (!qrCode) {
                    this._timer && clearInterval(this._timer);
                    return;
                }

                try {
                    combinedPayItems[0].paySubMode = PaySubModeEnum.ABC_PAY_SCAN_QR_CODE;
                    combinedPayItems[0].authCode = qrCode;

                    // 如果是一码付，需要校验是否是支付宝的一码付，同时还需要判断患者是否签约了一码付，如果都满足，则使用一码付 调用医保小端
                    if (this.$abcSocialSecurity?.isShowOneCodePay && !this.$abcSocialSecurity.isDisabledSocial) {

                        // 医保电子凭证 校验患者一码付是否签约，非医保电子凭证，进入通联支付
                        const isAliPaySocialQrCode = await this.isAlipayQrCode(qrCode);
                        if (isAliPaySocialQrCode) {
                            const isSupportAlipay = await this.queryAlipayQrCodeStatus(qrCode);
                            if (!isSupportAlipay) {
                                this.$alert({
                                    type: 'warn',
                                    title: '患者没开通“一码付”功能',
                                    content: ['请引导患者开通后使用 或 选择【医保】进行结算。'],
                                    onConfirm: () => {
                                        this._ABCPay.destroy();
                                        this._ABCPay = null;
                                    },
                                });
                                return;
                            }

                            if (isSupportAlipay) {
                                await this.yimafuPreCheckHandler(postData);
                                return;
                            }
                        }
                    }

                    // 校验追溯码
                    const isValidate = await this.validateTraceCode(this.includeSocialPay, postData);
                    if (!isValidate) {
                        return;
                    }

                    const result = await this._chargeService.submit(postData, this.onPartChargeSuccess);
                    const { payStatus } = result;
                    if (payStatus === OrderPayStatus.SUCCESS) {
                        this.chargeSuccessHandler(result);
                    } else {
                        this.startQueryChargeStatus(result);
                    }
                } catch (err) {
                    this.chargeErrorHandler(err);
                }
            },

            async yimafuPreCheckHandler(postData) {

                this.yimafuPostData = postData;

                // 使用医保支付方式算费
                await this.calcFee(PayModeEnum.SOCIAL_CARD);

                // 没有弹出费用变化的弹窗，直接调用一码付
                if (!this.showFeeChange) {
                    this.yimafuPayHandler();
                }
            },
            /**
             * @desc: 判断是否是支付宝的医保电子凭证
             * @author: ff
             * @time: 2025/5/23
             */
            async isAlipayQrCode(qrCode) {
                const data = await this.$abcSocialSecurity?.isAliPayChannel(qrCode);
                return data;
            },
            /**
             * @desc: 查询一码付签约状态
             * @author: ff
             * @time: 2025/5/23
             */
            async queryAlipayQrCodeStatus(qrCode) {
                const data = await this.$abcSocialSecurity?.getOneCodeCheckStatus({
                    qrCode,
                });
                return data?.status;
            },
            /**
             * @desc: 一码付
             * @author: ff
             * @time: 2025/5/26
             */
            async yimafuPayHandler() {
                if (this.chargeData.payMode === PayModeEnum.ABC_PAY) {
                    this.yimafuPostData.combinedPayItems[0].payMode = PayModeEnum.SOCIAL_CARD;
                    this.yimafuPostData.combinedPayItems[0].amount = Number(this.chargeData.money);
                    this.yimafuPostData.receivableFee = this.receivableFee;
                    const postData = this.getPostData();
                    // 校验追溯码
                    const isValidate = await this.validateTraceCode(true, postData);
                    if (!isValidate) {
                        this.showFeeChange = false;
                        return;
                    }
                    await this.socialCardPayHandler(this.yimafuPostData, {
                        payMode: PayModeEnum.SOCIAL_CARD,
                    });

                    Logger.report({
                        scene: 'charge_yimafu',
                        data: {
                            info: '医保一码付支付',
                            scene: 'yimafuPayHandler',
                            data: {
                                chargeSheetId: this.chargeSheetId,
                                yimafuPostData: this.yimafuPostData,
                                chargeData: this.chargeData,
                            },
                        },
                    });
                    this.showFeeChange = false;
                } else {
                    this.showFeeChange = false;
                    await this.confirm();
                }
            },
            /**
             * @desc: 取消一码付
             * @author: ff
             * @time: 2025/5/26
             */
            async handleCancelYimafuPay() {
                this.yimafuPostData = null;
                if (this.chargeData.payMode === PayModeEnum.ABC_PAY) {
                    await this.calcFee(this.chargeData.payMode);

                    if (this._ABCPay) {
                        this._ABCPay.destroy();
                        this._ABCPay = null;
                    }
                }
                this.showFeeChange = false;
            },

            /**
             * @desc 会员卡支付
             * <AUTHOR>
             * @date 2021-09-27 10:40:55
             * @update 2025-06-11 使用新的generateMemberPasswordV2方法
             */
            async memberPasswordPayHandler(postData) {
                try {
                    const { combinedPayItems } = postData;
                    const { amount } = combinedPayItems[0];

                    // 使用新的generateMemberPasswordV2方法
                    if (this._memberPasswordDialog) {
                        this._memberPasswordDialog.destroy();
                        this._memberPasswordDialog = null;
                    }

                    this._memberPasswordDialog = MemberPasswordPay.generateMemberPasswordV2({
                        $el: this.$el.querySelector('.charge-dialog-wrapper .abc-dialog'),
                        memberInfo: this.memberInfo,
                        amount,
                        onClose: () => {
                            console.log('会员卡密码验证已取消');
                        },
                    }, this);

                    const password = await this._memberPasswordDialog.getPasswordAsync();

                    if (!password) {
                        return;
                    }

                    this.showMessage({
                        type: 'loading',
                        title: '会员卡支付中',
                    });
                    combinedPayItems[0].memberCardPassword = password;
                    const result = await this._chargeService.submit(postData, this.onPartChargeSuccess);
                    this.chargeSuccessHandler(result);
                } catch (err) {
                    this.chargeErrorHandler(err);
                } finally {
                    if (this._memberPasswordDialog) {
                        this._memberPasswordDialog.destroy();
                        this._memberPasswordDialog = null;
                    }
                }
            },

            startQueryChargeStatus(result) {
                clearInterval(this._timer);
                this._timer = setInterval(async () => {
                    try {
                        await AbcChargeService.queryChargeStatus(result);
                        this.chargeSuccessHandler(result);
                        clearInterval(this._timer);
                    } catch (err) {
                        clearInterval(this._timer);
                        this.chargeErrorHandler(err);

                    }
                } , 1000);
            },

            async queryDispensingStatus(chargeSheetId, queryCount = 1) {
                try {
                    const { data } = await DispensaryAPI.getDispenseStatus(chargeSheetId);
                    const {
                        status,
                    } = data;

                    if (status === 1 || queryCount >= 5) {
                        clearTimeout(this._queryDispensingTimer);
                        return;
                    }

                    queryCount++;

                    clearTimeout(this._queryDispensingTimer);
                    this._queryDispensingTimer = setTimeout(() => {
                        this.queryDispensingStatus(chargeSheetId, queryCount);
                    }, 1000);
                } catch (err) {
                    clearTimeout(this._queryDispensingTimer);
                    this.dispensingTipsHandler(err);
                    const { code } = err || {};
                    // 发药单未生成
                    if (code === 84109) {
                        if (queryCount >= 5) return;
                        queryCount++;
                        this.queryDispensingStatus(chargeSheetId, queryCount);
                    }
                }
            },
            dispensingTipsHandler(err) {
                const {
                    code,
                } = err || {};
                // 收费成功发药失败才提示
                if (code === 84108) {
                    let { name } = this.postData.patient || {};
                    name = name ? `【${name}】` : '';
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: `患者${name}同时发药失败。请前往药房发药`,
                    });
                } else {
                    console.error(err);
                }
            },

            /**
             * @desc 取消0元收费单
             * <AUTHOR>
             * @date 2021-09-26 11:13:37
             */
            async handleZeroCancel(result) {
                const {
                    combineOrderPayTransactionId, chargeType, chargePayTransactionId,
                } = result || {};
                if (chargePayTransactionId || combineOrderPayTransactionId) {
                    // 在收费单已创建时，尝试零元取消单子
                    try {
                        let res;
                        if (chargeType === ChargeType.HOSPITAL || chargeType === ChargeType.REPAYMENT) {
                            res = await ChargeAPI.putChargeCancel(combineOrderPayTransactionId);
                        } else {
                            res = await ChargeAPI.cancelPayByTransaction(chargePayTransactionId);
                        }
                        const { data } = res;
                        if (data?.code === 200) {
                            // 在收费处收门诊收费过程中，调起医保支付端，然后在取消支付，在进行收费时，无法收费,此时需要重置dataSignature
                            this.postData.dataSignature = data.dataSignature;
                            this.currentIsReplay = false;
                            if (this._chargeService) {
                                this._chargeService.isReplay = false;
                            }
                        }
                    } catch (error) {
                        console.log('handleZeroCancel error', error);
                    }
                }
            },

            /**
             * @desc 反写chargeItemId
             * <AUTHOR> Yang
             * @date 2024-10-28 16:55:05
            */
            setChargeFormItemId(newForms) {
                try {
                    newForms.forEach((newForm) => {
                        const resForm = this.postData.chargeForms.find((form) => newForm.keyId && newForm.keyId === form.keyId);
                        if (resForm) {
                            resForm.id = newForm.id;
                            newForm.chargeFormItems?.forEach((newItem) => {
                                const resItem = resForm.chargeFormItems?.find((item) => newItem.keyId && newItem.keyId === item.keyId);
                                if (resItem) {
                                    resItem.id = newItem.id;
                                }
                                newItem.composeChildren?.forEach((newChild) => {
                                    const resChild = resItem?.composeChildren?.find((child) => newChild.keyId && newChild.keyId === child.keyId);
                                    if (resChild) {
                                        resChild.id = newChild.id;
                                    }
                                });
                            });
                        }
                    });
                } catch (e) {
                    console.error('回写id失败，不做任何处理', e);
                }
            },

            /**
             * @desc  部分收费成功，不关闭弹窗，更新弹窗内相关数据
             * @desc  收费完全成功需要关闭弹窗
             * <AUTHOR>
             * @date 2021-09-26 11:10:15
             */
            chargeSuccessHandler(result) {
                this.confirmDialog = false;
                this.btnLoading = false;

                if (!this.currentChargeSheetId && result.chargeForms) {
                    this.setChargeFormItemId(result.chargeForms);
                }

                this.currentChargeSheetId = result.id;
                if (this._chargeService) {
                    this._chargeService.chargeSheetId = result.id;
                }

                if (this._ABCPay) {
                    this._ABCPay.successHandler();
                    this._ABCPay = null;
                    this.closeMessage();
                } else {
                    this.showMessage({
                        type: 'success',
                        title: '收费成功',

                    }, 1000);
                }
                // 部分收费状态，不关闭弹窗 重置弹窗数据
                if (result.needPay) {

                    this.currentChargeStatus = ChargeStatusEnum.PART_CHARGED;
                    if (this._chargeService) {
                        this._chargeService.chargeStatus = ChargeStatusEnum.PART_CHARGED;
                    }

                    if (this.chargeData.payMode === PayModeEnum.MEMBER_CARD) {
                        // 当选择的是会员卡支付，支付完成需要重新拉取会员余额
                        this.getMemberTotal();
                    }
                    if (this.canPaidPatientCards && this.canPaidPatientCards.length) {
                        // 营销卡支付，需要更新营销卡余额
                        this.queryPatientCardByChargeSheetId();
                    }
                    if (this.chargeData.type === PayModeTypeEnum.THIRD_COMMON) {
                        this.queryThirdCommonPayInfo();
                    }
                    this.receivableFee = result.needPay;
                    this.onPartChargeSuccess && this.onPartChargeSuccess(result);
                    this.chargeData = null;
                    this.curChargeTransactions = result.chargeTransactions || [];
                } else {

                    this.currentChargeStatus = ChargeStatusEnum.CHARGED;
                    if (this._chargeService) {
                        this._chargeService.chargeStatus = ChargeStatusEnum.CHARGED;
                    }

                    this.cachePrintOpt();

                    // 全部收费成功,查询开票结果
                    if (store.state.invoice.writeInvoiceConfig.enableAutoBill) {
                        autoDestroyInvoice(result.chargeTransactionId);
                    }

                    this.$Toast({
                        message: '收费成功',
                        type: 'success',
                    });
                    this.closeScene = 'success';
                    // 收费成功如果收费弹窗中有 内部弹窗 则需要延时一秒关闭
                    if (this._ABCPay) {
                        this.timeoutId = setTimeout(() => {
                            this.visible = false;
                        }, 1000);
                    } else {
                        this.visible = false;
                    }
                    this.onChargeSuccess && this.onChargeSuccess(result);

                    if (this._isDispensing) {
                        this.queryDispensingStatus(result.id);
                    }
                }
            },


            /**
             * @desc 收费异常处理
             * <AUTHOR>
             * @date 2021-09-26 11:15:42
             */
            chargeErrorHandler(err) {
                this.btnLoading = false;
                if (this._ABCPay) {
                    this._ABCPay.destroy();
                    this._ABCPay = null;
                }
                if (!err) {
                    this.closeMessage();
                    return;
                }

                const {
                    code, message,
                } = err || {};
                let content = '';
                if (code === 17013 || code === 17050 || code === 17051) {
                    content = message || '药品信息发生变化，请重新收费';
                } else if (code === 25030) {
                    content = '该订单已使用医保卡支付，请改用其他支付方式';
                } else if (code === 17090) {
                    content = '空中药房 快递费发生改变';
                } else {
                    content = message;
                }
                this.onChargeError && this.onChargeError(err);
                if (this.cancelPayCallback && code === CancelOrderEnum.CODE && message === CancelOrderEnum.MSG) {
                    this.cancelPayCallback(err);
                    this.closeMessage();
                    return;
                }
                this.showMessage({
                    type: 'warn',
                    title: '收费失败',
                    content: [content],
                    showFooter: true,
                    showConfirm: true,
                    onConfirm: () => {
                        this.visible = false;
                    },
                });
            },

            validateMoney(_, callback) {
                if (this.inputError.error) {
                    return callback({
                        validate: false,
                        message: this.inputError.message,
                    });
                }
                return callback({
                    validate: true,
                });
            },

            /**
             * desc [当输入值改变时/聚焦时]
             */
            inputMoneyInput() {
                const target = this.chargeData;
                if (target === null) {
                    // 没有选择支付方式时，不处理
                    return;
                }
                // 首先清空错误提示
                this.inputError = {
                    error: false,
                    message: '',
                };
                const isXJ = target.payMode === PayModeEnum.CASH_PAY;
                // 优先判断不包含现金时（因现金找零）
                if (!isXJ && this.giveChange < 0) {
                    // 不包含现金同时又有找零（即超过应付）
                    this.inputError = {
                        error: true,
                        message: '超出应付金额',
                    };
                }
                // 会员收费不能超过余额
                const isHY = target.payMode === PayModeEnum.MEMBER_CARD;
                if (isHY && parseFloat(target.money) > this.memberTotal) {
                    // 金额大于余额
                    this.inputError = {
                        error: true,
                        message: '会员卡余额不足',
                    };
                }
                if (target.payMode === PayModeEnum.SOCIAL_CARD && this.$abcSocialSecurity.isOpenSocial) {
                    // 医保支付，金额不能为0
                    if (parseFloat(target.money) < 0) {
                        // 存在医保支付，且支持刷卡操作
                        this.inputError = {
                            error: true,
                            message: '支付金额不能为负数',
                        };
                    } else if (this.chargeType === ChargeType.NORMAL && !this.isCharged && parseFloat(target.money) > this.sheBaoReceivableFee) {
                        this.inputError = {
                            error: true,
                            message: `支付金额不能超过${this.sheBaoReceivableFee}元`,
                        };
                    }
                }
            },
            /**
             * desc [失去输入焦点时，格式化金额]
             */
            blurMoneyInput() {
                if (!this.chargeData) return;
                const { money } = this.chargeData;
                this.chargeData.money = moneyNum(money);
                this.inputMoneyInput();
            },


            keydownHandle(event) {
                if (this.btnLoading || this.disabledBtn) return;

                const KEY_ENTER = 13;
                if (event.keyCode === KEY_ENTER) {
                    if (this._showMessage || this.visibleDialogScanCode || this.showFeeChange || this.showYimafuOperationModal) return;
                    this.$refs['input-money']?.$el?.querySelector('input')?.blur();
                    /**
                     * @desc 二次确认弹窗打开收费，只响应 回车事件
                     * <AUTHOR>
                     * @date 2021-09-18 14:17:01
                     */
                    if (this.confirmDialog) {
                        this.domEvent(event);
                        this.confirmDialog = false;
                        this.confirm();
                    } else {
                        this.confirm(true);
                    }

                }
            },
            domEvent(event) {
                event.cancelBubble = true;
                event.returnValue = false;
                if (event.preventDefault) event.preventDefault();
                if (event.stopPropagation) event.stopPropagation();
            },

            /**
             * @desc 填写备注时不响应快捷方式
             * <AUTHOR>
             * @date 2019/11/21 14:05:47
             */
            focusComment() {
                this._disabledKeydown = true;
            },
            blurComment() {
                this._disabledKeydown = false;
            },

            /**
             * @desc 判断是否展示自主抹零, 实收发生改变时候需要判断是否有小数展示抹零相关按钮
             * <AUTHOR> Yang
             * @date 2021-04-14 19:27:04
             */
            selfOddFeeHandler(receivableFee) {
                if (!this.isSystemAutoOdd) {
                    const _arr = receivableFee.toString().split('.');
                    this.receivableFeeHasOdd = _arr[1] && +_arr[1] > 0;
                }
            },
            handleFloor() {
                const { receivableFee } = this;
                this.receivableFee = Math.floor(receivableFee);
                this.expectedOddFee = red(this.receivableFee, receivableFee);
                this.refreshChargeInputItem(true);
                this.calcFee();
            },
            handleCeil() {
                const { receivableFee } = this;
                this.receivableFee = Math.ceil(receivableFee);
                this.expectedOddFee = red(this.receivableFee, receivableFee);
                this.refreshChargeInputItem(true);
                this.calcFee();
            },
            handleCancelOddFee() {
                this.receivableFee = red(this.receivableFee, this.expectedOddFee);
                this.expectedOddFee = 0;
                this.refreshChargeInputItem(true);
                this.calcFee();
            },


            /**
             * @desc 部分收费
             * <AUTHOR>
             * @date 2021-08-11 19:55:59
             * @params
             * @return
             */
            async queryPatientCardByChargeSheetId() {
                if (!this.currentChargeSheetId || this.disabledPatientCardPay) return;
                try {
                    const { data } = await ChargeAPI.queryPatientCardByChargeSheetId(this.currentChargeSheetId);
                    this.canPaidPatientCards = data.patientCards || [];
                } catch (err) {
                    this.canPaidPatientCards = [];
                    this.visible = false;
                    this.onChargeError(err);
                } finally {
                    this.dialogContentLoading = false;
                }
            },

            /**
             * @desc 更新通用第三方支付余额
             * <AUTHOR> Yang
             * @date 2024-12-11 13:46:03
            */
            async queryThirdCommonPayInfo() {
                const {
                    thirdPartyPayCardId,
                    payMode,
                    paySubMode,
                } = this.chargeData;
                if (!thirdPartyPayCardId) return;
                try {
                    const { data } = await PayAPI.getPayInfo({
                        cardId: thirdPartyPayCardId,
                        payMode,
                        paySubMode,
                    });
                    const {
                        balance,
                        cardId,
                    } = data;
                    const $payModeList = this.$refs.payModeList;
                    if (cardId && $payModeList?.thirdPartyPayCardId === cardId) {
                        $payModeList.thirdCardBalance = balance;
                    }
                } catch (e) {
                    console.error(e);
                }
            },

            showMessage(options, closeDelay = 0) {
                this.closeMessage();
                const defaultOption = {
                    customClass: 'charge-message-dialog',
                    referenceEl: this.$el.querySelector('.charge-dialog-wrapper .abc-dialog'),
                    dialogType: 'tiny',
                    contentStyles: 'max-height: 300px',
                    type: '',
                    title: '',
                    content: [],
                    showFooter: false,
                    showClose: false,
                    showCancel: false,
                    showConfirm: false,
                    onConfirm: () => {},
                    noDialogAnimation: true,
                    confirmText: '确定',
                    onCancel: () => {},
                    cancelText: '取消',
                };
                this._messageInstance = this.$message(Object.assign(defaultOption, options));
                this._showMessage = true;
                if (closeDelay) {
                    const _timer = setTimeout(() => {
                        this._messageInstance.close();
                        this._showMessage = false;
                        clearTimeout(_timer);
                    }, closeDelay);
                }
            },
            closeMessage() {
                if (this._messageInstance) {
                    this._messageInstance.close();
                    this._messageInstance = null;
                    this._showMessage = false;
                }
            },

            destroyElement() {
                this._timer && clearInterval(this._timer);
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },

            changeMeanwhilePrintHandler() {
                if (!this.printOpt.finishSelect || !this.printOpt.finishSelect.length) {
                    this.meanWhileBottomExpand = true;
                }
            },

            // 同时发药配置
            onDispensingCheckChange(val) {
                if (this.isDaijianCenterDispensing) {
                    val = 0;
                }
                this.pcStoreVm?.dispatch('updateClinicEmployeeSetting',{
                    paid: {
                        sameTimeDispensing: val,
                    },
                });
            },
            judgeDateSort(date1, date2) {
                if (date1 && date2 && new Date(date1) < new Date(date2)) {
                    this.$Toast.error('收费时间不得早于开单时间');
                    return false;
                }
                return true;
            },
            handleDateChange(date) {
                this.defaultSpecifiedChargedDate = date;
                this.curSpecifiedChargedDate = date;

                this.judgeDateSort(`${date} ${this.defaultSpecifiedChargedTime}`, this.chargeSheetSummary.diagnosedDate);
            },
            handleTimeChange(time) {
                this.defaultSpecifiedChargedTime = time;
                this.curSpecifiedChargedTime = time;

                this.judgeDateSort(`${this.defaultSpecifiedChargedDate} ${time}`, this.chargeSheetSummary.diagnosedDate);
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: this.scene }).generateDialogAsync({ parent: this });
            },

            async cancelPay(chargePayTransactionId) {
                try {
                    await ChargeAPI.cancelPayByTransaction(chargePayTransactionId);
                } catch (err) {
                    Logger.error({
                        scene: 'cancelRefund',
                        err,
                    });
                }
            },
            timePickerDisabledFn (_, value, type) {
                if (!this.chargeSheetSummary.diagnosedDate) return false;
                const [diagnosedDate, diagnosedTime] = parseTime(new Date(this.chargeSheetSummary.diagnosedDate), 'y-m-d h:i', true).split(' ');
                const [diagnosedHour, diagnosedMinute] = diagnosedTime.split(':');
                if (type === 'hour') {
                    if (this.defaultSpecifiedChargedDate && this.defaultSpecifiedChargedDate === diagnosedDate) {
                        return value < parseInt(diagnosedHour);
                    }
                    return false;
                }
                if (!this.defaultSpecifiedChargedTime) return false;
                const [selectHour] = this.defaultSpecifiedChargedTime.split(':');
                if (
                    this.defaultSpecifiedChargedDate &&
                    this.defaultSpecifiedChargedDate === diagnosedDate &&
                    parseInt(selectHour) === parseInt(diagnosedHour)
                ) {
                    return value < parseInt(diagnosedMinute);
                }
                return false;
            },
            handleOneCodePayOpen() {
                this.showYimafuOperationModal = true;
                this.$abcPlatform.service.track.report({
                    key: BusinessKeyMap.CLK_ONE_CODE_PAY_IN_CHARGE.key,
                    extendData: {
                        id: this.userInfo.id,
                        name: this.userInfo.name,
                        roleIds: this.userInfo.roleIds,
                        roleNames: this.userInfo.roleNames,
                        mobile: this.userInfo.mobile,
                        isAdmin: this.userInfo.isAdmin,
                    },
                });
                this._oneCodePayOperationModal = new OneCodePayOperationModal({
                    onConfirm: () => {
                        this.showYimafuOperationModal = true;
                        this._oneCodePayOpenModal = new OneCodePayOpenModal({
                            onClose: () => {
                                this._oneCodePayOpenModal = null;
                                this.showYimafuOperationModal = false;
                            },
                        }).generateDialogAsync({ parent: this });
                    },
                    onClose: () => {
                        this._oneCodePayOperationModal = null;
                        this.showYimafuOperationModal = false;
                    },
                }).generateDialogAsync({ parent: this });
            },

            getBatchWhenDispensing() {
                // eslint-disable-next-line no-async-promise-executor
                return new Promise(async (resolve,reject) => {
                    const queryGoodsList = [];
                    this.postData.chargeForms.forEach((form) => {
                        form.chargeFormItems.forEach((item) => {
                            const {
                                productInfo = {},pharmacyType,pharmacyNo,keyId,traceableCodeList,unit,unitCount,lockId,
                            } = item;
                            const isChineseMedicineGoods = this.isChineseMedicine(productInfo);
                            const isBigUnit = unit === productInfo?.packageUnit;
                            let countParams = {};
                            if (!isBigUnit || isChineseMedicineGoods) {
                                countParams = {
                                    pieceCount: unitCount,
                                };
                            } else {
                                countParams = {
                                    packageCount: unitCount,
                                };
                            }
                            queryGoodsList.push({
                                pharmacyType,
                                pharmacyNo,
                                stockCheckSuggestBatches: 1,
                                goodsIds: [
                                    {
                                        goodsId: productInfo?.id || productInfo?.goodsId,
                                        keyId,
                                        lockId,
                                        noList: traceableCodeList?.map((code) => code.no) ?? [],
                                        ...countParams,
                                    },
                                ],
                            });
                            item.composeChildren?.forEach((child) => {
                                const {
                                    productInfo = {},pharmacyType,pharmacyNo,keyId,traceableCodeList,unit,unitCount,lockId,
                                } = child;
                                const isChineseMedicineGoods = this.isChineseMedicine(productInfo);
                                const isBigUnit = unit === productInfo?.packageUnit;
                                let countParams = {};
                                if (!isBigUnit || isChineseMedicineGoods) {
                                    countParams = {
                                        pieceCount: unitCount,
                                    };
                                } else {
                                    countParams = {
                                        packageCount: unitCount,
                                    };
                                }
                                queryGoodsList.push({
                                    pharmacyType,
                                    pharmacyNo,
                                    stockCheckSuggestBatches: 1,
                                    goodsIds: [
                                        {
                                            goodsId: productInfo?.id || productInfo?.goodsId,
                                            keyId,
                                            lockId,
                                            noList: traceableCodeList?.map((code) => code.no) ?? [],
                                            ...countParams,
                                        },
                                    ],
                                });
                            });
                        });
                    });
                    if (queryGoodsList.length > 0) {
                        const { list } = await GoodsAPIV3.getChargeFormItemBatchInfos({
                            queryGoodsList,
                        });
                        const filterList = (list ?? []).filter((item) => (item.checkBatchList && item.checkBatchList.length > 0)).map((item) => ({
                            ...item,
                            dispensingFormItemBatches: this.findChargeFormItemBatches(item),
                        }));
                        if (filterList && filterList.length > 0) {
                            await new PharmacyBatchesConfirmDialog({
                                list: filterList,
                                confirmText: '自动盘点并结算',
                                onConfirm: () => {
                                    this.btnLoading = false;
                                    resolve();
                                },
                                onClose: () => {
                                    this.btnLoading = false;
                                    reject();
                                },
                            }).generateDialogAsync({ parent: this });
                        } else {
                            resolve();
                        }
                    } else {
                        resolve();
                    }
                });
            },
            findChargeFormItemBatches(findItem) {
                const formItems = [];
                this.postData?.chargeForms?.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        item.composeChildren?.forEach((child) => {
                            if (child.keyId === item.id) {
                                formItems.push(...(item.chargeFormItemBatchInfos ?? []));
                            }
                        });
                        if (findItem.keyId === item.id) {
                            formItems.push(...(item.chargeFormItemBatchInfos ?? []));
                        }
                    });
                });
                return formItems;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme';
@import 'src/styles/mixin';

@mixin justify() {
    text-align: justify;
    text-align-last: justify;
    text-justify: distribute-all-lines;
}

.charge-dialog-wrapper {
    .abc-dialog {
        width: 400px;

        &.no-position-dialog {
            .abc-dialog-body {
                position: initial;
            }
        }
    }

    .charge-message-dialog {
        width: 240px;
    }

    .AbcDialog-header {
        display: flex;
        align-items: center;
        height: 58px;
        padding: 0 24px;
        background-color: #eff1f5;

        .AbcDialog-headerbtn {
            background-color: #eff1f5;
        }
    }

    .charge-dialog-header {
        position: relative;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 66px;
        margin: 0 24px;
        border-bottom: 1px solid $P6;

        .charge-odd-fee-wrapper {
            position: absolute;
            top: 19px;
            left: 0;
            display: flex;
            align-items: center;
            min-height: 28px;
            font-size: 12px;
            color: $T2;

            .abc-button-blank {
                min-width: 36px;
                height: 20px;
                font-size: 12px;
                color: #626d77;
                border: 1px solid #d3dbe1;
            }
        }

        .receivable-fee {
            font-size: 16px;
            color: $T1;
            text-align: center;

            span {
                color: $Y2;
            }

            b {
                font-size: 24px;
                line-height: 28px;
                color: $Y2;
            }
        }

        .adjustment-fee-wrapper {
            position: absolute;
            top: 19px;
            right: 0;

            .yijia-button {
                height: 28px;
                padding: 0 6px;
                color: #626d77;
                border: 1px solid #d3dbe1;

                &.abc-tipsy--sw::before {
                    right: 40%;
                }

                &.abc-tipsy--sw::after {
                    right: 0;
                }

                &.abc-tipsy--sw:hover::after,
                &.abc-tipsy--sw:focus::after {
                    top: 114%;
                }
            }

            .adjustment-fee {
                position: absolute;
                right: 0;
                bottom: -16px;
                font-size: 12px;
                color: $T2;
            }
        }

        .checked {
            background-color: $P6;
        }

        .abc-button-blank:hover {
            background-color: $P4;
        }

        .cut-line {
            height: 1px;
            background-color: $P6;
        }
    }

    .charge-form {
        padding: 15px 24px;
        background-color: $P5;
        border-top: 1px solid $P6;

        .right-b {
            .abc-input-wrapper {
                border-top-right-radius: var(--abc-border-radius-small);
                border-bottom-right-radius: var(--abc-border-radius-small);
            }

            .is-limit-price {
                position: absolute;
                top: 50%;
                right: 12px;
                z-index: 3;
                color: var(--abc-color-Y2);
                transform: translateY(-50%);
            }

            .give-change-status {
                position: absolute;
                top: 50%;
                right: 6px;
                z-index: 3;
                transform: translateY(-50%);
            }
        }

        .tips {
            margin-top: 4px;
            font-size: 12px;
            line-height: 16px;
            color: var(--abc-color-T2);

            .operate {
                color: var(--abc-color-theme1);
                cursor: pointer;
            }
        }

        .item {
            position: relative;
            display: flex;
            align-items: stretch;
            width: 100%;
            height: 42px;
            border-radius: var(--abc-border-radius-small);

            > label {
                min-width: 98px;
                line-height: 42px;
                color: #626d77;
                text-align: center;
                border: 1px solid $P1;
                border-right: 0;
                border-top-left-radius: var(--abc-border-radius-small);
                border-bottom-left-radius: var(--abc-border-radius-small);
            }

            .abc-input-wrapper {
                flex: 1;
                width: 100%;
                height: 100%;
                overflow: hidden;
                background-color: $S2;

                > input {
                    height: 100%;
                    padding-left: 24px;
                    font-size: 18px;
                    font-weight: bold;
                    border-top-left-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }

            > button {
                min-width: 81px;
                height: 100%;
                color: $theme1;
                background-color: $S2;
                border: 1px solid $P1;
                border-left: 0;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;

                i {
                    width: 14px;
                    height: 14px;
                    margin-right: 8px;
                    color: $theme1;
                }
            }

            .status {
                position: absolute;
                top: 0;
                right: 8px;
                display: flex;
                align-items: center;
                justify-content: right;
                height: 100%;
                font-size: 12px;
            }
        }

        .others {
            margin-top: 12px;

            .result {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 24px;
                color: #626d77;

                .remark {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    color: $T2;
                    cursor: pointer;
                }

                .money {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 16px;

                    .num {
                        margin-left: 8px;
                    }
                }

                .default-specified-charged-time {
                    display: flex;
                    align-items: center;
                    font-size: 12px;

                    .abc-button-text {
                        font-size: 12px;
                        color: $T2;
                    }

                    &.custom-date .abc-button-text {
                        color: $T1;
                    }
                }

                .abc-date-picker {
                    height: 24px;
                }
            }
        }

        .charge-form-tip {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 81px;
            font-size: 16px;
            color: $T2;
        }
    }

    .cis-icon-set-t1 {
        width: 10px;
        height: 10px;
        margin-right: 20px;
        color: $T2;
    }

    .print-meanwhile-set {
        display: flex;
        align-items: center;
    }

    .label-text {
        align-items: center;
        width: 100%;
        margin-left: 6px;
        font-size: 14px;

        .meanwhile-print-icon {
            margin-left: 2px;
            font-size: 14px;
            color: $T3;
            cursor: pointer;
        }
    }

    .meanwhile-dispensing {
        .abc-checkbox__label {
            font-size: 14px !important;
        }
    }

    .abc-dialog-footer {
        padding: 15px 24px;

        .dialog-footer {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            height: 38px;

            > button {
                min-width: 60px;
            }

            .stance {
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: flex-start;
                height: 100%;

                .abc-checkbox-wrapper {
                    display: inline-flex !important;

                    .abc-checkbox__label {
                        margin-left: 6px;
                        font-size: 12px;
                        color: $T1;
                    }
                }

                .cashier-print {
                    display: flex;
                    width: auto;
                    margin-right: 16px;

                    .abc-checkbox-wrapper {
                        margin-right: 6px;

                        .cis-icon-positive_ {
                            right: 0;
                        }
                    }

                    .reference {
                        > span {
                            display: flex;
                            align-items: center;
                        }
                    }

                    .text-info {
                        font-size: 12px;
                        color: #626d77;
                    }
                }
            }
        }
    }

    .checkout-dialog {
        width: 400px;
        min-height: 300px;
    }

    .adjustment-confirm-style {
        animation: left-right-dou 0.15s ease infinite;
    }

    .charge-dialog-left-extend {
        position: absolute;
        top: 40px;
        right: -282px;
        width: 280px;
        background-color: $S2;
        border-radius: var(--abc-dialog-border-radius);

        &.charge-dialog-left-extend__v2 {
            top: 56px;
        }
    }

    .cis-icon-dropdown_triangle {
        margin-left: -4px;
        font-size: 14px;
        color: $T2;
    }

    .charge-dialog-bottom-extend {
        position: absolute;
        bottom: -60px;
        left: 0;
        width: 100%;
        padding: 15px 24px;
        background-color: $S2;
        border-top: 1px solid $P6;
        border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);
    }
}

.card-promotions-popover-wrapper {
    p {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 14px;

        & + p {
            margin-top: 8px;
        }
    }

    .warn-tips {
        margin-top: 12px;
        margin-bottom: 8px;
        font-size: 12px;
        color: $O2;
    }
}

.confirm-charge-dialog-bottom-extend {
    position: absolute;
    left: 0;
    width: 100%;
    padding: 12px 24px;
    background-color: $P4;
    border-top: 1px solid $P6;
    border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);
    transform: translateY(70px);

    .print-checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 0;
    }

    .cut-off {
        margin: 5px 0;
        border-top: 1px dashed $P3;
    }

    .tip-text {
        margin-bottom: 6px;
        font-size: 12px;
        color: $T2;
        text-align: left;
    }

    .abc-checkbox-wrapper {
        margin: 0 !important;

        .abc-checkbox__label {
            color: $T1;
        }
    }

    .checkbox-width {
        width: 33%;
    }
}
</style>

