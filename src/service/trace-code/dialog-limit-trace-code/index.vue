<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        class="trace-code-limit-dialog"
        data-cy="trace-code-limit-dialog"
        append-to-body
        size="large"
        :show-close="showClose"
        :show-footer="false"
        :need-high-level="false"
        :disabled-keyboard="true"
        :close-after-confirm="false"
    >
        <abc-form ref="formRef" style="height: 100%;" item-no-margin>
            <abc-layout v-abc-loading="loading" class="dialog-content">
                <abc-section style="text-align: center;">
                    <abc-text bold size="large">
                        {{ title }}
                    </abc-text>
                </abc-section>
                <abc-section v-if="isShowTips" style="margin-top: 24px;">
                    <abc-tips-card-v2 theme="primary">
                        <abc-text v-if="tipsStatus === tipsStatusEnum.custom">
                            已自行设置追溯码可用量，点击右侧可恢复追溯码默认设置
                        </abc-text>
                        <abc-text v-else-if="tipsStatus === tipsStatusEnum.ali">
                            已使用<abc-text bold>
                                码上放心平台
                            </abc-text> 数据换算追溯码可用上限、剩余量，如数据有误请自行修正（码上放心平台规格：{{ aliSpecifications }}）
                        </abc-text>
                        <abc-text v-else>
                            已使用<abc-text bold>
                                ABC库存档案规格
                            </abc-text>换算追溯码可用上限、剩余量，如数据有误请自行修正（库存档案规格：{{ hisSpecifications }}）
                        </abc-text>
                    </abc-tips-card-v2>
                </abc-section>
                <abc-section style="margin-top: 16px;">
                    <abc-card background="gray" radius-size="small" style="padding: 16px 0 0; overflow: auto;">
                        <abc-flex
                            justify="center"
                            class="ellipsis"
                            :gap="8"
                            style="width: 100%;"
                        >
                            <abc-text
                                bold
                                :size="viewCode.no.length > 50 ? 'large' : 'xlarge'"
                                data-cy="trace-code-start"
                            >
                                {{ viewCode.start }}
                            </abc-text>
                            <abc-text
                                theme="gray"
                                bold
                                :size="viewCode.no.length > 50 ? 'large' : 'xlarge'"
                                data-cy="trace-code-end"
                            >
                                {{ viewCode.end }}
                            </abc-text>
                        </abc-flex>
                        <abc-flex justify="center" style="width: 100%; padding-bottom: 16px; margin-top: 4px;">
                            <abc-text theme="black">
                                {{ goodsInfoStr }}
                            </abc-text>
                        </abc-flex>
                        <abc-divider :margin="'none'"></abc-divider>
                        <abc-flex
                            v-if="!isShowLeftCountSet"
                            :gap="12"
                            align="center"
                            justify="center"
                            style="position: relative; width: 100%; padding: 24px 16px; background-color: var(--abc-color-div-white);"
                        >
                            <abc-text theme="gray">
                                可用上限
                            </abc-text>
                            <abc-form-item required placement="top">
                                <abc-input
                                    v-model="fetchParams.maxHisPackageCount"
                                    v-abc-focus-selected
                                    v-abc-auto-focus
                                    :width="72"
                                    size="large"
                                    type="number"
                                    :input-custom-style="{ textAlign: 'center' }"
                                >
                                    <abc-flex
                                        slot="append"
                                        align="center"
                                        justify="center"
                                        style="width: 38px;"
                                    >
                                        <span style="color: var(--abc-color-T2);">{{ goods?.packageUnit ?? '' }}</span>
                                    </abc-flex>
                                </abc-input>
                            </abc-form-item>
                        </abc-flex>
                        <abc-flex
                            v-else
                            id="guide-area"
                            class="guide-section"
                            align="center"
                            justify="center"
                        >
                            <abc-flex align="end" :gap="16">
                                <abc-space :size="24">
                                    <abc-form-item
                                        label-position="top"
                                        required
                                        label="可用上限"
                                        :custom-label-style="{
                                            padding: '0 0 4px','line-height': '22px'
                                        }"
                                    >
                                        <abc-input
                                            v-model="fetchParams.maxHisPackageCount"
                                            v-abc-focus-selected
                                            v-abc-auto-focus
                                            :width="isShowPieceInput ? 70 : 80"
                                            type="number"
                                            size="large"
                                            :input-custom-style="{ textAlign: 'center' }"
                                            @input="handleMaxHisPackageCountChange"
                                        >
                                            <abc-flex
                                                slot="append"
                                                align="center"
                                                justify="center"
                                                style="width: 30px;"
                                            >
                                                <span style="color: var(--abc-color-T2);">{{ goods?.packageUnit ?? '' }}</span>
                                            </abc-flex>
                                        </abc-input>
                                    </abc-form-item>

                                    <abc-form-item
                                        label-position="top"
                                        required
                                        label="当前码剩余量"
                                        :custom-label-style="{
                                            padding: '0 0 4px','line-height': '22px'
                                        }"
                                        :error-style="{ 'margin-left': '-40px' }"
                                        :validate-event="validateLeftCount()"
                                    >
                                        <abc-space is-compact>
                                            <abc-input
                                                v-model="fetchParams.leftPackageCount"
                                                v-abc-focus-selected
                                                :width="isShowPieceInput ? 70 : 80"
                                                type="number"
                                                size="large"
                                                :config="{
                                                    supportZero: true
                                                }"
                                                :input-custom-style="{ textAlign: 'center' }"
                                            >
                                                <abc-flex
                                                    slot="append"
                                                    align="center"
                                                    justify="center"
                                                    style="width: 30px;"
                                                >
                                                    <span style="color: var(--abc-color-T2);">{{ goods?.packageUnit ?? '' }}</span>
                                                </abc-flex>
                                            </abc-input>
                                            <abc-input
                                                v-if="isShowPieceInput"
                                                v-model="fetchParams.leftPieceCount"
                                                v-abc-focus-selected
                                                :width="isShowPieceInput ? 70 : 80"
                                                type="number"
                                                size="large"
                                                :config="{
                                                    supportZero: true
                                                }"
                                                :input-custom-style="{ textAlign: 'center' }"
                                            >
                                                <abc-flex
                                                    slot="append"
                                                    align="center"
                                                    justify="center"
                                                    style="width: 30px;"
                                                >
                                                    <span style="color: var(--abc-color-T2);">{{ goods?.pieceUnit ?? '' }}</span>
                                                </abc-flex>
                                            </abc-input>
                                        </abc-space>
                                    </abc-form-item>
                                </abc-space>
                                <abc-button variant="text" style="margin-bottom: 3px;" @click="isShowGuide = true">
                                    查看示例
                                </abc-button>
                            </abc-flex>
                        </abc-flex>
                    </abc-card>
                </abc-section>
                <abc-section style="margin-top: 24px;">
                    <abc-flex justify="center" style="width: 100%;">
                        <abc-space>
                            <abc-button
                                size="large"
                                data-cy="abc-button-confirm"
                                :loading="btnLoading"
                                style="position: relative; min-width: 116px;"
                                @click="confirm"
                            >
                                确定
                            </abc-button>
                            <abc-button
                                size="large"
                                variant="ghost"
                                data-cy="abc-button-cancel"
                                style="position: relative; min-width: 116px;"
                                @click="close"
                            >
                                取消本次采集
                            </abc-button>
                        </abc-space>
                    </abc-flex>
                </abc-section>
            </abc-layout>
        </abc-form>
        <abc-guide-image
            v-model="isShowGuide"
            :image="TraceCodeGuide"
            :image-width="520"
            :target-position="isShowPieceInput ? {
                x: -22, y: -31
            } : {
                x: -5, y: -31
            }"
            dom-selector="#guide-area"
        ></abc-guide-image>
    </abc-modal>
</template>

<script>
    import TraceCode from '@/service/trace-code/service';
    import {
        getSafeNumber, isNotNull,
    } from '@/utils';
    import GoodsV3API from 'api/goods/index-v3';
    import clone from 'utils/clone';
    // import { isEqual } from '@abc/utils';
    import Big from 'big.js';
    import TraceCodeGuideOne from 'assets/images/trace-code/trace-usage-detail-a-large.png';
    import TraceCodeGuideTwo from 'assets/images/trace-code/trace-usage-detail-b-large.png';
    import AbcGuideImage from 'components/abc-guide-image/index.vue';

    const tipsStatusEnum = Object.freeze({
        custom: 1,
        ali: 2,
        his: 3,
    });

    export default {
        name: 'DialogLimitTraceCode',
        components: { AbcGuideImage },
        props: {
            traceCodeInfo: {
                type: Object,
                required: true,
            },
            goods: {
                type: Object,
                required: true,
            },
            title: {
                type: String,
                default: '追溯码可用上限设置',
            },
            isShowTips: {
                type: Boolean,
                default: true,
            },
            showClose: {
                type: Boolean,
                default: true,
            },
            //是否展示剩余量设置
            isShowLeftCountSet: {
                type: Boolean,
                default: false,
            },
            onConfirm: {
                type: Function,
                default: () => {},
            },
            onClose: {
                type: Function,
                default: () => {},
            },
        },
        data() {
            return {
                TraceCodeGuideOne,
                TraceCodeGuideTwo,
                visible: false,
                loading: false,
                btnLoading: false,
                tipsStatusEnum,
                traceableCodeDetail: {},
                fetchParams: {
                    maxHisPackageCount: 0,
                    leftPackageCount: 0,
                    leftPieceCount: 0,
                },
                cacheFetchParams: {
                    maxHisPackageCount: 0,
                    leftPackageCount: 0,
                    leftPieceCount: 0,
                },
                isShowGuide: false,
            };
        },
        computed: {
            // isUpdated() {
            //     return !isEqual(this.fetchParams, this.cacheFetchParams);
            // },
            goodsId() {
                return this.goods?.id || this.goods?.goodsId;
            },

            isShowPieceInput() {
                return this.goods?.pieceUnit !== this.goods?.packageUnit && this.dismounting;
            },
            dismounting() {
                return this.goods?.dismounting ?? 0;
            },
            TraceCodeGuide() {
                return this.isShowPieceInput ? this.TraceCodeGuideOne : this.TraceCodeGuideTwo;
            },
            drugIdentificationCode() {
                const {
                    drugIdentificationCode,
                } = this.traceCodeInfo?.traceableCodeNoInfo || {};
                return drugIdentificationCode || '';
            },
            viewCode() {
                return TraceCode.formatTraceableCode({
                    drugIdentificationCode: this.drugIdentificationCode,
                    no: this.traceCodeInfo.no,
                });
            },
            goodsInfoStr() {
                if (!this.goods) {
                    return '';
                }
                const {
                    name,
                    displayName,
                    displaySpec,
                    manufacturer,
                } = this.goods;
                return `${displayName || name} ${displaySpec || ''} ${manufacturer || ''}`;
            },
            tipsStatus() {
                const {
                    customMaxHisPackageCount,aliMaxHisPackageCount,
                } = this.traceableCodeDetail;
                if (isNotNull(customMaxHisPackageCount)) {
                    return tipsStatusEnum.custom;
                } if (isNotNull(aliMaxHisPackageCount)) {
                    return tipsStatusEnum.ali;
                }
                return tipsStatusEnum.his;
            },
            hisSpecifications() {
                const {
                    packageUnit,pieceUnit,pieceNum,
                } = this.goods || {};
                return `${pieceNum || ''}${pieceUnit || ''}/${packageUnit || ''}`;
            },
            aliSpecifications() {
                const {
                    aliPieceNum, aliPieceUnit,aliPackageUnit,
                } = this.traceableCodeDetail;
                return `${aliPieceNum || ''}${aliPieceUnit || ''}/${aliPackageUnit || ''}`;
            },
        },
        created() {
            if (this.traceCodeInfo?.no) {
                this.getTraceableCodeDetail(this.traceCodeInfo?.no);
            }
        },
        methods: {
            async getTraceableCodeDetail(no) {
                try {
                    this.loading = true;
                    const { data } = await GoodsV3API.fetchTraceCodeDetail(no,{
                        goodsId: this.goodsId,
                    });
                    this.traceableCodeDetail = data ?? {};
                    Object.assign(this.fetchParams, {
                        maxHisPackageCount: data.useMaxHisPackageCount,
                        leftPackageCount: data.leftPackageCount,
                        leftPieceCount: data.leftPieceCount,
                    });
                    this.cacheFetchParams = clone(this.fetchParams);
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            handleMaxHisPackageCountChange() {
                const safeNewCount = Big(getSafeNumber(this.fetchParams.maxHisPackageCount));
                const safeOldCount = Big(getSafeNumber(this.cacheFetchParams.maxHisPackageCount));
                const minus = safeNewCount.minus(safeOldCount);

                const safeOldLeftCount = Big(getSafeNumber(this.cacheFetchParams.leftPackageCount));
                const final = minus.plus(safeOldLeftCount);
                if (final.gte(0)) {
                    this.fetchParams.leftPackageCount = (minus.plus(safeOldLeftCount)).toNumber();
                } else {
                    this.fetchParams.leftPackageCount = 0 ;
                    this.fetchParams.leftPieceCount = 0 ;
                }
            },
            validateLeftCount() {
                const {
                    maxHisPackageCount,leftPackageCount,leftPieceCount,
                } = this.fetchParams;
                const {
                    pieceNum,
                } = this.goods;
                const safePieceCount = Big(getSafeNumber(pieceNum ?? 1));
                const safeMaxHisPackageCount = Big(getSafeNumber(maxHisPackageCount));
                const safeLeftPackageCount = Big(getSafeNumber(leftPackageCount));
                const safeLeftPieceCount = this.isShowPieceInput ? Big(getSafeNumber(leftPieceCount)) : Big(0);
                const validateFlag = (safeLeftPieceCount.plus(safeLeftPackageCount.times(safePieceCount))).gt(safeMaxHisPackageCount.times(safePieceCount));
                if (validateFlag) {
                    return (_, callback) => {
                        return callback({
                            validate: false,
                            message: '剩余量不能大于可用上限',
                        });
                    };
                }
            },
            async confirm() {
                this.$refs.formRef.validate(async (val) => {
                    if (val && this.traceCodeInfo?.no) {
                        try {
                            this.btnLoading = true;
                            const {
                                maxHisPackageCount,leftPackageCount,leftPieceCount,
                            } = this.fetchParams;
                            await GoodsV3API.updateTraceCodeDetail(this.traceCodeInfo?.no, this.goods?.goodsId ?? '',{
                                maxHisPackageCount: +maxHisPackageCount,
                                leftPackageCount: +leftPackageCount,
                                leftPieceCount: +leftPieceCount,
                            });
                            this.onConfirm && this.onConfirm();
                            this.visible = false;
                        } catch (e) {
                            console.log(e);
                        } finally {
                            this.btnLoading = false;
                        }
                    }
                });
            },
            close() {
                this.onClose && this.onClose();
                this.visible = false;
            },
        },
    };
</script>

<style lang="scss">
.trace-code-limit-dialog {
    .guide-section {
        width: 100%;
        height: 160px;
        padding: 24px 16px;
    }
}
</style>
