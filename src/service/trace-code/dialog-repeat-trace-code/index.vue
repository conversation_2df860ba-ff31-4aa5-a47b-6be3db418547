<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        class="abc-trace-code-repeat-dialog"
        data-cy="abc-trace-code-repeat-dialog"
        append-to-body
        size="large"
        :show-footer="false"
        :need-high-level="false"
        :close-after-confirm="false"
        :disabled-keyboard="disabledKeyboard"
        @open="handleOpen"
    >
        <abc-form ref="formRef" style="height: 100%;" item-no-margin>
            <abc-layout class="dialog-content">
                <abc-section style="text-align: center;">
                    <abc-text bold size="large">
                        {{ title }}
                    </abc-text>
                </abc-section>
                <abc-section style="margin-top: 24px;">
                    <abc-tips-card-v2 theme="primary">
                        入库单位包装无追溯码时，可重复采集上层包装的追溯码
                    </abc-tips-card-v2>
                </abc-section>
                <abc-section style="margin-top: 16px;">
                    <abc-card background="gray" radius-size="small" style="padding: 16px 0 0; overflow: auto;">
                        <abc-flex
                            justify="center"
                            class="ellipsis"
                            :gap="8"
                            style="width: 100%;"
                        >
                            <abc-text
                                theme="black"
                                bold
                                :size="viewCode.no.length > 50 ? 'large' : 'xlarge'"
                                data-cy="trace-code-start"
                            >
                                {{ viewCode.start }}
                            </abc-text>
                            <abc-text
                                theme="gray"
                                bold
                                :size="viewCode.no.length > 50 ? 'large' : 'xlarge'"
                                data-cy="trace-code-end"
                            >
                                {{ viewCode.end }}
                            </abc-text>
                        </abc-flex>
                        <abc-flex justify="center" style="width: 100%; padding-bottom: 16px; margin-top: 4px;">
                            <abc-text theme="black">
                                {{ goodsInfoStr }}
                            </abc-text>
                        </abc-flex>
                        <abc-divider :margin="'none'"></abc-divider>
                        <abc-flex
                            :gap="12"
                            align="center"
                            justify="center"
                            style="position: relative; width: 100%; padding: 24px 16px; background-color: var(--abc-color-div-white);"
                        >
                            <abc-text theme="gray">
                                采集
                            </abc-text>
                            <abc-form-item placement="top">
                                <abc-space is-compact>
                                    <abc-tooltip
                                        placement="top"
                                        :disabled="!isUsageOverLimit"
                                    >
                                        <abc-input
                                            ref="inputNumberRef"
                                            v-model.number="count"
                                            v-abc-focus-selected
                                            type="number"
                                            :width="50"
                                            :config="{
                                                max: 9999,
                                                min: 1
                                            }"
                                            :input-custom-style="{ textAlign: 'center' }"
                                        ></abc-input>
                                        <template #content>
                                            <abc-flex :gap="4" vertical align="end">
                                                <abc-tips
                                                    icon
                                                    theme="warning"
                                                >
                                                    追溯码可用上限{{ displayFormatPieceUnit }}，本次入库：{{ count }}{{ traceCodeInfo?.unit }}
                                                </abc-tips>
                                                <abc-button variant="text" size="small" @click="handleToDetail()">
                                                    数据有误，去修正
                                                </abc-button>
                                            </abc-flex>
                                        </template>
                                    </abc-tooltip>
                                    <abc-select
                                        v-model="unit"
                                        :width="50"
                                        :inner-width="50"
                                    >
                                        <abc-option
                                            v-if="goods.pieceUnit"
                                            :value="goods.pieceUnit"
                                            :label="goods.pieceUnit"
                                        ></abc-option>
                                        <abc-option
                                            v-if="goods.packageUnit"
                                            :value="goods.packageUnit"
                                            :label="goods.packageUnit"
                                        ></abc-option>
                                    </abc-select>
                                </abc-space>
                            </abc-form-item>
                        </abc-flex>
                    </abc-card>
                </abc-section>
                <abc-section style="margin-top: 24px;">
                    <abc-flex justify="center" style="width: 100%;">
                        <abc-space>
                            <abc-button
                                size="large"
                                data-cy="abc-button-confirm"
                                :loading="btnLoading"
                                style="position: relative; min-width: 116px;"
                                @click="confirm"
                            >
                                确定
                                <span style="position: absolute; right: 6px; bottom: 3px; font-size: 10px; color: var(--abc-color-theme6);">
                                    F8
                                </span>
                            </abc-button>
                            <abc-button
                                size="large"
                                variant="ghost"
                                data-cy="abc-button-cancel"
                                style="position: relative; min-width: 116px;"
                                @click="close"
                            >
                                取消
                                <span style="position: absolute; right: 6px; bottom: 3px; font-size: 10px; color: var(--abc-color-T3);">
                                    Esc
                                </span>
                            </abc-button>
                        </abc-space>
                    </abc-flex>
                </abc-section>
            </abc-layout>
        </abc-form>
    </abc-modal>
</template>

<script type="text/babel">
    import TraceCode from '@/service/trace-code/service';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import {
        off, on,
    } from 'utils/dom';
    import TraceCodeLimitDialog from '@/service/trace-code/dialog-limit-trace-code';
    export default {
        name: 'TraceCodeConfirmDialog',
        props: {
            // 标题
            title: {
                type: String,
                required: true,
            },
            /** 后端换码接口返回数据结构
             * @typedef {Object} DrugTraceInfo
             * @property {string} no 追溯码编号
             * @property {number} count 追溯码数量(相同码重复的次数)
             * @property {TraceableCodeInfo} traceableCodeNoInfo 追溯码信息
             * @property {GoodsInfo} goods 商品信息
             * @property {Array<TraceableCodeListItem>} traceableCodeList 追溯码使用情况
             */
            traceCodeInfo: {
                type: Object,
                required: true,
            },
            isValidateCount: {
                type: Boolean,
                default: false,
            },
            goods: {
                type: Object,
                default: () => ({}),
            },
            onConfirm: Function,
            onClose: Function,
        },
        setup() {
            const {
                isScanning,
                startBarcodeDetect,
                stopBarcodeDetect,
                handleInputFocus,
                handleInputBlur,
            } = useBarcodeScanner();

            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('采集相同追溯码');

            return {
                isScanning,
                startBarcodeDetect,
                stopBarcodeDetect,
                handleInputFocus,
                handleInputBlur,

                disabledKeyboard,
                pushDialogName,
                popDialogName,
            };
        },
        data() {
            return {
                visible: false,
                btnLoading: false,
                count: Number(this.traceCodeInfo.count ?? 1),
                unit: this.traceCodeInfo.unit,
                showTransition: false,
                transitionType: 'plus',// plus or minus
            };
        },
        computed: {
            // 内蒙古定点机构限制1000
            // isNeimengguFixedPointMechanism() {
            //     if (!TraceCode.supportCollect(this.goodsInfo)) {
            //         return false;
            //     }
            //     return this.isValidateCount && this.$abcSocialSecurity.config?.isNeimenggu && this.$abcSocialSecurity.isOpenSocial;
            // },
            viewCode() {
                return TraceCode.formatTraceableCode({
                    drugIdentificationCode: this.drugIdentificationCode,
                    no: this.traceCodeInfo.no,
                });
            },
            drugIdentificationCode() {
                const {
                    drugIdentificationCode,
                } = this.traceCodeInfo?.traceableCodeNoInfo || {};
                return drugIdentificationCode || '';
            },
            goodsInfo() {
                return this.traceCodeInfo?.goodsInfo || this.traceCodeInfo?.goods;
            },
            goodsInfoStr() {
                if (!this.goodsInfo) {
                    return '';
                }
                const {
                    name,
                    displayName,
                    displaySpec,
                    manufacturer,
                } = this.goodsInfo;
                return `${displayName || name} ${displaySpec || ''} ${manufacturer || ''}`;
            },
            transitionName() {
                return this.transitionType === 'plus' ? 'scroll-up' : 'scroll-down';
            },
            isUsageOverLimit() {
                if (!TraceCode.hasCodeSafeOpened) return false;
                const leftCount = TraceCode.getTraceCodeLeftCount(this.traceCodeInfo) - TraceCode.getTraceCollectCodeCountBySmall(this.goodsInfo,this.traceCodeInfo);
                return leftCount < 0;
            },
            displayFormatPieceUnit() {
                const leftCount = TraceCode.getTraceCodeLeftCount(this.traceCodeInfo);
                return TraceCode.displayFormatPieceUnit(leftCount ?? 0, this.goodsInfo);
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.stopBarcodeDetect();
                    this.popDialogName();
                    this.onClose && this.onClose();
                }
            },
        },
        mounted() {
            on(document, 'keydown', this.keydownHandle);

            this._timer = setTimeout(() => {
                // 打开时自动加一次数量
                this.handlePlus(() => {
                    this.$refs.inputNumberRef?.$refs?.abcinput?.focus();
                });
            }, 300);
        },
        beforeDestroy() {
            off(document, 'keydown', this.keydownHandle);
            clearTimeout(this._timer);
        },
        methods: {
            async confirm() {
                this.$refs.formRef.validate((val) => {
                    if (val) {
                        this.onConfirm && this.onConfirm({
                            ...this.traceCodeInfo,
                            count: +this.count,
                            unit: this.unit,
                        });
                        this.visible = false;
                    }
                });
            },
            close() {
                this.visible = false;
            },
            handleOpen() {
                console.log('handleOpen');
                this.startBarcodeDetect((e, barcode) => {
                    this.handleBarcode(barcode);
                });
                this.pushDialogName();
            },
            handleBarcode(barcode) {
                if (barcode === this.traceCodeInfo.no) {
                    this.handlePlus();
                }
            },
            keydownHandle(event) {
                const KEY_F8 = 'F8';
                const keyCode = event.code;

                if (keyCode === KEY_F8) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.confirm();
                }
            },
            handleMinus() {
                this.showTransition = true;
                this.transitionType = 'minus';

                this.$nextTick(() => {
                    if (this.count > 1) {
                        this.count -= 1;
                    }
                });
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.showTransition = false;
                }, 100);
            },
            handlePlus(cb) {
                this.showTransition = true;
                this.transitionType = 'plus';

                this.$nextTick(() => {
                    if (this.count < 9999) {
                        this.count += 1;
                    }
                });
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.showTransition = false;
                    typeof cb === 'function' && cb();
                }, 100);
            },
            // validateCount(value, callback) {
            //     if (this.isNeimengguFixedPointMechanism && value > 1000) {
            //         callback({
            //             validate: false,
            //             message: '医保要求单次入库追溯码不可超过1000条，请拆分入库',
            //         });
            //     } else {
            //         callback({ validate: true });
            //     }
            // },
            handleToDetail() {
                new TraceCodeLimitDialog({
                    traceCodeInfo: this.traceCodeInfo,
                    goods: this.goodsInfo,
                }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>

<style lang="scss">
.scroll-up-enter-active,
.scroll-up-leave-active {
    position: absolute;
    transition: all 0.2s ease-out;
}

.scroll-up-enter {
    opacity: 0;
    transform: translateY(100%);
}

.scroll-up-leave-to {
    opacity: 0;
    transform: translateY(-100%);
}

.scroll-down-enter-active,
.scroll-down-leave-active {
    position: absolute;
    transition: all 0.2s ease-out;
}

.scroll-down-enter {
    opacity: 0;
    transform: translateY(-100%);
}

.scroll-down-leave-to {
    opacity: 0;
    transform: translateY(100%);
}
</style>

