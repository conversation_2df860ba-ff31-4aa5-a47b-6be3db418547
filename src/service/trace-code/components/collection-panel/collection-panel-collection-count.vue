<template>
    <abc-table-cell
        :style="{
            height: '100%',
            color: getCountColor(trData),
            padding: '0 12px',
            'justify-content': (hasEnableCollCheckStrictMode && isNoNeedCollect) ? 'center' : 'flex-end',
        }"
    >
        <template v-if="!isDispensed">
            <template v-if="isNoTraceCodeGoods || (isChineseMedicine && supportCollect)">
                {{ getUnitCount(trData) }} {{ trData.unit }}
            </template>
            <template v-else-if="hasEnableCollCheckStrictMode">
                <abc-popover
                    theme="yellow"
                    placement="top"
                    trigger="hover"
                    width="354px"
                    :open-delay="500"
                    :disabled="isDisabledPopover"
                >
                    <div slot="reference">
                        <abc-text v-if="isNoNeedCollect" theme="gray-light">
                            -
                        </abc-text>
                        <template v-else>
                            <abc-form-item
                                trigger="custom-active"
                                :validate-event="validateItemTraceableCodeCount"
                                :validate-params="trData"
                                :error-style="{
                                    'margin-left': isShowDescriptionVali ? '-170px' : '-108px',backgroundColor: isShowDescriptionVali ? 'var(--popover-popper-fill-color, #fffdec)' : 'var(--error-background-color, #f93)'
                                }"
                            >
                                <abc-input style="display: none;">
                                </abc-input>
                                <abc-space :size="13" direction="vertical">
                                    <abc-flex v-if="traceableCodeRule.hisPackageCount">
                                        <abc-text :theme="collectBigCount ? 'black' : 'gray-light'">
                                            {{ collectBigCount }}<abc-text style="padding: 0 4px 0 0;">
                                                {{ productInfo.packageUnit }}
                                            </abc-text>
                                        </abc-text>
                                        <div style="min-width: 16px;">
                                            <abc-icon
                                                v-if="collectBigCount === traceableCodeRule.hisPackageCount"
                                                icon="s-check-1-line"
                                                style="margin-left: auto; color: var(--abc-color-G2);"
                                            ></abc-icon>
                                        </div>
                                    </abc-flex>
                                    <abc-flex v-if="traceableCodeRule.hisPieceCount && !hasEnableDismountingMode">
                                        <abc-text :theme="collectSmallCount ? 'black' : 'gray-light'">
                                            {{ collectSmallCount }}<abc-text style="padding: 0 3px 0 1px;">
                                                {{ productInfo.pieceUnit }}
                                            </abc-text>
                                        </abc-text>
                                        <div style="min-width: 16px;">
                                            <abc-icon
                                                v-if="collectSmallCount === traceableCodeRule.hisPieceCount"
                                                icon="s-check-1-line"
                                                style="margin-left: auto; color: var(--abc-color-G2);"
                                            ></abc-icon>
                                        </div>
                                    </abc-flex>
                                </abc-space>
                            </abc-form-item>
                        </template>
                    </div>
                    <collection-panel-count-description
                        :tr-data="trData"
                        :support-collect="supportCollect"
                        :is-specification-match="isSpecificationMatch"
                        :has-enable-dismounting-mode="hasEnableDismountingMode"
                        :has-enable-coll-check-strict-mode="hasEnableCollCheckStrictMode"
                    ></collection-panel-count-description>
                </abc-popover>
            </template>
            <template v-else-if="!supportCollect">
                -
            </template>
            <abc-form-item
                v-else
                trigger="custom-active"
                :validate-event="validateItemTraceableCodeCount"
                :validate-params="trData"
                :error-style="{ 'margin-left': '-104px' }"
            >
                <abc-text>
                    {{ collectStr }}
                </abc-text>
                <abc-input style="display: none;">
                </abc-input>
            </abc-form-item>
        </template>
        <template v-else>
            <template v-if="isNoTraceCodeGoods || (isChineseMedicine && supportCollect)">
                {{ getUnitCount(trData) }} {{ trData.unit }}
            </template>
            <template v-else-if="isNoNeedCollect">
                -
            </template>
            <template v-else-if="hasEnableCollCheckStrictMode">
                <abc-space :size="13" direction="vertical">
                    <abc-flex v-if="traceableCodeRule.hisPackageCount">
                        <abc-text :theme="collectBigCount ? 'black' : 'gray-light'">
                            {{ collectBigCount }}<abc-text style="padding: 0 3px 0 1px;">
                                {{ productInfo.packageUnit }}
                            </abc-text>
                        </abc-text>
                        <div style="min-width: 16px;">
                            <abc-icon
                                v-if="collectBigCount === traceableCodeRule.hisPackageCount"
                                icon="s-check-1-line"
                                style="margin-left: auto; color: var(--abc-color-G2);"
                            ></abc-icon>
                        </div>
                    </abc-flex>
                    <abc-flex v-if="traceableCodeRule.hisPieceCount && !hasEnableDismountingMode">
                        <abc-text :theme="collectSmallCount ? 'black' : 'gray-light'">
                            {{ collectSmallCount }}<abc-text style="padding: 0 3px 0 1px;">
                                {{ productInfo.pieceUnit }}
                            </abc-text>
                        </abc-text>
                        <div style="min-width: 16px;">
                            <abc-icon
                                v-if="collectSmallCount === traceableCodeRule.hisPieceCount"
                                icon="s-check-1-line"
                                style="margin-left: auto; color: var(--abc-color-G2);"
                            ></abc-icon>
                        </div>
                    </abc-flex>
                </abc-space>
            </template>
            <abc-text v-else :theme="isDispensedTraceCode ? 'gray-light' : ''">
                {{ isCompatibleHistoryData ? getCollectCount(trData) : collectStr }}
            </abc-text>
        </template>
    </abc-table-cell>
</template>

<script>
    import TraceCode from '@/service/trace-code/service';
    import CollectionPanelCountDescription
        from '@/service/trace-code/components/collection-panel/collection-panel-count-description.vue';
    import { GoodsTypeIdEnum } from 'views/inventory/constant';

    export default {
        name: 'CollectionPanelCollectionCount',
        components: { CollectionPanelCountDescription },
        props: {
            trData: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            isDispensed: {
                type: Boolean,
                default: false,
            },
            isDispensedTraceCode: {
                type: Boolean,
                default: false,
            },
            isCompatibleHistoryData: {
                type: Boolean,
                default: false,
            },
            isNullCodeGoods: {
                type: Boolean,
                default: false,
            },
            showShouldCollect: {
                type: Boolean,
                default: false,
            },
            isSpecificationMatch: {
                type: Boolean,
                default: false,
            },
            isNoTraceCodeGoods: {
                type: Boolean,
                default: false,
            },
            isChineseMedicine: {
                type: Boolean,
                default: false,
            },
            supportCollect: {
                type: Boolean,
                default: false,
            },
            isSelectedSocialPay: {
                type: Boolean,
                default: false,
            },
            isNoNeedCollect: {
                type: Boolean,
                default: false,
            },
            hasEnableDismountingMode: {
                type: Boolean,
                default: false,
            },
            hasEnableCollCheckStrictMode: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                isDisabledPopover: false,
                isShowDescriptionVali: false,
            };
        },
        computed: {
            productInfo() {
                return this.trData?.productInfo ?? {};
            },
            traceableCodeRule() {
                return this.trData?.traceableCodeRule ?? {};
            },
            collectBigCount() {
                return this.getTraceCodeCollectCountInfo(this.trData)?.collectBigCount ?? 0;
            },
            collectSmallCount() {
                return this.getTraceCodeCollectCountInfo(this.trData)?.collectSmallCount ?? 0;
            },
            collectStr() {
                return this.getTraceCodeCollectCountInfo(this.trData)?.str ?? '';
            },
        },
        methods: {
            getCollectCount(trData) {
                return TraceCode.getTraceCodeCollectCount(trData);
            },
            getUnitCount(item) {
                return TraceCode.getUnitCount(item);
            },
            displayShebaoSpec(trData) {
                return TraceCode.displayShebaoSpec(trData.productInfo);
            },
            getCountColor(item) {
                const { isShebaoDismountingFlag } = item;
                if (window.$abcSocialSecurity.config.isSichuanChengdu && this.isSelectedSocialPay && [GoodsTypeIdEnum.MEDICINE_WESTERN, GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT].includes(item.productInfo?.typeId)) {
                    return TraceCode.supportCollect(item.productInfo) && this.validateTraceableCodeCount(item) && !this.isChineseMedicine && !this.isNoTraceCodeGoods && !this.disabled && !isShebaoDismountingFlag ? 'var(--abc-color-Y2)' : '';
                }
                return this.validateTraceableCodeCount(item) && (this.collectBigCount || this.collectSmallCount) && !this.isChineseMedicine && !this.isNoTraceCodeGoods && !this.disabled && !isShebaoDismountingFlag ? 'var(--abc-color-Y2)' : '';
            },
            validateTraceableCodeCount(item) {
                const {
                    validateFlag,
                } = TraceCode.validateTraceableCodeCount(item);
                return validateFlag;
            },
            validateItemTraceableCodeCount(value, callback, trData) {
                if (trData.isShebaoDismountingFlag || this.disabled) {
                    this.isDisabledPopover = false;
                    callback({ validate: true });
                    return;
                }
                if (
                    (
                        trData.traceableCodeList && trData.traceableCodeList.length
                    ) ||
                    (
                        window.$abcSocialSecurity.config.isSichuanChengdu &&
                        this.isSelectedSocialPay &&
                        TraceCode.supportCollect(trData.productInfo) &&
                        [GoodsTypeIdEnum.MEDICINE_WESTERN, GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT].includes(trData.productInfo?.typeId)
                    )
                ) {
                    const {
                        unit, productInfo,traceableCodeRule = {},
                    } = trData;
                    const {
                        hisPieceCount,hisPackageCount,
                    } = traceableCodeRule;
                    const dispensingCount = TraceCode.getUnitCount(trData);
                    if (this.hasEnableCollCheckStrictMode) {
                        const bigStr = hisPackageCount ? `${hisPackageCount}${productInfo.packageUnit}` : '';
                        const smallStr = hisPieceCount ? `${hisPieceCount}${productInfo.pieceUnit}` : '';
                        const dispensingCountStr = bigStr + smallStr;
                        const {
                            validateFlag,
                        } = TraceCode.validateTraceableCodeCount(trData);
                        if (validateFlag) {
                            this.isDisabledPopover = true;
                            this.isShowDescriptionVali = false;
                            callback({
                                validate: false,
                                message: `追溯码采集总量必须等于发药数量<br/><br/>本次发药：${dispensingCountStr}&nbsp;&nbsp;&nbsp;本次追溯码采集：${this.collectStr}`,
                            });
                            return;
                        }
                        const { bigShouldCollectCount } = TraceCode.getTraceCodeShouldCollectCountInfo(this.trData);
                        if (bigShouldCollectCount !== this.collectBigCount) {
                            this.isDisabledPopover = true;
                            this.isShowDescriptionVali = true;
                            callback({
                                validate: false,
                                validateComponent: () => (
                              <collection-panel-count-description
                                  tr-data={this.trData}
                                  has-enable-dismounting-mode={this.hasEnableDismountingMode}
                                  has-enable-coll-check-strict-mode={this.hasEnableCollCheckStrictMode}
                                  support-collect={this.supportCollect}
                                  is-specification-match={this.isSpecificationMatch}
                                  is-show-tips={true}
                              ></collection-panel-count-description>
                          ),
                            });
                        }
                        this.isDisabledPopover = false;
                        callback({ validate: true });
                    } else {
                        const {
                            validateFlag, str,
                        } = TraceCode.validateTraceableCodeCount(trData);
                        if (validateFlag) {
                            const dispensingCountStr = `${dispensingCount}${unit}`;
                            this.isDisabledPopover = true;
                            this.isShowDescriptionVali = false;
                            callback({
                                validate: false,
                                message: `追溯码采集总量必须等于发药数量<br/><br/>本次发药：${dispensingCountStr}&nbsp;&nbsp;&nbsp;本次追溯码采集：${str}`,
                            });
                            return;
                        }
                    }
                    this.isDisabledPopover = false;
                    callback({ validate: true });
                    return;
                }
                this.isDisabledPopover = false;
                callback({ validate: true });
            },
            getTraceCodeCollectCountInfo(trData) {
                return TraceCode.getTraceCodeCollectCountInfo(trData);
            },
        },
    };
</script>
