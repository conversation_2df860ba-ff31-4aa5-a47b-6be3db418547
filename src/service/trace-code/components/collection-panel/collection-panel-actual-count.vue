<template>
    <abc-table-cell
        :style="{
            height: '100%',padding: '8px 12px',
        }"
    >
        <abc-text v-if="!localTraceableCodeRule.hisPackageCount && !localTraceableCodeRule.hisPieceCount" theme="gray-light">
            -
        </abc-text>
        <abc-space v-else :size="8" direction="vertical">
            <abc-space v-if="isShowPackageCount" :size="8">
                <abc-text size="mini" theme="gray-light">
                    整
                </abc-text>
                <abc-form-item
                    :validate-event="validatePackageCount"
                    :error-style="{
                        'margin-left': '-130px',backgroundColor: 'var(--popover-popper-fill-color, #fffdec)'
                    }"
                >
                    <abc-input
                        v-model="localTraceableCodeRule.hisPackageCount"
                        v-abc-focus-selected
                        type="number"
                        :width="71"
                        :disabled="disabledPackageCount"
                        size="small"
                        :config="{
                            supportZero: true, max: 99999
                        }"
                        @change="handlePackageCountChange"
                    >
                        <span slot="appendInner">{{ productInfo.packageUnit }}</span>
                    </abc-input>
                </abc-form-item>
            </abc-space>
            <abc-space v-if="localTraceableCodeRule.hisPieceCount" :size="8">
                <abc-text size="mini" theme="gray-light">
                    零
                </abc-text>
                <abc-input
                    :value="localTraceableCodeRule.hisPieceCount"
                    type="number"
                    size="small"
                    :disabled="true"
                    :width="71"
                >
                    <span slot="appendInner">{{ productInfo.pieceUnit }}</span>
                </abc-input>
            </abc-space>
        </abc-space>
    </abc-table-cell>
</template>

<script>
    import Big from 'big.js';
    import { getSafeNumber } from '@/utils';
    import TraceCode, { ShebaoTraceableCodeDismountingFlagEnum } from '@/service/trace-code/service';
    import { clone } from '@abc/utils';

    export default {
        name: 'CollectionPanelActualCount',
        props: {
            trData: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            isDisabledActualCount: {
                type: Boolean,
                default: false,
            },
            isReReportMode: {
                type: Boolean,
                default: false,
            },
            isNoNeedCollect: {
                type: Boolean,
                default: false,
            },
            hasEnableDismountingMode: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                localTraceableCodeRule: {},
            };
        },
        computed: {
            productInfo() {
                return this.trData?.productInfo ?? {};
            },
            traceableCodeRule() {
                return this.trData?.traceableCodeRule ?? {};
            },
            disabledPackageCount() {
                return this.disabled || this.isDisabledActualCount || this.isReReportMode || this.productInfo?.pieceUnit === this.productInfo?.packageUnit;
            },
            packageOptions() {
                const {
                    hisMaxPackageCount = 0,packageCountTransFactor = 0,
                } = this.traceableCodeRule ?? {};
                // const safeHisMaxPackageCount = Big(getSafeNumber(hisMaxPackageCount));
                // const safeHisMaxPieceCount = Big(getSafeNumber(hisMaxPieceCount));
                // const safePieceNum = Big(getSafeNumber(pieceNum ?? 1));
                // const max = safeHisMaxPackageCount.plus(safeHisMaxPieceCount.div(safePieceNum)).toNumber();
                if (hisMaxPackageCount && packageCountTransFactor >= 1) {
                    const result = [0];
                    let next = result[result.length - 1] + packageCountTransFactor; // 计算下一个值
                    while (next <= hisMaxPackageCount) {
                        result.push(next);
                        next = result[result.length - 1] + packageCountTransFactor;
                    }
                    return result;
                }
                return [0].concat([hisMaxPackageCount ? hisMaxPackageCount : null].filter(Boolean));
            },
            getUnitCount() {
                return TraceCode.getUnitCount(this.trData);
            },
            isShowPackageCount() {
                //销售不足一个大单位 或 禁用状态下hisPackageCount=0 不展示整部分
                const {
                    packageUnit,pieceNum,
                } = this.productInfo;
                const { hisPackageCount } = this.localTraceableCodeRule;
                if (!hisPackageCount && this.disabledPackageCount) {
                    return false;
                }
                const safePieceNum = Big(getSafeNumber(pieceNum || 1));
                const isBigUnit = packageUnit === this.trData.unit;
                if (isBigUnit) {
                    return true;
                }
                return this.getUnitCount.gte(safePieceNum);
            },
        },
        watch: {
            traceableCodeRule: {
                handler(val) {
                    this.localTraceableCodeRule = clone(val);
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            handlePackageCountChange(_val) {
                let val = _val ? Number(_val) : 0;
                try {
                    if (!this.packageOptions.includes(val)) {
                        if (val > this.traceableCodeRule.hisMaxPackageCount) {
                            this.localTraceableCodeRule.hisPackageCount = this.traceableCodeRule.hisMaxPackageCount;
                            val = this.traceableCodeRule.hisMaxPackageCount;
                        } else {
                            return;
                        }
                    }
                    const safeHisPackageCount = Big(getSafeNumber(this.traceableCodeRule.hisPackageCount));
                    const safeHisPieceCount = Big(getSafeNumber(this.traceableCodeRule.hisPieceCount));
                    const safeVal = Big(getSafeNumber(val));
                    const safePieceNum = Big(getSafeNumber(this.productInfo.pieceNum ?? 1));
                    const hisPieceCount = ((safeHisPackageCount.minus(safeVal)).times(safePieceNum)).plus(safeHisPieceCount);
                    this.$set(this.traceableCodeRule, 'hisPieceCount', hisPieceCount.toNumber());
                    this.$set(this.traceableCodeRule, 'hisPackageCount', val);
                    if (this.hasEnableDismountingMode) {
                        if (!Number(this.traceableCodeRule?.hisPackageCount)) {
                            this.$set(this.trData,'shebaoDismountingFlag',ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_EDIT);
                            this.$set(this.trData,'isShebaoDismountingFlag',true);
                        } else {
                            this.$set(this.trData,'shebaoDismountingFlag',ShebaoTraceableCodeDismountingFlagEnum.NO_DISMOUNTING_EDIT);
                            this.$set(this.trData,'isShebaoDismountingFlag',false);
                        }
                    }
                    this.$emit('change');
                } catch (e) {
                    console.log(e);
                }
            },
            validatePackageCount(_,callback) {
                const {
                    hisPackageCount,
                } = this.localTraceableCodeRule;
                const {
                    hisMaxPackageCount,packageCountTransFactor,
                } = this.traceableCodeRule;
                if (!this.packageOptions.includes(+hisPackageCount)) {
                    callback({
                        validate: false,
                        validateComponent: () => (
                            <abc-flex vertical="vertical" gap={2}>
                                <abc-tips
                                    icon
                                    theme="warning"
                                >
                                    只能输入<abc-text bold>
                                    { packageCountTransFactor }
                                </abc-text>{ this.productInfo.packageUnit }的倍数且最大只能为<abc-text bold>
                                    { hisMaxPackageCount }
                                </abc-text>{ this.productInfo.packageUnit }
                                </abc-tips>
                                <abc-text style="padding-left: 26px" tag="div">
                                    his档案规格= { this.productInfo?.pieceNum || '' }{ this.productInfo?.pieceUnit || '-' }/{ this.productInfo?.packageUnit || '-' }，医保规格= { this.productInfo?.shebao?.shebaoPieceNum || '' }{ this.productInfo?.shebao?.shebaoPieceUnit || '-' }/{ this.productInfo?.shebao?.shebaoPackageUnit || '-' }
                                </abc-text>
                            </abc-flex>
                        ),
                    });
                }
            },
        },
    };
</script>
