<template>
    <abc-layout class="dialog-content" data-cy="trace-code-collect-panel">
        <abc-section v-if="!disabled">
            <abc-input
                ref="searchInput"
                v-model.trim="traceCode"
                placeholder="扫描或输入追溯码"
                size="medium"
                style="width: 100%;"
                :max-length="100"
                :loading="loading"
                loading-position="left"
                inputmode="none"
                data-cy="trace-code-collect-dialog-search-input"
                @focus="handleFocus"
                @blur="handleBlur"
                @enter="handleEnter"
            >
                <label slot="prepend">
                    <abc-icon icon="s-scan-line" color="var(--abc-color-T3)"></abc-icon>
                </label>
                <template #appendInner>
                    <abc-text v-if="traceCode" theme="gray-light">
                        敲击回车录入
                    </abc-text>
                    <template v-else>
                        <abc-button
                            v-if="isSupportDefaultTraceCode"
                            variant="text"
                            theme="primary"
                            size="small"
                            @click="handleOpenNoCode"
                        >
                            {{ $abcSocialSecurity.isOpenSocial && $abcSocialSecurity.config.isSichuan ? '无码商品申报' : '发药项目无追溯码？' }}
                        </abc-button>
                        <template v-if="hasEnableDismountingMode">
                            <abc-divider
                                size="normal"
                                style="min-height: 10px;"
                                layout="vertical"
                                margin="small"
                            ></abc-divider>
                            <abc-button
                                variant="text"
                                theme="primary"
                                size="small"
                                @click="handleOpenDismountingModeHelp"
                            >
                                如何判断拆零？
                            </abc-button>
                        </template>
                    </template>
                </template>
            </abc-input>
        </abc-section>
        <abc-section v-if="flatFormItems && flatFormItems.length">
            <abc-table
                id="collect-trace-code-table"
                ref="abcTable"
                :render-config="createdRenderConfig()"
                :data-list="flatFormItems"
                cell-size="large"
                :fixed-tr-height="false"
                :show-checked="false"
                :show-hover-tr-bg="false"
                :need-selected="false"
                :custom-tr-data-cy="customTrDataCy"
            >
                <template #name="{ trData }">
                    <table-cell-name :tr-data="trData" :is-no-need-collect="isNoNeedCollect(trData)"></table-cell-name>
                </template>
                <template #drugIdentificationCode="{ trData }">
                    <table-cell-identification-code :product-info="trData.productInfo" :is-no-need-collect="isNoNeedCollect(trData)"></table-cell-identification-code>
                </template>
                <template #saleCount="{ trData }">
                    <abc-table-cell
                        :style="{
                            height: '100%',padding: '0 12px',
                        }"
                    >
                        {{ getUnitCount(trData) }}
                        {{ trData.unit }}
                    </abc-table-cell>
                </template>
                <template #actualCount="{ trData }">
                    <collection-panel-actual-count
                        :tr-data="trData"
                        :disabled="disabled"
                        :is-disabled-actual-count="isDisabledActualCount"
                        :is-re-report-mode="isReReportMode"
                        :is-no-need-collect="isNoNeedCollect(trData)"
                        :has-enable-dismounting-mode="hasEnableDismountingMode"
                        @change="handleCodeChange(trData)"
                    ></collection-panel-actual-count>
                </template>
                <template #count="{ trData }">
                    <collection-panel-count
                        :tr-data="trData"
                        :is-compatible-history-data="isCompatibleHistoryData(trData,disabled)"
                        :is-chinese-medicine="isChineseMedicine(trData.productInfo)"
                        :is-no-trace-code-goods="isNoTraceCodeGoods(trData)"
                        :support-collect="supportCollect(trData)"
                        :is-specification-match="isSpecificationMatch(trData)"
                        :is-no-need-collect="isNoNeedCollect(trData)"
                    >
                    </collection-panel-count>
                </template>
                <template #collectionCount="{ trData }">
                    <collection-panel-collection-count
                        :tr-data="trData"
                        :disabled="disabled"
                        :is-compatible-history-data="isCompatibleHistoryData(trData,disabled)"
                        :is-null-code-goods="isNullCodeGoods(trData)"
                        :show-should-collect="showShouldCollect(trData)"
                        :is-specification-match="isSpecificationMatch(trData)"
                        :is-no-trace-code-goods="isNoTraceCodeGoods(trData)"
                        :is-chinese-medicine="isChineseMedicine(trData.productInfo)"
                        :support-collect="supportCollect(trData)"
                        :is-no-need-collect="isNoNeedCollect(trData)"
                        :is-selected-social-pay="isSelectedSocialPay"
                        :has-enable-dismounting-mode="hasEnableDismountingMode"
                        :has-enable-coll-check-strict-mode="hasEnableCollCheckStrictMode"
                    >
                    </collection-panel-collection-count>
                </template>
                <template #traceCode="{ trData }">
                    <table-cell-trace-code-list
                        :tr-data="trData"
                        :disabled="disabled"
                        :has-code-safe-opened="hasCodeSafeOpened"
                        :has-enable-dismounting-mode="hasEnableDismountingMode"
                        :has-enable-coll-check-strict-mode="hasEnableCollCheckStrictMode"
                        :is-compatible-history-data="isCompatibleHistoryData(trData,disabled)"
                        :is-no-need-collect="isNoNeedCollect(trData)"
                        @change="handleCodeChange(trData)"
                        @updateDetailSuccess="updateDetailSuccess(trData)"
                    ></table-cell-trace-code-list>
                </template>
                <template v-if="!disabled" #operate="{ trData }">
                    <abc-table-cell
                        v-if="!isCompatibleHistoryData(trData,disabled)"
                        :style="{
                            height: '100%',padding: '0',
                        }"
                        clickable
                    >
                        <table-cell-split-zero-code
                            :trace-code-use-info="trData"
                            :disabled="disabled"
                            :goods-id="trData.productId"
                            @add-code="handleAddCode"
                            @updateDetailSuccess="updateDetailSuccess(trData)"
                        >
                            <abc-flex justify="center" align="center" style="width: 100%; height: 100%;">
                                <abc-popover
                                    v-model="trData.isShowSplitZeroTip"
                                    placement="top-end"
                                    trigger="manual"
                                    theme="yellow"
                                    :offset="-20"
                                >
                                    <abc-icon
                                        slot="reference"
                                        icon="s-b-code-line"
                                        color="var(--abc-color-theme1)"
                                        @mouseenter="trData.isShowSplitZeroTip = true"
                                        @mouseleave="trData.isShowSplitZeroTip = false"
                                        @click.self="trData.isShowSplitZeroTip = false"
                                    ></abc-icon>
                                    <abc-text>
                                        选择拆零追溯码
                                    </abc-text>
                                </abc-popover>
                            </abc-flex>
                        </table-cell-split-zero-code>
                    </abc-table-cell>

                    <table-cell-history
                        v-else-if="!isNoTraceCodeGoods(traceCode)"
                        :disabled="disabled"
                        :goods-id="trData.productId"
                        :pharmacy-no="trData.pharmacyNo"
                        :is-compatible-history-data="isCompatibleHistoryData(trData,disabled)"
                        @add-code="handleAddCode"
                    ></table-cell-history>
                </template>
            </abc-table>
        </abc-section>
        <abc-section v-if="flatDispensedFormItems && flatDispensedFormItems.length">
            <abc-table
                style="margin-top: 6px;"
                :render-config="createdRenderConfig(true)"
                :data-list="flatDispensedFormItems"
                cell-size="large"
                :fixed-tr-height="false"
                :show-checked="false"
                :show-hover-tr-bg="false"
                :need-selected="false"
            >
                <template #name="{ trData }">
                    <table-cell-name :tr-data="trData" :is-no-need-collect="isNoNeedCollect(trData)" :is-dispensed-trace-code="isDispensedTraceCode(trData)"></table-cell-name>
                </template>
                <template #drugIdentificationCode="{ trData }">
                    <table-cell-identification-code :product-info="trData.productInfo" :is-dispensed-trace-code="isDispensedTraceCode(trData)" :is-no-need-collect="isNoNeedCollect(trData)"></table-cell-identification-code>
                </template>
                <template #saleCount="{ trData }">
                    <abc-table-cell
                        :style="{
                            height: '100%',padding: '0 12px',
                        }"
                    >
                        {{ getUnitCount(trData) }}
                        {{ trData.unit }}
                    </abc-table-cell>
                </template>
                <template #actualCount="{ trData }">
                    <collection-panel-actual-count
                        :tr-data="trData"
                        :is-no-need-collect="isNoNeedCollect(trData)"
                        :has-enable-dismounting-mode="hasEnableDismountingMode"
                        :disabled="disabled"
                    ></collection-panel-actual-count>
                </template>
                <template #count="{ trData }">
                    <collection-panel-count
                        :tr-data="trData"
                        :is-dispensed="true"
                        :is-dispensed-trace-code="isDispensedTraceCode(trData)"
                        :is-no-trace-code-goods="isNoTraceCodeGoods(trData)"
                        :support-collect="supportCollect(trData)"
                        :is-no-need-collect="isNoNeedCollect(trData)"
                    ></collection-panel-count>
                </template>
                <template #collectionCount="{ trData }">
                    <collection-panel-collection-count
                        :tr-data="trData"
                        disabled
                        :is-dispensed="true"
                        :is-dispensed-trace-code="isDispensedTraceCode(trData)"
                        :is-compatible-history-data="isCompatibleHistoryData(trData,true)"
                        :is-no-trace-code-goods="isNoTraceCodeGoods(trData)"
                        :is-chinese-medicine="isChineseMedicine(trData.productInfo)"
                        :is-no-need-collect="isNoNeedCollect(trData)"
                        :support-collect="supportCollect(trData)"
                        :is-selected-social-pay="isSelectedSocialPay"
                        :has-enable-dismounting-mode="hasEnableDismountingMode"
                        :has-enable-coll-check-strict-mode="hasEnableCollCheckStrictMode"
                    ></collection-panel-collection-count>
                </template>
                <template #traceCode="{ trData }">
                    <table-cell-trace-code-list
                        :tr-data="trData"
                        disabled
                        :has-code-safe-opened="hasCodeSafeOpened"
                        :is-compatible-history-data="isCompatibleHistoryData(trData,true)"
                        :is-dispensed-trace-code="isDispensedTraceCode(trData)"
                        :is-no-need-collect="isNoNeedCollect(trData)"
                        :has-enable-dismounting-mode="hasEnableDismountingMode"
                        :has-enable-coll-check-strict-mode="hasEnableCollCheckStrictMode"
                    ></table-cell-trace-code-list>
                </template>
            </abc-table>
        </abc-section>
    </abc-layout>
</template>

<script type="text/babel">
    import {
        createGUID, isNull,
    } from '@/utils';
    import Clone from 'utils/clone';
    import BarcodeDetectorV2 from 'utils/barcode-detector-v2';
    import TraceCode, {
        AliPackageLevelEnum, AliPackageLevelLabel,
        SceneTypeEnum, ShebaoTraceableCodeDismountingFlagEnum,
        TraceableCodeListItemErrorType,
        TraceableCodeTypeEnum,
        TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import TraceCodeConfirmDialog from '@/service/trace-code/dialog-trace-code-confirm';
    import {
        GoodsTypeEnum,
    } from '@abc/constants';
    import TraceCodeMatchDialog from '@/service/trace-code/dialog-trace-code-match';
    import NoTraceCodeCheckDialog from '@/service/trace-code/dialog-no-trace-code-check';
    import TableCellName from '@/service/trace-code/components/table-cell-name.vue';
    import TableCellIdentificationCode from '@/service/trace-code/components/table-cell-identification-code.vue';
    import TableCellHistory from '@/service/trace-code/components/table-cell-history.vue';
    import TableCellTraceCodeList from '@/service/trace-code/components/table-cell-trace-code-list.vue';
    import TraceCodeWarningDialog from '@/service/trace-code/dialog-trace-code-warning';
    // 药品
    import useSichuanNoCode from 'views/inventory/goods/archives/hook/useSichuanNoCode';
    import TableCellSplitZeroCode from '@/service/trace-code/components/table-cell-split-zero-code.vue';
    import { debounce } from 'utils/lodash';
    import { isChineseMedicine } from '@/filters';
    import GoodsV3API from '@/api/goods/index-v3';
    import { dispenseItemStatusEnum } from 'views/pharmacy/constants';
    import CollectionPanelCount from '@/service/trace-code/components/collection-panel/collection-panel-count.vue';
    import TraceCodeLimitDialog from '@/service/trace-code/dialog-limit-trace-code';
    import CollectionPanelCollectionCount
        from '@/service/trace-code/components/collection-panel/collection-panel-collection-count.vue';
    import CollectionPanelActualCount
        from '@/service/trace-code/components/collection-panel/collection-panel-actual-count.vue';

    export default {
        name: 'AbcTraceCodeCollectionPanel',
        components: {
            CollectionPanelActualCount,
            CollectionPanelCollectionCount,
            CollectionPanelCount,
            TableCellSplitZeroCode,
            TableCellName,
            TableCellIdentificationCode,
            TableCellHistory,
            TableCellTraceCodeList,
        },
        props: {
            // 平铺的 chargeFromItems、dispensingFormItems 列表结构
            formItems: {
                type: Array,
                default: () => [],
                required: true,
            },
            // 结构同上，已发药的列表
            dispensedFormItems: {
                type: Array,
                default: () => [],
            },
            // 确定后内部直接回写追溯码
            confirmInnerSet: {
                type: Boolean,
                default: true,
            },
            disabled: Boolean,
            callbackConfirm: Function,
            filterItemStatus: Array,
            patientOrderId: {
                type: String,
                default: '',
            },
            sceneType: {
                type: String,
                default: '',
            },
            saveLoading: {
                type: Boolean,
                default: false,
            },
            isReReportMode: {
                type: Boolean,
                default: false,
            },
            isSelectedSocialPay: {
                type: Boolean,
                default: false,
            },
            isDisabledActualCount: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                GoodsTypeEnum,
                TraceableCodeTypeEnum,
                ShebaoTraceableCodeDismountingFlagEnum,
                visible: false,
                isScanning: false,
                hasCodeSafeOpened: false,
                hasInterceptAliPackageLevel: false, //是否开启中码、大码拦截
                hasEnableDismountingMode: false, //是否启用拆零不采模式
                hasEnableCollCheckStrictMode: false, //是否启用采集强控模式
                curValue: '',
                flatFormItems: [],
                flatDispensedFormItems: [],
                curSelected: {},
                traceCode: '',
                dataList: [],
                TraceableCodeListItemErrorType,
                loading: false,
                codeQueue: [], // 获取到的追溯码队列
                debounceCreateMaxTraceCountMap: new Map(), // 按药品ID存储防抖函数
                traceCodeCallbackQueue: [], // 处理采集后追溯码回调队列
            };
        },
        computed: {
            isExistCompatibleHistoryData() {
                return this.flatFormItems.some((item) => this.isCompatibleHistoryData(item,this.disabled));
            },
            isExistDispensedCompatibleHistoryData() {
                return this.flatDispensedFormItems.some((item) => this.isCompatibleHistoryData(item,true));
            },
            productInfo() {
                return this.curSelected?.productInfo || {};
            },

            isSupportDefaultTraceCode() {
                return this.$abcSocialSecurity.defaultDrugTraceCode?.isSupportDefaultTraceCode;
            },
        },
        created() {
            this.hasCodeSafeOpened = TraceCode.hasCodeSafeOpened;
            this.hasEnableDismountingMode = TraceCode.hasEnableDismountingMode;
            this.hasEnableCollCheckStrictMode = TraceCode.hasEnableCollCheckStrictMode;
            this.hasInterceptAliPackageLevel = TraceCode.hasInterceptAliPackageLevel();
            this.initHandler();
        },
        mounted() {
            this._debounceCreateMaxTraceCount = debounce(this.createMaxTraceCount, 300, true);
            this._openAllCallbackQueueDialog = debounce(() => {
                if (this.traceCodeCallbackQueue.length > 0) {
                    this.processDialogQueue();
                }
            }, 300, true);
            this.startBarcodeDetect();
        },
        beforeDestroy() {
            this.stopBarcodeDetect();
            // 清理防抖函数映射表
            this.debounceCreateMaxTraceCountMap.clear();
        },
        methods: {
            isNull,
            isChineseMedicine,
            isNoNeedCollect(trData) {
                return (this.hasEnableDismountingMode && trData.isShebaoDismountingFlag) || this.isNoTraceCodeGoods(trData) || !this.supportCollect(trData) || this.isChineseMedicine(trData.productInfo);
            },
            createdRenderConfig(isDispensed = false) {
                const list = [
                    {
                        key: 'name',
                        label: isDispensed || this.isReReportMode ? (this.hasEnableCollCheckStrictMode ? '发药项目' : '已发药项目') : '待发药项目',
                    },
                    {
                        key: 'drugIdentificationCode',
                        label: '产品标识码',
                        style: {
                            width: '96px',
                            maxWidth: '96px',
                        },
                        headerStyle: {
                            padding: '0 4px',
                        },
                    },
                    {
                        key: 'saleCount',
                        label: this.sceneType === SceneTypeEnum.CHARGE ? '销售' : '发药',
                        style: {
                            width: '76px',
                            maxWidth: '76px',
                            textAlign: 'right',
                        },
                        headerStyle: {
                            padding: '0 4px',
                        },
                        isHidden: !this.hasEnableCollCheckStrictMode,
                    },
                    {
                        key: 'actualCount',
                        label: '发药组合',
                        style: {
                            width: '112px',
                            maxWidth: '112px',
                            textAlign: 'center',
                        },
                        isHidden: !this.hasEnableCollCheckStrictMode,
                    },
                    {
                        key: 'count',
                        label: (isDispensed ? this.isExistDispensedCompatibleHistoryData : this.isExistCompatibleHistoryData) ? '发药数量' : '应采',
                        style: {
                            width: '80px',
                            maxWidth: '80px',
                            textAlign: 'right',
                        },
                        isHidden: this.hasEnableCollCheckStrictMode,
                    },
                    {
                        key: 'collectionCount',
                        label: TraceCode.isStrictCount ? '已采' : '采集数量',
                        style: {
                            width: this.hasEnableCollCheckStrictMode ? '92px' : '80px',
                            maxWidth: this.hasEnableCollCheckStrictMode ? '92px' : '80px',
                            textAlign: this.hasEnableCollCheckStrictMode ? 'center' : 'right',
                        },
                    },
                    {
                        key: 'traceCode',
                        label: '追溯码',
                        style: {
                            width: '348px',
                            maxWidth: '348px',
                        },
                        headerStyle: {
                            padding: '0 4px',
                        },
                    },
                    {
                        key: 'operate',
                        label: '',
                        style: {
                            width: '40px',
                            maxWidth: '40px',
                        },
                    },
                ];
                return {
                    hasInnerBorder: true,
                    list: list.filter((it) => !it.isHidden),
                };
            },
            isCompatibleHistoryData(trData,disabled) {
                return TraceCode.isCompatibleHistoryData(trData,disabled);
            },
            isDispensedTraceCode(trData) {
                return trData?.status === dispenseItemStatusEnum.RETURN;
            },
            getTraceCodeShouldCollectCountInfo(trData) {
                return TraceCode.getTraceCodeShouldCollectCountInfo(trData);
            },
            isNullCodeGoods(trData) {
                return TraceCode.isNullCodeGoods(trData.productInfo);
            },
            showShouldCollect(trData) {
                if (!TraceCode.isSupportTraceCodeForceCheckPharmacy()) return false;
                if (!TraceCode.isStrictCount) return false;
                if (!window.$abcSocialSecurity.isOpenSocial) return false;
                return this.supportCollect(trData);
            },
            supportCollect(trData) {
                return TraceCode.supportCollect(trData.productInfo);
            },
            isSpecificationMatch(trData) {
                return TraceCode.isSpecificationMatch(trData.productInfo);
            },
            customTrDataCy(item) {
                return `trace-code-collect-panel-tr-${item.name}`;
            },
            getUnitCount(item) {
                return TraceCode.getUnitCount(item);
            },
            validateTraceableCodeCount(item) {
                const {
                    validateFlag,
                } = TraceCode.validateTraceableCodeCount(item);
                return validateFlag;
            },
            handleOpenNoCode() {
                this._noTraceCodeCheckDialog = new NoTraceCodeCheckDialog({
                    formItems: this.flatFormItems,
                    onConfirm: (checkedList) => {
                        checkedList.forEach((item) => {
                            this.flatFormItems.forEach((it) => {
                                const goodsId = item.productInfo?.id;
                                // 将同一种商品都设置为无码
                                if (goodsId === it.productInfo?.id) {
                                    this.$set(it.productInfo, 'traceableCodeNoInfoList', [{
                                        type: TraceableCodeTypeEnum.NO_CODE,
                                        no: TraceCode.getDefaultNoCodeIdentification(it.productInfo),
                                    }]);
                                    it.traceableCodeList = this.initTraceCodeList(it);
                                }
                            });
                        });

                        this.updateProductInfoHandler();
                    },
                    onClose: () => {
                        this._noTraceCodeCheckDialog = null;
                    },
                    afterConfirm: () => {
                        this.initSichuanNoCodeGoods();
                    },
                });
                this._noTraceCodeCheckDialog.generateDialogAsync();
            },

            async initHandler() {
                const formItems = (this.hasEnableCollCheckStrictMode && this.dispensedFormItems && this.dispensedFormItems.length) ? [] : this.formItems;
                this.flatFormItems = this.flatItemsHandler(Clone(this.isReReportMode ? this.dispensedFormItems : formItems), this.isReReportMode ? [dispenseItemStatusEnum.DISPENSED,dispenseItemStatusEnum.WAITING] : this.filterItemStatus);
                this.flatDispensedFormItems = this.isReReportMode ? [] : this.flatItemsHandler(Clone(this.dispensedFormItems));
                await this.createMaxTraceCount();
                await this.getTraceCodeAvailableLog();
                // 初始化只生成code上的错误
                this.generateCodeWarnInfo(false);
                this.emitCountHandler();

                await this.initSichuanNoCodeGoods();
            },
            async getTraceCodeAvailableLog() {
                for (const item of this.flatFormItems) {
                    if (!item.traceableCodeList || item.traceableCodeList.length === 0) {
                        const { data } = await GoodsV3API.fetchTraceCodeAvailableLog({
                            goodsId: item.productId,
                            limit: 4,
                            offset: 0,
                        });
                        const rows = data?.rows ?? [];
                        if (rows && rows?.length > 0) {
                            this.$set(item,'isShowSplitZeroTip',true);
                            this._setTimer = setTimeout(() => {
                                this.$set(item,'isShowSplitZeroTip',false);
                                this._setTimer && clearTimeout(this._setTimer);
                            }, 3000);
                            break;
                        }
                    }
                }
            },
            async handleCodeChange(res) {
                await this.createMaxTraceCount([res]);
                this.generateCodeWarnInfo(false);
                this.emitCountHandler();
            },
            async updateDetailSuccess(res) {
                await this.createMaxTraceCount([res]);
            },
            emitCountHandler() {
                const shouldCollectItems = this.flatFormItems.filter((item) => TraceCode.supportCollect(item.productInfo));
                const collectedItems = this.flatFormItems.filter((item) => item.traceableCodeList?.length);
                this.$emit('init-count', {
                    shouldCollectItem: shouldCollectItems.length,
                    shouldCollectCount: shouldCollectItems.reduce((acc, item) => acc + (item._maxTraceCodeCount ?? 0), 0),
                    collectedItem: collectedItems.length,
                    collectedCount: collectedItems.reduce((total, item) => {
                        const codeCount = item.traceableCodeList?.reduce((acc, cur) => acc + (+cur.count || 1), 0);
                        return total + (+codeCount || 1);
                    }, 0),
                    collectCountError: shouldCollectItems.some(((it) => {
                        const codeCount = it.traceableCodeList?.reduce((acc, cur) => acc + (+cur.count || 1), 0);
                        return codeCount < (it._maxTraceCodeCount ?? 0);
                    })),
                });
            },
            // 计算应采数量，拆零标记，追溯码数量平摊等
            async createMaxTraceCount(list) {
                try {
                    if (this.disabled) return;
                    this.$emit('update:saveLoading', true);
                    const maxTraceCountList = await TraceCode.getMaxTraceCountList({
                        dataList: list || this.flatFormItems,
                        scene: TraceCodeScenesEnum.PHARMACY,
                        getUnitInfo: (item) => {
                            return {
                                ...item,
                                unitCount: TraceCode.getUnitCount(item),
                            };
                        },
                        sceneType: this.sceneType,
                        patientOrderId: this.patientOrderId,
                    });
                    if (maxTraceCountList?.length) {
                        this.flatFormItems.forEach((item) => {
                            const res = maxTraceCountList.find((it) => (it.keyId === item.keyId));
                            if (res) {
                                if (typeof res.traceableCodeMaxNum === 'number') {
                                    item._traceableCodeMaxNum = res.traceableCodeMaxNum;
                                }
                                item._maxTraceCodeCount = res.traceableCodeNum ?? 0;
                                // 是否应用了限价单位
                                this.$set(item, '_useLimitPriceTargetUnit', res.useLimitPriceTargetUnit);
                                this.$set(item,'pieceNum',res.pieceNum);
                                this.$set(item, 'traceableCodeRule',res.traceableCodeRule ?? {});

                                /**
                                 * 医保拆零标志 二进制
                                 * 第0位：是否允许拆零 0-不拆零 1-拆零 在10中0是第0位
                                 * 第1位：是否允许编辑是否拆零 0-不允许 1-允许 在10中1是第1位
                                 * 10->2 不拆零允许编辑; 11->3 拆零允许编辑;00->0 不拆零不允许编辑; 01->1 拆零不允许编辑
                                 */
                                this.$set(item,'shebaoDismountingFlag',res.shebaoDismountingFlag); //医保拆零标志
                                if (TraceCode.isShebaoDismountingFlag(res.shebaoDismountingFlag)) {
                                    this.$set(item,'isShebaoDismountingFlag',true);
                                } else {
                                    this.$set(item,'isShebaoDismountingFlag',false);
                                }
                                // 默认采集数量，按医保大单位开单，但是地区医保要求采“小单位数量”个追溯码，这儿开1盒，规格是 10支/盒，此时新加的追溯码数量就应该是10
                                item._collectCountTransFactor = res.collectCountTransFactor || 1;
                                const {
                                    productInfo,
                                    unit,
                                } = item;
                                // 是无码商品并且又返回了traceableCodeNum，需要修正数量
                                if (TraceCode.isNoTraceCodeGoods(productInfo)) {
                                    const _list = TraceCode.initNoTraceCodeList(item);
                                    if (_list && _list.length) {
                                        item.traceableCodeList = _list;
                                    }
                                }

                                // 使用后端算好的追溯码数量与单位等信息
                                if (res.list) {
                                    const traceCodeMap = (res.list || []).reduce((res, codeInfo) => {
                                        res[codeInfo.no] = codeInfo;
                                        return res;
                                    }, {});

                                    const codesToRemove = new Set();
                                    item.traceableCodeList?.forEach((codeInfo) => {
                                        const _codeInfo = traceCodeMap[codeInfo.no];
                                        // 开启拆零不采模式
                                        if (this.hasCodeSafeOpened && [AliPackageLevelEnum.MEDIUM,AliPackageLevelEnum.BIG].includes(_codeInfo?.aliPackageLevel) && this.hasInterceptAliPackageLevel) {
                                            this.$Toast({
                                                type: 'error',
                                                message: `无法采集：本次采集追溯码为${AliPackageLevelLabel[_codeInfo?.aliPackageLevel]}，请检查并采集小包装上的追溯码`,
                                            });
                                            codesToRemove.add(codeInfo.no);
                                        } else if (_codeInfo) {
                                            const {
                                                hisPieceCount,
                                                hisPackageCount,
                                                hisLeftTotalPieceCount,
                                                trdnFlag,
                                            } = _codeInfo || {};
                                            const isDismounting = TraceCode.isShouldApplyPieceUnit(_codeInfo);
                                            this.$set(codeInfo, 'hisLeftTotalPieceCount',hisLeftTotalPieceCount);
                                            this.$set(codeInfo, 'hisPieceCount',hisPieceCount ?? 0);
                                            this.$set(codeInfo, 'hisPackageCount',hisPackageCount ?? 0);
                                            this.$set(codeInfo, 'unit', (isDismounting ? productInfo.pieceUnit : productInfo.packageUnit) || codeInfo.unit || unit || item.pieceUnit);
                                            this.$set(codeInfo, 'count',!(hisPackageCount > 0 || hisPieceCount > 0) ? Number(codeInfo.count ?? 1) : (isDismounting ? hisPieceCount : hisPackageCount) ?? Number(codeInfo.count ?? 1));
                                            this.$set(codeInfo, 'trdnFlag',trdnFlag);

                                            //首次采集追溯码，并且从码上放心转换规格失败的
                                            if (_codeInfo?.newTraceCodeNo) {
                                                this.traceCodeCallbackQueue.push(() => {
                                                    this.openLimitSetDialog(item,codeInfo);
                                                });
                                            }
                                        }
                                    });
                                    if (codesToRemove.size > 0) {
                                        item.traceableCodeList = item.traceableCodeList.filter((c) => !codesToRemove.has(c.no));
                                    }
                                }
                            }
                        });
                    }
                } catch (e) {
                    console.error('createMaxTraceCount', e);
                } finally {
                    this.$emit('update:saveLoading', false);
                    this._openAllCallbackQueueDialog && this._openAllCallbackQueueDialog();
                }
            },
            flatItemsHandler(formItems, filterItemStatus) {
                const _arr = [];
                formItems.filter((item) => {
                    // 不传不过滤
                    if (!filterItemStatus) return true;
                    item.composeChildren = item.composeChildren?.filter((it) => {
                        return it.checked || filterItemStatus.indexOf(it.status) > -1;
                    }) || [];
                    return filterItemStatus.indexOf(item.status) > -1 || item.composeChildren?.length;
                }).forEach((item) => {
                    if (this.isNoTraceCodeGoods(item) && this.hasEnableDismountingMode) {
                        //如果是无码商品 强制为不拆零不可编辑
                        this.$set(item,'shebaoDismountingFlag',ShebaoTraceableCodeDismountingFlagEnum.NO_DISMOUNTING_NO_EDIT);
                    }
                    if (item.productType === GoodsTypeEnum.COMPOSE) {
                        item.composeChildren?.forEach((child) => {
                            if (TraceCode.formItemSupportTraceCode(child)) {
                                const _obj = {
                                    ...child,
                                    keyId: child.keyId || createGUID(),
                                    isCompose: true,
                                    _chargeItemCount: item.unitCount,
                                    isShebaoDismountingFlag: TraceCode.isShebaoDismountingFlag(child.shebaoDismountingFlag),
                                };
                                _obj.traceableCodeList = this.initTraceCodeList({
                                    ..._obj,
                                    unitCount: this.getUnitCount(_obj),
                                });
                                _arr.push(_obj);
                            }
                        });
                    } else {
                        _arr.push({
                            ...item,
                            traceableCodeList: this.initTraceCodeList(item),
                            isShebaoDismountingFlag: TraceCode.isShebaoDismountingFlag(item.shebaoDismountingFlag),
                            keyId: item.keyId || createGUID(),
                        });
                    }
                });
                _arr.sort((a, b) => {
                    let aSort = 0;
                    let bSort = 0;

                    // 无需采集排最后
                    if (TraceCode.isNoTraceCodeGoods(a.productInfo)) {
                        aSort += 1;
                    } else if (!TraceCode.supportCollect(a.productInfo)) {
                        aSort += 2;
                    }

                    if (TraceCode.isNoTraceCodeGoods(b.productInfo)) {
                        bSort += 1;
                    } else if (!TraceCode.supportCollect(b.productInfo)) {
                        bSort += 2;
                    }
                    return aSort - bSort;
                });
                return _arr;
            },
            isNoTraceCodeGoods(item) {
                return TraceCode.isNoTraceCodeGoods(item.productInfo);
            },
            /**
             * @desc 无码需要初始化且不可改
             * <AUTHOR> Yang
             * @date 2024-09-10 14:01:00
            */
            initTraceCodeList(item) {
                const {
                    productInfo = {},
                } = item;
                // 是无码商品并且没有录入追溯码的情况下生成
                if (TraceCode.isNoTraceCodeGoods(productInfo) && !item.traceableCodeList?.length) {
                    const res = TraceCode.initNoTraceCodeList(item);
                    if (res && res.length) {
                        return res;
                    }
                }
                return (item.traceableCodeList || []).map((it) => {
                    const isDismounting = TraceCode.isShouldApplyPieceUnit(it);
                    it.keyId = it.keyId || createGUID();
                    it.unit = isDismounting ? productInfo?.pieceUnit : productInfo?.packageUnit;
                    it.count = !(it.hisPackageCount > 0 || it.hisPieceCount > 0) ? it.count : (isDismounting ? it.hisPieceCount : it.hisPackageCount);
                    return it;
                });
            },
            startBarcodeDetect() {
                if (this.disabled) return;
                if (this.barcodeDetector) return;
                this.barcodeDetector = BarcodeDetectorV2.getInstance();
                this.barcodeDetector.startDetect(this.handleScanCode, true, (instance) => {
                    this.isScanning = instance.isScanning;
                });
            },
            stopBarcodeDetect() {
                if (!this.barcodeDetector) return;
                this.barcodeDetector.stopDetect(this.handleScanCode);
                this.barcodeDetector = null;
            },
            getItemId(item) {
                return item.id || item.keyId;
            },

            getConfirmTitle() {
                // if (this.$abcSocialSecurity.config.isSichuan) {
                //     return '「依码支付」风险提示';
                // }
                return '风险提醒';
            },
            getConfirmContent(res) {
                const resContents = res.errorList.map((it) => {
                    const {
                        count, warnTips,
                    } = it;
                    return (
                        <div key={warnTips}>
                            有 {count} 个商品{warnTips}
                        </div>
                    );
                });
                // if (this.$abcSocialSecurity.config.isSichuan) {
                //     resContents.push(
                //         <div key="sichuan-warning">
                //             若继续提交，对应费用医保将 <abc-text theme="warning-light">不予支付</abc-text>。是否继续？
                //         </div>,
                //     );
                // }
                return <div>{resContents}</div>;
            },
            async confirm(isNeedValidate = true,action) {
                // 开启拆零不采集模式，且 item 为拆零，则清空已采集追溯码
                if (this.hasEnableDismountingMode) {
                    this.flatFormItems.forEach((item) => {
                        if ([ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_NO_EDIT, ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_EDIT].includes(item.isShebaoDismountingFlag) && item.traceableCodeList?.length) {
                            this.$set(item, 'traceableCodeList', []);
                        }
                    });
                }
                if (!isNeedValidate) {
                    await this.validatePharmaceutical(action);
                    return;
                }
                const res = await TraceCode.validate({
                    scene: TraceCodeScenesEnum.PHARMACY,
                    dataList: this.flatFormItems,
                    sceneType: SceneTypeEnum.SELL,
                    getUnitInfo: (item) => {
                        return {
                            ...item,
                            unitCount: TraceCode.getUnitCount(item),
                        };
                    },
                    patientOrderId: this.patientOrderId,
                    needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckPharmacy(),
                });
                if (res.flag) {
                    await this.validatePharmaceutical(action);
                } else {
                    this.generateCodeWarnInfo();
                    this.$confirm({
                        type: 'warn',
                        title: '风险提醒',
                        content: () => {
                            return <abc-flex vertical>
                                {
                                    res.errorList.map((it) => {
                                        const {
                                            count,
                                            warnTips,
                                        } = it;
                                        return <abc-flex gap={4}>
                                            <abc-text theme="warning-light" size="normal" bold>{count}</abc-text>
                                            <abc-text theme="black" size="normal">个商品{warnTips}</abc-text>
                                        </abc-flex>;
                                    })
                                }
                            </abc-flex>;
                        },
                        confirmText: '去修改',
                        cancelText: '仍要提交',
                        disabledKeyboard: true,
                        showClose: false,
                        onCancel: () => this.validatePharmaceutical(action),
                    });

                    this.scrollTarget(res.errorList[0]);
                }
            },
            scrollTarget(item) {
                if (item.keyId) {
                    this.$nextTick(() => {
                        this.$refs.abcTable?.$el.querySelector(`.abc-table-tr[data-id="${item.keyId}"]`)?.scrollIntoView({
                            behavior: 'smooth',
                        });
                    });
                }
            },
            async validatePharmaceutical(action) {
                // 海南对接药监追溯码查询
                if (await TraceCode.getPharmaceuticalTraceCodeQueryConfig()) {
                    const data = await TraceCode.fetchPharmaceuticalTraceCodeDetails({
                        dataList: this.flatFormItems,
                        beforeHook: () => {
                            this._loading = this.$Loading({
                                text: '正在核验追溯码',
                                customClass: 'print-loading-wrapper',
                            });
                        },
                        afterHook: () => {
                            this._loading?.close();
                        },
                        errorHook: (error) => {
                            this.$Toast({
                                type: 'error',
                                message: error?.message ?? '查询失败',
                            });
                        },
                    });
                    if (data?.length) {
                        await new TraceCodeWarningDialog({
                            value: true,
                            traceCodeDetails: data,
                            onConfirm: () => this.handleConfirm(action),
                        }).generateDialogAsync();
                        return;
                    }
                }
                this.handleConfirm(action);
            },
            handleConfirm(action) {
                this.handleInnerSet();
                this.callbackConfirm && this.callbackConfirm(this.flatFormItems,action);
            },
            handleInnerSet() {
                this.confirmInnerSet && this.flatFormItems.forEach((r) => {
                    (this.isReReportMode ? this.dispensedFormItems : this.formItems).forEach((item) => {
                        // flatFormItems 包含套餐拍平的数据，需要回写到composeChildren中
                        if (item.productType === GoodsTypeEnum.COMPOSE && item.composeChildren?.length) {
                            const child = item.composeChildren.find((it) => (this.getItemId(it)) === this.getItemId(r));
                            if (child) {
                                child.warnInfo = r.warnInfo;
                                child.traceableCodeList = r.traceableCodeList || [];
                                child.productInfo = r.productInfo;
                                this.$set(child,'shebaoDismountingFlag',r.shebaoDismountingFlag);
                                this.$set(child,'traceableCodeRule',r.traceableCodeRule);
                            }
                        } else {
                            // 反写追溯码
                            if ((item.keyId && item.keyId === r.keyId) || (item.id && item.id === r.id)) {
                                item.warnInfo = r.warnInfo;
                                item.traceableCodeList = r.traceableCodeList || [];
                                item.productInfo = r.productInfo;
                                this.$set(item,'shebaoDismountingFlag',r.shebaoDismountingFlag);
                                this.$set(item,'traceableCodeRule',r.traceableCodeRule);
                            }
                        }
                    });
                });
            },
            generateCodeWarnInfo(needItemWarn = true) {
                this.flatFormItems.forEach((item) => {
                    item.composeChildren?.forEach((child) => {
                        if (needItemWarn || child.warnInfo) {
                            this.$set(child, 'warnInfo', TraceCode.validateDataItem({
                                dataItem: child,
                                sceneType: SceneTypeEnum.SELL,
                            }));
                        }
                        child.traceableCodeList.forEach((it) => {
                            it.warnInfo = TraceCode.validateTraceCode({
                                dataItem: child,
                                traceCodeObj: it,
                                goodsInfo: child.productInfo,
                                sceneType: SceneTypeEnum.SELL,
                            });
                        });
                    });

                    if (needItemWarn || item.warnInfo) {
                        this.$set(item, 'warnInfo', TraceCode.validateDataItem({
                            dataItem: item,
                            sceneType: SceneTypeEnum.SELL,
                        }));
                    }
                    item.traceableCodeList.forEach((it) => {
                        it.warnInfo = TraceCode.validateTraceCode({
                            dataItem: item,
                            traceCodeObj: it,
                            goodsInfo: item.productInfo,
                            sceneType: SceneTypeEnum.SELL,
                        });
                    });
                });
            },
            handleFocus() {
                this.stopBarcodeDetect();
            },
            handleBlur() {
                this.startBarcodeDetect();
            },
            handleEnter(e) {
                if (!e.target.value) return;
                this.codeQueue.push(e.target.value);
                this.traceCode = '';
                e.target.value = '';
                this.triggerHandler();
            },
            handleScanCode(e, code) {
                if (e.target === this.$refs.searchInput?.$el) {
                    return;
                }
                this.codeQueue.push(code);
                this.triggerHandler();
            },
            async triggerHandler() {
                // 在处理中不再重复执行
                if (this.loading) return;
                const code = this.codeQueue.shift();
                await this.handleTraceCode(code);
                if (this.codeQueue.length) {
                    this.triggerHandler();
                }
            },
            async handleTraceCode(code) {
                code = code.replace(/\s+/g, '');
                if (!TraceCode.isTraceableCode(code)) {
                    this.$Toast({
                        type: 'error',
                        message: TraceCode.hasInterceptTraceCodeFormatLength() ? '无法采集追溯码（医保要求药品追溯码必须为20位或30位，且不应存在汉字或其他符号）' : '未识别到有效的追溯码，请检查扫描或输入是否正确！',
                    });
                    this.traceCode = '';
                    return;
                }

                this.loading = true;
                try {
                    /**
                     * @desc 优先在本地发药项目中找一下是否有直接用，不再发请求
                     * <AUTHOR> Yang
                     * @date 2024-11-01 11:07:38
                     */
                    let traceCodeInfo = await TraceCode.findCodeInfoInLocal(this.flatFormItems, code);
                    if (!traceCodeInfo) {
                        traceCodeInfo = await TraceCode.fetchByCode(code);
                    }
                    const {
                        goodsInfo,
                        traceableCodeNoInfo,
                        traceCode,
                    } = traceCodeInfo;


                    const noCode = TraceCode.getDefaultNoCodeIdentification(goodsInfo);
                    if (noCode && (code.toLocaleLowerCase()).startsWith(noCode.toLocaleLowerCase())) {
                        this.handleOpenNoCode();
                        return;
                    }
                    // 没有解析出药品标识码，都走选择绑定发药项目逻辑
                    if (!traceableCodeNoInfo?.drugIdentificationCode) {
                        this.handleMatchTraceCode(traceCodeInfo);
                        return;
                    }

                    const res = TraceCode.findCanAddTraceCodeItemByCollection({
                        goodsInfo,
                        traceableCodeNoInfo,
                        dataList: this.flatFormItems,
                    });
                    if (res) {
                        if (this.hasEnableDismountingMode) {
                            if (res.isShebaoDismountingFlag) {
                                this.$Toast({
                                    message: '根据医保最新要求，拆零追溯码无需采集',
                                });
                                return;
                            }
                            const { bigShouldCollectCount } = this.getTraceCodeShouldCollectCountInfo(res);
                            const traceCodeCount = res.traceableCodeList?.length; //追溯码个数
                            if (!this.validateTraceableCodeCount(res) || traceCodeCount === bigShouldCollectCount) {
                                // 拆零不采集模式 并且已采=应采需要提示并阻止采集
                                this.$Toast({
                                    message: `不拆零发药时，相同追溯码只允许采集${bigShouldCollectCount}个最小包装单位`,
                                });
                                return;
                            }
                        }
                        const repeatCode = res.traceableCodeList.find((it) => it.no === traceCode);
                        if (repeatCode && this.hasEnableCollCheckStrictMode) {
                            this.$Toast({
                                message: '无法采集追溯码（不拆零采集时，相同追溯码只允许采集一个最小包装单位）',
                            });
                            return;
                        }
                        const ret = this.handleAddTraceCode(res, traceCodeInfo);
                        // 1.重复扫码不走后台计算
                        // 2.追溯码数量超上限无法采集
                        if (!repeatCode && ret) {
                            try {
                                const keyId = res.keyId || (res.productInfo && res.productInfo.id);
                                if (!this.debounceCreateMaxTraceCountMap.has(keyId)) {
                                    this.debounceCreateMaxTraceCountMap.set(
                                        keyId,
                                        debounce((items) => this.createMaxTraceCount(items), 500, true),
                                    );
                                    await this.createMaxTraceCount([res]);
                                } else {
                                    await this.debounceCreateMaxTraceCountMap.get(keyId)([res]);
                                }
                            } catch (error) {
                                console.error('防抖计算失败:', error);
                                // 防抖失败时直接调用原函数确保功能正常
                                await this.createMaxTraceCount([res]);
                            }
                        }
                        this.generateCodeWarnInfo(false);
                        this.scrollTarget(res);
                    } else {

                        if (!goodsInfo) {
                            this.handleMatchTraceCode(traceCodeInfo);
                            return;
                        }

                        if (this._traceCodeConfirmDialog) return;
                        this._traceCodeConfirmDialog = new TraceCodeConfirmDialog({
                            title: '追溯码关联的商品不在发药项目中',
                            traceableCodeNoInfo,
                            traceCode,
                            goodsInfo,
                            onConfirm: (params) => {
                                const {
                                    clearBound = false,
                                } = params || {};
                                // 解绑后清空goodsInfo
                                if (clearBound) {
                                    delete traceCodeInfo.goodsInfo;
                                }
                                this.handleMatchTraceCode(traceCodeInfo);
                            },
                            onClose: () => {
                                this._traceCodeConfirmDialog = null;
                            },
                        });
                        this._traceCodeConfirmDialog.generateDialogAsync();
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            handleMatchTraceCode(traceCodeInfo) {
                const {
                    goodsInfo,
                    traceableCodeNoInfo,
                    traceCode,
                } = traceCodeInfo;
                this._TraceCodeMatchDialog = new TraceCodeMatchDialog({
                    formItems: this.flatFormItems,
                    traceableCodeNoInfo,
                    traceCode,
                    goodsInfo,
                    onConfirm: (res) => {
                        this.confirmMatchHandler(res, traceCodeInfo);
                    },
                    onClose: () => {
                        this._TraceCodeMatchDialog = null;
                    },
                });
                this._TraceCodeMatchDialog.generateDialogAsync();
            },

            async confirmMatchHandler(res, traceCodeInfo) {
                // [特殊无码商品]绑码后需要更新应采数量, 因为初始化会过滤掉他
                // const _obj = this.flatFormItems.find((it) => it.keyId === res.keyId);
                // if (_obj && TraceCode.isNullCodeGoods(_obj.productInfo)) {
                //     await this.createMaxTraceCount([res]);
                // }
                if (this.hasEnableDismountingMode) {
                    if (res.isShebaoDismountingFlag) {
                        this.$Toast({
                            message: '无法采集追溯码（药品勾选拆零）',
                        });
                        return;
                    }
                    const { bigShouldCollectCount } = this.getTraceCodeShouldCollectCountInfo(res);
                    const traceCodeCount = res.traceableCodeList?.length; //追溯码个数
                    if (!this.validateTraceableCodeCount(res) || traceCodeCount === bigShouldCollectCount) {
                        // 拆零不采集模式 并且已采=应采需要提示并阻止采集
                        this.$Toast({
                            message: `不拆零发药时，相同追溯码只允许采集${bigShouldCollectCount}个最小包装单位`,
                        });
                        return;
                    }
                }
                // 某些情况下需要暂存绑定规则
                this.flatFormItems.forEach((it) => {
                    if (it.productInfo && it.productInfo.id === res.productInfo.id) {
                        this.$set(it.productInfo, 'traceableCodeNoInfoList', res.productInfo.traceableCodeNoInfoList);
                    }
                    if (it.keyId === res.keyId) {
                        // 非无码商品匹配后需要清空无码标记
                        if (!TraceCode.isNoTraceCodeGoods(it.productInfo)) {
                            it.traceableCodeList = it.traceableCodeList.filter((item) => {
                                return item.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE;
                            });
                        }
                        this.handleAddTraceCode(it, traceCodeInfo);
                    }
                });
                // 需要直接绑定productInfo药品标识码
                this.updateProductInfoHandler();
                // 始终重新计算
                await this.createMaxTraceCount();
                this.generateCodeWarnInfo(false);
                this.emitCountHandler();
                this.scrollTarget(res);
            },

            handleAddTraceCode(item, traceCodeInfo) {
                const {
                    traceableCodeNoInfo,
                    traceableCodeList,
                    traceCode,
                } = traceCodeInfo;

                // 重复扫码只用加数量
                const repeatCode = item.traceableCodeList.find((it) => it.no === traceCode);
                let keyId = null;
                const unit = '';
                if (repeatCode) {
                    const {
                        count,unit,
                    } = TraceCode.getTraceCodeRepeatedCollectCount(item,repeatCode);
                    /**
                     * @desc 后台说如果是1就不会返回count
                     * <AUTHOR> Yang
                     * @date 2024-11-07 17:32:47
                    */
                    if (!repeatCode.count) {
                        this.$set(repeatCode, 'count', 1);
                    }
                    repeatCode.count = count;
                    repeatCode.unit = unit;
                    if (item.productInfo.packageUnit === unit) {
                        this.$set(repeatCode, 'hisPackageCount', count);
                        this.$set(repeatCode, 'hisPieceCount', 0);
                    } else {
                        this.$set(repeatCode, 'hisPieceCount', count);
                        this.$set(repeatCode, 'hisPackageCount', 0);
                    }
                    keyId = repeatCode.keyId;
                } else {
                    if (typeof item._traceableCodeMaxNum === 'number' && item.traceableCodeList.length >= item._traceableCodeMaxNum) {
                        this.$Toast({
                            type: 'error',
                            message: `无法继续采集追溯码（医保要求追溯码最多采集${item._traceableCodeMaxNum}条）`,
                        });
                        return false;
                    }
                    const count = null;
                    keyId = createGUID();
                    item.traceableCodeList.push({
                        traceableCodeNoInfo,
                        traceableCodeList,
                        no: traceCode,
                        keyId,
                        count,
                        unit,
                    });
                    // /**
                    //  * @desc 剩余应采数量<=0，默认数量就是1，需要在已采集并且采集数量大于1的最末位码减去1
                    //  * <AUTHOR> Yang
                    //  * @date 2024-11-18 16:20:34
                    //  */
                    // const {
                    //     _maxTraceCodeCount,
                    //     _collectCountTransFactor,
                    // } = item;
                    // if (_collectCountTransFactor > 1 && item.traceableCodeList.length > 0) {
                    //     const totalCount = TraceCode.getTraceCodeCollectCount(item);
                    //     if (totalCount - _maxTraceCodeCount > 0) {
                    //         let idx = -1;
                    //         item.traceableCodeList.forEach((it, index) => {
                    //             if (it.count > 1) {
                    //                 idx = index;
                    //             }
                    //         });
                    //         if (idx > -1) {
                    //             item.traceableCodeList[idx].count--;
                    //         }
                    //     }
                    // }
                }
                this.emitCountHandler();
                this.$nextTick(() => {
                    const $code = this.$el.querySelector(`.trace-code-list-item-${keyId}`);
                    if ($code) {
                        $code.classList.add('highlight');
                        this._timer = setTimeout(() => {
                            $code.classList.remove('highlight');
                        }, 1500);
                    }
                });
                return true;
            },

            updateProductInfoHandler() {
                this.flatFormItems.forEach((r) => {
                    this.formItems.forEach((item) => {
                        // flatFormItems 包含套餐拍平的数据，需要回写到composeChildren中
                        if (item.productType === GoodsTypeEnum.COMPOSE && item.composeChildren?.length) {
                            const child = item.composeChildren.find((it) => (this.getItemId(it)) === this.getItemId(r));
                            if (child) {
                                child.productInfo = r.productInfo;
                            }
                        } else {
                            if ((this.getItemId(item)) === this.getItemId(r)) {
                                item.productInfo = r.productInfo;
                            }
                        }
                    });
                });
            },

            handleAddCode(codeInfo) {
                this.handleTraceCode(codeInfo.traceCode);
            },

            getNoTraceCodeGoodsList() {
                const list = this.flatFormItems?.filter((it) => {
                    return this.isNoTraceCodeGoods(it);
                });
                return list || [];
            },
            async initSichuanNoCodeGoods() {
                if (!this.$abcSocialSecurity.config.isSichuan || !this.$abcSocialSecurity.isOpenSocial) {
                    return ;
                }
                const {
                    getGoodsListNoCode, checkNeedApplySichuanNoCode,
                } = useSichuanNoCode();
                const goodsItems = this.getNoTraceCodeGoodsList().filter((item) => {
                    return checkNeedApplySichuanNoCode(item.productInfo.shebao);
                }).map((item) => {
                    return item.productInfo;
                });
                try {
                    if (!goodsItems?.length) {
                        return;
                    }
                    const data = await getGoodsListNoCode(goodsItems);
                    const { queryItemList } = data;
                    if (queryItemList?.length) {
                        // 遍历 flatFormItems，设置 applyNoCodeInfo 到 productInfo
                        this.flatFormItems.forEach((item) => {
                            const noCodeInfo = queryItemList.find((info) => info.goodsId === item.productInfo?.id);
                            if (item.productInfo) {
                                this.$set(item.productInfo, 'applyNoCodeInfo', noCodeInfo || null);
                            }
                        });
                    }
                } catch (e) {
                    console.log(e);
                }
            },
            openLimitSetDialog(traceCodeUseInfo, code) {
                return new Promise((resolve) => {
                    new TraceCodeLimitDialog({
                        title: '请确认追溯码可用上限',
                        isShowTips: false,
                        showClose: false,
                        isShowLeftCountSet: true,
                        traceCodeInfo: code,
                        goods: traceCodeUseInfo.productInfo,
                        onConfirm: () => {
                            if (this.traceCodeCallbackQueue.length === 0) {
                                this._debounceCreateMaxTraceCount();
                            }
                            resolve();
                        },
                        onClose: () => {
                            //取消本次采集 移除当前码
                            traceCodeUseInfo.traceableCodeList = traceCodeUseInfo.traceableCodeList.filter((c) => c.no !== code.no);
                            if (this.traceCodeCallbackQueue.length === 0) {
                                this._debounceCreateMaxTraceCount();
                            }
                            resolve();
                        },
                    }).generateDialogAsync({ parent: this });
                });
            },
            // 添加一个新的队列处理方法
            async processDialogQueue() {
                if (this.traceCodeCallbackQueue.length === 0) {
                    return;
                }

                // 取出队列中的第一个回调并执行
                const callback = this.traceCodeCallbackQueue.shift();
                if (typeof callback === 'function') {
                    await callback();
                }

                // 处理队列中的下一个回调
                this.processDialogQueue();
            },
            handleOpenDismountingModeHelp() {
                const currentHeight = window.innerHeight;
                const newHeight = Math.min(Math.round(currentHeight * 0.76), 800);
                window.open(`https://${location.host}/cms/view/loginfo/ffffffff000000003504ccf021654000`, '_blank', `width=788,height=${newHeight}`);
            },
        },
    };
</script>
